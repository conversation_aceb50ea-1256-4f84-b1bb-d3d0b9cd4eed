import com.android.build.api.dsl.ApplicationExtension
import noice.app.configureKotlinAndroid
import noice.app.libs
import noice.app.versionCodeByGitMerges
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

class AndroidApplicationConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.application")
                apply("kotlin-android")
                apply("kotlin-kapt")
                apply("kotlin-parcelize")
                apply("androidx.navigation.safeargs.kotlin")
            }

            extensions.configure<ApplicationExtension> {
                defaultConfig {
                    versionCode = project.versionCodeByGitMerges()
                    versionName = libs.findVersion("appVersionName").get().toString()
                    targetSdk = libs.findVersion("appTargetSdk").get().toString().toInt()
                }
                configureKotlinAndroid(this)
            }
        }
    }
}
