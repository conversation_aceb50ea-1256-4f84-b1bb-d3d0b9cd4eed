package noice.app

import com.android.build.api.dsl.CommonExtension

internal fun configurePackaging(
    commonExtension: CommonExtension<*, *, *, *, *, *>
) {
    commonExtension.apply {
        packaging {
            resources {
                excludes.addAll(
                    listOf(
                        "lib/*/libRSSupport.so",
                        "lib/*/librsjni.so",
                        "lib/*/librsjni_androidx.so",
                        "lib/*/libagora_ai_denoise_extension.so",
                        "lib/*/libagora_fdkaac.so",
                        "lib/*/libagora_full_audio_format_extension.so",
                        "lib/*/libagora_mpg123.so",
                        "lib/*/libagora-core.so",
                        "lib/*/libagora-rtc-sdk.so",
                        "lib/*/libagora-ffmpeg.so",
                        "lib/*/libagora_segmentation_extension.so",
                        "lib/*/libagora-soundtouch.so",
                        "lib/*/libagora_dav1d.so",
                        "lib/*/libagora-fdkaac.so",
                        "META-INF/*"
                    )
                )
            }
        }
    }
}