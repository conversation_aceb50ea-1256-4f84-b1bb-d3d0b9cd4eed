package noice.app

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.BuildType

enum class NoiceBuildType(
    val buildType: String,
    val isMinifyEnabled: <PERSON>olean,
    val isShrinkResources: <PERSON><PERSON><PERSON>,
    val isDefault: Boolean,
) {
    RELEASE("release", true, true, true),
    DEBUG("debug", false, false, true),
    TOYOTA_RELEASE("toyotaRelease", true, true, false),
    TOYOTA_DEBUG("toyotaDebug", false, false, false),
}

internal fun configureBuildTypes(
    applicationExtension: ApplicationExtension,
    buildTypeConfigurationBlock: BuildType.(buildType: NoiceBuildType) -> Unit = {}
) {
    applicationExtension.apply {
        buildTypes {
            NoiceBuildType.values().forEach {
                val action =
                    if (it.isDefault) getByName(it.buildType) else create(it.buildType)
                action.apply {
                    val orientation : String
                    when (it) {
                        NoiceBuildType.TOYOTA_RELEASE -> {
                            initWith(getByName(NoiceBuildType.RELEASE.buildType))
                            orientation = "fullSensor"
                        }

                        NoiceBuildType.TOYOTA_DEBUG -> {
                            initWith(getByName(NoiceBuildType.DEBUG.buildType))
                            orientation = "fullSensor"
                        }

                        else -> {
                            isMinifyEnabled = it.isMinifyEnabled
                            isShrinkResources = it.isShrinkResources
                            signingConfig = signingConfigs.getByName(it.buildType)
                            orientation = "portrait"
                        }

                    }
                    manifestPlaceholders["screenOrientation"] = orientation
                    proguardFiles(
                        getDefaultProguardFile("proguard-android-optimize.txt"),
                        "proguard-rules.pro"
                    )
                    buildTypeConfigurationBlock(this, it)
                }
            }
        }
    }
}