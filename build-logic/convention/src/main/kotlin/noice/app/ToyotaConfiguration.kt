package noice.app

import com.android.build.api.dsl.AndroidSourceSet
import com.android.build.api.dsl.CommonExtension
import org.gradle.api.Project
import org.gradle.api.artifacts.Configuration
import org.gradle.api.artifacts.ConfigurationContainer
import org.gradle.kotlin.dsl.exclude

private const val toyotaImplementation = "toyotaImplementation"

internal fun Project.configureToyotaAndroid(
    commonExtension: CommonExtension<*, *, *, *, *, *>
) {
    commonExtension.apply {
        configurations.create(toyotaImplementation) {
            appendToyotaExclusions()
        }
        configurations.combineImplementation()
        sourceSets {
            val toyotaSourceSet = { sourceSet: AndroidSourceSet ->
                sourceSet.java.srcDirs("src/toyota/java")
                sourceSet.res.srcDirs("src/toyota/res")
                sourceSet.manifest.srcFile("src/toyota/AndroidManifest.xml")
            }
            getByName("toyotaRelease") {
                toyotaSourceSet(this)
            }
            getByName("toyotaDebug") {
                toyotaSourceSet(this)
            }
        }
    }

}

private fun Configuration.appendToyotaExclusions() {
    exclude(group = "com.google.android.gms")
    // Play
    exclude(group = "com.google.android.play")
    // Firebase
    exclude(group = "com.google.firebase")
    exclude(group = "com.firebaseui")
    // Billing
    exclude(group = "com.android.billingclient")
    // Install Referrer
    exclude(group = "com.android.installreferrer", module = "installreferrer")
    // Recaptcha
    exclude(group = "com.google.android.recaptcha", module = "recaptcha")
    // Exoplayer
    exclude(group = "com.google.android.exoplayer", module = "extension-ima")
    exclude(group = "com.google.android.exoplayer", module = "extension-cast")

}

private fun ConfigurationContainer.combineImplementation() {
    getByName("toyotaReleaseImplementation") {
        extendsFrom(getByName(toyotaImplementation))
    }
    getByName("toyotaDebugImplementation") {
        extendsFrom(getByName(toyotaImplementation))
    }
}