package noice.app

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project

internal fun Project.configureKotlinAndroid(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    commonExtension.apply {
        compileSdk = libs.findVersion("appCompileSdk").get().toString().toInt()
        ndkVersion =  libs.findVersion("appNdk").get().toString()
        defaultConfig {
            minSdk = libs.findVersion("appMinVersion").get().toString().toInt()
        }

        buildFeatures {
            viewBinding = true
            dataBinding { enable = true }
            aidl = true
        }

        compileOptions {
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
        }

        androidResources {
            noCompress += setOf("db")
        }
    }
}

