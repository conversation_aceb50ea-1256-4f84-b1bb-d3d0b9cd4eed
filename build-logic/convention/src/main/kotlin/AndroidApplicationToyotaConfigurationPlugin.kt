import com.android.build.api.dsl.ApplicationExtension
import noice.app.configureToyotaAndroid
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

class AndroidApplicationToyotaConfigurationPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            extensions.configure<ApplicationExtension> {
                configureToyotaAndroid(this)
            }
        }
    }
}
