import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    `kotlin-dsl`
}

group = "noice.app.build.logic"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

kotlin {
    compilerOptions {
        jvmTarget = JvmTarget.JVM_17
    }
}

dependencies {
    compileOnly(libs.android.gradlePlugin)
    compileOnly(libs.android.tools.common)
    compileOnly(libs.firebase.crashlytics.gradlePlugin)
    compileOnly(libs.kotlin.gradlePlugin)
}

gradlePlugin {
    plugins {
        register("androidFlavors") {
            id = "noice.android.application.flavors"
            implementationClass = "AndroidApplicationFlavorsConventionPlugin"
        }
        register("androidBuildTypes") {
            id = "noice.android.application.build.types"
            implementationClass = "AndroidApplicationBuildTypeConventionPlugin"
        }
        register("androidToyotaConfiguration") {
            id = "noice.android.application.toyota.configuration"
            implementationClass = "AndroidApplicationToyotaConfigurationPlugin"
        }
        register("androidPackaging") {
            id = "noice.android.application.packaging"
            implementationClass = "AndroidApplicationPackagingConventionPlugin"
        }
        register("androidAppSigning") {
            id = "noice.android.application.signing"
            implementationClass = "AndroidApplicationSigningConventionPlugin"
        }
        register("androidApplication") {
            id = "noice.android.application"
            implementationClass = "AndroidApplicationConventionPlugin"
        }
        register("androidCompose") {
            id = "noice.android.application.dependencies.compose"
            implementationClass = "AndroidApplicationComposeConventionPlugin"
        }
        register("androidHilt") {
            id = "noice.android.application.dependencies.hilt"
            implementationClass = "AndroidHiltConventionPlugin"
        }
        register("androidRoom") {
            id = "noice.android.application.dependencies.room"
            implementationClass = "AndroidRoomConventionPlugin"
        }
        register("firebaseDependencies") {
            id = "noice.android.application.dependencies.firebase"
            implementationClass = "AndroidFirebaseConventionPlugin"
        }
        register("kaptsDependencies") {
            id = "noice.android.application.dependencies.kapts"
            implementationClass = "AndroidKaptConventionPlugin"
        }
    }

}