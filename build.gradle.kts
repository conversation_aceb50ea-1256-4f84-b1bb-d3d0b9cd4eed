// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.firebase.crashlytics) apply false
    alias(libs.plugins.gms) apply false
    alias(libs.plugins.safeargs) apply false
    alias(libs.plugins.hilt) apply false
    alias(libs.plugins.android.test) apply false
    alias(libs.plugins.baselineprofile) apply false
}

buildscript {
    dependencies {
        classpath(libs.keyninja)
    }
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
    delete(file(rootProject.projectDir.path + "/app/src/main/java/androidx/"))
    delete(file(rootProject.projectDir.path + "/app/src/main/res/values/key_ninja_strings.xml"))
}