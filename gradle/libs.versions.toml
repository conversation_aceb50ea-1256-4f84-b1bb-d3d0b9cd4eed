[versions]

#Project configs
appVersionName = "3.51"
appVersionCode = "253"

appCompileSdk = "34"
appId = "noice.app"
appTargetSdk = "34"
appMinVersion = "23"
appNdk = "23.1.7779620"
appRenderscript = "21"

#Compiler configs
keyninja = "1.1"
kotlinCompilerExtension = "1.4.4"
kotlin = "1.8.10"
androidTools = "31.4.0"
androidBuildTools = "7.2.2"
gms = "4.4.2"
crashlytics = "2.8.1"
media3 = "1.4.1"
navigationSafeArgsGradlePlugin = "2.5.3"

#Dependencies configs
exoplayer = "2.19.1"
androidLifecycle = "2.5.1"
ktorServer = "2.2.3"
room = "2.4.3"
firebase = "31.0.1"
compose = "2023.04.01"
glide = "4.15.1"
billing = "6.0.1"
hilt = "2.50"
camerax = '1.3.0-alpha06'
jwt = "0.11.5"
retrofit = "2.11.0"

# MoEngage
moeAndroidSdk = "13.02.00"
moeInApp = "8.3.1"
moeRichNotification = "5.0.1"
moeCardsCore = "2.0.2"

# AGP and tools should be updated together
androidGradlePlugin = "8.4.1"
material3Android = "1.2.1"
material3AndroidVersion = "1.2.1"
uiautomator = "2.2.0"
benchmarkMacroJunit4 = "1.2.3"
baselineprofile = "1.2.3"
profileinstaller = "1.3.1"

[bundles]
androidx-major = ["androidx-core-ktx", "androidx-splash", "androidx-workRunTime", "androidx-concurrent",
    "androidx-security", "androidx-arch", "androidx-room-runtime", "androidx-room-ktx"]

androidx-ui = ["androidx-appcompat", "androidx-multidex", "androidx-legacySupport", "androidx-constraintlayout",
    "androidx-recyclerview", "androidx-swiperefreshlayout", "androidx-fragment", "androidx-navigationFragment",
    "androidx-navigationUi", "androidx-pagingRuntime", "androidx-palette", "material", "flexbox"]

androidx-lifecycle = ["androidx-lifecycle-viewModel", "androidx-lifecycle-liveData", "androidx-lifecycle-runtime",
    "androidx-lifecycle-service", "androidx-lifecycle-process"]

androidx-camera = ["androidx-camera-core", "androidx-camera-view", "androidx-camera-lifecycle"]

#CAUTION: used in plugins
androidx-compose = ["androidx-compose-material3", "androidx-compose-material", "androidx-compose-ui",
    "androidx-compose-ui-tooling-preview", "androidx-compose-animation", "androidx-compose-constraintLayout",
    "androidx-activity-compose", "androidx-lifecycle-viewModel-compose", "androidx-lifecycle-runtime-compose", "coil-compose",
    "accompanist-pager", "accompanist-ui", "accompanist-refresh"]

exoplayer = ["exoplayer-hls", "exoplayer-dash", "exoplayer-ima",
    "exoplayer-cronet", "exoplayer-ui", "exoplayer-core",
    "exoplayer-mediasession", "exoplayer-cast"]

#CAUTION: used in plugins
firebase = ["firebase-analytics", "firebase-crashlytics", "firebase-auth", "firebase-messaging", "firebase-config",
    "firebase-firestore", "firebase-database", "firebase-ui"]

http = ["retrofit", "retrofit-gson", "logging-interceptor", "chucker", "glide", "glide-compose", "glide-svg", "glide-okhttp3"]

google = ["billing", "billing-ktx", "integrity", "recaptcha", "play-services-analytics", "play-services-location",
    "play-services-auth", "play-services-auth", "play-services-basement", "play-services-ads-identifier", "play-services-ads",
    "play-services-cast-framework", "androidyoutubeplayer-core", "installreferrer", "play-review", "play-review-ktx", "play-update", "play-update-ktx"]

toyota = ["checkerframework", "cronet", "posthog", "car-ui"]

others = ["ktor-core", "ktor-netty", "jwt", "bouncycastle", "pubnub", "libphonenumber", "facebook-login",
    "facebook-share", "eventbus", "apacheCommon", "agora", "appsflyer", "moe-core", "moe-cards-core", "moe-inapp",
    "moe-notification", "circleindicator", "skeleton", "gesture-recycler", "supernova-emoji", "lottie", "qr-generator"]

android-unit-testing = ["junit", "turbine", "coroutines-test", "arch-testing", "mockito-inline", "mockito-kotlin"]

android-ui-testing = ["test-extension", "espresso-core"]


[libraries]

androidx-media3-cast = { module = "androidx.media3:media3-cast", version.ref = "media3" }
androidx-media3-common = { module = "androidx.media3:media3-common", version.ref = "media3" }
androidx-media3-datasource-cronet = { module = "androidx.media3:media3-datasource-cronet", version.ref = "media3" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
androidx-media3-exoplayer-dash = { module = "androidx.media3:media3-exoplayer-dash", version.ref = "media3" }
androidx-media3-exoplayer-hls = { module = "androidx.media3:media3-exoplayer-hls", version.ref = "media3" }
androidx-media3-exoplayer-ima = { module = "androidx.media3:media3-exoplayer-ima", version.ref = "media3" }
androidx-media3-exoplayer-rtsp = { module = "androidx.media3:media3-exoplayer-rtsp", version.ref = "media3" }
androidx-media3-session = { module = "androidx.media3:media3-session", version.ref = "media3" }
androidx-media3-transformer = { module = "androidx.media3:media3-transformer", version.ref = "media3" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3" }
keyninja = { module = "keyninja:keyninja", version.ref = "keyninja" }
jsonwebtoken-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jwt" }
jsonwebtoken-orgjson = { module = "io.jsonwebtoken:jjwt-orgjson", version.ref = "jwt" }

# uiTesting
test-extension = { module = "androidx.test.ext:junit", version = "1.1.3" }
espresso-core = { module = "androidx.test.espresso:espresso-core", version = "3.4.0" }

# unitTesting
junit = { module = "junit:junit", version = "4.13.2" }
turbine = { module = "app.cash.turbine:turbine", version = "0.9.0" }
coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version = "1.6.4" }
arch-testing = { module = "androidx.arch.core:core-testing", version = "2.1.0" }
mockito-inline = { module = "org.mockito:mockito-inline", version = "3.10.0" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version = "3.2.0" }

# others
ktor-core = { module = "io.ktor:ktor-server-core", version.ref = "ktorServer" }
ktor-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktorServer" }
jwt = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jwt" }
bouncycastle = { module = "org.bouncycastle:bcpkix-jdk15on", version = "1.56" }
pubnub = { module = "com.pubnub:pubnub-gson", version = "6.3.6" }
libphonenumber = { module = "com.googlecode.libphonenumber:libphonenumber", version = "8.13.6" }
facebook-login = { module = "com.facebook.android:facebook-login", version = "latest.release" }
facebook-share = { module = "com.facebook.android:facebook-share", version = "latest.release" }
eventbus = { module = "org.greenrobot:eventbus", version = "3.3.1" }
apacheCommon = { module = "org.apache.commons:commons-text", version = "1.10.0" }
agora = { module = "io.agora.rtc:full-rtc-basic", version = "4.1.1" }
appsflyer = { module = "com.appsflyer:af-android-sdk", version = "6.12.2" }
moe-core = { module = "com.moengage:moe-android-sdk", version.ref = "moeAndroidSdk" }
moe-cards-core = { module = "com.moengage:cards-core", version.ref = "moeCardsCore" }
moe-inapp = { module = "com.moengage:inapp", version.ref = "moeInApp" }
moe-notification = { module = "com.moengage:rich-notification", version.ref = "moeRichNotification" }
circleindicator = { module = "me.relex:circleindicator", version = "2.1.6" }
skeleton = { module = "com.faltenreich:skeletonlayout", version = "4.0.0" }
gesture-recycler = { module = "com.github.thesurix:gesture-recycler", version = "1.17.0" }
supernova-emoji = { module = "com.github.hani-momanii:SuperNova-Emoji", version = "1.1" }
lottie = { module = "com.airbnb.android:lottie", version = "5.2.0" }

# toyotaDeps
checkerframework = { module = "org.checkerframework:checker-qual", version = "3.42.0" }
cronet = { module = "org.chromium.net:cronet-api", version = "119.6045.31" }
posthog = {module = "com.posthog:posthog-android", version = "3.3.0"}
car-ui = { module = "com.android.car.ui:car-ui-lib", version = "2.6.0" }
qr-generator = { module = "com.github.alexzhirkevich:custom-qr-generator", version = "2.0.0-alpha01"}

# google
billing = { module = "com.android.billingclient:billing", version.ref = "billing" }
billing-ktx = { module = "com.android.billingclient:billing-ktx", version.ref = "billing" }
integrity = { module = "com.google.android.play:integrity", version = "1.1.0" }
recaptcha = { module = "com.google.android.recaptcha:recaptcha", version = "18.1.1" }
play-services-analytics = { module = "com.google.android.gms:play-services-analytics", version = "18.0.2" }
play-services-location = { module = "com.google.android.gms:play-services-location", version = "21.0.0" }
play-review = { module = "com.google.android.play:review", version = "2.0.1" }
play-review-ktx = { module = "com.google.android.play:review-ktx", version = "2.0.1" }
play-update = { module = "com.google.android.play:app-update", version = "2.1.0" }
play-update-ktx = { module = "com.google.android.play:app-update-ktx", version = "2.1.0" }
play-services-auth = { module = "com.google.android.gms:play-services-auth", version = "20.3.0" }
play-services-basement = { module = "com.google.android.gms:play-services-basement", version = "18.1.0" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version = "18.0.1" }
play-services-ads = { module = "com.google.android.gms:play-services-ads", version = "22.6.0" }
play-services-cast-framework = { module = "com.google.android.gms:play-services-cast-framework", version = "21.3.0" }
androidyoutubeplayer-core = { module = "com.pierfrancescosoffritti.androidyoutubeplayer:core", version = "11.1.0" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version = "2.2" }

# http
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version = "4.10.0" }
chucker = { module = "com.github.chuckerteam.chucker:library", version = "3.5.2" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-compose = { module = "com.github.bumptech.glide:compose", version = "1.0.0-alpha.3" }
glide-svg = { module = "com.github.qoqa:glide-svg", version = "4.0.2" }
glide-okhttp3 = { module = "com.github.bumptech.glide:okhttp3-integration", version.ref = "glide" }

# firebase
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-auth = { group = "com.google.firebase", name = "firebase-auth-ktx" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }
firebase-config = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-firestore = { group = "com.google.firebase", name = "firebase-firestore-ktx" }
firebase-database = { group = "com.google.firebase", name = "firebase-database-ktx" }
firebase-ui = { module = "com.firebaseui:firebase-ui-firestore", version = "8.0.2" }

# exoplayer
exoplayer-hls = { module = "com.google.android.exoplayer:exoplayer-hls", version.ref = "exoplayer" }
exoplayer-dash = { module = "com.google.android.exoplayer:exoplayer-dash", version.ref = "exoplayer" }
exoplayer-ima = { module = "com.google.android.exoplayer:extension-ima", version.ref = "exoplayer" }
exoplayer-cronet = { module = "com.google.android.exoplayer:extension-cronet", version.ref = "exoplayer" }
exoplayer-ui = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayer" }
exoplayer-core = { module = "com.google.android.exoplayer:exoplayer-core", version.ref = "exoplayer" }
exoplayer-mediasession = { module = "com.google.android.exoplayer:extension-mediasession", version.ref = "exoplayer" }
exoplayer-cast = { module = "com.google.android.exoplayer:extension-cast", version.ref = "exoplayer" }

# androidx compose
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-material = { group = "androidx.compose.material", name = "material" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-animation = { group = "androidx.compose.animation", name = "animation-graphics" }
androidx-compose-constraintLayout = { module = "androidx.constraintlayout:constraintlayout-compose", version = "1.0.1" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version = "1.7.0" }
androidx-lifecycle-viewModel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version = "2.6.1" }
androidx-lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version = "2.6.1" }
coil-compose = { module = "io.coil-kt:coil-compose", version = "2.2.2" }
accompanist-pager = { module = "com.google.accompanist:accompanist-pager", version = "0.29.1-alpha" }
accompanist-ui = { module = "com.google.accompanist:accompanist-systemuicontroller", version = "0.30.0" }
accompanist-refresh = { module = "com.google.accompanist:accompanist-swiperefresh", version = "0.19.0" }

# androidx lifecycle
androidx-lifecycle-viewModel = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidLifecycle" }
androidx-lifecycle-liveData = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "androidLifecycle" }
androidx-lifecycle-runtime = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidLifecycle" }
androidx-lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "androidLifecycle" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidLifecycle" }

# androidx camera
androidx-camera-core = { module = "androidx.camera:camera-camera2", version.ref = "camerax" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "camerax" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camerax" }

# androidx major
androidx-core-ktx = { module = "androidx.core:core-ktx", version = "1.13.1" }
androidx-splash = { module = "androidx.core:core-splashscreen", version = "1.0.0" }
androidx-workRunTime = { module = "androidx.work:work-runtime-ktx", version = "2.7.1" }
androidx-concurrent = { module = "androidx.concurrent:concurrent-futures", version = "1.1.0" }
androidx-security = { module = "androidx.security:security-crypto", version = "1.1.0-alpha06" }
androidx-arch = { module = "android.arch.lifecycle:extensions", version = "1.1.1" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "room" }

# androidx ui
androidx-appcompat = { module = "androidx.appcompat:appcompat", version = "1.4.1" }
androidx-multidex = { module = "androidx.multidex:multidex", version = "2.0.1" }
androidx-legacySupport = { module = "androidx.legacy:legacy-support-v4", version = "1.0.0" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version = "2.1.4" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version = "1.3.0-alpha02" }
androidx-swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version = "1.1.0" }
androidx-fragment = { module = "androidx.fragment:fragment-ktx", version = "1.5.4" }
androidx-navigationFragment = { module = "androidx.navigation:navigation-fragment-ktx", version = "2.5.3" }
androidx-navigationUi = { module = "androidx.navigation:navigation-ui-ktx", version = "2.5.3" }
androidx-pagingRuntime = { module = "androidx.paging:paging-runtime-ktx", version = "3.1.1" }
androidx-palette = { module = "androidx.palette:palette-ktx", version = "1.0.0" }
material = { module = "com.google.android.material:material", version = "1.7.0" }
flexbox = { module = "com.google.android.flexbox:flexbox", version = "3.0.0" }

hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hilt" }

# kapt
room-compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
hilt-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hilt" }

# Dependencies of the included build-logic
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
android-tools-common = { group = "com.android.tools", name = "common", version.ref = "androidTools" }
firebase-crashlytics-gradlePlugin = { group = "com.google.firebase", name = "firebase-crashlytics-gradle", version.ref = "crashlytics" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
androidx-uiautomator = { group = "androidx.test.uiautomator", name = "uiautomator", version.ref = "uiautomator" }
androidx-benchmark-macro-junit4 = { group = "androidx.benchmark", name = "benchmark-macro-junit4", version.ref = "benchmarkMacroJunit4" }
androidx-profileinstaller = { group = "androidx.profileinstaller", name = "profileinstaller", version.ref = "profileinstaller" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "crashlytics" }
gms = { id = "com.google.gms.google-services", version.ref = "gms" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
safeargs = { id = "androidx.navigation.safeargs.kotlin", version.ref = "navigationSafeArgsGradlePlugin" }

# Plugins defined by this project
noice-android-application = { id = "noice.android.application", version = "unspecified" }
noice-android-application-flavors = { id = "noice.android.application.flavors", version = "unspecified" }
noice-android-application-build-types = { id = "noice.android.application.build.types", version = "unspecified" }
noice-android-application-toyota-configuration = { id = "noice.android.application.toyota.configuration", version = "unspecified" }
noice-android-application-packaging = { id = "noice.android.application.packaging", version = "unspecified" }
noice-android-application-signing = { id = "noice.android.application.signing", version = "unspecified" }
noice-android-application-dependencies-firebase = { id = "noice.android.application.dependencies.firebase", version = "unspecified" }
noice-android-application-dependencies-kapts = { id = "noice.android.application.dependencies.kapts", version = "unspecified" }
noice-android-application-dependencies-compose = { id = "noice.android.application.dependencies.compose", version = "unspecified" }
noice-android-application-dependencies-hilt = { id = "noice.android.application.dependencies.hilt", version = "unspecified" }
noice-android-application-dependencies-room = { id = "noice.android.application.dependencies.room", version = "unspecified" }
android-test = { id = "com.android.test", version.ref = "androidGradlePlugin" }
baselineprofile = { id = "androidx.baselineprofile", version.ref = "baselineprofile" }