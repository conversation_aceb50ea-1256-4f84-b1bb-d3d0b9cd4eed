# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Built application files
*.apk
*.ap_
*.aab

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

build/
.gradle/
**/build/
!src/**/build/
app/production/
app/uat/

# Ignore Gradle GUI flavors
gradle-app.setting

# Avoid ignoring Gradle wrapper jar file (.jar files are usually ignored)
!gradle-wrapper.jar

# Cache of project
.gradletasknamecache
local.properties

# IntelliJ
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/jarRepositories.xml

# Android Studio 3 in .gitignore file.
.idea/caches
.idea/modules.xml
.idea/vcs.xml
# Comment next line if keeping position of elements in Navigation Editor is relevant for you
.idea/navEditor.xml
.idea/misc.xml
.idea/compiler.xml

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/

# Android Profiling
*.hprof

# Key Ninja
/app/src/main/java/androidx/appcompat/
/app/src/main/java/androidx/appcompat/view/
/app/src/main/res/values/key_ninja_strings.xml
/keys.json
/build-logic/convention/.bin/

# fastfile env
.env