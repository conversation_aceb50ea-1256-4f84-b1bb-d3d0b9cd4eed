pluginManagement {
    includeBuild("build-logic")
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        flatDir {
            name = "libs"
            dirs("app/libs/")
        }
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven(url = "https://jitpack.io")
        maven(url = "https://storage.googleapis.com/r8-releases/raw/")
    }
}

rootProject.name = "NOICE"
include(":app")
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
include(":baselineprofile")
