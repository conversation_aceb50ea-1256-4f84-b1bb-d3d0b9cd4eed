image: alpine:latest

pipelines:
  branches:
    "develop":
      - step:
          name: <PERSON><PERSON> Build
          script:
            - apk add curl
            - curl "http://${J<PERSON><PERSON>INS_DEV_CREDENTIALS}@${JENKINS_DEV_SERVER}/job/${JENKINS_JOB_NAME_PROD}/buildWithParameters?token=${JENKINS_JOB_TOKEN}&BRANCH_NAME=${BITBUCKET_BRANCH}"

    "qa/*":
      - step:
          name: Tri<PERSON> Jenkins Build
          script:
            - apk add curl
            - curl "http://${JENKINS_DEV_CREDENTIALS}@${JENKINS_DEV_SERVER}/job/${JENKINS_JOB_NAME_UAT}/buildWithParameters?token=${JENKINS_JOB_TOKEN}&BRANCH_NAME=${BITBUCKET_BRANCH}"
