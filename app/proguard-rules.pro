# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.

-keepattributes Signature, InnerClasses, EnclosingMethod
# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items).
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-dontwarn okio.**
-dontwarn javax.annotation.**

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit
-dontwarn kotlin.**

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

#GLIDE

# added for MoEngage SDK to work
-keep class com.bumptech.glide.**{*;}

-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# EventBus
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# If using AsyncExecutord, keep required constructor of default event used.
# Adjust the class name if a custom failure event type is used.
-keepclassmembers class org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# Accessed via reflection, avoid renaming or removal
-keep class org.greenrobot.eventbus.android.AndroidComponentsImpl


##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }


# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclasseswithmembers class * {
    public ** component1();
    <fields>;
}
# We want to keep methods in Activity that could be used in the XML attribute onClick
#-keepclassmembers class * extends android.app.Activity {
#   public void *(android.view.View);
#}
#-keepclassmembers class * implements android.os.Parcelable {
#  public static final android.os.Parcelable$Creator CREATOR;
#}

##---------------End: proguard configuration for Gson  ----------

##--- Begin:GSON ----
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# Prevent proguard from stripping interface information from TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)

-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
# keep enum so gson can deserialize it
-keepclassmembers enum * { *; }

# Application classes that will be serialized/deserialized over Gson
-keep public class noice.app.model.** { *; }
-keep public class noice.app.model.appconfig.** { *; }
-keep public class noice.app.room.** { *; }
-keep public class noice.app.modules.audiobook.model.** { *; }
-keep public class noice.app.modules.chat.model.** { *; }
-keep public class noice.app.modules.clips.model.** { *; }
-keep public class noice.app.modules.dashboard.model.** { *; }
-keep public class noice.app.modules.follow.model.** { *; }
-keep public class noice.app.modules.indexpages.model.** { *; }
-keep public class noice.app.modules.media.model.** { *; }
-keep public class noice.app.modules.notification.models.** { *; }
-keep public class noice.app.modules.onboarding.models.** { *; }
-keep public class noice.app.modules.podcast.model.** { *; }
-keep public class noice.app.modules.profile.model.** { *; }
-keep public class noice.app.modules.radio.model.** { *; }
-keep public class noice.app.modules.search.model.** { *; }
-keep public class noice.app.modules.live.model.** { *; }
-keep public class noice.app.enums.** { *; }
-keep public class noice.app.model.appconfig.** { *; }
-keep class noice.app.model.user.** { *; }
-keep class noice.app.model.user.preference.** { *; }

-keep class noice.app.modules.podcast.model.Channel**
-keepclassmembers class noice.app.modules.podcast.model.Channel { *; }
-keepclassmembers class noice.app.modules.media.model.MediaAction { *; }
-keepclassmembers class noice.app.modules.live.model.AgoraToken { *; }
-keepclassmembers class noice.app.modules.live.model.CreateRoomRequest { *; }
-keepclassmembers class noice.app.model.appconfig.LiveCreator { *; }
-keepclassmembers class noice.app.model.user.UserLocation { *; }
-keepclassmembers class noice.app.modules.media.model.Community { *; }

-keep class noice.app.modules.live.model.PinChatImpl
-keepclassmembers class noice.app.modules.live.model.PinChatImpl { *; }

-keep class noice.app.modules.live.model.PinChatImpl$Details
-keepclassmembers class noice.app.modules.live.model.PinChatImpl$Details { *; }

-keep class noice.app.modules.live.model.PinChatImpl$PinnedComment
-keepclassmembers class noice.app.modules.live.model.PinChatImpl$PinnedComment{ *; }


-keepclassmembers class noice.app.modules.live.model.LiveRoom { *; }
-keepclassmembers class noice.app.modules.live.model.Participant { *; }
-keepclassmembers class noice.app.modules.live.model.RoomData { *; }
-keepclassmembers class noice.app.modules.live.model.RoomParticipant { *; }
-keepclassmembers class noice.app.modules.podcast.model.MentionData { *; }
-keep class noice.app.modules.chat.model.Message
-keep class noice.app.modules.media.model.MediaAction
-keep class noice.app.modules.live.model.RoomParticipant
-keep class noice.app.modules.live.model.AgoraToken
-keep class noice.app.model.user.User
-keep class noice.app.model.appconfig.LiveCreator
-keep class noice.app.model.appconfig.** { *; }
-keepclassmembers class noice.app.modules.chat.model.Message { *; }
-keepclassmembers class noice.app.modules.chat.model.Action { *; }
-keepclassmembers class noice.app.modules.podcast.model.MentionUser { *; }
-keepclassmembers class noice.app.model.TagData {*;}

-keep class noice.app.cache.** { *; }
-keep class noice.app.utils.mention.** { *; }

#-keepclasseswithmembers class * {
#    public <init>(android.content.Context, android.util.AttributeSet);
#}

#-keepclasseswithmembers class * {
#    public <init>(android.content.Context, android.util.AttributeSet, int);
#}
-keepclassmembers class **.R$* {
    public static <fields>;
}
-keepclasseswithmembernames class * {
    native <methods>;
}
-keepclassmembers class * {
    public void *ButtonClicked(android.view.View);
}
-keepclassmembers class noice.app.* {
    public static ** Companion;
}

-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type

-dontpreverify
-repackageclasses ''
-allowaccessmodification
 -dontoptimize
-optimizations !code/simplification/arithmetic


-keepclassmembers class * extends android.content.Context {
   public void *(android.view.View);
   public void *(android.view.MenuItem);
}


# ServiceLoader support
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcherFactory {}

-keep class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keep class kotlinx.coroutines.CoroutineExceptionHandler {}
-keep class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keep class kotlinx.coroutines.android.AndroidDispatcherFactory {}
-keepnames class kotlinx.** { *; }
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}
# facebook
-keep class com.facebook.android.*
-keep class android.webkit.WebViewClient
-keep class * extends android.webkit.WebViewClient
-keepclassmembers class * extends android.webkit.WebViewClient {
    <methods>;
}

#Crashlytics
-keepattributes SourceFile,LineNumberTable        # Keep file names and line numbers.
-keep public class * extends java.lang.Exception

#Room
-dontwarn android.arch.util.paging.CountedDataSource
-dontwarn android.arch.persistence.room.paging.LimitOffsetDataSource
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

#appsflyer
-dontwarn com.appsflyer.**
-keep class com.appsflyer.** { *; }
-keep public class com.google.firebase.messaging.FirebaseMessagingService {
  public *;
}
# jwt
-keepattributes InnerClasses

-keep class io.jsonwebtoken.** { *; }
-keepnames class io.jsonwebtoken.* { *; }
-keepnames interface io.jsonwebtoken.* { *; }

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**


#Agora
-keep class io.agora.**{*;}

# Gson specific classes
-keep,includedescriptorclasses class com.google.common.**
#-keep class com.google.gson.stream.** { *; }

# Amplitude sdk
-keep class com.google.android.gms.ads.** { *; }
    -dontwarn okio.**

# Logback and slf4j-api
-dontwarn ch.qos.logback.core.net.*
-dontwarn org.slf4j.**
-keep class ch.qos.** { *; }
-keep class org.slf4j.** { *; }

# PubNub
-dontwarn com.pubnub.**
-keep class com.pubnub.** { *; }
-keep class org.json.* { *; }

# Ktor
-keep class io.ktor.** { *; }
-keep class kotlinx.coroutines.** { *; }
-keepclassmembers class io.ktor.** {
    volatile <fields>;
}
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** {
    *;
}
-keepnames class io.netty.** {
    *;
}
-dontwarn kotlinx.atomicfu.**
-dontwarn io.netty.**
-dontwarn com.typesafe.**
-dontwarn org.slf4j.**

#Crypto
-keep class com.google.crypto.** { *; }

#Keys
-keep public class androidx.appcompat.AppCompatViewBase { *; }
-keep public class androidx.appcompat.AppCompatViewHelper { *; }
-keep public class androidx.appcompat.view.PKeys { *; }

#AGP 8.4.1 MISSING_RULES
-dontwarn com.google.api.client.http.GenericUrl
-dontwarn com.google.api.client.http.HttpHeaders
-dontwarn com.google.api.client.http.HttpRequest
-dontwarn com.google.api.client.http.HttpRequestFactory
-dontwarn com.google.api.client.http.HttpResponse
-dontwarn com.google.api.client.http.HttpTransport
-dontwarn com.google.api.client.http.javanet.NetHttpTransport$Builder
-dontwarn com.google.api.client.http.javanet.NetHttpTransport
-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension
-dontwarn com.google.protobuf.java_com_google_ads_interactivemedia_v3__sdk_1p_binary_b0308732GeneratedExtensionRegistryLite$Loader
-dontwarn com.google.protobuf.java_com_google_android_gmscore_sdk_target_granule__proguard_group_gtm_N1281923064GeneratedExtensionRegistryLite$Loader
-dontwarn java.lang.management.ManagementFactory
-dontwarn java.lang.management.RuntimeMXBean
-dontwarn org.conscrypt.Conscrypt$Version
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.ConscryptHostnameVerifier
-dontwarn org.joda.time.Instant
-dontwarn org.openjsse.javax.net.ssl.SSLParameters
-dontwarn org.openjsse.javax.net.ssl.SSLSocket
-dontwarn org.openjsse.net.ssl.OpenJSSE

#Toyota
-dontwarn com.google.android.gms.tasks.OnCompleteListener
-dontwarn com.google.android.gms.tasks.Task
-dontwarn com.google.firebase.messaging.FirebaseMessaging