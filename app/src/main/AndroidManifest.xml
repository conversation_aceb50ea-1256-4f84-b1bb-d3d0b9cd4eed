<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- used for MoEngage rich notification -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <queries>
        <package android:name="com.facebook.katana" />
        <package android:name="com.facebook.lite" />
        <package android:name="com.whatsapp" />
        <package android:name="com.instagram.android" />
        <package android:name="org.telegram.messenger" />
        <package android:name="jp.naver.line.android" />
        <package android:name="com.twitter.android" />

        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <application
        android:name=".BaseApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="${appIcon}"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="${appIconRound}"
        android:supportsRtl="true"
        android:theme="@style/Theme.NOICE"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".modules.subscription.activity.SubscriptionCancelActivity"
            android:exported="false"
            android:label="@string/title_activity_subscription_cancel"
            android:theme="@style/Theme.NOICE" />
        <activity
            android:name=".modules.subscription.activity.PackageDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.NOICE" />
        <activity
            android:name=".exoplayer.FullscreenPlayerActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:screenOrientation="sensorLandscape" />
        <activity
            android:name=".modules.onboarding.activity.CompleteProfileActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.podcast.fragment.SubscriptionSuccessPage"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.coins.activity.CoinTopUpActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.live.activity.LiveRoomSummaryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.live.activity.PinChatActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.live.activity.LiveTypeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.onboarding.activity.ForceUpdateActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.onboarding.activity.OnBoardingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:excludeFromRecents="true"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".modules.onboarding.activity.DeleteAccountActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.onboarding.activity.EditGenreActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.live.activity.CommunityGuidelinesActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.profile.activity.ProfileMenu"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.chat.report.ReportActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.onboarding.activity.CompleteUserNameActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".modules.live.activity.InviteSpeakerActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.browser.InAppBrowserActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".services.observer.DataObserverActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.profile.PolicyTermsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.dashboard.home.activity.EditPlaylistActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.dashboard.home.activity.CreatePlaylistActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.dashboard.activity.HomeActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.media.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </activity>
        <activity
            android:name=".modules.onboarding.activity.GenreSelectionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.onboarding.activity.MobileLoginActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.onboarding.activity.EditProfileActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.onboarding.activity.LoginActivity"
            android:excludeFromRecents="true"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.qrlogin.QrLoginActivity"
            android:configChanges="uiMode"
            android:exported="false">
            <meta-data android:name=
                "distractionOptimized" android:value="true"/>
        </activity>

         <activity
             android:name=".modules.onboarding.activity.SplashActivity"
             android:exported="true"
             android:screenOrientation="${screenOrientation}"
             android:theme="@style/SplashTheme">
             <intent-filter>
                 <action android:name="android.intent.action.MAIN" />

                 <category android:name="android.intent.category.LAUNCHER" />
             </intent-filter>
        </activity>

        <activity
            android:name=".utils.deeplink.DeepLinkDispatcherActivity"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:exported="true"
            android:autoRemoveFromRecents="true">

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- appsflyer production branded link -->
                <data
                    android:host="noiceid.onelink.me"
                    android:pathPrefix="/cyOg"
                    android:scheme="https" />
            </intent-filter>

            <!-- this separate intent-filter is required else Deferred Deeplink from social media platforms won't work -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- this URI scheme is used to resolve Deferred Deeplink from social media platforms -->
                <data
                    android:host="app"
                    android:scheme="noice" />
            </intent-filter>

            <!-- Intent filters for specific segments (excluding tv-login and car-login) -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/livestream" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/livestream" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/livestream" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/livestream" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/missions" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/missions" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/missions" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/missions" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:path="/" android:scheme="https" />
                <data android:host="${hostName}" android:path="/" android:scheme="http" />
                <data android:host="*.${hostName}" android:path="/" android:scheme="https" />
                <data android:host="*.${hostName}" android:path="/" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/content" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/content" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/content" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/content" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/catalog" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/catalog" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/catalog" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/catalog" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/playlist" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/playlist" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/playlist" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/playlist" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/themepage" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/themepage" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/themepage" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/themepage" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/segment" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/segment" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/segment" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/segment" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/voucher" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/voucher" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/voucher" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/voucher" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/coin" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/coin" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/coin" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/coin" android:scheme="http" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/subscription" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/subscription" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/subscription" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/subscription" android:scheme="http" />
            </intent-filter>

            <!-- Intent filter for user profile pages -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:host="${hostName}" android:pathPrefix="/u" android:scheme="https" />
                <data android:host="${hostName}" android:pathPrefix="/u" android:scheme="http" />
                <data android:host="*.${hostName}" android:pathPrefix="/u" android:scheme="https" />
                <data android:host="*.${hostName}" android:pathPrefix="/u" android:scheme="http" />
            </intent-filter>
        </activity>
        <activity-alias
            android:name=".modules.onboarding.activity.SplashActivityAlias1"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launcher1_round"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher1"
            android:screenOrientation="${screenOrientation}"
            android:targetActivity=".modules.onboarding.activity.SplashActivity"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity-alias
            android:name=".modules.onboarding.activity.SplashActivityAlias2"
            android:enabled="false"
            android:exported="true"
            android:icon="@mipmap/ic_launcher_ramadhan_2025"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_ramadhan_2025_round"
            android:screenOrientation="${screenOrientation}"
            android:targetActivity=".modules.onboarding.activity.SplashActivity"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>
        <activity
            android:name=".modules.live.activity.EditRoomActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.live.feature.createroom.CreateRoomPlusActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.NOICE.Material3.Dark"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.live.activity.PreviewVideoRoomActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.NOICE.Material3.Dark"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".modules.live.activity.RoomSettingsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.clips.activities.ClipsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.clips.activities.ClipMenuActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".modules.themedpages.fragment.VideoPlayerActivity"
            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout" />

        <meta-data
            android:name="gcp_api_key"
            android:value="@string/google_api_key" />
        <meta-data
            android:name="gcp_project_id"
            android:value="@string/project_id" />
        <meta-data
            android:name="gcp_app_id"
            android:value="@string/google_app_id" />
        <meta-data
            android:name="com.facebook.sdk.AutoInitEnabled"
            android:value="false" />
        <meta-data
            android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
            android:value="false" />
        <meta-data
            android:name="com.facebook.sdk.AutoLogAppEventsEnabled"
            android:value="true" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data
            android:name="com.google.android.safetynet.ATTEST_API_KEY"
            android:value="@string/google_api_key" />
        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_noice_logo_notification" /> <!-- Ad Manager app ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/app_id_ad_manager" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="@string/facebook_provider"
            android:exported="true" />

        <service
            android:name=".services.NoiceFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

<!--    Change into d-ataSync due to error java.lang.IllegalArgumentException: foregroundServiceType 0x00000001 is not a subset of foregroundServiceType attribute 0x00000002 in service element of manifest file-->
<!--    Download Service need to use dataSync https://github.com/google/ExoPlayer/issues/11239-->
        <service
            android:name=".exoplayer.ExoplayerDownloadService"
            android:foregroundServiceType="dataSync"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <service
            android:name="com.google.android.exoplayer2.scheduler.PlatformScheduler$PlatformSchedulerService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name=".player.service.MediaService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"
            android:stopWithTask="false" />
        <service
            android:name=".modules.live.stream.core.LiveStreamService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"
            android:stopWithTask="false" />

        <receiver android:name=".utils.ApplicationSelectorReceiver" />
        <receiver
            android:name=".exoplayer.NotificationBroadcast"
            android:exported="false">
            <intent-filter>
                <action android:name="noice.app.PAUSE_DOWNLOAD" />
                <action android:name="noice.app.PLAY_DOWNLOAD" />
                <action android:name="noice.app.RETRY_DOWNLOAD" />
                <action android:name="noice.app.CANCEL_DOWNLOAD" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.google.android.gms.cast.framework.media.MediaIntentReceiver"
            android:exported="false" />

        <meta-data
            android:name="com.google.android.gms.cast.framework.OPTIONS_PROVIDER_CLASS_NAME"
            android:value="noice.app.exoplayer.cast.CastOptionsProvider"/>

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />
    </application>
</manifest>