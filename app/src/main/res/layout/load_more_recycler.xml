<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/loadMoreProgressLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/load_more_progressBar"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:layout_margin="@dimen/dp16"
        android:indeterminateDuration="800"
        android:padding="@dimen/dp8"
        android:indeterminateTint="@color/dull_yellow"
        android:visibility="visible" />

    <TextView
        android:id="@+id/text_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/dp50"
        android:layout_marginTop="@dimen/dp30"
        android:gravity="center_horizontal"
        android:padding="@dimen/dp8"
        android:textSize="@dimen/sp16"
        android:visibility="gone" />
</FrameLayout>
