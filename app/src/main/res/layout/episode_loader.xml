<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700">

    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp140"
        android:layout_height="@dimen/dp140"
        android:layout_marginTop="@dimen/dp20"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/labelLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v1"
        >
        <View
            android:id="@+id/txtVideo"
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp20"
            android:gravity="top"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_marginTop="@dimen/dp18"
            android:drawablePadding="@dimen/dp1"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"/>
        <View
            android:id="@+id/txtPurchased"
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp20"
            android:layout_marginStart="@dimen/dp10"
            android:gravity="top"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_marginTop="@dimen/dp18"
            android:drawablePadding="@dimen/dp1"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"/>

    </LinearLayout>

    <View
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/labelLayout"/>

    <View
        android:id="@+id/duration"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/title"/>

    <LinearLayout
        android:id="@+id/tersisa"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/duration"
        >
        <View
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp10"
            android:gravity="top"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_marginTop="@dimen/dp18"
            android:drawablePadding="@dimen/dp1"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"/>
        <View
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp10"
            android:layout_marginStart="@dimen/dp10"
            android:gravity="top"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_marginTop="@dimen/dp18"
            android:drawablePadding="@dimen/dp1"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"/>

    </LinearLayout>

    <View
        android:id="@+id/playbtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tersisa"/>


    <LinearLayout
        android:id="@+id/buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/playbtn"
        android:weightSum="2"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp48"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_weight="1"
            android:background="@drawable/background_skeleton_8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tersisa"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp48"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp12"
            android:layout_weight="1"
            android:background="@drawable/background_skeleton_8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tersisa"/>

    </LinearLayout>


    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/dp189"
        android:layout_height="@dimen/dp21"
        android:layout_marginTop="@dimen/dp35"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/buttons"
        app:layout_constraintStart_toStartOf="@id/buttons" />

    <View
        android:id="@+id/count"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp21"
        android:layout_marginTop="@dimen/dp35"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/buttons"
        app:layout_constraintEnd_toEndOf="parent" />



    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/dp45"
        android:layout_height="@dimen/dp25"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v8"
        app:layout_constraintStart_toStartOf="@id/v8" />

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/dp45"
        android:layout_height="@dimen/dp25"
        android:layout_marginStart="@dimen/dp4"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v13"
        app:layout_constraintTop_toTopOf="@id/v13"/>
    <View
        android:id="@+id/v141"
        android:layout_width="@dimen/dp45"
        android:layout_height="@dimen/dp25"
        android:layout_marginStart="@dimen/dp4"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v14"
        app:layout_constraintTop_toTopOf="@id/v14"/>


    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp68"
        android:layout_marginTop="@dimen/dp22"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v14"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/dp189"
        android:layout_height="@dimen/dp21"
        android:layout_marginTop="@dimen/dp19"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v15"
        app:layout_constraintStart_toStartOf="@id/v15" />


    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp38"
        android:layout_height="@dimen/dp38"
        android:background="@drawable/background_skeleton_20dp"
        app:layout_constraintStart_toStartOf="@id/v16"
        app:layout_constraintTop_toTopOf="@id/v19"
        app:layout_constraintBottom_toBottomOf="@id/v19"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp50"
        android:layout_marginTop="@dimen/dp23"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v18"
        app:layout_constraintTop_toBottomOf="@id/v16"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp38"
        android:layout_height="@dimen/dp38"
        android:background="@drawable/background_skeleton_20dp"
        app:layout_constraintStart_toStartOf="@id/v16"
        app:layout_constraintTop_toTopOf="@id/v21"
        app:layout_constraintBottom_toBottomOf="@id/v21"/>

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp50"
        android:layout_marginTop="@dimen/dp23"
        android:layout_marginStart="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v20"
        app:layout_constraintTop_toBottomOf="@id/v19"
        app:layout_constraintEnd_toEndOf="@id/v19"/>
</androidx.constraintlayout.widget.ConstraintLayout>