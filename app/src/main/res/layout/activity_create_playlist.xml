<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.dashboard.home.activity.CreatePlaylistActivity">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:toolbarViewTitle="@string/create_new_list"
        app:toolbarBackgroundColor="@color/black700"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/imageLayout"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp100"
        android:layout_marginTop="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        app:cardElevation="@dimen/size_0dp"
        app:cardCornerRadius="@dimen/dp4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <ImageView
            android:id="@+id/playlistImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_thumb_default"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/uploadPicBtn"
        android:layout_width="@dimen/dp158"
        android:layout_height="@dimen/dp36"
        android:layout_marginStart="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textAllCaps="true"
        android:textStyle="bold"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_yellow_dark"
        android:letterSpacing="0.2"
        android:gravity="center"
        android:text="@string/upload_photo"
        android:background="@drawable/border_dull_yellow_dark_radius_4dp"
        app:layout_constraintStart_toEndOf="@id/imageLayout"
        app:layout_constraintTop_toTopOf="@id/imageLayout"
        app:layout_constraintBottom_toBottomOf="@id/imageLayout"/>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/inputLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp18"
        android:layout_marginEnd="@dimen/dp16"
        android:hint="@string/name_playlist"
        android:textColorHint="@color/white70"
        app:hintTextColor="@color/white70"
        app:boxBackgroundColor="@color/blackish_blue"
        app:boxBackgroundMode="filled"
        app:boxCornerRadiusBottomEnd="@dimen/dp4"
        app:boxCornerRadiusBottomStart="@dimen/dp4"
        app:boxCornerRadiusTopEnd="@dimen/dp4"
        app:boxCornerRadiusTopStart="@dimen/dp4"
        app:boxStrokeColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/imageLayout"
        app:layout_constraintStart_toStartOf="@id/imageLayout"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/playListName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLength="100"
            android:textSize="@dimen/sp14"
            android:fontFamily="@font/readex_pro"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:inputType="textPersonName"/>
    </com.google.android.material.textfield.TextInputLayout>


    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/privacy"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:text="@string/playlist_private"
        android:textSize="@dimen/sp16"
        android:textColor="@color/dull_white"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:lineSpacingExtra="@dimen/sp4"
        app:layout_constraintTop_toBottomOf="@id/inputLayout"
        app:layout_constraintStart_toStartOf="@id/inputLayout"
        app:layout_constraintEnd_toEndOf="@id/inputLayout"/>

    <TextView
        android:id="@+id/submitButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:padding="@dimen/dp18"
        android:background="@drawable/background_yellow_radius_4dp"
        android:gravity="center"
        android:textSize="@dimen/sp12"
        android:textColor="@color/blackish_blue"
        android:text="@string/done"
        android:textAllCaps="true"
        android:letterSpacing="0.2"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="@id/imageLayout"
        app:layout_constraintEnd_toEndOf="@id/inputLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintVertical_bias="0.0" />
</androidx.constraintlayout.widget.ConstraintLayout>