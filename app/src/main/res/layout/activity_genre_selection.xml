<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.onboarding.activity.GenreSelectionActivity">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:skipPaddingEnd="@dimen/dp24"
        app:toolbarBackIcon="@drawable/ic_left_arrow_white"
        app:toolbarViewTitle="@string/select_genre"
        app:toolbarSkipVisibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp4"
        android:fillViewport="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/doneBtn">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/heading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:text="@string/choose_genre_you_like"
                android:textColor="@color/white"
                android:textSize="@dimen/sp20"/>

            <TextView
                android:id="@+id/desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp12"
                android:fontFamily="sans-serif"
                android:text="@string/get_recommendation_from_genre"
                android:textColor="@color/white80"
                android:textSize="@dimen/sp14"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/genreRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp16"
                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/doneBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp24"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24"
        android:gravity="center"
        android:background="@drawable/selector_yellow_btn"
        android:padding="@dimen/dp16"
        android:textAllCaps="true"
        android:text="@string/done"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0.2"
        android:fontFamily="sans-serif"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>