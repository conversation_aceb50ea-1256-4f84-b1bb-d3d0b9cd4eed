<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/v1"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/v2"/>

    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v1"/>

    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v3"/>

    <ImageView
        android:id="@+id/v2"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v6"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v2"/>

    <View
        android:id="@+id/v7"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v6"/>


    <ImageView
        android:id="@+id/v9"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v7"
        app:layout_constraintEnd_toEndOf="@id/v1"/>

    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v9"/>

    <View
        android:id="@+id/v11"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v10"/>


    <ImageView
        android:id="@+id/v13"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v9"/>

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v13"/>

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v14"/>


    <ImageView
        android:id="@+id/v17"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v15"
        app:layout_constraintEnd_toEndOf="@id/v1"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v17"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v18"/>


    <ImageView
        android:id="@+id/v21"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v17"/>

    <View
        android:id="@+id/v22"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v21"/>

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v22"/>

    <ImageView
        android:id="@+id/v31"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v23"
        app:layout_constraintEnd_toStartOf="@id/v2"/>

    <View
        android:id="@+id/v33"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v31"/>

    <View
        android:id="@+id/v34"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v33"/>

    <ImageView
        android:id="@+id/v35"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v23"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v31"/>

    <View
        android:id="@+id/v36"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v35"/>

    <View
        android:id="@+id/v37"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v36"/>


    <ImageView
        android:id="@+id/v38"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v34"
        app:layout_constraintEnd_toStartOf="@id/v2"/>

    <View
        android:id="@+id/v39"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v38"/>

    <View
        android:id="@+id/v40"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v39"/>

    <ImageView
        android:id="@+id/v41"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radio"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v37"
        app:layout_constraintEnd_toEndOf="@id/v35"
        app:layout_constraintTop_toTopOf="@id/v38"/>

    <View
        android:id="@+id/v42"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v41"/>

    <View
        android:id="@+id/v43"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v41"/>



</androidx.constraintlayout.widget.ConstraintLayout>