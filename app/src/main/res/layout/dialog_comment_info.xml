<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black700"
    android:paddingBottom="@dimen/dp24"
    tools:context=".modules.onboarding.fragments.GuestLoginDialog">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp86"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtTutup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/custom_ripple"
        android:fontFamily="sans-serif"
        android:letterSpacing="0.02"
        android:lineSpacingExtra="@dimen/sp5"
        android:padding="@dimen/dp8"
        android:text="@string/tutup"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtTutup"
        app:srcCompat="@drawable/image_comment_block" />

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:gravity="center_horizontal"
        android:lineSpacingExtra="0sp"
        android:text="@string/fitur_komentar_dimatikan"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image" />

    <TextView
        android:id="@+id/banLiftDate"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:gravity="center_horizontal"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="0sp"
        android:textColor="@color/neutral_90"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="Sampai DD MM YYYY" />

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:gravity="center_horizontal"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="4sp"
        android:text="@string/msg_comment_ban"
        android:textColor="@color/neutral_90"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/banLiftDate" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonLearnMore"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginTop="@dimen/dp24"
        android:backgroundTint="@color/dull_yellow"
        android:fontFamily="@font/readex_pro"
        android:gravity="center"
        android:insetTop="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:lineSpacingExtra="0sp"
        android:text="@string/pelajari_aturan_komunitas"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        android:textStyle="bold"
        app:cornerRadius="@dimen/dp14"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/subTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>