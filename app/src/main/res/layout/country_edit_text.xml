<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/countryLayout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/size_0dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/background_country_chooser"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/numberLayout">

        <TextView
            android:id="@+id/countryFlag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:fontFamily="@font/readex_pro"
            tools:text="🇮🇳"
            android:textSize="@dimen/sp20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp9"
            android:layout_marginStart="@dimen/dp8"
            android:src="@drawable/ic_angle_down_white"
            app:tint="@color/greyText1"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:layout_constraintTop_toTopOf="@id/countryFlag"
            app:layout_constraintBottom_toBottomOf="@id/countryFlag"
            app:layout_constraintStart_toEndOf="@id/countryFlag"/>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/numberLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_country_chooser_black_600"
        app:layout_constraintStart_toEndOf="@id/countryLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginTop="@dimen/dp8"
            android:gravity="center_vertical"
            android:fontFamily="@font/readex_pro"
            android:text="@string/nomor_handphone"
            android:textStyle="bold"
            android:textSize="@dimen/sp12"
            android:letterSpacing="0.04"
            android:textColor="@color/neutral_90"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/phoneCode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginBottom="@dimen/dp8"
            android:gravity="center_vertical"
            android:fontFamily="@font/readex_pro"
            tools:text="+62"
            android:textSize="@dimen/sp14"
            android:letterSpacing="0.01"
            android:textColor="@color/neutral_90"
            app:layout_constraintStart_toStartOf="@id/hint"
            app:layout_constraintTop_toBottomOf="@id/hint"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <EditText
            android:id="@+id/editText"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp12"
            android:layout_marginStart="@dimen/dp8"
            android:gravity="center_vertical"
            android:hint="@string/mobile_number_hint"
            android:textColorHint="@color/neutral_grey_60"
            android:fontFamily="@font/readex_pro"
            android:background="@null"
            android:textSize="@dimen/sp14"
            android:textColor="@color/neutral_90"
            android:letterSpacing="0.01"
            android:importantForAutofill="no"
            android:inputType="phone"
            app:layout_constraintStart_toEndOf="@id/phoneCode"
            app:layout_constraintTop_toTopOf="@id/phoneCode"
            app:layout_constraintBottom_toBottomOf="@id/phoneCode"
            app:layout_constraintEnd_toStartOf="@id/errorIcon"/>
        
        <ImageView
            android:id="@+id/errorIcon"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginEnd="@dimen/dp16"
            android:visibility="gone"
            android:src="@drawable/ic_error_text_input"
            android:contentDescription="@string/app_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/numberLayout"/>

    <TextView
        android:id="@+id/errorText"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp4"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:fontFamily="@font/readex_pro"
        tools:text="Belum ada nomer yang dimasukkin"
        android:textSize="@dimen/sp11"
        android:textColor="@color/pinkish_red"
        android:letterSpacing="0.04"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/numberLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>