<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.dashboard.home.fragment.CatalogMenu">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:paddingBottom="@dimen/dp30"
        android:clipToPadding="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/coverImageLayout"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp100"
                android:layout_marginTop="@dimen/dp16"
                app:cardElevation="@dimen/size_0dp"
                app:cardCornerRadius="@dimen/dp8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/coverImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/catalogName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp6"
                android:gravity="center_horizontal"
                tools:text="Musuh Masyarakat"/>

            <TextView
                android:id="@+id/details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp12"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:gravity="center_horizontal"
                tools:text="45 Episode · 1 Juta Subscriber"/>

            <TextView
                android:id="@+id/artistName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp12"
                android:textColor="@color/white90"
                android:lineSpacingExtra="@dimen/sp5"
                android:gravity="center_horizontal"
                tools:text="Oleh Musuh Masyarakat"/>

            <TextView
                android:id="@+id/share"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/share"
                app:drawableStartCompat="@drawable/ic_share_large" />

            <TextView
                android:id="@+id/reportContent"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginBottom="@dimen/dp60"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp12"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/red"
                android:lineSpacingExtra="@dimen/sp3"
                android:text="@string/report_content"
                app:drawableStartCompat="@drawable/ic_information_red"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>