<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/dp16"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    tools:context=".modules.onboarding.fragments.BirthdayFragment">

    <ImageView
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        android:layout_marginTop="@dimen/dp28"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_birthday_cake"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textSize="@dimen/sp24"
        android:textColor="@color/white"
        android:lineSpacingExtra="0sp"
        android:text="@string/whats_your_birthday"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp16"
        android:textColor="@color/black900"
        android:lineSpacingExtra="@dimen/sp6"
        android:letterSpacing="0.01"
        android:text="@string/data_disclaimer"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/birthdayField"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/upper_radius_bg_grey">

        <TextView
            android:id="@+id/hint"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp8"
            android:fontFamily="@font/readex_pro"
            android:text="@string/tanggal_"
            android:textStyle="bold"
            android:textSize="@dimen/sp12"
            android:textColor="@color/neutral_90"
            android:letterSpacing="0.04"
            android:lineSpacingExtra="0sp"
            app:layout_constraintEnd_toStartOf="@id/action"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/placeholder"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:layout_marginBottom="@dimen/dp8"
            android:fontFamily="@font/readex_pro_light"
            android:text="@string/choose_a_date"
            android:textSize="@dimen/sp14"
            android:textColor="@color/greyText1"
            android:letterSpacing="0.01"
            android:lineSpacingExtra="0sp"
            app:layout_constraintTop_toBottomOf="@id/hint"
            app:layout_constraintStart_toStartOf="@id/hint"
            app:layout_constraintEnd_toEndOf="@id/hint"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/action"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginEnd="@dimen/dp18"
            android:visibility="gone"
            android:adjustViewBounds="true"
            android:src="@drawable/ic_close_button"
            android:contentDescription="@string/app_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/line"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp1"
            android:background="@color/neutral_grey"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/errorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp2"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/size_0dp"
        android:fontFamily="@font/readex_pro"
        android:text="@string/choose_a_date"
        android:textSize="@dimen/sp11"
        android:textColor="@color/pinkish_red"
        android:letterSpacing="0.05"
        android:lineSpacingExtra="0sp"
        app:layout_constraintTop_toBottomOf="@id/hint"
        app:layout_constraintStart_toStartOf="@id/hint"
        app:layout_constraintEnd_toEndOf="@id/hint"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/proceed"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp52"
        android:layout_marginTop="@dimen/dp24"
        android:insetTop="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:gravity="center"
        app:cornerRadius="@dimen/dp14"
        android:backgroundTint="@color/dull_yellow"
        android:text="@string/lanjut"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        android:lineSpacingExtra="0sp"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"/>
</LinearLayout>