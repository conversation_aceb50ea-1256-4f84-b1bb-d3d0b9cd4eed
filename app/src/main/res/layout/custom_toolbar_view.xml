<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="?actionBarSize"
    android:id="@+id/toolbarView"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/dropDownButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:padding="@dimen/dp8"
        android:contentDescription="@string/app_name"
        android:foreground="?selectableItemBackgroundBorderless"
        android:src="@drawable/ic_angle_down_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/toolbarTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="sans-serif"
        android:textStyle="bold"
        android:textSize="@dimen/sp14"
        android:textColor="#ffffff"
        android:maxLines="1"
        android:gravity="center"
        tools:text="Title"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/dropDownButton"
        app:layout_constraintEnd_toStartOf="@id/threeDotIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/dp40"
        android:alpha="0.15"
        android:background="@drawable/background_white_100dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/audioVideoTab"
        app:layout_constraintEnd_toEndOf="@id/audioVideoTab"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/audioVideoTab"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp40"
        android:orientation="horizontal"
        app:layout_constraintStart_toEndOf="@id/dropDownButton"
        app:layout_constraintEnd_toStartOf="@id/threeDotIcon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/audioTab"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp30"
            android:letterSpacing="0.05"
            android:background="@drawable/background_dull_white_radius_100"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:textSize="@dimen/sp11"
            android:layout_marginVertical="@dimen/dp5"
            android:layout_marginStart="@dimen/dp5"
            android:textColor="@color/black"
            android:gravity="center"
            android:text="@string/audio"/>

        <TextView
            android:id="@+id/videoTab"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp30"
            android:layout_marginVertical="@dimen/dp5"
            android:layout_marginEnd="@dimen/dp5"
            android:letterSpacing="0.05"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:textSize="@dimen/sp11"
            android:textColor="#FFFFFFCC"
            android:gravity="center"
            android:text="@string/video"/>
    </LinearLayout>

    <noice.app.exoplayer.cast.CustomizedChromesCastButton
        android:id="@+id/media_route_button"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:mediaRouteTypes="user"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/threeDotIcon"
        />
<!--
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgCast"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:mediaRouteTypes="user"
        android:visibility="gone"
        app:srcCompat="@drawable/custom_cast_button"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/threeDotIcon"
        />
-->


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/threeDotIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        android:contentDescription="@string/app_name"
        app:srcCompat="@drawable/ic_three_dot"
        android:foreground="?selectableItemBackgroundBorderless"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>