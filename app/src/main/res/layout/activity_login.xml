<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/ic_login_bg"
    tools:context=".modules.onboarding.activity.LoginActivity">

    <ImageView
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginStart="@dimen/dp30"
        android:layout_marginEnd="@dimen/dp30"
        android:layout_marginBottom="@dimen/dp24"
        android:layout_marginTop="@dimen/dp34"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_login_image"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/heading"/>

    <TextView
        android:id="@+id/heading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24"
        android:layout_marginBottom="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:gravity="center"
        android:lineSpacingExtra="0sp"
        android:text="@string/enter_noice_first"
        android:textColor="@color/white"
        android:textSize="@dimen/sp22"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/socialLoginLabel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/socialLoginLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp16"
        android:textColor="@color/white80"
        android:gravity="center_horizontal"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="0sp"
        android:text="@string/choose_one_of_the_options"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/facebookLogin"/>

    <ImageView
        android:id="@+id/googleLogin"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp24"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_google_button"
        android:foreground="@drawable/custom_ripple_grey"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/orLabel"
        app:layout_constraintEnd_toStartOf="@id/facebookLogin"/>

    <ImageView
        android:id="@+id/facebookLogin"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp5"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_facebook_button"
        android:foreground="@drawable/custom_ripple_grey"
        app:layout_constraintStart_toEndOf="@id/googleLogin"
        app:layout_constraintTop_toTopOf="@id/googleLogin"
        app:layout_constraintEnd_toStartOf="@id/loginWithPhone"/>

    <com.facebook.login.widget.LoginButton
        android:id="@+id/facebookLoginOriginal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp30"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/facebookLogin"
        app:layout_constraintTop_toTopOf="@id/facebookLogin"/>

    <ImageView
        android:id="@+id/loginWithPhone"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_marginStart="@dimen/dp5"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_phone_signin"
        android:foreground="@drawable/custom_ripple_grey"
        app:layout_constraintStart_toEndOf="@id/facebookLogin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/googleLogin"/>

    <TextView
        android:id="@+id/orLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp35"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white80"
        android:gravity="center_horizontal"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="0sp"
        android:text="@string/or"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/continueAsGuest"/>

    <TextView
        android:id="@+id/continueAsGuest"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp60"
        android:padding="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:foreground="@drawable/custom_ripple_grey"
        android:textStyle="bold"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_yellow"
        android:letterSpacing="0.01"
        android:text="@string/continue_as_guest"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>