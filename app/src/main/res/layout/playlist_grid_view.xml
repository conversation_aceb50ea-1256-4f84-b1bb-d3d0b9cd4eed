<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/playListLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    android:orientation="vertical">

    <noice.app.views.SquareCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <FrameLayout
            android:id="@+id/audioBookLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/coverBackgroundImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:contentDescription="@string/app_name"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black60"/>

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp6"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp">

                <ImageView
                    android:id="@+id/coverImageAudioBook"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>
            </androidx.cardview.widget.CardView>
        </FrameLayout>

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>
    </noice.app.views.SquareCardView>

    <TextView
        android:id="@+id/playListName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:maxLines="2"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="Malam Minggu"/>

    <TextView
        android:id="@+id/stats"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:textColor="@color/white70"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="Total Content •  Total Duration" />

    <TextView
        android:id="@+id/creator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/white70"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        tools:text="Playlist by KarinM" />

    <TextView
        android:id="@+id/privacy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:gravity="center_vertical"
        android:drawablePadding="@dimen/dp4"
        android:letterSpacing="0.04"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="bold"
        android:textSize="@dimen/sp10"
        android:textColor="@color/white70"
        android:lineSpacingExtra="@dimen/sp2"
        android:text="@string/public_"
        app:drawableStartCompat="@drawable/ic_public_globle" />
</LinearLayout>