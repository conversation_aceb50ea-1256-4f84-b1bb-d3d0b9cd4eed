<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/artistView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/custom_ripple_bg"
    android:paddingBottom="@dimen/dp8">

    <FrameLayout
        android:id="@+id/artistImageLayout"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp104"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgArtist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp5"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_user_profile" />

        <View
            android:id="@+id/speakingIndicator"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/speaking_indicator"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/image_creator_selected"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dp5"
            android:background="@color/black60"
            android:contentDescription="@string/clip_cover_image"
            android:padding="@dimen/dp20"
            android:visibility="gone"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_tick_white" />

    </FrameLayout>

    <ImageView
        android:id="@+id/unMute"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp4"
        android:layout_marginBottom="@dimen/dp7"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_unmute_button"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/artistImageLayout"
        app:layout_constraintEnd_toEndOf="@+id/artistImageLayout" />

    <TextView
        android:id="@+id/txtType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_blackish_grey_22dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/dp6"
        android:paddingTop="@dimen/dp2"
        android:paddingEnd="@dimen/dp6"
        android:paddingBottom="@dimen/dp2"
        android:textColor="@color/white700"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/artistImageLayout" />

    <LinearLayout
        android:id="@+id/unameLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp4"
        android:paddingStart="@dimen/dp4"
        android:paddingEnd="@dimen/dp4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtType">

        <TextView
            android:id="@+id/txtName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_marginEnd="@dimen/dp2"
            android:drawablePadding="@dimen/dp4"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:textStyle="normal"
            app:layout_constraintHorizontal_chainStyle="packed"
            tools:text="Atiqul Alam" />

        <ImageView
            android:id="@+id/noicemakerBadge"
            android:layout_width="@dimen/dp12"
            android:layout_height="@dimen/dp12"
            android:layout_marginStart="@dimen/dp2"
            android:layout_gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/ic_noicemaker_badge"
            android:contentDescription="@string/app_name" />
    </LinearLayout>

    <TextView
        android:id="@+id/txtRole"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:background="@drawable/background_blackish_grey_4dp"
        android:fontFamily="@font/roboto"
        android:paddingStart="@dimen/dp4"
        android:paddingTop="@dimen/dp2"
        android:paddingEnd="@dimen/dp4"
        android:paddingBottom="@dimen/dp2"
        android:textColor="@color/white700"
        android:textSize="@dimen/sp10"
        android:textStyle="normal"
        android:visibility="gone"
        android:includeFontPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/unameLayout" />


    <TextView
        android:id="@+id/txtFollowers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:fontFamily="sans-serif"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/unameLayout"
        tools:text="3.7 followers" />

    <TextView
        android:id="@+id/txtFollow"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp24"
        android:layout_marginTop="@dimen/dp4"
        android:background="@drawable/small_follow_button_bg"
        android:drawablePadding="@dimen/dp4"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:padding="@dimen/dp4"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/follow"
        android:textColor="@color/follow_text_color"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_follower_plus"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtFollowers" />
</androidx.constraintlayout.widget.ConstraintLayout>