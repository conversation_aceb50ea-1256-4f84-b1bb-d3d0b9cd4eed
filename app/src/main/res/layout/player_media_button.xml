<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp52"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/background_yellow_14dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/musicButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/custom_ripple_bg_circle"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_black_play"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/label"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            />
        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="@dimen/dp25"
            android:layout_height="@dimen/dp25"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_gravity="center"
            android:indeterminateTint="@color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/label"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            />

        <TextView
            android:id="@+id/label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/putar"
            android:layout_marginStart="@dimen/dp8"
            android:textColor="@color/black"
            android:textSize="@dimen/sp16"
            android:fontFamily="@font/readex_pro_semi_bold"
            android:includeFontPadding="false"
            android:lineSpacingExtra="0sp"
            android:gravity="center_horizontal|center_vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/musicButton"
            app:layout_constraintEnd_toEndOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>