<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700"
    tools:context=".modules.profile.fragment.KaryaFragment">

    <FrameLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/creationRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:orientation="vertical"
            app:spanCount="2"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/emptyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp59"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_no_karya"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_gravity="center_horizontal"
                android:textSize="@dimen/sp18"
                android:textColor="@color/white"
                android:text="@string/creation_empty"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv"/>

            <TextView
                android:id="@+id/emptyDescription"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textSize="@dimen/sp16"
                android:textColor="@color/white80"
                android:text="@string/creation_empty_text_for_self"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv"/>

            <TextView
                android:id="@+id/addKarya"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/selector_yellow_btn"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:letterSpacing="0.2"
                android:padding="@dimen/dp16"
                android:text="@string/add_karya"
                android:textAllCaps="true"
                android:textColor="@color/blackish_blue"
                android:textSize="@dimen/sp12"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/emptyDescription"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/customErrorView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp59"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_karya_error_fetching"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <TextView
                android:id="@+id/tv2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_gravity="center_horizontal"
                android:textSize="@dimen/sp18"
                android:textColor="@color/white"
                android:text="@string/oh_tidak"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv2"/>

            <TextView
                android:id="@+id/errorDesc"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_gravity="center_horizontal"
                android:gravity="center"
                android:textSize="@dimen/sp16"
                android:textColor="@color/white80"
                android:text="@string/we_need_more_time_for_this_work_to_appear_lets_refresh"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv2"/>

            <TextView
                android:id="@+id/errorReloadBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:background="@drawable/selector_yellow_btn"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:letterSpacing="0.2"
                android:padding="@dimen/dp16"
                android:text="@string/returned"
                android:textAllCaps="true"
                android:textColor="@color/blackish_blue"
                android:textSize="@dimen/sp12"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/errorDesc"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp300"/>
    </FrameLayout>
</androidx.core.widget.NestedScrollView>