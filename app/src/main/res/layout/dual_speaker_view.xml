<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/dualSpeakerLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/custom_ripple_bg"
    tools:background="@color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/imgLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp17"
        android:layout_marginEnd="@dimen/dp17"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/speakerImage1"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:layout_marginTop="@dimen/dp5"
            android:src="@drawable/ic_user_profile"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/speakerOverlay"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:background="@color/black80"
            android:contentDescription="@string/app_name"
            app:layout_constraintBottom_toBottomOf="@+id/speakerImage1"
            app:layout_constraintEnd_toEndOf="@+id/speakerImage1"
            app:layout_constraintStart_toStartOf="@+id/speakerImage1"
            app:layout_constraintTop_toTopOf="@+id/speakerImage1"
            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.Round" />


        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtPlusNoOfSpeaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white300"
            android:textSize="@dimen/sp16"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/speakerImage1"
            tools:text="+4" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/noOfSpeakerMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginBottom="@dimen/dp12"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:textColor="@color/white300"
        android:textSize="@dimen/sp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imgLayout"
        tools:text="dan 4\nLainnya" />
</androidx.constraintlayout.widget.ConstraintLayout>