<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerTopHighlight"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp250"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewShadowTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp50"
        android:background="@drawable/shadow_gradient_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/recyclerTopHighlight" />

    <View
        android:id="@+id/viewShadowBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp50"
        android:background="@drawable/shadow_gradient_bottom"
        app:layout_constraintBottom_toBottomOf="@id/recyclerTopHighlight"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <me.relex.circleindicator.CircleIndicator2
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp20"
        android:layout_gravity="bottom|center"
        app:ci_animator="@anim/scale_with_alpha_custom"
        app:ci_drawable="@drawable/circle_indicator_selected_rectangle"
        app:ci_drawable_unselected="@drawable/circle_indicator_unselected"
        app:ci_height="@dimen/dp6"
        app:ci_margin="@dimen/dp2"
        app:ci_width="@dimen/dp6"
        app:layout_constraintEnd_toEndOf="@+id/recyclerTopHighlight"
        app:layout_constraintStart_toStartOf="@+id/recyclerTopHighlight"
        app:layout_constraintTop_toBottomOf="@+id/recyclerTopHighlight" />

    <LinearLayout
        android:id="@+id/linearSave"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/buttonPlay"
        app:layout_constraintEnd_toStartOf="@+id/buttonPlay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/buttonPlay">

        <ImageView
            android:id="@+id/saveIcon"
            android:layout_width="@dimen/dp18"
            android:layout_height="@dimen/dp18"
            android:src="@drawable/ic_heart_white"
            android:contentDescription="@string/app_name" />

        <TextView
            android:id="@+id/saveText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:text="@string/save"
            android:textColor="@color/white" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/buttonPlay"
        android:layout_width="@dimen/dp110"
        android:layout_height="@dimen/dp50"
        android:layout_marginTop="@dimen/dp15"
        android:background="@drawable/background_white_30dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/indicator">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <ProgressBar
                android:id="@+id/playButtonProgress"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                android:indeterminateTint="@color/black"/>

            <ImageView
                android:id="@+id/playButtonImage"
                android:layout_width="@dimen/dp14"
                android:layout_height="@dimen/dp14"
                android:visibility="gone"
                android:src="@drawable/ic_play_triangle_filled_black"
                android:contentDescription="@string/play"/>

            <TextView
                android:id="@+id/playButtonText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp8"
                android:fontFamily="@font/roboto_bold"
                android:gravity="center"
                android:letterSpacing="0.2"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/play"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:textSize="@dimen/sp12"
                android:textStyle="normal"/>
        </LinearLayout>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/linearInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@+id/buttonPlay"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/buttonPlay"
        app:layout_constraintTop_toTopOf="@+id/buttonPlay">

        <ImageView
            android:layout_width="@dimen/dp18"
            android:layout_height="@dimen/dp18"
            android:src="@drawable/ic_information_white"
            android:contentDescription="@string/app_name" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:text="@string/info"
            android:textColor="@color/white" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>