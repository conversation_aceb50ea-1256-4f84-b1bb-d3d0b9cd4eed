<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:orientation="vertical"
    tools:context=".modules.onboarding.activity.MobileLoginActivity">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp22"
        app:toolbarBackIcon="@drawable/arrow_back"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/navHostMobileLogin"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/mobile_login_navigation" />
</LinearLayout>