<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="?actionBarSize"
    android:id="@+id/toolbarView"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/backButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:paddingStart="@dimen/dp8"
        android:paddingTop="@dimen/dp8"
        android:paddingBottom="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:contentDescription="@string/app_name"
        android:background="@drawable/custom_ripple_bg"
        android:src="@drawable/ic_left_arrow_white"/>

    <TextView
        android:id="@+id/toolbarTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="bold"
        android:textSize="@dimen/sp14"
        android:textColor="#ffffff"
        android:maxLines="1"
        tools:text="Title"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/secondaryButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:visibility="gone"
            android:padding="@dimen/dp8"
            android:fontFamily="sans-serif"
            android:textSize="@dimen/sp14"
            android:textColor="@color/dull_yellow"
            android:background="@drawable/custom_ripple"
            android:text="@string/skip"/>

        <TextView
            android:id="@+id/skipButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:visibility="gone"
            android:padding="@dimen/dp8"
            android:fontFamily="sans-serif"
            android:textStyle="bold"
            android:textSize="@dimen/sp12"
            android:textColor="@color/whitish_grey"
            android:background="@drawable/custom_ripple_bg"
            android:textAllCaps="true"
            android:text="@string/skip"/>

        <ImageView
            android:id="@+id/searchIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:padding="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            android:contentDescription="@string/app_name"
            tools:src="@drawable/ic_search_white"
            android:background="@drawable/custom_ripple_bg"
            android:adjustViewBounds="true"/>

        <ImageView
            android:id="@+id/rightIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_gravity="center_vertical"
            android:padding="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            android:contentDescription="@string/app_name"
            tools:src="@drawable/ic_three_dot"
            android:background="@drawable/custom_ripple_bg"
            android:adjustViewBounds="true"/>
    </LinearLayout>
</LinearLayout>