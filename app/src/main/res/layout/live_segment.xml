<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="noice.app.utils.Constants.LiveRoom.Type" />

        <import type="androidx.lifecycle.MutableLiveData" />

        <variable
            name="state"
            type="MutableLiveData&lt;noice.app.views.LiveSegment.UIState&gt;" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/liveLayout"
        android:layout_width="@dimen/dp156"
        android:layout_height="@dimen/dp180"
        android:foreground="@drawable/custom_ripple_bg_4dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:placeholder="@{@drawable/ic_thumb_default}"
            app:shapeAppearance="@style/ShapeAppearanceOverlay.App.RoundedCorners"
            app:srcUrl="@{state.coverUrl}"
            tools:srcCompat="@color/persian_blue" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_overlay"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/ShapeAppearanceOverlay.App.RoundedCorners"
            app:srcCompat="@drawable/gradient_overlay" />

        <include
            android:id="@+id/layout_left_info"
            layout="@layout/layout_linear_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginTop="@dimen/dp8"
            app:infoState="@{state.infoStateLeft}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleIf="@{state.infoStateLeft != null &amp;&amp; !state.infoStateLeft.isGone}" />

        <include
            android:id="@+id/layout_right_info"
            layout="@layout/layout_linear_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            app:infoState="@{state.infoStateRight}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleIf="@{state.infoStateRight != null &amp;&amp; !state.infoStateRight.isGone}" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgVipBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp8"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/txt_username"
            app:layout_constraintStart_toStartOf="@+id/txt_username"
            app:srcCompat="@drawable/ic_vip_badge"
            app:visibleIf="@{state.vipBadge}"
            tools:visibility="visible" />


        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txt_username"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp2"
            android:drawableStart="@{state.contentType.equals(Type.VIDEO)? @drawable/ic_video_live :  @drawable/ic_audio_live}"
            android:drawablePadding="@dimen/dp4"
            android:ellipsize="end"
            android:fontFamily="@font/readex_pro_semi_bold"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@{state.userName}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@+id/txt_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="@+id/txt_title"
            tools:text="Username" />
        <!-- Auto layout, variables, and unit scale are not yet supported -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipToOutline="true"
            android:orientation="horizontal"
            app:layout_constraintStart_toEndOf="@+id/txt_username"
            app:layout_constraintTop_toTopOf="@+id/txt_username"
            >
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgVerified"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                app:srcCompat="@drawable/ic_verified_tag"
                android:layout_marginStart="@dimen/dp3"
                android:visibility="gone"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgNplus"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                app:srcCompat="@drawable/ic_n_plus_badge"
                android:layout_marginStart="@dimen/dp3"
                android:visibility="gone"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgCreator"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                app:srcCompat="@drawable/ic_noicemaker_badge"
                android:layout_marginStart="@dimen/dp3"
                android:visibility="gone"
                />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgVip"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                app:srcCompat="@drawable/vip_badge"
                android:layout_marginStart="@dimen/dp3"
                android:visibility="gone"
                />
        </LinearLayout>


        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txt_title"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp12"
            android:ellipsize="end"
            android:fontFamily="@font/readex_pro_regular"
            android:includeFontPadding="false"
            android:maxLines="1"
            android:text="@{state.title}"
            android:textColor="@color/neutral_80"
            android:textSize="@dimen/sp12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Live room title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>