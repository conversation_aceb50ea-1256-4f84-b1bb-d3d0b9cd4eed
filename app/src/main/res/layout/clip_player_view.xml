<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_blackish_grey_8dp"
    android:padding="@dimen/dp16"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/imageCard"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp4"
        android:minHeight="@dimen/dp48"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/txtTitle"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgEpisode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name" />

        <noice.app.views.EqualizerView
            android:id="@+id/equalizer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:background="@color/black30"
            android:visibility="gone"
            app:barHeight="@dimen/dp40"
            app:marginLeft="@dimen/dp2"
            app:marginRight="@dimen/dp2"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="@dimen/dp3"
        android:maxLines="1"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/txtCatalog"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/imageCard"
        app:layout_constraintTop_toTopOf="@+id/imageCard"
        tools:text="Eps 8 : Melayat Hanya Buang-Buang Waktu (bersama Benidictivy)" />

    <TextView
        android:id="@+id/txtCatalog"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/readex_pro"
        android:textColor="@color/grey"
        android:textSize="@dimen/sp12"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintBottom_toBottomOf="@id/imageCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/txtTitle"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        tools:text="Musuh Masyarakat" />

    <LinearLayout
        android:id="@+id/playerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="visible"
        android:baselineAligned="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtCatalog">

        <RelativeLayout
            android:id="@+id/relative_play"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp12"
            android:layout_weight="1"
            android:background="@drawable/custom_ripple_bg_4dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgPlay"
                android:layout_width="@dimen/dp32"
                android:layout_height="@dimen/dp32"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp12"
                app:srcCompat="@drawable/ic_play_grey_white" />

            <ProgressBar
                android:id="@+id/loader"
                android:layout_width="@dimen/dp35"
                android:layout_height="@dimen/dp35"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp12"
                android:indeterminateTint="@color/black"
                android:visibility="gone" />

            <TextView
                android:id="@+id/text_play_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/imgPlay"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:paddingStart="@dimen/dp2"
                android:paddingEnd="@dimen/dp2"
                android:letterSpacing="0.04"
                android:text="@string/putar_eps_ini"
                android:textColor="@color/white"
                android:textSize="@dimen/sp10"
                app:layout_constraintEnd_toEndOf="@id/imgPlay"
                app:layout_constraintStart_toStartOf="@+id/imgPlay"
                app:layout_constraintTop_toBottomOf="@id/imgPlay" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relative_download"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp12"
            android:layout_weight="1"
            android:background="@drawable/custom_ripple_bg_4dp">

            <FrameLayout
                android:id="@+id/frame_download"
                android:layout_width="@dimen/dp32"
                android:layout_height="@dimen/dp32"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp12">

                <ImageView
                    android:id="@+id/image_download"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_download"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>

                <ProgressBar
                    android:id="@+id/progress_download"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_gravity="center"
                    android:visibility="invisible"
                    android:indeterminate="false"
                    android:max="100"
                    android:progress="50"
                    android:progressDrawable="@drawable/circular_progress_bar"
                    android:background="@drawable/circular_progress_background"
                    style="?android:attr/progressBarStyleHorizontal"/>
            </FrameLayout>

            <TextView
                android:id="@+id/text_download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/frame_download"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:text="@string/download_eps_ini"
                android:textColor="@color/white"
                android:textSize="@dimen/sp10"
                android:letterSpacing="0.04"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relative_add_to_queue"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp12"
            android:layout_weight="1"
            android:background="@drawable/custom_ripple_bg_4dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/image_add_to_queue"
                android:layout_width="@dimen/dp32"
                android:layout_height="@dimen/dp32"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp12"
                app:layout_constraintHorizontal_bias="0.5"
                app:srcCompat="@drawable/ic_add_to_que" />

            <TextView
                android:id="@+id/text_add_to_queue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/image_add_to_queue"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:letterSpacing="0.04"
                android:text="@string/add_to_queue_"
                android:textColor="@color/white"
                android:textSize="@dimen/sp10" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relative_add_to_playlist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp12"
            android:layout_weight="1"
            android:background="@drawable/custom_ripple_bg_4dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/image_add_to_playlist"
                android:layout_width="@dimen/dp32"
                android:layout_height="@dimen/dp32"
                android:padding="@dimen/dp5"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp12"
                app:srcCompat="@drawable/add_to_playlist" />

            <TextView
                android:id="@+id/text_add_to_playlist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/image_add_to_playlist"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:text="@string/add_to_playlist_clips"
                android:textColor="@color/white"
                android:textSize="@dimen/sp10"
                android:letterSpacing="0.04"/>
        </RelativeLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/episodeDetail"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:background="@drawable/background_yellow_radius_4dp"
        android:visibility="gone"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:minHeight="@dimen/dp48"
        android:text="@string/episode_detail"
        android:textAllCaps="true"
        android:textColor="@color/black"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/playerLayout"/>
</androidx.constraintlayout.widget.ConstraintLayout>