<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black">

    <ImageView
        android:id="@+id/userProfileImage"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_user_profile"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/userDisplayName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toEndOf="@id/userProfileImage"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"
        tools:text="Vaibhav Pal" />

    <TextView
        android:id="@+id/userName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/white70"
        android:textSize="@dimen/sp12"
        android:drawablePadding="@dimen/dp3"
        app:layout_constraintBottom_toBottomOf="@id/userDisplayName"
        app:layout_constraintStart_toEndOf="@id/userDisplayName"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"
        tools:text="\@palvaibhav89" />

    <TextView
        android:id="@+id/activityTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif"
        android:letterSpacing="0.04"
        android:lineSpacingExtra="@dimen/sp2"
        android:textColor="@color/white70"
        android:textSize="@dimen/sp10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"
        tools:text="1 jam lalu" />

    <TextView
        android:id="@+id/activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:text="@string/commented_colon"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        app:layout_constraintStart_toStartOf="@id/userDisplayName"
        app:layout_constraintTop_toBottomOf="@id/userDisplayName" />

    <noice.app.views.ReadMoreTextView
        android:id="@+id/comment"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        app:layout_constraintEnd_toStartOf="@+id/commentMenu"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/userProfileImage"
        tools:text="Setuju dengan beberapa point disini, ya memang kadang kita harus meluangkan waktu sejenak untuk keluarga." />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/commentMenu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:padding="@dimen/dp2"
        android:clickable="true"
        android:focusable="true"
        app:srcCompat="@drawable/ic_three_dot"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/comment"
        app:layout_constraintBottom_toBottomOf="@+id/comment"/>


    <noice.app.views.EpisodeView
        android:id="@+id/episodeView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        app:layout_constraintEnd_toEndOf="@id/activityTime"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/comment" />

    <include
        android:id="@+id/activity_error"
        layout="@layout/item_user_activity_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/activityTime"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/comment" />

</androidx.constraintlayout.widget.ConstraintLayout>