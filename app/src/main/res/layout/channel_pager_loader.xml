<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:padding="@dimen/dp15"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <View
            android:id="@+id/v1"
            app:layout_constraintTop_toTopOf="@+id/v2"
            app:layout_constraintBottom_toBottomOf="@+id/v2"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="@dimen/dp189"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v2"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="@dimen/dp110"
            android:layout_height="@dimen/dp36"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v3"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintTop_toBottomOf="@+id/v2"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v4"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintTop_toBottomOf="@+id/v2"
            app:layout_constraintStart_toEndOf="@+id/v3"
            android:layout_marginStart="@dimen/dp11"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v5"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintTop_toBottomOf="@+id/v2"
            app:layout_constraintStart_toEndOf="@+id/v4"
            android:layout_marginStart="@dimen/dp11"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v6"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintTop_toBottomOf="@+id/v2"
            app:layout_constraintStart_toEndOf="@+id/v5"
            android:layout_marginStart="@dimen/dp11"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v7"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintTop_toBottomOf="@+id/v2"
            app:layout_constraintStart_toEndOf="@+id/v6"
            android:layout_marginStart="@dimen/dp11"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp21"
            android:background="@drawable/background_skeleton_8dp"/>

        <View
            android:id="@+id/v8"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/v7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp31"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v9"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/v7"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp184"
            android:background="@drawable/background_skeleton_8dp"/>

        <View
            android:id="@+id/v10"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/v9"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp184"
            android:background="@drawable/background_skeleton_8dp"/>
        <View
            android:id="@+id/v11"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/v10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp184"
            android:background="@drawable/background_skeleton_8dp"/>

        <View
            android:id="@+id/v12"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/v11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp184"
            android:background="@drawable/background_skeleton_8dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>