<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24"
    tools:context=".modules.dashboard.home.fragment.ShareDialog">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/text_tutup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:paddingTop="@dimen/dp16"
        android:text="@string/tutup"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        android:background="@drawable/custom_ripple"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp26"
        android:fontFamily="sans-serif"
        android:text="@string/clip_dialog_heading"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgWave"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp170"
        android:layout_marginTop="@dimen/dp30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_tutup"
        app:srcCompat="@drawable/clip_dialog_banner" />

    <TextView
        android:id="@+id/untuk_menga"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp17"
        android:layout_marginEnd="@dimen/dp24"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/clip_dlalog_text"
        android:textColor="@color/white80"
        android:textSize="@dimen/sp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imgWave" />

    <TextView
        android:id="@+id/textOkSiap"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp17"
        android:layout_marginEnd="@dimen/dp24"
        android:background="@drawable/background_yellow_radius_4dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/ok_siap"
        android:textColor="@color/black"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/untuk_menga" />

</androidx.constraintlayout.widget.ConstraintLayout>