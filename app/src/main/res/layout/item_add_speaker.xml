<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/addSpeakerView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/custom_ripple_bg"
        android:paddingBottom="@dimen/dp8">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imfAdd"
            style="@style/ShapeAppearanceOverlay.App.Round"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:layout_marginStart="@dimen/dp17"
            android:layout_marginTop="@dimen/dp5"
            android:layout_marginEnd="@dimen/dp17"
            android:background="@drawable/ic_circle_dotted_stroke"
            android:backgroundTint="@color/black600"
            android:backgroundTintMode="screen"
            android:scaleType="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_add_rounded_stroke"
            app:tint="@color/white" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp12"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:text="Tambah\nPembicara"
            android:textColor="@color/white300"
            android:textSize="@dimen/sp10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/imfAdd"
            app:layout_constraintStart_toStartOf="@+id/imfAdd"
            app:layout_constraintTop_toBottomOf="@+id/imfAdd" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>