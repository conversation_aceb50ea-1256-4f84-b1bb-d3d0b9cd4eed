<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linear_otp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:layout_constraintEnd_toEndOf="@id/txtHeading"
    app:layout_constraintStart_toStartOf="@id/txtHeading"
    app:layout_constraintTop_toBottomOf="@+id/txtEdit">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp56"
        android:orientation="horizontal">

        <noice.app.views.otp.OTPEditText
            android:id="@+id/editText1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_weight="1"
            android:background="@drawable/bg_otp_item"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="1"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp24"
            android:importantForAutofill="no" />

        <noice.app.views.otp.OTPEditText
            android:id="@+id/editText2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_weight="1"
            android:background="@drawable/bg_otp_item"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="1"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp24"
            android:importantForAutofill="no" />

        <noice.app.views.otp.OTPEditText
            android:id="@+id/editText3"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_weight="1"
            android:background="@drawable/bg_otp_item"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="1"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp24"
            android:importantForAutofill="no" />

        <noice.app.views.otp.OTPEditText
            android:id="@+id/editText4"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp8"
            android:layout_weight="1"
            android:background="@drawable/bg_otp_item"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:gravity="center"
            android:inputType="numberDecimal"
            android:maxLength="1"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp24"
            android:importantForAutofill="no"/>
    </LinearLayout>

    <TextView
        android:id="@+id/errorText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp11"
        android:textColor="@color/pinkish_red"
        android:letterSpacing="0.05"
        android:lineSpacingExtra="0sp"/>
</LinearLayout>