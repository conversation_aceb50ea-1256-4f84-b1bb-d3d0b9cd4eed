<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp70"
    android:orientation="horizontal"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/coverImage"
        android:layout_width="@dimen/dp50"
        android:layout_height="@dimen/dp50"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp5"
        android:drawablePadding="@dimen/dp3"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp6"
        android:maxLines="1"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        app:layout_constraintEnd_toStartOf="@id/text_terima"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toTopOf="@id/coverImage"
        tools:text="Musuh Masyarakat" />

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp3"
        android:layout_marginEnd="@dimen/dp5"
        android:fontFamily="sans-serif"
        android:maxLines="1"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toStartOf="@id/text_terima"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_goneMarginTop="@dimen/dp2"
        tools:text="Coki Pardede, Tretan Muslim" />

    <TextView
        android:id="@+id/text_terima"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp28"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/selector_follow_btn"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/accept"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/text_tolak"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/text_tolak"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp28"
        android:background="@drawable/border_red_radius_4dp"
        android:drawablePadding="@dimen/dp5"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/reject"
        android:textColor="@color/red"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/white10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/title" />
</androidx.constraintlayout.widget.ConstraintLayout>
