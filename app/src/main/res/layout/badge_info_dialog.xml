<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textTutup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp8"
        android:padding="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:text="@string/tutup"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        android:letterSpacing="0.01"
        android:background="@drawable/custom_ripple"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:id="@+id/dialogTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:text="@string/noicemaker_badge"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp16"
        android:textStyle="bold"
        android:lineSpacingExtra="0sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <ImageView
        android:id="@+id/dialogIcon"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp34"
        android:src="@drawable/ic_creator_badge"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialogTitle" />

    <TextView
        android:id="@+id/dialogMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:text="@string/special_badge_for_noice_creator"
        android:textColor="@color/neutral_90"
        android:textSize="@dimen/sp14"
        android:lineSpacingExtra="@dimen/sp5"
        android:letterSpacing="0.02"
        app:layout_constraintStart_toStartOf="@id/textTutup"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialogIcon" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/textOk"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp52"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginEnd="@dimen/dp16"
        android:insetTop="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:gravity="center"
        app:cornerRadius="@dimen/dp14"
        android:backgroundTint="@color/dull_yellow"
        android:text="@string/okay"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        android:lineSpacingExtra="0sp"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialogMessage"/>
</androidx.constraintlayout.widget.ConstraintLayout>