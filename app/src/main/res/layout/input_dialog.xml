<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black80">

    <androidx.cardview.widget.CardView
        android:id="@+id/userCrd"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@color/black"
        app:cardBackgroundColor="@color/black90"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/dp8"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/inputLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        >

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvUser"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:nestedScrollingEnabled="false"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            android:paddingTop="@dimen/dp90"
            android:paddingBottom="@dimen/dp16"
            android:clipToPadding="false"
            android:visibility="gone"
            tools:visibility="visible"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/content_horizontal_segment" />

        <LinearLayout
            android:id="@+id/userEmptyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp200"
            android:background="@color/black"
            android:clickable="true"
            android:focusable="true"
            android:orientation="vertical"
            android:padding="@dimen/dp16"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/filterRecycler">

            <TextView
                android:id="@+id/emptyMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp6"
                android:text="@string/search_empty_message"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp6"
                android:text="@string/no_user_search_result"
                android:textColor="@color/white70"
                android:textSize="@dimen/sp14" />
        </LinearLayout>

    </androidx.cardview.widget.CardView>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/inputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >

        <ImageView
            android:id="@+id/image_user_profile"
            android:layout_width="@dimen/dp32"
            android:layout_height="@dimen/dp32"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_user_profile"
            app:layout_constraintBottom_toBottomOf="@id/linear_comment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/linear_comment" />

        <LinearLayout
            android:id="@+id/linear_comment"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp16"
            android:background="@drawable/black50_radius_48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/image_user_profile"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_smiley"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:contentDescription="@string/app_name"
                android:focusable="true"
                android:paddingStart="@dimen/dp12"
                android:paddingTop="@dimen/dp16"
                android:paddingEnd="@dimen/dp8"
                android:paddingBottom="@dimen/dp16"
                android:src="@drawable/ic_smileys" />

            <hani.momanii.supernova_emoji_library.Helper.EmojiconEditText
                android:id="@+id/edit_text_comment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="@string/type_a_comment_here"
                android:imeOptions="actionSend"
                android:importantForAutofill="no"
                android:inputType="textCapSentences|textMultiLine"
                android:maxLines="3"
                android:maxLength="2000"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp8"
                android:textColor="@color/white"
                android:textColorHint="@color/white80"
                android:textSize="@dimen/sp14" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/image_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp16"
                android:padding="@dimen/dp6"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                app:srcCompat="@drawable/ic_send" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>