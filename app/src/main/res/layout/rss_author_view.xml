<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/role"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="Kreator"
        android:textSize="@dimen/sp10"
        android:textColor="@color/text_grey"
        android:textStyle="normal"
        android:fontFamily="sans-serif"
        android:letterSpacing="0.04"
        android:lineSpacingExtra="@dimen/sp2"/>

    <TextView
        android:id="@+id/name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        tools:text="Kreator"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white"
        android:textStyle="normal"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"/>
</LinearLayout>