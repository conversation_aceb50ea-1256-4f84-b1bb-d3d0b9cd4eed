<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black700"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/userProfileImage"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        app:layout_constraintTop_toTopOf="@+id/textLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/textLayout"
        app:srcCompat="@drawable/ic_user_profile"
        android:layout_gravity="center_vertical"
        android:contentDescription="@string/app_name"/>

    <LinearLayout
        android:id="@+id/textLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/rvUser"
        app:layout_constraintStart_toEndOf="@+id/userProfileImage"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/background_blackish_grey_4dp">

            <EditText
                android:id="@+id/commentEditText"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="@dimen/dp18"
                android:maxLength="2000"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/type_comments_here"
                android:textColorHint="@color/white70"
                android:textSize="@dimen/sp14"
                android:fontFamily="sans-serif"
                android:textColor="@color/white"
                android:inputType="textCapSentences|textMultiLine"
                android:maxLines="3"
                android:importantForAutofill="no"/>

        <ImageView
            android:id="@+id/submit"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:src="@drawable/send_selector"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:paddingEnd="@dimen/dp14"
            android:paddingStart="@dimen/dp14"
            android:contentDescription="@string/app_name"/>
    </LinearLayout>
</LinearLayout>