<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/noiceSegmentLayout"
    android:layout_width="@dimen/dp130"
    android:layout_height="wrap_content"
    android:background="@color/black">

    <noice.app.views.SquareCardView
        android:id="@+id/cardLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/black"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/hostBgBlur"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black80"
            />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/thumbnailLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/dp5"
            android:paddingEnd="@dimen/dp5">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideLine5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/noiceLive"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                app:drawableStartCompat="@drawable/ic_live_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/noiceLiveTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:textAlignment="viewStart"
                android:textColor="@color/grey"
                android:textSize="@dimen/sp8"
                android:visibility="gone"
                android:fontFamily="@font/roboto_bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="18.30 WIB" />

            <TextView
                android:id="@+id/liveListenersCount"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:drawablePadding="@dimen/dp6"
                android:textColor="@color/light_grey"
                android:textSize="@dimen/sp12"
                app:drawableStartCompat="@drawable/ic_listener_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="124" />

            <ImageView
                android:id="@+id/hostProfileImage"
                android:layout_width="@dimen/dp36"
                android:layout_height="@dimen/dp36"
                android:layout_marginEnd="@dimen/dp4"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_user_profile"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/guideLine5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/hostUsername"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:drawablePadding="@dimen/dp2"
                android:ellipsize="end"
                android:maxLines="1"
                android:gravity="center_horizontal"
                android:maxLength="7"
                android:textColor="@color/light_blue"
                android:textSize="@dimen/sp10"
                app:drawableStartCompat="@drawable/ic_live_host_icon"
                app:layout_constraintEnd_toEndOf="@id/hostProfileImage"
                app:layout_constraintStart_toStartOf="@id/hostProfileImage"
                app:layout_constraintTop_toBottomOf="@id/hostProfileImage"
                tools:text=" @surajpaln2011" />

            <ImageView
                android:id="@+id/speakerImage1"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_marginStart="@dimen/dp4"
                android:layout_marginTop="@dimen/dp3"
                android:contentDescription="@string/app_name"
                android:visibility="gone"
                android:src="@drawable/ic_user_profile"
                app:layout_constraintStart_toEndOf="@id/guideLine5"
                app:layout_constraintTop_toTopOf="@id/hostProfileImage"/>

            <ImageView
                android:id="@+id/speakerImage2"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp4"
                android:contentDescription="@string/app_name"
                android:visibility="gone"
                android:src="@drawable/ic_user_profile"
                app:layout_constraintBottom_toBottomOf="@id/hostProfileImage"
                app:layout_constraintEnd_toEndOf="parent"
                tools:ignore="MissingConstraints" />

            <ImageView
                android:id="@+id/speakerImageBig"
                android:layout_width="@dimen/dp36"
                android:layout_height="@dimen/dp36"
                android:layout_marginBottom="@dimen/dp4"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_user_profile"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/guideLine5"
                app:layout_constraintTop_toTopOf="@id/hostProfileImage"/>

            <TextView
                android:id="@+id/totalSpeakers"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/white"
                android:textSize="@dimen/sp10"
                tools:text="+ 8 Orang"
                app:layout_constraintEnd_toEndOf="@id/speakerImage2"
                app:layout_constraintStart_toStartOf="@id/speakerImage1"
                app:layout_constraintTop_toBottomOf="@id/speakerImage2"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </noice.app.views.SquareCardView>

    <TextView
        android:id="@+id/noiceLiveTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:maxLines="1"
        android:ellipsize="end"
        android:fontFamily="@font/roboto_bold"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardLayout"
        tools:text="Tidak Perlu Tersi..." />

    <TextView
        android:id="@+id/noiceLiveSubTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:maxLines="1"
        android:text=""
        android:fontFamily="@font/roboto"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/noiceLiveTitle"
        tools:text="Seru-seruan ngekritik Coki &amp;..." />

</androidx.constraintlayout.widget.ConstraintLayout>