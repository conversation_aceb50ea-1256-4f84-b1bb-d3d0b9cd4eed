<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarViewTitle=" " />

    <noice.app.views.homesegmentviews.ClipHorizontalSegment
        android:id="@+id/clip_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:title_visibility="gone" />

    <TextView
        android:id="@+id/text_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:fontFamily="@font/roboto_bold"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:textSize="@dimen/sp16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clip_view"
        tools:text="Tidak Perlu Tersinggung" />

    <TextView
        android:id="@+id/text_play"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/sp5"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        android:text="@string/putar_eps_ini"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        app:drawableStartCompat="@drawable/ic_play_triangle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_title" />


    <LinearLayout
        android:id="@+id/downloadLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_play"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12">

            <ImageView
                android:id="@+id/downloadIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@drawable/ic_download_16dp"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

            <ProgressBar
                android:id="@+id/downloadProgress"
                android:layout_width="@dimen/dp28"
                android:layout_height="@dimen/dp28"
                android:layout_gravity="center"
                android:visibility="gone"
                android:indeterminate="false"
                android:max="100"
                android:progress="50"
                android:progressDrawable="@drawable/circular_progress_bar"
                android:background="@drawable/circular_progress_background"
                style="?android:attr/progressBarStyleHorizontal"/>
        </FrameLayout>

        <TextView
            android:id="@+id/download"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:fontFamily="sans-serif"
            android:textSize="@dimen/sp14"
            android:textColor="@color/dull_white"
            android:lineSpacingExtra="@dimen/sp5"
            android:text="@string/download" />
    </LinearLayout>

    <TextView
        android:id="@+id/text_add_to_queue"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/sp5"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        android:text="@string/add_to_queue_"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        app:drawableStartCompat="@drawable/ic_add_to_queue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/downloadLayout" />

    <TextView
        android:id="@+id/text_add_to_playlist"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/sp5"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        android:text="@string/add_to_playlist_clips"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        app:drawableStartCompat="@drawable/add_to_playlist"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_add_to_queue" />

    <TextView
        android:id="@+id/text_report_clip"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp10"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/sp5"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        android:text="@string/laporkan_klip"
        android:textColor="@color/red"
        android:textSize="@dimen/sp14"
        app:drawableStartCompat="@drawable/ic_information_red"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_add_to_playlist" />

</androidx.constraintlayout.widget.ConstraintLayout>