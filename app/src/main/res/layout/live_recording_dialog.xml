<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp280"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardBackgroundColor="@color/black600"
    android:layout_gravity="center"
    app:cardCornerRadius="@dimen/dp4">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black600"
        android:padding="@dimen/dp16"
        >

        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp16"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/white"
            android:text="@string/Siaran_Ulang_Live"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <TextView
            android:id="@+id/txtDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/sp14"
            android:layout_marginTop="@dimen/dp12"
            android:fontFamily="@font/roboto"
            android:textColor="@color/text_grey2"
            android:text="@string/supporting_"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />


        <TextView
            android:id="@+id/txtSubmit"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp32"
            android:textSize="@dimen/sp12"
            android:layout_marginTop="@dimen/dp32"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/black90"
            android:text="@string/mengerti"
            android:gravity="center"
            android:textAllCaps="true"
            android:background="@drawable/follow_button_bg"
            app:layout_constraintTop_toBottomOf="@+id/txtDesc"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>