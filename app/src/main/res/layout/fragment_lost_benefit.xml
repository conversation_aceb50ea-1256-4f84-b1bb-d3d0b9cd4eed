<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".modules.onboarding.fragments.LostBenefitFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        app:toolbarViewTitle="@string/delete_account"
        app:titleTextSize="@dimen/sp16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:fillViewport="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/continueBtn">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="@dimen/dp32">

            <TextView
                android:id="@+id/headingText"
                android:layout_width="@dimen/size_0dp"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp24"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:textSize="@dimen/sp18"
                android:text="@string/need_to_know_before_deleting_account"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/headingDesc"
                android:layout_width="@dimen/size_0dp"
                android:layout_marginTop="@dimen/dp8"
                android:layout_height="wrap_content"
                android:textColor="@color/white_600"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/here_are_things_that_can_be_your_consideration"
                app:layout_constraintStart_toStartOf="@id/headingText"
                app:layout_constraintEnd_toEndOf="@id/headingText"
                app:layout_constraintTop_toBottomOf="@+id/headingText"/>

            <TextView
                android:id="@+id/lostBenefit1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:visibility="gone"
                android:gravity="center_vertical"
                android:textColor="@color/medium_grey_100"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/coin_and_diamond_will_be_lost"
                android:drawablePadding="@dimen/dp16"
                app:drawableStartCompat="@drawable/ic_benefit_1"
                app:layout_constraintStart_toStartOf="@id/headingDesc"
                app:layout_constraintEnd_toEndOf="@id/headingDesc"
                app:layout_constraintTop_toBottomOf="@id/headingDesc" />

            <TextView
                android:id="@+id/lostBenefit2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:textColor="@color/medium_grey_100"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/your_work_will_still_be_there_but_not_as_creator"
                android:drawablePadding="@dimen/dp16"
                app:drawableStartCompat="@drawable/ic_benefit_2"
                app:layout_constraintStart_toStartOf="@id/headingDesc"
                app:layout_constraintEnd_toEndOf="@id/headingDesc"
                app:layout_constraintTop_toBottomOf="@id/lostBenefit1" />

            <TextView
                android:id="@+id/lostBenefit3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:textColor="@color/medium_grey_100"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/followers_will_be_lost"
                android:drawablePadding="@dimen/dp16"
                app:drawableStartCompat="@drawable/ic_benefit_3"
                app:layout_constraintStart_toStartOf="@id/headingDesc"
                app:layout_constraintEnd_toEndOf="@id/headingDesc"
                app:layout_constraintTop_toBottomOf="@id/lostBenefit2" />

            <TextView
                android:id="@+id/lostBenefit4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:textColor="@color/medium_grey_100"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/your_collection_will_be_lost"
                android:drawablePadding="@dimen/dp16"
                app:drawableStartCompat="@drawable/ic_benefit_4"
                app:layout_constraintStart_toStartOf="@id/headingDesc"
                app:layout_constraintEnd_toEndOf="@id/headingDesc"
                app:layout_constraintTop_toBottomOf="@id/lostBenefit3" />

            <TextView
                android:id="@+id/lostBenefit5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:textColor="@color/medium_grey_100"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:text="@string/your_username_will_be_lost"
                android:drawablePadding="@dimen/dp16"
                app:drawableStartCompat="@drawable/ic_benefit_5"
                app:layout_constraintStart_toStartOf="@id/headingDesc"
                app:layout_constraintEnd_toEndOf="@id/headingDesc"
                app:layout_constraintTop_toBottomOf="@id/lostBenefit4" />

            <CheckBox
                android:id="@+id/sincereCheckbox"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp26"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp8"
                android:buttonTint="@color/white"
                android:text="@string/yes_gpp_is_sincere"
                android:textSize="@dimen/sp14"
                android:fontFamily="sans-serif"
                android:textColor="@color/white"
                android:theme="@style/MyCheckboxTheme"
                app:layout_constraintStart_toStartOf="@id/headingText"
                app:layout_constraintEnd_toEndOf="@id/headingText"
                app:layout_constraintTop_toBottomOf="@id/lostBenefit5"/>

            <ImageView
                android:id="@+id/showErrorToast"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp16"
                android:contentDescription="@string/app_name"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_error_message"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintStart_toStartOf="@id/headingText"
                app:layout_constraintTop_toBottomOf="@id/sincereCheckbox">
            </ImageView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/continueBtn"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:layout_gravity="center"
        android:background="@drawable/selector_yellow_button"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:text="@string/continue_txt"
        android:textAllCaps="true"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp12"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>