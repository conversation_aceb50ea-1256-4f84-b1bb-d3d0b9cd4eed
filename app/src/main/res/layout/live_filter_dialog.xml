<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtFilter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/custom_ripple"
        android:fontFamily="@font/roboto_bold"
        android:paddingTop="@dimen/dp18"
        android:text="@string/filter"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />



    <CheckBox
        android:id="@+id/checkLive"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/live_sekarang"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:layoutDirection="rtl"
        android:theme="@style/checkBoxStyle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtFilter" />

    <CheckBox
        android:id="@+id/checkDatang"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="Akan Datang"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:layoutDirection="rtl"
        android:theme="@style/checkBoxStyle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/checkLive" />

    <CheckBox
        android:id="@+id/checkUlang"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="Siaran Ulang"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:layoutDirection="rtl"
        android:theme="@style/checkBoxStyle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/checkDatang" />

</androidx.constraintlayout.widget.ConstraintLayout>