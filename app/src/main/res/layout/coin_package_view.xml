<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable
            name="item"
            type="noice.app.modules.coins.model.CoinPackage" />
    </data>

    <FrameLayout
        android:id="@+id/packageLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp8"
        app:isSelected="@{item.isSelected}"
        android:background="@drawable/selector_coin_package">

        <TextView
            android:id="@+id/packageTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp4"
            android:visibility="@{item.tag == null ? View.GONE : View.VISIBLE}"
            tools:text="sjdkhasd"
            android:text="@{item.tag.label.toUpperCase()}"
            app:txtColor="@{item.tag.textColor}"
            app:bgTint="@{item.tag.bgColor}"
            android:maxLines="1"
            android:ellipsize="end"
            android:textSize="@dimen/sp8"
            android:textStyle="bold"
            android:background="@drawable/economical_tag"
            android:letterSpacing="0.01"
            android:fontFamily="sans-serif"
            android:lineSpacingExtra="0sp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp20"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/packageThumbnail"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp45"
                android:layout_gravity="center"
                app:loadImageUrl="@{item.config.imageUrl}"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

            <TextView
                android:id="@+id/packageCoins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:layout_gravity="center"
                android:text="@{item.formattedCoinQuantity}"
                tools:text="38"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="bold"
                android:letterSpacing="0"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp3"
                android:drawablePadding="@dimen/dp3"
                app:drawableStartCompat="@drawable/ic_noice_coin" />

            <TextView
                android:id="@+id/packageCoinPlus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:layout_gravity="center"
                android:visibility="@{item.areFreeCoinsAvailable ? View.VISIBLE : View.GONE}"
                android:text="@{`+` + item.formattedFreeCoinQuantity + ` coin`}"
                tools:text="+8 coin"
                android:textColor="@color/light_green"
                android:textSize="@dimen/sp12"
                android:letterSpacing="0"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp3" />

            <TextView
                android:id="@+id/packagePrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:layout_marginBottom="@dimen/dp16"
                android:layout_gravity="center"
                android:text="@{item.priceIDR.priceAfterDiscount.toString()}"
                tools:text="Rp999.000"
                android:textColor="@color/medium_greyed"
                android:textSize="@dimen/sp12"
                android:letterSpacing="0"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp3" />
        </LinearLayout>
    </FrameLayout>
</layout>