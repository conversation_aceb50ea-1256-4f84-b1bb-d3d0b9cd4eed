<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp12"
        app:toolbarBackIcon="@drawable/ic_cross_white"
        app:rightIconVisibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/playerLayout"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/dp40">

            <TextView
                android:id="@+id/txtTitle"
                android:layout_margin="@dimen/dp16"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="@dimen/sp18"
                android:text="@string/currently_playing"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:gravity="top" />

            <include
                android:id="@+id/currentPlaying"
                app:layout_constraintTop_toBottomOf="@+id/txtTitle"
                layout="@layout/queue_item"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp21"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/manualHeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp16"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/header_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineHeight="@dimen/dp21"
                    android:text="@string/next_from_the_queue_list"
                    android:background="@color/black700"
                    android:textColor="@color/white"
                    android:maxLines="2"
                    android:textStyle="bold"
                    android:textSize="@dimen/sp18"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif"/>
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvQueue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp14"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/queue_item"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/playerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/txtRemove"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginHorizontal="@dimen/dp16"
            android:background="@drawable/custom_ripple_bg"
            android:text="@string/remove"
            android:textColor="@color/orange_yellow"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            android:gravity="center"
            android:fontFamily="sans-serif" />

        <noice.app.exoplayer.PlayerControllerView
            android:id="@+id/playerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            app:show_timeout="0"
            android:visibility="gone"
            app:controller_layout_id="@layout/custom_player_view"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>