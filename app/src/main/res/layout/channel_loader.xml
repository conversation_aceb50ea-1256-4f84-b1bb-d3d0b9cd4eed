<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:clickable="true"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp16"
        android:paddingTop="@dimen/dp16"
        android:clipToPadding="false"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        >

        <View
            android:id="@+id/thumbnail_s"
            android:layout_width="@dimen/dp140"
            android:layout_height="@dimen/dp140"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/background_skeleton_8dp"
            />
        <View
            android:id="@+id/view112"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/thumbnail_s"
            android:background="@drawable/background_skeleton_8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp21"/>


        <View
            android:id="@+id/view1"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/view112"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp21"/>
        <View
            android:id="@+id/view113"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginStart="@dimen/dp30"
            android:layout_marginEnd="@dimen/dp30"
            app:layout_constraintTop_toBottomOf="@+id/view1"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp21"/>
        <View
            android:id="@+id/view14"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginStart="@dimen/dp45"
            android:layout_marginEnd="@dimen/dp45"
            app:layout_constraintTop_toBottomOf="@+id/view113"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp21"/>



        <View
            android:id="@+id/view2"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/view14"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp10"/>

        <View
            android:id="@+id/view3"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/view2"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp52"/>
        <View
            android:id="@+id/subscribe"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/view3"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp52"/>

        <View
            android:id="@+id/description"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/subscribe"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp10"/>

        <LinearLayout
            android:id="@+id/viewLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/description"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <View
                android:id="@+id/view4"
                android:background="@drawable/background_skeleton_8dp"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp25"/>
            <View
                android:id="@+id/view5"
                android:layout_marginStart="@dimen/dp4"
                android:background="@drawable/background_skeleton_8dp"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp25"/>
        </LinearLayout>

        <View
            android:id="@+id/view7"
            android:layout_marginTop="@dimen/dp24"
            android:background="#FFFFFE"
            android:alpha=".5"
            app:layout_constraintTop_toBottomOf="@+id/viewLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"/>
        <View
            android:id="@+id/view8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/view9"
            app:layout_constraintBottom_toBottomOf="@+id/view9"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="@dimen/dp189"
            android:layout_height="@dimen/dp21"/>
        <View
            android:id="@+id/view9"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp24"
            app:layout_constraintTop_toBottomOf="@+id/view7"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="@dimen/dp110"
            android:layout_height="@dimen/dp36"/>

        <View
            android:id="@+id/view10"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintTop_toBottomOf="@+id/view9"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp184"/>

        <View
            android:id="@+id/view11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/view10"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/background_skeleton_8dp"
            android:layout_width="match_parent"
            android:layout_height="0dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>