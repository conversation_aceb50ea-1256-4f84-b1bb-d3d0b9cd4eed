<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/listSegmentLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:foreground="@drawable/custom_ripple_bg_4dp">

    <noice.app.views.SquareCardView
        android:id="@+id/cardCoverImage"
        android:layout_width="@dimen/dp100"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dp5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/imageLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/coverImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/ic_thumb_default" />

            <FrameLayout
                android:id="@+id/audioBookLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/coverBackgroundImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/app_name"
                    android:scaleType="centerCrop" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/black50" />

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_margin="@dimen/dp10"
                    app:cardCornerRadius="@dimen/dp3"
                    app:cardElevation="@dimen/size_0dp">

                    <ImageView
                        android:id="@+id/coverImageAudioBook"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name" />
                </androidx.cardview.widget.CardView>
            </FrameLayout>
        </FrameLayout>
    </noice.app.views.SquareCardView>

    <TextView
        android:id="@+id/title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:drawablePadding="@dimen/dp2"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/white"
        android:textSize="@dimen/sp13"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cardCoverImage"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Catalog Title" />

    <TextView
        android:id="@+id/description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="@dimen/dp4"
        android:maxLines="2"
        android:textColor="@color/neutral_90"
        android:textSize="@dimen/sp13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cardCoverImage"
        app:layout_constraintTop_toBottomOf="@+id/title"
        tools:text="Musuh Masyarakat Musuh MasyarakatMusuh MasyarakatMusuh MasyarakatMusuh MasyarakatMusuh MasyarakatMusuh MasyarakatMusuh Masyarakat" />

    <ImageView
        android:id="@+id/imgDuration"
        android:layout_width="@dimen/dp9"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_play_triangle_filled_black"
        app:layout_constraintBottom_toBottomOf="@+id/duration"
        app:layout_constraintStart_toStartOf="@+id/description"
        app:layout_constraintTop_toTopOf="@+id/duration"
        app:tint="@color/neutral_80" />

    <TextView
        android:id="@+id/duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textColor="@color/neutral_80"
        android:textSize="@dimen/sp12"
        app:layout_constraintStart_toEndOf="@+id/imgDuration"
        app:layout_constraintTop_toBottomOf="@+id/description"
        tools:text="200 rb" />

</androidx.constraintlayout.widget.ConstraintLayout>