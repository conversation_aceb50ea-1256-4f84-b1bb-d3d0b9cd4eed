<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/tile"
    app:cardBackgroundColor="@color/red"
    app:cardCornerRadius="@dimen/dp4"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp34"
        android:paddingBottom="@dimen/dp33">

        <TextView
            android:id="@+id/genreName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:lineSpacingExtra="@dimen/sp4"
            android:paddingStart="@dimen/dp14"
            android:paddingEnd="@dimen/dp4"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="Suraj"
            android:textStyle="bold"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white"
            android:fontFamily="sans-serif" />

        <ImageView
            android:id="@+id/genreIcon"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_gravity="center_vertical|end"
            android:adjustViewBounds="true"
            android:contentDescription="@string/select_genre"
            tools:src="@drawable/ic_heart_selected"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/baruLayout"
        android:layout_width="@dimen/dp54"
        android:layout_height="@dimen/dp20"
        android:layout_gravity="end|top"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/newVertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="@string/new_vertical"
            android:background="@drawable/background_white_4dp_round"
            android:textColor="@color/black600"
            android:textSize="@dimen/sp10"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingStart="@dimen/dp5"
            android:paddingEnd="@dimen/dp5"
            android:textAllCaps="true"
            android:lineSpacingExtra="0sp"
            app:drawableStartCompat="@drawable/ic_star"/>
    </FrameLayout>
</androidx.cardview.widget.CardView>