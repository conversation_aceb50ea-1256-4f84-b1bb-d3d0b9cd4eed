<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bottomSheetPlayer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:behavior_peekHeight="@dimen/size_0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black60"
        android:clickable="false" />

    <androidx.media3.ui.PlayerView
        android:id="@+id/videoPlayerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:animation_enabled="false"
        app:auto_show="false"
        app:controller_layout_id="@layout/video_controller_view"
        app:layout_constraintBottom_toTopOf="@id/headingLayoutPlayer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:resize_mode="fill"
        app:show_buffering="never"
        app:show_timeout="0"
        app:use_controller="false" />

    <View
        android:id="@+id/videoPlayerOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black30"
        android:clickable="false"
        android:visibility="gone" />

    <View
        android:id="@+id/controllerOverlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shadow_gradient_bottom_black"
        android:clickable="false"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/controllerLayout"
        app:layout_constraintTop_toBottomOf="@id/playerViewPager" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/expandedPlayerScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/playerLayoutMain"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <noice.app.views.CustomToolbarView
                    android:id="@+id/toolbarPlayer"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    app:customStreamType="audio"
                    app:dropDownButtonVisibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:threeDotIconVisibility="visible"
                    app:titleTextSize="@dimen/sp16" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/playerViewPager"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/size_0dp"
                    app:layout_constraintBottom_toTopOf="@id/controllerLayout"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/toolbarPlayer" />

                <FrameLayout
                    android:id="@+id/controllerLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp7"
                    app:layout_constraintBottom_toTopOf="@+id/commentCard"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <noice.app.exoplayer.PlayerControllerView
                        android:id="@+id/playerControlView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:controller_layout_id="@layout/custom_player_view"
                        app:show_timeout="0"
                        tools:visibility="visible" />

                    <noice.app.exoplayer.PlayerControllerView
                        android:id="@+id/playerControlViewRadio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:controller_layout_id="@layout/radio_controller_view"
                        app:show_timeout="0" />
                </FrameLayout>

                <androidx.cardview.widget.CardView
                    android:id="@+id/commentCard"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/dp58"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_marginBottom="@dimen/dp7"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white20"
                    app:cardCornerRadius="@dimen/dp16"
                    app:cardElevation="@dimen/size_0dp"
                    app:layout_constraintBottom_toTopOf="@id/buttonBarBarrier"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="gone">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/contentLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/imgUser"
                            android:layout_width="@dimen/dp24"
                            android:layout_height="@dimen/dp24"
                            android:layout_marginStart="@dimen/dp8"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_user_profile" />

                        <TextView
                            android:id="@+id/txtName"
                            android:layout_width="@dimen/size_0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginTop="@dimen/dp8"
                            android:layout_marginEnd="@dimen/dp5"
                            android:ellipsize="end"
                            android:fontFamily="@font/readex_pro"
                            android:maxLines="1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp12"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toTopOf="@id/txtMessage"
                            app:layout_constraintEnd_toStartOf="@id/imgArrow"
                            app:layout_constraintStart_toEndOf="@id/imgUser"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="Don" />

                        <TextView
                            android:id="@+id/txtMessage"
                            android:layout_width="@dimen/size_0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_marginBottom="@dimen/dp8"
                            android:ellipsize="end"
                            android:fontFamily="@font/readex_pro"
                            android:maxLines="1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp12"
                            android:textStyle="normal"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/imgArrow"
                            app:layout_constraintStart_toEndOf="@id/imgUser"
                            app:layout_constraintTop_toBottomOf="@id/txtName"
                            tools:text="Don ko pakdna mushkil hi nahi, namumkin hai!" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/imgArrow"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp12"
                            android:background="@drawable/custom_ripple_bg"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_arrow_right" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.cardview.widget.CardView>

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/buttonBarBarrier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:barrierDirection="top"
                    app:constraint_referenced_ids="buttonBar, likeCommentsShareBar" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/buttonBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp24"
                    android:layout_marginBottom="@dimen/dp16"
                    android:background="@drawable/bg_rounded_9dp"
                    android:backgroundTint="@color/white_05_opacity"
                    android:elevation="0dp"
                    android:padding="@dimen/dp8"
                    android:visibility="gone"
                    app:layout_constraintBottom_toTopOf="@id/headingLayoutPlayer"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/ll_speed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple_bg"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_playback_speed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="@dimen/dp5"
                            android:background="@drawable/custom_ripple_bg_4dp"
                            android:fontFamily="@font/readex_pro"
                            android:includeFontPadding="false"
                            android:text="@string/_1x"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_speed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:text="Speed"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp10"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_sleep"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple_bg"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toStartOf="@id/ll_share"
                        app:layout_constraintStart_toEndOf="@id/ll_speed"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_sleep"
                            android:layout_width="@dimen/dp17"
                            android:layout_height="@dimen/dp17"
                            android:layout_gravity="center"
                            android:layout_marginBottom="@dimen/dp5"
                            android:background="@drawable/custom_ripple_bg_circle"
                            android:src="@drawable/ic_sleep"
                            app:tint="@color/white_80_opacity" />

                        <TextView
                            android:id="@+id/tv_sleep"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:text="Timer"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp10"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_share"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple_bg"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toStartOf="@id/ll_comment"
                        app:layout_constraintStart_toEndOf="@id/ll_sleep"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_share"
                            android:layout_width="@dimen/dp17"
                            android:layout_height="@dimen/dp17"
                            android:layout_gravity="center"
                            android:layout_marginBottom="@dimen/dp5"
                            android:background="@drawable/custom_ripple_bg_4dp"
                            android:src="@drawable/ic_share_white"
                            app:tint="@color/white_80_opacity" />

                        <TextView
                            android:id="@+id/tv_share"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:text="Share"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp10"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_comment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple_bg"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ll_queue"
                        app:layout_constraintStart_toEndOf="@id/ll_share"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_comment"
                            android:layout_width="@dimen/dp17"
                            android:layout_height="@dimen/dp17"
                            android:layout_gravity="center"
                            android:layout_marginBottom="5dp"
                            android:background="@drawable/custom_ripple_bg_circle"
                            android:src="@drawable/ic_comment_bubble"
                            app:tint="@color/white_80_opacity" />

                        <TextView
                            android:id="@+id/tv_total_comment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:fontFamily="@font/readex_pro"
                            android:text="1"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp10"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ll_queue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple_bg"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="vertical"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_queue"
                            android:layout_width="@dimen/dp17"
                            android:layout_height="@dimen/dp17"
                            android:layout_gravity="center"
                            android:layout_marginBottom="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/custom_ripple_bg_4dp"
                            android:src="@drawable/ic_queue"
                            app:tint="@color/white_80_opacity" />

                        <TextView
                            android:id="@+id/tv_queue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:text="Antrian"
                            android:textColor="@color/white_80_opacity"
                            android:textSize="@dimen/sp10"
                            android:textStyle="bold" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--This layout only used for radio content-->
                <LinearLayout
                    android:id="@+id/likeCommentsShareBar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp16"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toTopOf="@id/headingLayoutPlayer"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="gone">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/commentLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp28"
                        android:layout_marginStart="@dimen/dp22"
                        android:background="@drawable/background_white_10_percent_14dp"
                        android:minWidth="@dimen/dp90">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/commentIcon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="@string/app_name"
                            android:fontFamily="sans-serif"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/noOfComments"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_comment_white" />

                        <TextView
                            android:id="@+id/noOfComments"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp6"
                            android:fontFamily="@font/readex_pro"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/commentIcon"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="12k" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/shareLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp28"
                        android:layout_marginStart="@dimen/dp22"
                        android:layout_marginEnd="@dimen/dp16"
                        android:background="@drawable/background_white_10_percent_14dp"
                        android:minWidth="@dimen/dp98">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/noOfSharePlayer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="@string/app_name"
                            android:fontFamily="sans-serif"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/titleShare"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_share_16" />

                        <TextView
                            android:id="@+id/titleShare"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp6"
                            android:fontFamily="@font/readex_pro"
                            android:letterSpacing="0.03"
                            android:text="@string/share"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/noOfSharePlayer"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/adsPlayerLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/size_0dp"
                    android:background="@color/black"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/toolbarPlayer">

                    <noice.app.exoplayer.CompanionAdView
                        android:id="@+id/companionAdSlot"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/size_0dp"
                        android:layout_margin="@dimen/dp20"
                        android:gravity="center"
                        android:textAlignment="center"
                        app:bgShape="normal"
                        app:layout_constraintBottom_toTopOf="@id/learnMore"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/learnMore"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp45"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:layout_marginBottom="@dimen/dp30"
                        android:backgroundTint="@color/color_ad_button"
                        android:fontFamily="@font/readex_pro"
                        android:insetTop="@dimen/size_0dp"
                        android:insetBottom="@dimen/size_0dp"
                        android:text="@string/visit_advertising"
                        android:textAllCaps="false"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:visibility="gone"
                        app:cornerRadius="@dimen/dp14"
                        app:layout_constraintBottom_toTopOf="@+id/adPosition"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />


                    <TextView
                        android:id="@+id/adPosition"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/dp8"
                        android:fontFamily="sans-serif"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:text="00:00"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp12"
                        app:layout_constraintBottom_toTopOf="@id/adProgressBar"
                        app:layout_constraintStart_toStartOf="@id/adProgressBar" />

                    <TextView
                        android:id="@+id/adDuration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/dp8"
                        android:fontFamily="sans-serif"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:text="00:00"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp12"
                        app:layout_constraintBottom_toBottomOf="@id/adPosition"
                        app:layout_constraintEnd_toEndOf="@id/adProgressBar" />

                    <ProgressBar
                        android:id="@+id/adProgressBar"
                        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/dp2"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:layout_marginBottom="@dimen/dp10"
                        android:progressBackgroundTint="@color/neutral_grey_900"
                        android:progressTint="@color/white"
                        app:layout_constraintBottom_toTopOf="@id/adsControlView"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        tools:progress="70" />

                    <noice.app.exoplayer.AdControlView
                        android:id="@+id/adsControlView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/dp16"
                        app:controller_layout_id="@layout/ads_controller_layout"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:show_timeout="0" />


                    <noice.app.views.ErrorView
                        android:id="@+id/adsLoader"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/size_0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:id="@+id/headingLayoutPlayer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bottom_sheet_rounded_grey_24dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:visibility="gone">

                    <View
                        android:id="@+id/bottomsheetLine"
                        android:layout_width="@dimen/dp56"
                        android:layout_height="@dimen/dp4"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/dp16"
                        android:background="#303030" />

                    <TextView
                        android:id="@+id/headerTextPlayer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/dp12"
                        android:layout_marginBottom="@dimen/dp20"
                        android:ellipsize="end"
                        android:fontFamily="@font/readex_pro"
                        android:maxLines="1"
                        android:paddingStart="@dimen/dp16"
                        android:paddingEnd="@dimen/dp16"
                        android:text="@string/queue_list"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvQueuePlayer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white10"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp8"
                android:visibility="gone"
                tools:listitem="@layout/recently_played_segment" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <noice.app.views.ErrorView
        android:id="@+id/errorViewPlayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/swipeScrim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black70"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/dp16"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_swipe_left_white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp52"
                android:layout_marginTop="@dimen/dp18"
                android:layout_marginEnd="@dimen/dp52"
                android:gravity="center"
                android:lineHeight="@dimen/dp25"
                android:text="@string/swipe_left_to_change_other_content"
                android:textColor="@color/white_300"
                android:textSize="@dimen/sp18" />
        </LinearLayout>
    </FrameLayout>

</FrameLayout>