<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="noice.app.utils.filter.FilterViewModel" />

    </data>
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:clickable="true"
        android:focusable="true"
        tools:context=".modules.live.fragment.LiveHomeFragment">

        <View
            android:id="@+id/view_visit"
            android:layout_width="@dimen/dp1"
            android:layout_height="@dimen/dp1"
            android:layout_gravity="center_horizontal" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/pullToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context=".modules.live.fragment.LiveHomeFragment">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/channel_gradiant"
            android:fitsSystemWindows="true"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsingToolbar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:collapsedTitleTextAppearance="@style/CollapsingToolbarLayoutCollapsedTextStyle"
                app:contentScrim="@color/black"
                app:expandedTitleMarginStart="@dimen/dp16"
                app:expandedTitleTextAppearance="@style/CollapsingToolbarLayoutExpandedTextStyle"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <TextView
                    android:id="@+id/txtLive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp60"
                    android:fontFamily="@font/readex_pro_semi_bold"
                    android:text="@string/live"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp22"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:layout_collapseMode="pin"
                    app:contentInsetStartWithNavigation="0dp"
                    android:contentInsetLeft="0dp"
                    android:contentInsetStart="0dp"
                    app:contentInsetLeft="0dp"
                    app:contentInsetStart="0dp"
                    app:layout_anchor="@id/collapsingToolbar"
                    app:theme="@style/ThemeOverlay.AppCompat.Dark"
                    app:title="">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:layout_marginStart="@dimen/dp16"
                        >
                        <TextView
                            android:id="@+id/title"
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/dp8"
                            android:gravity="bottom"
                            android:layout_gravity="center_vertical"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:visibility="invisible"
                            android:text="@string/live"
                            android:textSize="@dimen/sp22"
                            android:textColor="@color/dull_white"
                            android:fontFamily="@font/readex_pro_semi_bold" />

                    </LinearLayout>


                </androidx.appcompat.widget.Toolbar>


            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp15"
                android:paddingVertical="@dimen/dp15"
                app:items="@{viewModel.state.list}"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/txtLive"
                tools:itemCount="2"
                tools:listitem="@layout/item_page_filter" />


        </com.google.android.material.appbar.AppBarLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                >

                <FrameLayout
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_0dp">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/liveRecycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

                    <noice.app.views.ErrorView
                        android:id="@+id/errorView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:error_padding_bottom="@dimen/dp60" />

                    <LinearLayout
                        android:id="@+id/emptyView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/black"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp24"
                        android:paddingBottom="@dimen/dp25"
                        android:visibility="gone"
                        android:clickable="true"
                        android:focusable="true"
                        app:layout_constraintHeight_min="@dimen/dp200"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/toolbar"
                        app:layout_constraintBottom_toTopOf="@id/line">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:contentDescription="@string/app_name"
                            app:srcCompat="@drawable/empty_live" />

                        <TextView
                            android:id="@+id/emptyViewHeading"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp21"
                            android:fontFamily="sans-serif"
                            android:gravity="end"
                            android:text="@string/live_empty"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/emptyViewMessage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp30"
                            android:layout_marginTop="@dimen/dp8"
                            android:layout_marginEnd="@dimen/dp30"
                            android:fontFamily="sans-serif"
                            android:gravity="center"
                            android:text="@string/live_empty_message"
                            android:textColor="@color/white70"
                            android:textSize="@dimen/sp14" />
                    </LinearLayout>


                </FrameLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fabCreateRoom"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center"
            android:layout_marginBottom="@dimen/dp16"
            android:fontFamily="@font/readex_pro_semi_bold"
            android:text="@string/room_baru"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="@dimen/sp14"
            app:backgroundTint="@color/dull_yellow"
            android:paddingStart="@dimen/dp24"
            android:paddingEnd="@dimen/dp24"
            app:icon="@drawable/ic_create_room"
            app:iconSize="@dimen/dp15"
            app:iconPadding="@dimen/dp8"
            app:rippleColor="@color/white" />

        <FrameLayout
            android:id="@+id/homeContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:elevation="@dimen/dp6"/>
    </FrameLayout>
</layout>