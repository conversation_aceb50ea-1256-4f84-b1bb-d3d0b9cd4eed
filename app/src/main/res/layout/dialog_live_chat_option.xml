<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="noice.app.utils.Constants.LiveChatType" />

        <import type="noice.app.utils.Constants.LiveRoom.ClientRole" />

        <import type="noice.app.utils.PrefUtils" />

        <variable
            name="clientRole"
            type="String" />

        <variable
            name="isPinned"
            type="Boolean" />

        <variable
            name="message"
            type="noice.app.modules.live.model.message.LiveMessage" />

        <variable
            name="callBack"
            type="noice.app.utils.ReadMoreOption.Listener" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_rounded_24dp"
        android:paddingTop="@dimen/dp24">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/dp24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <include
                android:id="@+id/layoutComment"
                layout="@layout/item_live_chat_comment_option"
                app:callBack="@{callBack}"
                app:clientRole="@{clientRole}"
                app:message="@{message}" />

            <include
                android:id="@+id/layoutPinComment"
                layout="@layout/item_live_chat_action_option"
                app:drawable="@{@drawable/ic_pinned}"
                app:text="@{isPinned ? @string/update_pin_chat : @string/pin}"
                app:visibleIf="@{clientRole.equals(ClientRole.ROLE_HOST) &amp;&amp; !message.type.equals(LiveChatType.TYPE_GIFT) &amp;&amp; message.isActive}" />

            <include
                android:id="@+id/layoutRemovePinComment"
                layout="@layout/item_live_chat_action_option"
                app:drawable="@{@drawable/ic_close_live}"
                app:text="@{@string/unpin}"
                app:visibleIf="@{isPinned &amp;&amp; clientRole.equals(ClientRole.ROLE_HOST)}" />

            <include
                android:id="@+id/layoutReplyComment"
                layout="@layout/item_live_chat_action_option"
                app:drawable="@{@drawable/ic_user_profile_white}"
                app:text="@{@string/reply_comment_to(message.userName)}"
                app:visibleIf="@{!message.userId.equals(PrefUtils.INSTANCE.userDetails.id)}" />

            <include
                android:id="@+id/layoutReportComment"
                layout="@layout/item_live_chat_action_option"
                app:drawable="@{@drawable/ic_report_comment_outline}"
                app:text="@{@string/report_comment}"
                app:visibleIf="@{!message.userId.equals(PrefUtils.INSTANCE.userDetails.id)}" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progressIndicator"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="@color/dull_yellow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>