<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="@dimen/dp16"
    android:background="@drawable/background_blackish_grey_8dp">
    <TextView
        android:id="@+id/txtHeading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/udah_settin"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/sp18"
        android:textColor="@color/white"
        />
    <TextView
        android:id="@+id/txtDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/txtHeading"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/kalau_belum"
        android:layout_marginTop="@dimen/dp16"
        android:fontFamily="@font/roboto"
        android:textSize="@dimen/sp14"
        android:textColor="#8f8f8f"
        />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtDescription"
        android:layout_marginEnd="@dimen/dp8"
        android:layout_marginTop="@dimen/dp24"
        >
        <TextView
            android:id="@+id/txtKembali"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/kembali"
            android:textAllCaps="true"
            android:textColor="@color/dull_yellow"
            android:background="@drawable/custom_ripple"
            android:gravity="center_horizontal"
            android:textSize="@dimen/sp14"
            android:fontFamily="@font/roboto_bold"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp5"
            />
        <TextView
            android:id="@+id/txtLanjut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/lanjut"
            android:textAllCaps="true"
            android:textColor="@color/dull_yellow"
            android:background="@drawable/custom_ripple"
            android:gravity="center_horizontal"
            android:textSize="@dimen/sp14"
            android:layout_marginStart="@dimen/dp32"
            android:fontFamily="@font/roboto_bold"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp5"
            />


    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>