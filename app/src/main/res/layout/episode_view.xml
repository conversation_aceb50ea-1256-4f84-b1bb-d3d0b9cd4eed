<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/mainLayout"
    android:paddingTop="@dimen/dp16"
    android:paddingHorizontal="@dimen/dp16"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    android:background="@drawable/episode_view_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/vipBadge"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp22"
        app:srcCompat="@drawable/ic_vip_badge"
        android:adjustViewBounds="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/imgPurchased"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/purchased"
        android:paddingStart="@dimen/dp2"
        android:paddingEnd="@dimen/dp2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="@string/already_purchased"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/imageTitleLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dp12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vipBadge"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/imageCard"
            android:layout_width="@dimen/dp60"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp60"
            app:cardCornerRadius="@dimen/dp8"
            app:cardElevation="@dimen/size_0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imgEpisode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

<!--
            <ImageView
                android:id="@+id/premiumLock"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:contentDescription="@string/app_name"
                android:padding="@dimen/dp12"
                android:background="@color/black70"
                android:src="@drawable/ic_premium_lock"/>
-->

            <noice.app.views.EqualizerView
                android:id="@+id/equalizer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                app:barHeight="@dimen/dp40"
                android:background="@color/black30"
                android:visibility="gone"
                app:marginLeft="@dimen/dp2"
                app:marginRight="@dimen/dp2"/>
        </androidx.cardview.widget.CardView>


        <TextView
            android:id="@+id/txtDate"
            app:layout_constraintStart_toEndOf="@+id/imageCard"
            app:layout_constraintTop_toTopOf="@+id/imageCard"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            tools:text="33 Menit"
            android:textSize="@dimen/sp11"
            android:textColor="@color/white_600"
            android:lineSpacingExtra="@dimen/dp3"
            android:translationY="-1.37sp"
            android:gravity="top"
            android:fontFamily="@font/readex_pro"
            android:textStyle="normal"/>




        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            tools:text="Eps 8 : Melayat Hanya Buang-Buang Waktu (bersama Benidictivy)"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp14"
            android:textFontWeight="400"
            android:textStyle="normal"
            android:lineSpacingExtra="@dimen/dp3"
            android:ellipsize="end"
            android:maxLines="2"
            android:fontFamily="@font/readex_pro"
            app:layout_constraintTop_toBottomOf="@+id/txtDate"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/imageCard"/>

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp2"
            android:layout_marginTop="@dimen/dp12"
            android:progressDrawable="@drawable/custom_progress_drawable"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            tools:progress="70"
            tools:visibility="visible"
            android:visibility="gone"
            android:progressTint="@color/dull_yellow"
            android:progressBackgroundTint="@color/white"
            app:layout_constraintTop_toBottomOf="@+id/imageCard"
            app:layout_constraintStart_toStartOf="parent"/>

        <FrameLayout
            android:id="@+id/playButtonLayout"
            android:layout_marginTop="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@+id/progressBar"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp28"
            android:paddingStart="@dimen/dp24"
            android:paddingEnd="@dimen/dp24"
            android:paddingTop="@dimen/dp3"
            android:paddingBottom="@dimen/dp3"
            android:background="@drawable/episode_play_button_bg">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_gravity="center"
                >

                <FrameLayout
                    android:id="@+id/frame"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    >
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/musicButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:padding="@dimen/dp3"
                        android:visibility="visible"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name"
                        app:srcCompat="@drawable/ic_play_yellow_small"/>

                    <ProgressBar
                        android:id="@+id/loader"
                        android:layout_width="@dimen/dp20"
                        android:layout_height="@dimen/dp20"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginEnd="@dimen/dp4"
                        android:layout_gravity="center_vertical"
                        android:indeterminateTint="@color/dull_yellow"/>
                </FrameLayout>


                <TextView
                    android:id="@+id/label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp2"
                    android:fontFamily="@font/readex_pro_semi_bold"
                    android:gravity="center_horizontal|center_vertical"
                    android:includeFontPadding="false"
                    android:lineSpacingExtra="0sp"
                    android:text="@string/putar"
                    android:textColor="@color/dull_yellow"
                    android:textSize="@dimen/sp14"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toEndOf="@+id/frame"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>



        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgQue"
            android:layout_marginEnd="@dimen/dp12"
            app:layout_constraintTop_toTopOf="@+id/playButtonLayout"
            app:layout_constraintBottom_toBottomOf="@+id/playButtonLayout"
            android:background="@drawable/custom_ripple_bg"
            app:layout_constraintEnd_toStartOf="@id/downloadLayout"
            app:srcCompat="@drawable/ic_add_to_queque"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <FrameLayout
            android:id="@+id/downloadLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/custom_ripple_bg"
            app:layout_constraintTop_toTopOf="@+id/playButtonLayout"
            app:layout_constraintBottom_toBottomOf="@+id/playButtonLayout"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/downloadIcon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/custom_ripple_bg_circle"
                android:src="@drawable/ic_download_circle"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

            <ProgressBar
                android:id="@+id/downloadProgress"
                android:layout_width="@dimen/dp36"
                android:layout_height="@dimen/dp36"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible"
                android:indeterminate="false"
                android:max="100"
                android:progress="50"
                android:progressDrawable="@drawable/circular_progress_bar"
                android:background="@color/transparent"
                style="?android:attr/progressBarStyleHorizontal"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/priceLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/playButtonLayout"
            app:layout_constraintBottom_toBottomOf="@+id/playButtonLayout"
            android:visibility="gone"
            android:background="@drawable/border_2_dark_yellow"
            >
            <TextView
                android:id="@+id/premiumEpisodePrice"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp28"
                android:layout_gravity="center"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp16"
                android:paddingTop="@dimen/dp3"
                android:paddingBottom="@dimen/dp3"
                android:fontFamily="@font/readex_pro_semi_bold"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lineSpacingExtra="@dimen/sp3"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp13"
                android:letterSpacing="0"
                android:drawablePadding="@dimen/dp6"
                tools:text="buka 30 Coin"
                app:drawableStartCompat="@drawable/ic_cart_small" />

        </FrameLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:layout_marginTop="@dimen/dp16"
            android:background="@color/black500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/playButtonLayout"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>