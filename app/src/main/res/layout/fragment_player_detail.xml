<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    tools:context=".exoplayer.PlayerDetailFragment">

    <androidx.cardview.widget.CardView
        android:id="@+id/squareCardView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginStart="@dimen/dp50"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp50"
        android:layout_marginBottom="@dimen/dp24"
        android:visibility="gone"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toTopOf="@id/frameAdView"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/audioBookLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/coverBackgroundImage"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                android:contentDescription="@string/app_name"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                android:background="@color/black60"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/size_0dp"
                android:layout_gravity="center"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="0.85"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/coverImageAudioBook"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_audiobook_default" />
            </androidx.cardview.widget.CardView>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_thumb_default"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/vipBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:contentDescription="@string/original"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_vip_badge" />
    </androidx.cardview.widget.CardView>


    <androidx.media3.ui.PlayerView
        android:id="@+id/videoPlayerView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp16"
        android:visibility="gone"
        app:animation_enabled="false"
        app:auto_show="false"
        app:controller_layout_id="@layout/video_controller_view"
        app:layout_constraintBottom_toTopOf="@id/frameAdView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:show_timeout="2000" />

    <FrameLayout
        android:id="@+id/castView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:background="@color/black"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toTopOf="@id/frameAdView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <noice.app.views.EqualizerView
                android:id="@+id/equalizer"
                android:layout_width="@dimen/dp30"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/dp3"
                android:background="@color/black30"
                android:visibility="visible"
                app:barHeight="@dimen/dp18"
                app:marginLeft="@dimen/dp2"
                app:marginRight="@dimen/dp2" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:fontFamily="@font/readex_pro"
                android:gravity="bottom"
                android:text="@string/cast_description"
                android:textColor="@color/white"
                android:textSize="@dimen/sp16" />
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/frameAdView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp20"
        app:layout_constraintBottom_toTopOf="@id/unlock_content_banner"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/unlock_content_banner"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp24"
        android:layout_marginBottom="@dimen/dp20"
        android:foreground="@drawable/custom_ripple_bg"
        android:clickable="true"
        android:focusable="true"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="@dimen/dp24"
        app:cardElevation="@dimen/size_0dp"
        app:layout_constraintBottom_toTopOf="@id/titlePlayer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp9">

            <ImageView
                android:layout_width="@dimen/dp18"
                android:layout_height="@dimen/dp18"
                android:src="@drawable/ic_cart"
                app:tint="@color/black" />

            <TextView
                android:id="@+id/tv_unlock_content_banner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:fontFamily="@font/readex_pro_semi_bold"
                android:text="Buka Konten"
                android:textColor="@color/black"
                android:textSize="@dimen/sp14"/>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/subscribeCardView"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_marginEnd="@dimen/dp24"
        app:cardBackgroundColor="@color/transparent"
        app:layout_constraintBottom_toBottomOf="@id/subtitleLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/titlePlayer">

        <ImageView
            android:id="@+id/subscribeImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:contentDescription="@string/subscribe"
            android:src="@drawable/ic_subscribe"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/subscribeView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/circuler_white_ripple" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/titlePlayer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp8"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:fadingEdge="horizontal"
        android:fontFamily="@font/readex_pro"
        android:marqueeRepeatLimit="marquee_forever"
        android:scrollHorizontally="true"
        android:textColor="@color/dull_white2"
        android:textSize="@dimen/sp16"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/subtitleLayout"
        app:layout_constraintEnd_toStartOf="@id/likeIcon"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Eps 9 : Berdoa Tidak Ada Gunanya" />

    <ImageView
        android:id="@+id/likeIcon"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginEnd="@dimen/dp10"
        android:background="@drawable/custom_ripple_bg_circle"
        android:contentDescription="@string/like"
        android:padding="@dimen/dp7"
        android:src="@drawable/like_selector_white"
        app:layout_constraintBottom_toBottomOf="@id/subscribeCardView"
        app:layout_constraintEnd_toStartOf="@id/subscribeCardView"
        app:layout_constraintTop_toTopOf="@id/subscribeCardView" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/subtitleLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp20"
        app:layout_constraintEnd_toStartOf="@id/likeIcon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/txtCurrent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp8"
            android:ellipsize="marquee"
            android:fadingEdge="horizontal"
            android:fontFamily="@font/readex_pro"
            android:gravity="center"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp12"
            android:textStyle="normal"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_music"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toTopOf="@id/channelInfoLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="jshkdhkas hgjahs jasg ahs "
            tools:visibility="gone" />

        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/channelInfoLayout"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp20"
            app:alignItems="center"
            app:flexWrap="wrap"
            app:justifyContent="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:maxLine="1">

            <TextView
                android:id="@+id/txtChannelPlayer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.7"
                android:background="@drawable/custom_ripple_bg"
                android:ellipsize="marquee"
                android:fadingEdge="horizontal"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:lineSpacingExtra="5.6sp"
                android:marqueeRepeatLimit="marquee_forever"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="normal"
                tools:text="Oleh Dummy Content" />

            <TextView
                android:id="@+id/followCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:fontFamily="@font/readex_pro"
                android:lineSpacingExtra="5.6sp"
                android:textColor="@color/neutral_grey"
                android:textSize="@dimen/sp12"
                android:textStyle="normal"
                android:visibility="gone"
                app:layout_flexShrink="0"
                tools:text="13k" />

            <TextView
                android:id="@+id/followBtnPlayer"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp28"
                android:layout_marginStart="@dimen/dp8"
                android:background="@drawable/background_white_20_percent_14dp"
                android:fontFamily="@font/readex_pro"
                android:gravity="center"
                android:lineSpacingExtra="5.6sp"
                android:paddingStart="@dimen/dp10"
                android:paddingEnd="@dimen/dp10"
                android:text="@string/subscribe"
                android:textColor="@color/follow_text_color"
                android:textSize="@dimen/sp14"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_flexShrink="0" />
        </com.google.android.flexbox.FlexboxLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/errorLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp203"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginBottom="@dimen/dp24"
        android:background="@color/black700"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="@dimen/dp16"
        android:paddingTop="@dimen/dp24"
        android:paddingEnd="@dimen/dp16"
        android:paddingBottom="@dimen/dp24"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/titlePlayer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/errorTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readex_pro_semi_bold"
            android:gravity="center"
            android:text="@string/no_internet"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp14"
            app:layout_constraintBottom_toTopOf="@id/errorSubTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/errorSubTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/sp12"
            android:fontFamily="@font/readex_pro"
            android:gravity="center"
            android:text="@string/make_sure_you_are_connected_to_stable_network"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp12"
            app:layout_constraintBottom_toTopOf="@id/loader"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/errorTitle" />

        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginTop="@dimen/sp12"
            android:indeterminateTint="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/reloading"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/errorSubTitle" />

        <TextView
            android:id="@+id/reloading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp6"
            android:fontFamily="@font/readex_pro"
            android:gravity="center"
            android:text="@string/reloading"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp12"
            app:layout_constraintBottom_toBottomOf="@id/loader"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/loader"
            app:layout_constraintTop_toTopOf="@id/loader" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>