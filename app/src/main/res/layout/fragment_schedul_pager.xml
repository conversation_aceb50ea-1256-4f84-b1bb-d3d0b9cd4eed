<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black700">

    <noice.app.views.ToolbarView
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:id="@+id/toolbar"
        android:elevation="0dp"
        app:rightIcon="@drawable/ic_share_large"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>


    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/black700"
        app:tabTextAppearance="@style/NoiceTabLayoutStyle"
        app:tabGravity="start"
        app:tabMode="scrollable"
        app:tabBackground="@color/black700"
        app:tabIndicatorColor="@color/dull_yellow"
        app:tabTextColor="@color/white50"
        app:tabSelectedTextColor="@color/white"/>

    <noice.app.views.NonSwipeableViewPager
        android:id="@+id/viewPager"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>