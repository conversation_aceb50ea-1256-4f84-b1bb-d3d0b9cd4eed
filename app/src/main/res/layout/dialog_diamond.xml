<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="noice.app.utils.HandleValues" />

        <variable
            name="isReceiver"
            type="Boolean" />

        <variable
            name="viewState"
            type="noice.app.modules.live.model.gift.history.viewstate.GiftHistoryViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cardToolTip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:elevation="@dimen/dp2"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/viewCardTop"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp22"
                android:src="@drawable/ic_square_tilted"
                android:tint="@color/white"
                app:layout_constraintBottom_toBottomOf="@+id/card_view_tooltip"
                app:layout_constraintEnd_toEndOf="@+id/card_view_tooltip"
                app:layout_constraintStart_toStartOf="@+id/card_view_tooltip"
                app:layout_constraintTop_toBottomOf="@id/card_view_tooltip" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_view_tooltip"
                android:layout_width="@dimen/dp260"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="@dimen/dp8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/innerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp16">

                    <TextView
                        android:id="@+id/txtMessage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/readex_pro_regular"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:text="@string/coins_will_be_converted_to_diamond"
                        android:textAlignment="textStart"
                        android:textColor="@color/neutral_60"
                        android:textSize="@dimen/sp13"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/positiveBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp28"
                        android:layout_marginTop="@dimen/dp10"
                        android:minWidth="@dimen/dp100"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/okay"
                        android:textAlignment="center"
                        android:textAppearance="@style/chipTextSemiBoldStyle"
                        android:textColor="@color/black"
                        android:textSize="@dimen/sp14"
                        app:chipBackgroundColor="@color/dull_yellow"
                        app:chipEndPadding="@dimen/dp8"
                        app:chipMinTouchTargetSize="@dimen/size_0dp"
                        app:chipStartPadding="@dimen/dp8"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txtMessage" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bottom_sheet_rounded_black700_24dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <FrameLayout
                android:id="@+id/shadow_tooltip"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/bottom_sheet_rounded_black700_24dp"
                android:backgroundTint="@color/black50"
                android:elevation="@dimen/dp1"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <View
                android:id="@+id/line"
                android:layout_width="@dimen/dp56"
                android:layout_height="@dimen/dp4"
                android:layout_marginTop="@dimen/dp16"
                android:background="@color/color_sheet_drag_bar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:text="@string/diamond"
                android:textColor="@color/white"
                android:textSize="@dimen/sp18"
                app:fontFamily="@font/roboto_bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgInfo"
                android:layout_width="@dimen/dp20"
                android:layout_height="@dimen/dp20"
                android:layout_marginEnd="@dimen/dp26"
                app:layout_constraintBottom_toBottomOf="@+id/txtTitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/txtTitle"
                app:srcCompat="@drawable/ic_information_white" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="@dimen/dp2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txtTitle">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/shapeableImageView"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/size_0dp"
                    android:scaleType="centerCrop"
                    app:srcCompat="@drawable/ic_background_diamond"
                    app:layout_constraintBottom_toBottomOf="@+id/consCard"
                    app:layout_constraintEnd_toEndOf="@+id/consCard"
                    app:layout_constraintStart_toStartOf="@+id/consCard"
                    app:layout_constraintTop_toTopOf="@+id/consCard"
                    app:shapeAppearanceOverlay="@style/roundedCorners"
                    app:strokeColor="@android:color/transparent"
                    app:strokeWidth="@dimen/dp4" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/consCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp24"
                    android:layout_marginEnd="@dimen/dp16"
                    android:paddingTop="@dimen/dp16"
                    android:paddingBottom="@dimen/dp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">


                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/txtTypeText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp24"
                        android:text="@{isReceiver ? @string/number_of_diamonds : @string/host_diamonds}"
                        android:textColor="@color/white400"
                        android:textSize="@dimen/sp12"
                        app:fontFamily="@font/roboto_med"
                        app:layout_constraintBottom_toTopOf="@+id/txtDiamonds"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="@string/number_of_diamonds" />

                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/txtDiamonds"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp4"
                        android:text="@{HandleValues.convertNumberToLocaleIndonesia(isReceiver ? viewState.receiverWallet.diamondBalance.toString() : viewState.receiverWallet.liveRoomDiamondBalance.toString())}"
                        android:textColor="@color/white400"
                        android:textSize="@dimen/sp24"
                        app:fontFamily="@font/roboto_bold"
                        app:layout_constraintBottom_toTopOf="@+id/txtIncreasingInfo"
                        app:layout_constraintStart_toStartOf="@+id/txtTypeText"
                        app:layout_constraintTop_toBottomOf="@+id/txtTypeText"
                        tools:text="1.480.400" />


                    <com.google.android.material.textview.MaterialTextView
                        android:id="@+id/txtIncreasingInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp4"
                        android:background="@drawable/background_white_8_per_30dp"
                        android:drawableEnd="@drawable/ic_information_white_12dp"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:paddingStart="@dimen/dp10"
                        android:paddingTop="@dimen/dp6"
                        android:paddingEnd="@dimen/dp10"
                        android:paddingBottom="@dimen/dp6"
                        android:text="@{HandleValues.convertNumberToLocaleIndonesia(viewState.receiverWallet.liveRoomDiamondBalance.toString())}"
                        android:textAlignment="center"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp14"
                        app:drawableTint="@color/dull_yellow"
                        app:fontFamily="@font/roboto_bold"
                        app:layout_constraintStart_toStartOf="@+id/txtDiamonds"
                        app:layout_constraintTop_toBottomOf="@+id/txtDiamonds"
                        app:visibleIf="@{isReceiver &amp;&amp; viewState.receiverWallet.diamondBalance > 0}"
                        tools:text="1.2000"
                        tools:visibility="visible" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/imgDiamond"
                        android:layout_width="@dimen/dp48"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp28"
                        android:adjustViewBounds="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_diamond" />

                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_noice_maker_badge"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp44"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp25"
                android:layout_marginEnd="@dimen/dp16"
                android:background="@drawable/background_blackish_grey_22dp"
                app:layout_constraintEnd_toEndOf="@+id/constraintLayout"
                app:layout_constraintStart_toStartOf="@+id/constraintLayout"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout"
                app:visibleIf="@{isReceiver}"
                tools:visibility="visible">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/txtBottomPre"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp24"
                    android:includeFontPadding="false"
                    android:text="@string/see_more_details_in"
                    android:textColor="@color/white400"
                    android:textSize="@dimen/sp12"
                    app:fontFamily="@font/roboto"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/imgLogo"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgLogo"
                    android:layout_width="@dimen/dp8"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    app:layout_constraintBottom_toBottomOf="@+id/txtBottomPre"
                    app:layout_constraintEnd_toStartOf="@+id/txtBottomPost"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/txtBottomPre"
                    app:layout_constraintTop_toTopOf="@+id/txtBottomPre"
                    app:srcCompat="@drawable/ic_noice_home_logo_yellow" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/txtBottomPost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:includeFontPadding="false"
                    android:text="@string/creator_studio"
                    android:textColor="@color/white400"
                    android:textSize="@dimen/sp12"
                    app:fontFamily="@font/roboto"
                    app:layout_constraintBaseline_toBaselineOf="@+id/txtBottomPre"
                    app:layout_constraintEnd_toStartOf="@+id/imgArrowForward"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/imgLogo" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgArrowForward"
                    android:layout_width="@dimen/dp12"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="16dp"
                    android:rotation="180"
                    app:layout_constraintBottom_toBottomOf="@+id/txtBottomPost"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/txtBottomPost"
                    app:layout_constraintTop_toTopOf="@+id/txtBottomPost"
                    app:srcCompat="@drawable/ic_left_arrow_white" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp32"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layout_noice_maker_badge" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/viewCardTop"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:layout_marginTop="@dimen/dp92"
            app:layout_constraintTop_toTopOf="@+id/constraintLayout2" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>