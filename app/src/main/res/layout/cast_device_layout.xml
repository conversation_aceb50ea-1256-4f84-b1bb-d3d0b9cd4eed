<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/txtDevice"
        android:paddingStart="@dimen/dp16"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp56"
        android:drawableStart="@drawable/ic_chrom_disconnect"
        tools:text="Chromecast"
        android:drawablePadding="@dimen/dp21"
        android:textSize="@dimen/sp14"
        android:gravity="center_vertical"
        android:fontFamily="@font/readex_pro"
        android:textColor="@color/white300"
        />

</FrameLayout>