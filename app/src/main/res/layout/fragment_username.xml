<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:padding="@dimen/dp16"
    tools:context=".modules.onboarding.fragments.UsernameFragment">

    <ImageView
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        android:layout_marginTop="@dimen/dp28"
        android:contentDescription="@string/app_name"
        android:src="@drawable/username_info"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textSize="@dimen/sp24"
        android:textColor="@color/white"
        android:lineSpacingExtra="0sp"
        android:text="@string/enter_username"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp16"
        android:textColor="@color/black900"
        android:lineSpacingExtra="@dimen/sp6"
        android:letterSpacing="0.01"
        android:text="@string/username_disclaimer"/>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:orientation="horizontal">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/usernameLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:errorEnabled="true"
            app:errorTextColor="@color/pinkish_red"
            app:errorIconTint="@color/pinkish_red"
            app:endIconMode="custom"
            app:endIconDrawable="@drawable/green_tick_circle"
            app:endIconTint="@color/parrot_green"
            android:hint="@string/username_"
            android:textColorHint="@color/white"
            app:helperTextTextColor="@color/neutral_grey"
            app:hintTextColor="@color/neutral_90"
            app:placeholderText="@string/create_a_unique_username"
            app:placeholderTextColor="@color/greyText1"
            app:boxStrokeColor="@color/white"
            app:boxBackgroundColor="@color/black600">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/username"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:inputType="textVisiblePassword"
                android:maxLength="15"
                android:fontFamily="@font/readex_pro"
                android:textSize="@dimen/sp14"
                android:textColor="@color/neutral_90"
                android:lineSpacingExtra="0sp"
                android:letterSpacing="0.01" />
        </com.google.android.material.textfield.TextInputLayout>
        
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginEnd="@dimen/dp13"
            android:layout_marginTop="@dimen/dp17"
            android:layout_gravity="end"
            android:indeterminateTint="@color/white"
            android:visibility="gone"/>
    </FrameLayout>

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/acceptTncCheckBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/size_0dp"
        android:gravity="top"
        android:buttonTint="@color/selector_check_box_yellow_black"
        android:text="@string/accept_tnc"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0.01"
        android:fontFamily="@font/readex_pro"
        android:textColor="@color/white500"
        android:lineSpacingExtra="@dimen/sp3"
        android:theme="@style/MyCheckboxTheme"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/subTitle"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/proceed"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp52"
        android:layout_marginTop="@dimen/dp24"
        android:insetTop="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:gravity="center"
        app:cornerRadius="@dimen/dp14"
        android:backgroundTint="@color/dull_yellow"
        android:text="@string/lanjut"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        android:lineSpacingExtra="0sp"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"/>
</LinearLayout>