<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp8"
    android:background="@color/black700"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/bannerRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"/>

    <me.relex.circleindicator.CircleIndicator2
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp20"
        android:layout_gravity="bottom|center"
        app:ci_height="@dimen/dp6"
        app:ci_width="@dimen/dp6"
        app:ci_margin="@dimen/dp2"
        app:ci_animator="@anim/scale_with_alpha_custom"
        app:ci_drawable="@drawable/circle_indicator_selected"
        app:ci_drawable_unselected="@drawable/circle_indicator_unselected"/>
</LinearLayout>