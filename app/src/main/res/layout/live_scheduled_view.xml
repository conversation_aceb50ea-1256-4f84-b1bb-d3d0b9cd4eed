<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/dp14"
    android:background="@color/black700"
    android:id="@+id/scheduledRoom">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgUser"
        android:layout_width="@dimen/dp44"
        android:layout_height="@dimen/dp44"
        android:background="@drawable/bg_skeleton_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/roomName"
        app:layout_constraintBottom_toBottomOf="@id/roomName" />

    <TextView
        android:id="@+id/txtDate"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:paddingStart="@dimen/dp5"
        android:paddingEnd="@dimen/dp4"
        android:paddingTop="@dimen/dp2"
        android:paddingBottom="@dimen/dp2"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="3sp"
        android:drawablePadding="3dp"
        android:background="@drawable/background_blackish_grey_4dp"
        app:drawableStartCompat="@drawable/ic_schedule"
        tools:text="18 May - 3:34"
        app:layout_constraintStart_toEndOf="@id/imgUser"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/imgRecording"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:visibility="gone"
        android:paddingStart="@dimen/dp2"
        android:paddingEnd="2dp"
        android:layout_marginStart="@dimen/dp4"
        tools:visibility="visible"
        android:background="@drawable/background_blackish_grey_4dp"
        app:layout_constraintStart_toEndOf="@id/txtDate"
        app:layout_constraintTop_toTopOf="@id/txtDate"
        app:layout_constraintBottom_toBottomOf="@id/txtDate">

        <ImageView
            android:id="@+id/imgDot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp2"
            android:paddingEnd="@dimen/dp2"
            app:srcCompat="@drawable/ic_red_dot"
            android:contentDescription="@string/app_name"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/rec"
            android:paddingEnd="@dimen/dp2"
            android:paddingTop="@dimen/dp2"
            android:paddingBottom="@dimen/dp2"
            android:textColor="@color/white"
            android:textAllCaps="true"
            android:textSize="@dimen/sp12"
            android:layout_marginStart="@dimen/dp3"
            android:fontFamily="@font/roboto_bold" />

    </LinearLayout>

    <TextView
        android:id="@+id/roomName"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp13"
        android:lines="1"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="3sp"
        tools:text="Suraj's Android Live Room"
        app:layout_constraintStart_toStartOf="@id/txtDate"
        app:layout_constraintEnd_toStartOf="@id/notifyMe"
        app:layout_constraintTop_toBottomOf="@id/txtDate" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/notifyMe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp14"
        android:paddingEnd="@dimen/dp14"
        android:paddingTop="@dimen/dp5"
        android:paddingBottom="@dimen/dp5"
        android:background="@drawable/pill_blackish_grey_100_dp"
        android:foreground="?selectableItemBackground"
        android:src="@drawable/ic_notification_16x16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/roomName"
        app:layout_constraintBottom_toBottomOf="@id/roomName" />

    <TextView
        android:id="@+id/hostName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:drawablePadding="@dimen/dp4"
        android:lines="1"
        android:ellipsize="end"
        android:textColor="@color/light_blue"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="3sp"
        tools:text="Suraj Pal"
        app:drawableStartCompat="@drawable/ic_host"
        app:layout_constraintStart_toStartOf="@id/roomName"
        app:layout_constraintTop_toBottomOf="@id/roomName"/>

    <TextView
        android:id="@+id/txtHost"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:paddingStart="@dimen/dp9"
        android:paddingEnd="@dimen/dp9"
        android:paddingTop="@dimen/dp2"
        android:paddingBottom="@dimen/dp2"
        android:background="@drawable/background_blackish_grey_22dp"
        android:textColor="@color/white"
        android:textSize="@dimen/sp10"
        android:textStyle="normal"
        android:fontFamily="sans-serif"
        android:text="@string/host"
        app:layout_constraintStart_toEndOf="@id/hostName"
        app:layout_constraintTop_toTopOf="@id/hostName"
        app:layout_constraintBottom_toBottomOf="@id/hostName"/>

</androidx.constraintlayout.widget.ConstraintLayout>