<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewState"
            type="noice.app.modules.live.model.gift.history.viewstate.GiftHistoryViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_rounded_black700_24dp">

        <View
            android:id="@+id/line"
            android:layout_width="@dimen/dp56"
            android:layout_height="@dimen/dp4"
            android:layout_marginTop="@dimen/dp16"
            android:background="@color/color_sheet_drag_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            android:text="@string/suporter_kamu_nih"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp453"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp24"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:listitem="@layout/item_gift_history" />

            <ProgressBar
                android:id="@+id/progressbar"
                android:layout_width="@dimen/dp25"
                android:layout_height="@dimen/dp25"
                android:indeterminateTint="@color/dull_yellow"
                android:visibility="@{viewState.loading &amp;&amp; viewState.items.empty ? View.VISIBLE: View.GONE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp1"
                android:indeterminate="true"
                android:visibility="@{viewState.loading &amp;&amp; !viewState.items.empty ? View.VISIBLE: View.GONE}"
                app:indicatorColor="@color/dull_yellow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <include
                android:id="@+id/layout_empty"
                layout="@layout/layout_gift_empty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:drawable="@{@drawable/ic_gift_history_empty}"
                app:label="@{@string/text_gift_history_empty}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/recyclerview"
                app:visible="@{viewState.items.empty &amp;&amp; !viewState.loading}"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>