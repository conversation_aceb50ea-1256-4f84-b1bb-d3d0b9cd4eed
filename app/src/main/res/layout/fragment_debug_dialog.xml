<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".modules.dashboard.home.fragment.DebugDialog">

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp16"
        android:textColor="@color/white"
        android:gravity="center"
        android:textSize="@dimen/sp18"
        android:lineSpacingExtra="@dimen/sp4"
        android:text="Debug Dialog"
        android:textStyle="bold"
        android:fontFamily="sans-serif"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/black"
            android:padding="@dimen/dp16">

            <TextView
                android:id="@+id/logBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Generate Log File"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/agoraLogBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Share Agora Log File"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/deviceIdBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Copy Device Id"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/userIdBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Copy User Id"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/copyFcmToken"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Copy FCM token"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/copyToken"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Copy Api token"
                android:fontFamily="sans-serif"/>

            <EditText
                android:id="@+id/addToken"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:inputType="textNoSuggestions"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:textColorHint="@color/white50"
                android:hint="Add Token"
                android:fontFamily="sans-serif"
                android:importantForAutofill="no" />

            <TextView
                android:id="@+id/debugQueue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Debug Queue"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/liveStreamStateBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Live Stream State"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/clearQueue"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Clear Queue"
                android:fontFamily="sans-serif"/>

            <TextView
                android:id="@+id/sendExitInfos"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp8"
                android:textColor="@color/white"
                android:textSize="@dimen/sp15"
                android:text="Send Exit Infos"
                android:fontFamily="sans-serif"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>