<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp100"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/dp97"
        android:layout_height="@dimen/dp18"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v1"/>

    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp18"
        android:layout_marginTop="@dimen/dp6"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v2"/>

    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp18"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v3"/>

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp51"
        android:layout_height="@dimen/dp20"
        android:layout_marginTop="@dimen/dp21"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v4"/>

    <View
        android:id="@+id/v6"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp156"
        android:layout_marginTop="@dimen/dp21"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v5"
        app:layout_constraintEnd_toStartOf="@id/v10"/>

    <View
        android:id="@+id/v7"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v6"
        app:layout_constraintEnd_toEndOf="@id/v6"/>

    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v7"
        app:layout_constraintEnd_toEndOf="@id/v6"/>

    <View
        android:id="@+id/v9"
        android:layout_width="@dimen/dp54"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v8"/>

    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp156"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v6"
        app:layout_constraintStart_toEndOf="@id/v6"/>

    <View
        android:id="@+id/v11"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v10"
        app:layout_constraintEnd_toEndOf="@id/v10"/>

    <View
        android:id="@+id/v12"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v11"
        app:layout_constraintEnd_toEndOf="@id/v10"/>

    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/dp54"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v12"/>

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp156"
        android:layout_marginTop="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v9"
        app:layout_constraintEnd_toStartOf="@id/v18"/>

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v14"
        app:layout_constraintEnd_toEndOf="@id/v6"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v15"
        app:layout_constraintEnd_toEndOf="@id/v6"/>

    <View
        android:id="@+id/v17"
        android:layout_width="@dimen/dp54"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v16"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp156"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v14"
        app:layout_constraintStart_toEndOf="@id/v14"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v18"
        app:layout_constraintEnd_toEndOf="@id/v10"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v19"
        app:layout_constraintEnd_toEndOf="@id/v10"/>

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/dp54"
        android:layout_height="@dimen/dp16"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v20"/>
</androidx.constraintlayout.widget.ConstraintLayout>