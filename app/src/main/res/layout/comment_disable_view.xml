<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/commentDisable"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dp40"
    android:paddingBottom="@dimen/dp40"
    android:background="@color/black">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgComment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_live_chat_disabled"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/txtTitle"/>

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal|center_vertical"
        app:layout_constraintTop_toBottomOf="@+id/imgComment"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/white"
        android:fontFamily="@font/roboto_bold"
        android:textSize="@dimen/sp14"
        android:text="@string/comment_disable_title"
        android:layout_marginTop="@dimen/dp8"
        app:layout_constraintBottom_toTopOf="@id/txtDescription"/>

    <TextView
        android:id="@+id/txtDescription"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal|center_vertical"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textColor="@color/white70"
        android:fontFamily="@font/roboto"
        android:textSize="@dimen/sp14"
        android:text="@string/comment_disable_description"
        android:layout_marginTop="@dimen/dp8"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>