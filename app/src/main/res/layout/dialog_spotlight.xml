<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view_tooltip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/home_search"
    app:cardCornerRadius="@dimen/dp8">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/innerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp20">

        <TextView
            android:id="@+id/txtMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="@dimen/sp3"
            android:text="@string/msg_tooltip_home"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/negativeBtn"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp42"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/background_blackish_grey_4dp"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:text="@string/skip"
            android:visibility="visible"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/positiveBtn"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtMessage" />

        <TextView
            android:id="@+id/positiveBtn"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp42"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/background_blackish_ship_grey_4dp"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:text="@string/okay"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/negativeBtn"
            app:layout_constraintTop_toBottomOf="@+id/txtMessage"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>