<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="label"
            type="String" />

        <variable
            name="drawable"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="visible"
            type="Boolean" />
    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/layout_sheet_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:visibleIf="@{visible}">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp32"
            android:layout_marginEnd="@dimen/dp32"
            android:adjustViewBounds="true"
            android:src="@{drawable}"
            tools:src="@drawable/ic_gift_history_empty" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp32"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp32"
            android:text="@{label}"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            tools:text="@string/text_gift_history_empty" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>