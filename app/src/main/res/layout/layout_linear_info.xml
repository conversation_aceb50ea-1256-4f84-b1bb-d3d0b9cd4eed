<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="infoState"
            type="noice.app.views.LiveSegment.UIState.InfoState" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp18"
        android:background="@drawable/background_blur_24dp_round"
        android:paddingStart="@dimen/dp4"
        android:paddingEnd="@dimen/dp4"
        android:paddingVertical="0dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageview"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/size_0dp"
            app:animate="@{infoState.animate}"
            app:animationType="@{infoState.type}"
            app:drawableRes="@{infoState.drawableRes}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_max="@dimen/dp10"
            tools:src="@drawable/live_equalizer" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtPills"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp3"
            android:fontFamily="@font/readix_pro_bold"
            android:text="@{infoState.text}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            android:paddingVertical="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageview"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/size_0dp"
            tools:text="196" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>