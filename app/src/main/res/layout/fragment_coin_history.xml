<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700"
    tools:context=".modules.coins.fragments.CoinHistoryFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:rightIcon="@drawable/ic_information_grey"
        app:rightIconVisibility="visible"
        app:toolbarBackIcon="@drawable/ic_left_angle_white"
        app:toolbarViewTitle="Coin"
        app:toolbarBackgroundColor="@color/black700"
        app:titleTextSize="@dimen/sp18"/>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/pullToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/headerLayout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp105"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ic_banner_coin">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/coinsHeaderLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/size_0dp"
                    android:paddingEnd="@dimen/dp24"
                    android:paddingStart="@dimen/size_0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <TextView
                        android:id="@+id/noOfCoinTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp24"
                        android:text="@string/number_of_coins"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:fontFamily="sans-serif"
                        app:layout_constraintVertical_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/coins"/>

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/coinImg"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp24"
                        android:layout_marginEnd="@dimen/dp8"
                        android:adjustViewBounds="true"
                        app:lottie_rawRes="@raw/animation_coin_topup"
                        app:lottie_autoPlay="false"
                        app:lottie_loop="false"
                        app:layout_constraintEnd_toStartOf="@id/coins"
                        app:layout_constraintTop_toTopOf="@id/coins"
                        app:layout_constraintBottom_toBottomOf="@id/coins"/>

                    <ImageView
                        android:id="@+id/coinImgForToolTip"
                        android:layout_width="@dimen/dp24"
                        android:layout_height="@dimen/dp24"
                        android:visibility="gone"
                        android:src="@drawable/ic_noice_coin_24"
                        android:contentDescription="@string/app_name"
                        android:adjustViewBounds="true"
                        app:layout_constraintEnd_toEndOf="@id/coinImg"
                        app:layout_constraintBottom_toBottomOf="@id/coinImg"
                        app:layout_constraintTop_toTopOf="@id/coinImg"/>

                    <TextView
                        android:id="@+id/coins"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp32"
                        android:layout_marginTop="@dimen/dp11"
                        android:text="@string/zero"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp24"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif"
                        android:lineSpacingExtra="@dimen/sp1"
                        app:layout_constraintStart_toStartOf="@id/noOfCoinTitle"
                        app:layout_constraintTop_toBottomOf="@id/noOfCoinTitle"
                        app:layout_constraintBottom_toBottomOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/topUpBtn"
                    android:layout_width="@dimen/dp92"
                    android:layout_height="@dimen/dp32"
                    android:layout_marginEnd="@dimen/dp24"
                    android:background="@drawable/background_white_round_4dp"
                    android:foreground="?android:attr/selectableItemBackground"
                    android:gravity="center"
                    android:text="@string/top_up"
                    android:textColor="@color/black600"
                    android:textSize="@dimen/sp12"
                    android:textStyle="bold"
                    android:textAllCaps="true"
                    android:letterSpacing="0.2"
                    android:lineSpacingExtra="@dimen/sp1"
                    android:fontFamily="sans-serif"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/onBoardingLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp16"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/historyTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    android:layout_marginStart="@dimen/dp16"
                    android:text="@string/history"
                    android:textSize="@dimen/sp18"
                    android:textColor="@color/dull_white"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:lineSpacingExtra="@dimen/sp4"/>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/emptyView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp24"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_empty_coins"
                            android:adjustViewBounds="true"
                            android:contentDescription="@string/app_name"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp16"
                            android:layout_gravity="center"
                            android:text="@string/no_transaction_yet"
                            android:textSize="@dimen/sp18"
                            android:textColor="@color/white"
                            android:textStyle="bold"
                            android:fontFamily="sans-serif"/>

                        <TextView
                            android:id="@+id/emptyViewText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp16"
                            android:layout_marginStart="@dimen/dp24"
                            android:layout_marginEnd="@dimen/dp24"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/empty_coin_history_text"
                            android:textSize="@dimen/sp16"
                            android:textColor="@color/white80"
                            android:fontFamily="sans-serif"/>

                        <TextView
                            android:id="@+id/topUpBtnEmptyView"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp48"
                            android:layout_gravity="center"
                            android:layout_margin="@dimen/dp16"
                            android:background="@drawable/background_yellow_radius_4dp"
                            android:gravity="center"
                            android:text="@string/top_up"
                            android:textColor="@color/black600"
                            android:textSize="@dimen/sp12"
                            android:textStyle="bold"
                            android:textAllCaps="true"
                            android:letterSpacing="0.2"
                            android:lineSpacingExtra="@dimen/sp1"
                            android:fontFamily="sans-serif"/>
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/coinHistoryRecycler"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:orientation="vertical"/>

                    <noice.app.views.ErrorView
                        android:id="@+id/errorView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </FrameLayout>
            </LinearLayout>
        </LinearLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout>