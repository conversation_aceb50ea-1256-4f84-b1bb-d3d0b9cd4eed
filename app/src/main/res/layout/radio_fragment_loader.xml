<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/thumbnail_s"
        android:layout_width="@dimen/dp140"
        android:layout_height="@dimen/dp140"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@color/skeleton_color"
        />

    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/bg_skeleton_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/thumbnail_s"/>


    <View
        android:id="@+id/episodeName"
        android:layout_width="@dimen/dp190"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@color/skeleton_color"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v2"/>
    <View
        android:id="@+id/timeLeft"
        android:layout_width="@dimen/dp300"
        android:layout_height="@dimen/dp20"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/skeleton_color"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/episodeName"
        app:layout_constraintEnd_toEndOf="parent"/>
    <LinearLayout
        android:id="@+id/rvGenre"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:visibility="visible"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/timeLeft"
        >
        <View
            android:background="@color/skeleton_color"
            android:layout_width="@dimen/dp50"
            android:layout_height="@dimen/dp20"/>
        <View
            android:background="@color/skeleton_color"
            android:layout_width="@dimen/dp50"
            android:layout_height="@dimen/dp20"
            android:layout_marginStart="@dimen/dp3"
            />

        <View
            android:background="@color/skeleton_color"
            android:layout_width="@dimen/dp50"
            android:layout_height="@dimen/dp20"
            android:layout_marginStart="@dimen/dp3"
            />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/buttonLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rvGenre"
        >
        <View
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp33"
            android:layout_weight="1"
            android:layout_marginEnd="@dimen/dp8"
            android:background="@color/skeleton_color"
            />
        <View
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp33"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp8"
            android:background="@color/skeleton_color"
            />



    </LinearLayout>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/dp44"
        app:layout_constraintTop_toBottomOf="@+id/buttonLayout"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp16"
            android:layout_margin="@dimen/dp16"
            android:background="@drawable/background_blackish_grey_8dp"
            >

            <View
                android:id="@+id/img"
                android:layout_width="@dimen/dp80"
                android:layout_height="@dimen/dp80"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:background="@color/skeleton_color"
                />

            <View
                android:id="@+id/txtWib"
                android:layout_width="@dimen/dp200"
                android:layout_height="@dimen/dp14"
                android:layout_marginStart="@dimen/dp16"
                android:background="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@+id/img"
                />
            <!-- LIVE -->
            <View
                android:id="@+id/txtLive"
                android:layout_width="@dimen/dp40"
                android:layout_height="@dimen/dp20"
                android:layout_marginTop="@dimen/dp7"
                app:layout_constraintTop_toBottomOf="@+id/txtWib"
                app:layout_constraintStart_toStartOf="@+id/txtWib"
                android:background="@color/skeleton_color"
                />
            <View
                android:id="@+id/txtTitle"
                android:layout_width="148dp"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/dp8"
                android:background="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="@+id/txtLive"
                app:layout_constraintStart_toEndOf="@+id/txtLive"
                app:layout_constraintTop_toBottomOf="@+id/txtWib"
                />
            <!-- Tike Priatnakusumah, Ronal Surapradja, Melissa Karim -->
            <View
                android:id="@+id/txtliveDesc"
                android:layout_width="@dimen/dp163"
                android:layout_height="@dimen/dp34"
                android:layout_marginTop="@dimen/dp8"
                app:layout_constraintStart_toStartOf="@+id/txtLive"
                app:layout_constraintTop_toBottomOf="@+id/txtTitle"
                android:background="@color/skeleton_color"
                />
            <View
                android:id="@+id/txtListeners"
                android:layout_marginEnd="@dimen/dp18"
                android:layout_width="@dimen/dp37"
                android:layout_height="@dimen/dp8"
                android:layout_marginTop="@dimen/dp12"
                android:background="@color/skeleton_color"
                app:layout_constraintStart_toStartOf="@+id/txtliveDesc"
                app:layout_constraintTop_toBottomOf="@+id/txtliveDesc"

                />
            <View
                android:id="@+id/txtCommentCount"
                android:layout_width="@dimen/dp37"
                android:layout_height="@dimen/dp8"
                android:layout_marginStart="@dimen/dp8"
                android:background="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="@+id/txtListeners"
                app:layout_constraintStart_toEndOf="@+id/txtListeners"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="@dimen/dp100"
            android:layout_height="@dimen/dp25"
            android:layout_marginStart="@dimen/dp16"
            android:background="@color/skeleton_color"
            android:layout_marginTop="@dimen/dp24"
            />
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp36"
            android:background="@color/home_search"
            android:layout_marginTop="@dimen/dp16"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp16"
            >
            <View
                android:id="@+id/view1"
                android:layout_width="@dimen/dp64"
                android:layout_height="@dimen/dp64"
                android:background="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />

            <View
                android:id="@+id/view2"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp20"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                app:layout_constraintTop_toTopOf="@+id/view1"
                app:layout_constraintStart_toEndOf="@+id/view1"
                />
            <View
                android:id="@+id/view3"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp34"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp3"
                app:layout_constraintTop_toBottomOf="@+id/view2"
                app:layout_constraintStart_toEndOf="@+id/view1"
                />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp16"
            >
            <androidx.cardview.widget.CardView
                android:id="@+id/view11"
                android:layout_width="@dimen/dp64"
                android:layout_height="@dimen/dp64"
                app:cardCornerRadius="@dimen/dp8"
                app:cardBackgroundColor="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />

            <View
                android:id="@+id/view21"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp20"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                app:layout_constraintTop_toTopOf="@+id/view11"
                app:layout_constraintStart_toEndOf="@+id/view11"
                />
            <View
                android:id="@+id/view31"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp34"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp3"
                app:layout_constraintTop_toBottomOf="@+id/view21"
                app:layout_constraintStart_toEndOf="@+id/view11"
                />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp16"
            >
            <androidx.cardview.widget.CardView
                android:id="@+id/view12"
                android:layout_width="@dimen/dp64"
                android:layout_height="@dimen/dp64"
                app:cardCornerRadius="@dimen/dp8"
                app:cardBackgroundColor="@color/skeleton_color"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />

            <View
                android:id="@+id/view22"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp20"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                app:layout_constraintTop_toTopOf="@+id/view12"
                app:layout_constraintStart_toEndOf="@+id/view12"
                />
            <View
                android:id="@+id/view32"
                android:layout_width="@dimen/dp90"
                android:layout_height="@dimen/dp34"
                android:background="@color/home_search"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp3"
                app:layout_constraintTop_toBottomOf="@+id/view22"
                app:layout_constraintStart_toEndOf="@+id/view12"
                />


        </androidx.constraintlayout.widget.ConstraintLayout>




    </LinearLayout>







</androidx.constraintlayout.widget.ConstraintLayout>