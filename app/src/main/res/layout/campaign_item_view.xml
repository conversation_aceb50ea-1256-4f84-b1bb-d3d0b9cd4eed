<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/imgCampaign"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@mipmap/ic_launcher"
        android:foreground="?selectableItemBackgroundBorderless"
        android:scaleType="fitXY"
        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.Round"
        />
</androidx.constraintlayout.widget.ConstraintLayout>