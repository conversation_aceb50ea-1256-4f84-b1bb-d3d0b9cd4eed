<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="noice.app.modules.live.model.summary.LiveRoomSummaryItemViewState" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imgType"
            android:layout_width="@dimen/dp59"
            android:layout_height="@dimen/dp59"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp12"
            android:background="@color/blackish_grey"
            android:alpha="0.99"
            android:scaleType="center"
            app:tint="@color/dull_yellow"
            app:drawableAsPerLiveSummaryType="@{item.type}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.Round"
            app:srcCompat="@drawable/ic_live_clock" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            tools:text="01:46:03"
            android:text="@{item.typeValue}"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp16"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="@+id/imgType"
            app:layout_constraintStart_toStartOf="@+id/imgType"
            app:layout_constraintTop_toBottomOf="@+id/imgType" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginBottom="@dimen/dp12"
            tools:text="Durasi"
            android:text="@{item.typeTitle}"
            android:textColor="@color/white70"
            android:textSize="@dimen/sp14"
            app:fontFamily="@font/roboto"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/txtValue"
            app:layout_constraintStart_toStartOf="@+id/txtValue"
            app:layout_constraintTop_toBottomOf="@+id/txtValue" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>