<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.profile.activity.ProfileMenu">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        app:navigationIcon="@drawable/ic_left_arrow_white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/profilePic"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp100"
        android:layout_marginTop="@dimen/dp16"
        app:srcCompat="@drawable/ic_user_profile"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <TextView
        android:id="@+id/profileName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center_vertical"
        android:lineSpacingExtra="@dimen/sp3"
        android:drawablePadding="@dimen/dp7"
        android:fontFamily="sans-serif"
        tools:text="Joko Anwar"
        android:textStyle="bold"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profilePic"/>

    <TextView
        android:id="@+id/profileUserName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:lineSpacingExtra="@dimen/sp3"
        android:fontFamily="sans-serif"
        tools:text="\@Dusidsk"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profileName"/>

    <TextView
        android:id="@+id/profileDesc"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp4"
        android:gravity="center"
        android:fontFamily="sans-serif"
        tools:text="Seorang penyuka musik dan podcast komedi dan horror"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profileUserName"/>

    <TextView
        android:id="@+id/reportUser"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp26"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:drawablePadding="@dimen/dp8"
        android:gravity="center_vertical"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/red"
        android:lineSpacingExtra="@dimen/sp3"
        android:text="@string/report_user"
        app:drawableStartCompat="@drawable/ic_information_red"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/profileDesc"/>

    <TextView
        android:id="@+id/blockUser"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:drawablePadding="@dimen/dp8"
        android:gravity="center_vertical"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/red"
        android:lineSpacingExtra="@dimen/sp3"
        android:text="@string/block_user"
        app:drawableStartCompat="@drawable/ic_information_red"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/reportUser"/>
</androidx.constraintlayout.widget.ConstraintLayout>