<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp16"
    android:paddingBottom="@dimen/dp20">

    <TextView
        android:id="@+id/headerText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:text="@string/genre_kamu"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp20"
        android:textStyle="bold" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        app:cardBackgroundColor="@color/home_search"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            android:paddingTop="@dimen/dp24"
            android:paddingBottom="@dimen/dp24">

            <ImageView
                android:id="@+id/imageEmptyGenre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/select_genre"
                android:src="@drawable/ic_no_genre_small"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guidline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.58" />

            <TextView
                android:id="@+id/txtDes"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp15"
                android:layout_marginStart="@dimen/dp5"
                android:fontFamily="@font/readex_pro"
                android:text="@string/get_audio_content_that_you_really_like_by_chosing_favorite_genre"
                android:textColor="@color/white80"
                android:textSize="@dimen/sp14"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/guidline"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/txtChooseGenre"/>

            <TextView
                android:id="@+id/txtChooseGenre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:paddingTop="@dimen/dp3"
                android:paddingBottom="@dimen/dp3"
                android:paddingStart="@dimen/dp5"
                android:paddingEnd="@dimen/dp5"
                android:background="@drawable/custom_ripple"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:text="@string/pilih_genre"
                android:textAllCaps="true"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp12"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/txtDes"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>