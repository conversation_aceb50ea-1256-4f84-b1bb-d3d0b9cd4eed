<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:clickable="true"
    android:focusable="true"
    tools:context=".modules.search.fragment.SearchFragment">

    <LinearLayout
        android:id="@+id/edLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp39"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_blackish_grey_4dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/backButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/app_name"
            android:paddingStart="@dimen/dp20"
            android:paddingTop="@dimen/dp8"
            android:paddingEnd="@dimen/dp20"
            android:paddingBottom="@dimen/dp8"
            app:srcCompat="@drawable/ic_left_angle_white" />

        <EditText
            android:id="@+id/searchEditText"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@null"
            android:fontFamily="@font/readex_pro"
            android:hint="@string/search_for_the_latest_podcasts"
            android:imeOptions="actionSearch"
            android:importantForAutofill="no"
            android:inputType="text"
            android:textColor="@color/dull_white"
            android:textColorHint="#8d8d8d"
            android:textSize="@dimen/sp16" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/crossButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:contentDescription="@string/app_name"
            android:padding="@dimen/dp10"
            app:srcCompat="@drawable/ic_cross_black_circle"
            android:visibility="gone" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/filterRecycler"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/edLayout" />

    <FrameLayout
        android:id="@+id/recyclerLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filterRecycler"
        app:layout_constraintVertical_bias="0.0">

        <noice.app.views.RootTitleRecycler
            android:id="@+id/rootTitleGenre"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"
            android:visibility="gone"
            app:headerTextPaddingStart="@dimen/dp8"
            app:headingPaddingBottom="@dimen/size_0dp"
            app:headingPaddingTop="@dimen/dp8"
            app:headingText="@string/your_fav_genre"
            app:headingTextColor="@color/white70"
            app:showSeeAll="false" />

        <noice.app.views.RootTitleRecycler
            android:id="@+id/rootTitleRecent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"
            android:visibility="gone"
            app:headerTextPaddingStart="@dimen/dp8"
            app:headingPaddingBottom="@dimen/dp8"
            app:headingPaddingTop="@dimen/dp8"
            app:headingText="@string/last_search"
            app:headingTextColor="@color/white70"
            app:showSeeAllText="true"
            app:seeAllText="@string/clear_recent_history"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/resultRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/emptyView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:background="@color/black700"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical"
        android:padding="@dimen/dp16"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center_horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/filterRecycler">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/noInternetImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginBottom="@dimen/dp24"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_no_search_result"/>

        <TextView
            android:id="@+id/emptyMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readex_pro"
            android:includeFontPadding="false"
            android:lineSpacingExtra="@dimen/sp6"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:textStyle="bold"
            tools:text="Tidak bisa menemukan “SHDIAHDduw”" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:fontFamily="@font/readex_pro"
            android:lineSpacingExtra="@dimen/sp6"
            android:text="@string/no_search_result"
            android:textColor="@color/white70"
            android:textSize="@dimen/sp14" />
    </LinearLayout>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/edLayout" />
</androidx.constraintlayout.widget.ConstraintLayout>