<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp104"
    android:layout_height="wrap_content"
    android:id="@+id/artistSegmentView"
    android:gravity="center_horizontal"
    tools:background="@color/black"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/coverImage"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp104"
        android:contentDescription="@string/app_name" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="5.6sp"
        android:gravity="center_horizontal"
        tools:text="Afgan" />

    <TextView
        android:id="@+id/txtFollowers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:visibility="gone"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp12"
        android:textColor="@color/medium_grey"
        android:lineSpacingExtra="4.8sp"
        android:gravity="center_horizontal"
        tools:text="3.7k Pengikut" />

    <TextView
        android:id="@+id/followBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="@dimen/dp2"
        android:paddingTop="@dimen/dp5"
        android:paddingBottom="@dimen/dp5"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:background="@drawable/border_white_radius_4dp"
        app:drawableStartCompat="@drawable/ic_follower_plus"
        android:gravity="center_vertical"
        android:drawablePadding="@dimen/dp5"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="5.6sp"
        android:text="@string/follow" />
</LinearLayout>