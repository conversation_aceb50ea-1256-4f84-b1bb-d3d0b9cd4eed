<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".modules.radio.fragment.RadioDetailFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black700">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="@color/black700"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_collapseMode="pin">

                <ImageView
                    android:id="@+id/imgCover"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:scaleType="fitXY"
                    android:contentDescription="@string/app_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/headerLayout"
                    />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@drawable/channel_gradient_dark"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/headerLayout"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/headerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="?actionBarSize"
                    app:layout_constraintTop_toTopOf="parent"
                    >

                    <androidx.cardview.widget.CardView
                        android:id="@+id/coverLayout"
                        android:layout_width="@dimen/dp140"
                        android:layout_height="@dimen/dp140"
                        android:layout_marginTop="@dimen/dp16"
                        app:cardElevation="@dimen/size_0dp"
                        app:cardCornerRadius="@dimen/dp8"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/imgRadio"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_thumb_default"
                            android:adjustViewBounds="true"/>
                    </androidx.cardview.widget.CardView>

                    <noice.app.views.MediaButton
                        android:id="@+id/mediaButton"
                        android:layout_width="@dimen/dp52"
                        android:layout_height="@dimen/dp52"
                        android:layout_marginTop="@dimen/dp16"
                        app:mediaType="radio"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/coverLayout"/>

                    <TextView
                        android:id="@+id/txtRadioName"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:fontFamily="sans-serif"
                        android:textStyle="bold"
                        android:textSize="@dimen/sp20"
                        android:textColor="@color/dull_white"
                        android:lineSpacingExtra="@dimen/dp8"
                        android:gravity="center_horizontal"
                        tools:text="MAS FM"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/mediaButton"/>

                    <TextView
                        android:id="@+id/txtDescription"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:fontFamily="sans-serif"
                        android:textStyle="normal"
                        android:textSize="@dimen/sp14"
                        android:textColor="@color/dull_white"
                        android:letterSpacing="0.04"
                        android:lineSpacingExtra="4sp"
                        tools:text="Musik Terbaik di Jakarta"
                        android:gravity="center_horizontal"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/txtRadioName"
                        app:layout_constraintEnd_toEndOf="parent"/>
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvGenre"
                        android:layout_marginTop="@dimen/dp12"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:visibility="visible"
                        android:orientation="horizontal"
                        tools:listitem="@layout/genre_pills"
                        android:layout_gravity="center_horizontal"
                        tools:itemCount="3"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txtDescription"
                        />

                <LinearLayout
                    android:id="@+id/favShareLinearLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_marginStart="@dimen/dp16"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rvGenre"
                    android:baselineAligned="false">
                    <FrameLayout
                        android:id="@+id/layoutFav"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/dp33"
                        android:layout_weight="1"
                        android:layout_marginEnd="@dimen/dp8"
                        android:background="@drawable/border_white_radius_4dp"
                        >
                        <TextView
                            android:id="@+id/txtFav"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawablePadding="@dimen/dp8"
                            android:fontFamily="sans-serif"
                            android:textStyle="bold"
                            android:textSize="@dimen/sp12"
                            android:textColor="@color/white"
                            android:letterSpacing="0.2"
                            android:textAllCaps="true"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:text="@string/favorite"
                            app:drawableLeftCompat="@drawable/ic_heart_white" />

                    </FrameLayout>
                    <FrameLayout
                        android:id="@+id/layoutShare"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/dp33"
                        android:layout_weight="1"
                        android:layout_marginStart="@dimen/dp8"
                        android:background="@drawable/border_white_radius_4dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:drawablePadding="@dimen/dp2"
                            android:fontFamily="sans-serif"
                            android:gravity="center"
                            android:letterSpacing="0.2"
                            android:text="@string/share"
                            android:textAllCaps="true"
                            android:textColor="@color/white"
                            android:textSize="@dimen/sp12"
                            android:textStyle="bold"
                            app:drawableLeftCompat="@drawable/ic_share" />

                    </FrameLayout>

                </LinearLayout>

                <noice.app.views.DonateView
                    android:id="@+id/donateView"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/dp16"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/favShareLinearLayout"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:contentInsetStartWithNavigation="0dp"
                app:navigationIcon="@drawable/ic_left_arrow_white"
                app:layout_anchor="@id/collapsingToolbar"
                app:theme="@style/ThemeOverlay.AppCompat.Dark"
                app:title="">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    tools:ignore="UseCompoundDrawables">

                    <TextView
                        android:id="@+id/txtToolbarTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="@dimen/dp16"
                        android:layout_gravity="center_vertical"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:fontFamily="sans-serif"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/sp14" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/imgSearch"
                        android:background="@drawable/custom_ripple_bg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp16"
                        android:layout_gravity="center_vertical"
                        app:srcCompat="@drawable/ic_search_white" />

                    <ImageView
                        android:id="@+id/threeDotMenu"
                        android:layout_width="@dimen/dp32"
                        android:layout_height="@dimen/dp32"
                        android:layout_marginEnd="@dimen/dp12"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_three_dot"
                        android:background="@drawable/custom_ripple_bg"
                        android:contentDescription="@string/app_name"/>
                </LinearLayout>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/dp1"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/dp44"
            >
            <LinearLayout
                android:id="@+id/noLiveView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/background_blackish_grey_8dp"
                android:orientation="vertical"
                android:padding="@dimen/dp16"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp16"
                android:visibility="gone"
                tools:visibility="visible"
                >
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_noliveradio"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp24"
                    />
                <TextView
                    android:id="@+id/program_rad"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp13"
                    android:text="@string/program_not_live"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    android:gravity="center_horizontal|top"
                    android:fontFamily="@font/roboto_bold"
                    />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp8"
                    android:text="@string/no_live_desc"
                    android:textColor="@color/white80"
                    android:gravity="center_horizontal|top"
                    android:fontFamily="@font/roboto"
                    android:layout_marginBottom="@dimen/dp24"
                    />


            </LinearLayout>



            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/liveLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp16"
                android:layout_margin="@dimen/dp16"
                android:background="@drawable/background_blackish_grey_8dp"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/img"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp80"
                    tools:src="@drawable/ic_thumb_square"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:background="@drawable/round_outline_8"
                    />

                <noice.app.views.EqualizerView
                    android:id="@+id/equalizer"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp80"
                    android:paddingTop="@dimen/dp20"
                    android:paddingBottom="@dimen/dp20"
                    app:marginLeft="@dimen/dp3"
                    app:marginRight="@dimen/dp3"
                    android:visibility="gone"
                    android:src="@drawable/equalizer"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:barHeight="@dimen/dp40"/>

                <TextView
                    android:id="@+id/txtWib"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:textSize="@dimen/sp10"
                    android:layout_marginStart="@dimen/dp16"
                    android:lineSpacingExtra="2sp"
                    android:translationY="-1.14sp"
                    tools:text="10.00 WIB - 13.00 WIB"
                    android:gravity="top"
                    android:alpha="0.5"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/img"
                    />
                <!-- LIVE -->
                <TextView
                    android:id="@+id/txtLive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp7"
                    android:text="@string/live"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:lineHeight="@dimen/dp14"
                    android:textSize="@dimen/sp10"
                    android:lineSpacingExtra="2sp"
                    android:translationY="-1.14sp"
                    android:textColor="@color/dull_yellow"
                    android:gravity="center"
                    android:textAllCaps="false"
                    android:paddingStart="@dimen/dp10"
                    android:paddingEnd="@dimen/dp10"
                    android:paddingTop="@dimen/dp3"
                    android:paddingBottom="@dimen/dp3"
                    app:layout_constraintTop_toBottomOf="@+id/txtWib"
                    app:layout_constraintStart_toStartOf="@+id/txtWib"
                    android:background="@drawable/border_dull_yellow_dark_radius_20dp"
                    />
                <TextView
                    android:id="@+id/txtTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:textSize="@dimen/sp14"
                    tools:text="Sarapan Seru"
                    android:fontFamily="sans-serif"
                    android:lineSpacingExtra="3sp"
                    android:translationY="-1.6sp"
                    android:gravity="start"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginStart="@dimen/dp8"
                    app:layout_constraintTop_toTopOf="@+id/txtLive"
                    app:layout_constraintStart_toEndOf="@+id/txtLive"
                    app:layout_constraintTop_toBottomOf="@+id/txtWib"
                    />
                <!-- Tike Priatnakusumah, Ronal Surapradja, Melissa Karim -->
                <TextView
                    android:id="@+id/txtliveDesc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Tike Priatnakusumah, Ronal Surapradja, Melissa Karim"
                    android:fontFamily="sans-serif"
                    android:lineSpacingExtra="3sp"
                    android:translationY="-1.37sp"
                    android:gravity="top"
                    android:textColor="@color/white70"
                    android:layout_marginTop="@dimen/dp5"
                    app:layout_constraintStart_toStartOf="@+id/txtLive"
                    app:layout_constraintTop_toBottomOf="@+id/txtTitle"
                    />
                <TextView
                    android:id="@+id/txtListeners"
                    android:layout_marginEnd="@dimen/dp18"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:textSize="@dimen/sp12"
                    android:textColor="@color/white70"
                    android:fontFamily="sans-serif"
                    android:textStyle="normal"
                    android:drawablePadding="@dimen/dp6"
                    android:lineSpacingExtra="0sp"
                    android:gravity="top"
                    app:drawableLeftCompat="@drawable/ic_headphone"
                    android:layout_marginTop="@dimen/dp12"
                    app:layout_constraintStart_toStartOf="@+id/txtliveDesc"
                    app:layout_constraintTop_toBottomOf="@+id/txtliveDesc"
                    />
                <TextView
                    android:id="@+id/txtCommentCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/zero"
                    android:drawablePadding="@dimen/dp6"
                    android:fontFamily="sans-serif"
                    android:textStyle="normal"
                    android:textSize="@dimen/sp12"
                    android:textColor="@color/white70"
                    android:lineSpacingExtra="0sp"
                    android:gravity="top"
                    android:layout_marginStart="@dimen/dp13"
                    app:drawableLeftCompat="@drawable/ic_chat_gray"
                    app:layout_constraintTop_toTopOf="@+id/txtListeners"
                    app:layout_constraintStart_toEndOf="@+id/txtListeners"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/radioLayout"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginBottom="@dimen/dp30"
                >
                <!-- Setelah Ini -->
                <TextView
                    android:id="@+id/setelah_ini"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    android:text="@string/setelah_ini"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp20"
                    android:fontFamily="@font/roboto_bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgNews"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_news"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/setelah_ini"
                    />
                <TextView
                    android:id="@+id/txtNews"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp13"
                    android:text="@string/program_scheduled_not_yet_available"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    android:gravity="center_horizontal|top"
                    android:fontFamily="@font/roboto_bold"
                    app:layout_constraintTop_toBottomOf="@+id/imgNews"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />

                <TextView
                    android:id="@+id/txtNewsDesc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp8"
                    android:text="@string/try_again_another_day"
                    android:textColor="@color/white80"
                    android:gravity="center_horizontal|top"
                    android:fontFamily="@font/roboto"
                    android:layout_marginBottom="@dimen/dp24"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtNews"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>


            <noice.app.views.RootTitleRecycler
                android:id="@+id/rvSchedule"
                android:layout_marginTop="@dimen/dp24"
                app:headingText="@string/setelah_ini"
                app:headingSize="@dimen/dp20"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/txtLihatSemua"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_gravity="center"
                android:textColor="@color/dull_yellow"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:textSize="@dimen/sp12"
                android:textAllCaps="true"
                android:text="@string/view_all_schedules"
                android:gravity="center"
                android:visibility="gone"
                android:layout_margin="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp" />

            <noice.app.views.RootTitleRecycler
                android:id="@+id/rvPodcast"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginEnd="@dimen/dp8"
                android:visibility="gone"
                app:headingSize="@dimen/dp20"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>


            <noice.app.views.RootTitleRecycler
                android:id="@+id/rvArtist"
                android:layout_marginTop="@dimen/dp8"
                app:headingText="@string/artist"
                app:headingSize="@dimen/dp20"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp38"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                >
                <TextView
                    android:id="@+id/txtMobile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:translationY="-1.6sp"
                    android:gravity="center"
                    tools:text="08123456789"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/white"
                    android:fontFamily="@font/roboto"
                    android:drawablePadding="@dimen/dp16"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:drawableStartCompat="@drawable/ic_telephone" />
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgWeb"
                    android:layout_width="@dimen/dp19"
                    android:layout_height="@dimen/dp19"
                    app:srcCompat="@drawable/ic_global"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:clickable="true"
        android:layout_marginTop="?actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true" />


</androidx.coordinatorlayout.widget.CoordinatorLayout>