<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/textReport"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:fontFamily="@font/roboto"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        tools:text="Spam" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp20"
        android:background="@color/home_search" />
</LinearLayout>