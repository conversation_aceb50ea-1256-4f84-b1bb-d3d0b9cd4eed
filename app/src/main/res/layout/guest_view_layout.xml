<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp13"
    android:background="@color/black"
    android:id="@+id/guestView">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgUser"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:background="@drawable/bg_skeleton_20dp"
        android:padding="@dimen/dp14"
        android:src="@drawable/ic_profile_grey"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dp16"/>

    <TextView
        android:id="@+id/guestCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/imgUser"
        app:layout_constraintStart_toEndOf="@+id/imgUser"
        android:layout_marginStart="@dimen/dp6"
        android:textSize="@dimen/sp14"
        android:fontFamily="@font/roboto"
        android:textColor="@color/white"
        tools:text="2.450"
        android:drawablePadding="@dimen/dp4"
        />

    <TextView
        android:id="@+id/txtVisitor"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/guestCount"
        app:layout_constraintEnd_toEndOf="@+id/guestInfo"
        app:layout_constraintStart_toEndOf="@+id/imgUser"
        android:layout_marginStart="@dimen/dp6"
        android:layout_marginTop="@dimen/dp4"
        android:textSize="@dimen/sp14"
        android:fontFamily="@font/roboto"
        android:textColor="@color/medium_grey"
        android:text="@string/visitor"
        android:gravity="start"
        />

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp15"
        app:layout_constraintTop_toBottomOf="@+id/txtVisitor"
        app:layout_constraintStart_toEndOf="@+id/imgUser"
        android:background="@color/white10"
        android:layout_marginEnd="@dimen/dp16"
        />

    <TextView
        android:id="@+id/guestInfo"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp21"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp12"
        android:drawablePadding="@dimen/dp5"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/imgUser"
        app:layout_constraintBottom_toBottomOf="@id/imgUser"
        app:drawableStartCompat="@drawable/ic_about" />

</androidx.constraintlayout.widget.ConstraintLayout>