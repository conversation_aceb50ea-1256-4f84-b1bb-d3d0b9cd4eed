<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black700"
    tools:context=".modules.onboarding.fragments.IntroductionFragment">

    <ImageView
        android:id="@+id/imageNoiceLogo"
        android:layout_width="@dimen/dp70"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:contentDescription="@string/app_name"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_noice_logo_insta" />

    <TextView
        android:id="@+id/text_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/imageNoiceLogo"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="@font/roboto_bold"
        android:lineSpacingExtra="@dimen/sp8"
        android:text="@string/podcast_radio_audiobook_live"
        android:textColor="@color/white"
        android:textSize="@dimen/sp34"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/text_sub_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/text_heading"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:text="@string/onboarding_sub_heading"
        android:textColor="@color/greyText"
        android:textSize="@dimen/sp16"
        android:textStyle="normal" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp280"
        android:layout_above="@+id/startBtn"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp24"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_onboarding_intro" />

    <View
        android:id="@+id/viewShadowBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp35"
        android:layout_above="@+id/startBtn"
        android:background="@drawable/shadow_gradient_bottom"/>

    <TextView
        android:id="@+id/startBtn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp56"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16"
        android:background="@drawable/background_yellow_radius_4dp"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:text="@string/start"
        android:textAllCaps="true"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp12"
        android:textStyle="bold" />
</RelativeLayout>