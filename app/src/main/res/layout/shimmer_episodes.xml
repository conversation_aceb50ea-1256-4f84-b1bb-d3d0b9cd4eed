<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/similarCatalogSkeleton"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black700">

    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v2"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintTop_toTopOf="@id/v1"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v3"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v4"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@+id/v1" />

    <View
        android:id="@+id/v4"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintStart_toEndOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@+id/v1" />

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp32"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v3" />

    <View
        android:id="@+id/v6"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintTop_toTopOf="@id/v5"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v7"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v8"
        app:layout_constraintStart_toStartOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@+id/v5" />

    <View
        android:id="@+id/v8"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v6"
        app:layout_constraintStart_toEndOf="@id/v7"
        app:layout_constraintTop_toBottomOf="@+id/v5" />

    <View
        android:id="@+id/v9"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp32"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v7" />

    <View
        android:id="@+id/v10"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v9"
        app:layout_constraintTop_toTopOf="@id/v9"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v11"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v12"
        app:layout_constraintStart_toStartOf="@id/v9"
        app:layout_constraintTop_toBottomOf="@+id/v9" />

    <View
        android:id="@+id/v12"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v10"
        app:layout_constraintStart_toEndOf="@id/v11"
        app:layout_constraintTop_toBottomOf="@+id/v9" />

    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp32"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v12" />

    <View
        android:id="@+id/v14"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v13"
        app:layout_constraintTop_toTopOf="@id/v13"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v15"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v16"
        app:layout_constraintStart_toStartOf="@id/v13"
        app:layout_constraintTop_toBottomOf="@+id/v13" />

    <View
        android:id="@+id/v16"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v14"
        app:layout_constraintStart_toEndOf="@id/v15"
        app:layout_constraintTop_toBottomOf="@+id/v13" />

    <View
        android:id="@+id/v17"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp32"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v16" />

    <View
        android:id="@+id/v18"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v17"
        app:layout_constraintTop_toTopOf="@id/v17"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v19"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v20"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintTop_toBottomOf="@+id/v17" />

    <View
        android:id="@+id/v20"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v18"
        app:layout_constraintStart_toEndOf="@id/v19"
        app:layout_constraintTop_toBottomOf="@+id/v17" />

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp32"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v20" />

    <View
        android:id="@+id/v22"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp60"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v21"
        app:layout_constraintTop_toTopOf="@id/v21"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v23"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toStartOf="@id/v24"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintTop_toBottomOf="@+id/v21" />

    <View
        android:id="@+id/v24"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_white_round_8dp"
        app:layout_constraintEnd_toEndOf="@id/v22"
        app:layout_constraintStart_toEndOf="@id/v23"
        app:layout_constraintTop_toBottomOf="@+id/v21" />
</androidx.constraintlayout.widget.ConstraintLayout>