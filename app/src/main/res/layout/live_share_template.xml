<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/shareLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgBlur"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black60" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/innerLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/txtHeading"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp27"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/dp2"
            android:textColor="@color/white"
            android:textSize="@dimen/sp24"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/hostProfileImage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/live_share_description" />

        <ImageView
            android:id="@+id/hostProfileImage"
            android:layout_width="@dimen/dp228"
            android:layout_height="@dimen/dp228"
            android:layout_marginEnd="@dimen/dp4"
            android:background="@drawable/border_dull_yellow_dark_radius_circle"
            android:contentDescription="@string/app_name"
            android:padding="@dimen/dp5"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_user_profile" />


        <TextView
            android:id="@+id/noiceLiveTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp13"
            android:layout_marginBottom="@dimen/dp8"
            android:fontFamily="@font/roboto_bold"
            android:textColor="@color/grey"
            android:textSize="@dimen/sp10"
            android:textStyle="bold"
            android:visibility="visible"
            app:drawableStartCompat="@drawable/ic_live_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hostProfileImage"
            tools:text="" />

        <TextView
            android:id="@+id/title"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:maxLines="3"
            android:ellipsize="end"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noiceLiveTime"
            tools:text="Tidak Perlu Tersinggung" />

        <TextView
            android:id="@+id/subTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp24"
            android:drawablePadding="@dimen/dp3"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:drawableStartCompat="@drawable/ic_host_layer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            tools:text="atiq257" />

        <ImageView
            android:id="@+id/logo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp100"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_noice_logo_insta"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>