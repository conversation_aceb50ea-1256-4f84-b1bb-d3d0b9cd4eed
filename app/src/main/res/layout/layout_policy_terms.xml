<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700"
    tools:context=".modules.profile.fragment.PolicyTermsFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        app:toolbarViewTitle="@string/terms_and_condition"
        app:toolbarBackIcon="@drawable/ic_left_angle_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <WebView
        android:id="@+id/webView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>