<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="canCreateVideoRoom"
            type="Boolean" />
        <variable
            name="audioVideoToggleEnabled"
            type="Boolean" />
        <variable
            name="uploadViewModel"
            type="noice.app.modules.live.viewmodel.UploadViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black">

        <noice.app.views.ToolbarView
            android:id="@+id/toolBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rightIcon="@drawable/ic_search_white" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/toolBar">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16">

                <include
                    android:id="@+id/layout_upload_image"
                    layout="@layout/layout_upload_image"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:state="@{uploadViewModel.state}" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textRoomNameLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:hint="@string/nama_room"
                    android:textColorHint="@color/white70"
                    app:boxBackgroundColor="@color/blackish_blue"
                    android:layout_marginTop="@dimen/dp16"
                    app:boxBackgroundMode="filled"
                    app:boxCornerRadiusBottomEnd="@dimen/dp4"
                    app:boxCornerRadiusBottomStart="@dimen/dp4"
                    app:boxCornerRadiusTopEnd="@dimen/dp4"
                    app:boxCornerRadiusTopStart="@dimen/dp4"
                    app:boxStrokeColor="@color/white"
                    app:hintTextColor="@color/white70"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_upload_image">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/textRoomName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif"
                        android:imeOptions="actionNext"
                        android:inputType="textCapWords"
                        android:maxLength="60"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14" />
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/textErrorRoomName"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp8"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/msg_room_name_info"
                    android:textColor="@color/red"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/textRoomNameLayout"
                    app:layout_constraintStart_toStartOf="@id/textRoomNameLayout"
                    app:layout_constraintTop_toBottomOf="@id/textRoomNameLayout" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textRoomDescriptionLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:hint="@string/description"
                    android:textColorHint="@color/white70"
                    app:boxBackgroundColor="@color/blackish_blue"
                    app:boxBackgroundMode="filled"
                    app:boxCornerRadiusBottomEnd="@dimen/dp4"
                    app:boxCornerRadiusBottomStart="@dimen/dp4"
                    app:boxCornerRadiusTopEnd="@dimen/dp4"
                    app:boxCornerRadiusTopStart="@dimen/dp4"
                    app:boxStrokeColor="@color/white"
                    app:hintTextColor="@color/white70"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textErrorRoomName">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/textRoomDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif"
                        android:gravity="start|top"
                        android:maxLength="200"
                        android:maxLines="4"
                        android:minLines="4"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/edtLayoutPin"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:hint="@string/pin_komentar"
                    android:textColorHint="@color/white70"
                    app:boxBackgroundColor="@color/blackish_blue"
                    app:boxBackgroundMode="filled"
                    app:boxCornerRadiusBottomEnd="@dimen/dp4"
                    app:boxCornerRadiusBottomStart="@dimen/dp4"
                    app:boxCornerRadiusTopEnd="@dimen/dp4"
                    app:boxCornerRadiusTopStart="@dimen/dp4"
                    app:boxStrokeColor="@color/white"
                    app:hintTextColor="@color/white70"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/textRoomDescriptionLayout">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edtPin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="sans-serif"
                        android:imeOptions="actionNext"
                        android:inputType="text"
                        android:maxLines="1"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14" />
                </com.google.android.material.textfield.TextInputLayout>


                <TextView
                    android:id="@+id/textTypeRoom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp20"
                    app:fontFamily="@font/roboto"
                    android:text="@string/type_live"
                    app:visibleIf="@{canCreateVideoRoom &amp;&amp; audioVideoToggleEnabled}"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/edtLayoutPin" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchVideoRoom"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:checked="false"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/live_video"
                    app:visibleIf="@{canCreateVideoRoom &amp;&amp; audioVideoToggleEnabled}"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textTypeRoom" />


                <TextView
                    android:id="@+id/textJadwal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp20"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/jadwal"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/switchVideoRoom" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchSekarang"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:checked="true"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/sekarang"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textJadwal" />

                <TextView
                    android:id="@+id/textRoomTanggal"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginTop="@dimen/dp16"
                    android:background="@drawable/background_blackish_blue_4dp"
                    android:fontFamily="sans-serif"
                    android:padding="@dimen/dp15"
                    android:text="@string/tanggal"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp15"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/switchSekarang" />

                <TextView
                    android:id="@+id/textErrorRoomTanggal"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp8"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/msg_room_live_date"
                    android:textColor="@color/red"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/textRoomTanggal"
                    app:layout_constraintStart_toStartOf="@id/textRoomTanggal"
                    app:layout_constraintTop_toBottomOf="@id/textRoomTanggal" />

                <TextView
                    android:id="@+id/textRoomJamMulai"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginTop="@dimen/dp16"
                    android:background="@drawable/background_blackish_blue_4dp"
                    android:fontFamily="sans-serif"
                    android:padding="@dimen/dp15"
                    android:text="@string/jam_mulai"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp15"
                    android:visibility="gone"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textErrorRoomTanggal" />


                <TextView
                    android:id="@+id/textErrorRoomJamMulai"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp8"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/msg_room_live_time"
                    android:textColor="@color/red"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/textRoomJamMulai"
                    app:layout_constraintStart_toStartOf="@id/textRoomJamMulai"
                    app:layout_constraintTop_toBottomOf="@id/textRoomJamMulai" />

                <TextView
                    android:id="@+id/textHostAndTamu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/speaker_guest"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textErrorRoomJamMulai" />

                <TextView
                    android:id="@+id/textCariHostAtauTamuLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginTop="@dimen/dp16"
                    android:background="@drawable/background_blackish_blue_4dp"
                    android:fontFamily="sans-serif"
                    android:padding="@dimen/dp15"
                    android:text="@string/speaker_palceholder"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp15"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textHostAndTamu" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerHosts"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textCariHostAtauTamuLayout" />

                <TextView
                    android:id="@+id/text_anti_spam"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/anti_spam"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/recyclerHosts" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switch_allow_speakers"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:checked="true"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/izinkan_peserta_jadi_pembicara"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_anti_spam" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switch_comment_time_limit"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:checked="true"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/batasi_waktu_komentar"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/switch_allow_speakers" />

                <TextView
                    android:id="@+id/text_dropdown_comment_time_limit"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginTop="@dimen/dp16"
                    android:background="@drawable/background_blackish_blue_4dp"
                    android:fontFamily="sans-serif"
                    android:padding="@dimen/dp15"
                    android:text="@string/_15_detik"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp15"
                    tools:visibility="visible"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/switch_comment_time_limit"
                    app:drawableEndCompat="@drawable/ic_arrow_down" />

                <TextView
                    android:id="@+id/text_live_settings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/live_settings"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp12"
                    android:textStyle="normal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_dropdown_comment_time_limit" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchRecording"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:checked="false"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_live_settings" />

                <TextView
                    android:id="@+id/textRecording"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:clickable="true"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/record_live_room"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    android:drawablePadding="@dimen/dp5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/switchRecording"
                    app:layout_constraintBottom_toBottomOf="@+id/switchRecording"
                    app:drawableEndCompat="@drawable/ic_live_settings"
                    android:focusable="true" />


                <View
                    android:id="@+id/viewVip"
                    android:layout_width="@dimen/dp90"
                    android:layout_height="@dimen/size_0dp"
                    app:layout_constraintTop_toTopOf="@+id/text_private"
                    app:layout_constraintBottom_toBottomOf="@+id/text_private"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:background="@drawable/transparent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgVip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:layout_marginEnd="@dimen/dp10"
                    app:srcCompat="@drawable/ic_arrow_right"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/text_private"
                    app:layout_constraintBottom_toBottomOf="@+id/text_private" />

                <TextView
                    android:id="@+id/txtType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:textColor="@color/dull_yellow"
                    android:textSize="@dimen/sp12"
                    tools:text="Ticketed"
                    android:layout_marginEnd="@dimen/dp16"
                    android:fontFamily="@font/roboto_bold"
                    app:layout_constraintEnd_toStartOf="@+id/imgVip"
                    app:layout_constraintTop_toTopOf="@+id/text_private"
                    app:layout_constraintBottom_toBottomOf="@+id/text_private" />

                <TextView
                    android:id="@+id/text_private"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp4"
                    android:clickable="true"
                    android:paddingTop="@dimen/dp5"
                    android:paddingBottom="@dimen/dp5"
                    android:lineSpacingExtra="@dimen/sp4"
                    android:text="@string/vip_room"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp16"
                    android:drawablePadding="@dimen/dp5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/switchRecording"
                    app:drawableEndCompat="@drawable/ic_live_settings"
                    android:focusable="true" />

                <TextView
                    android:id="@+id/submitButton"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp25"
                    android:layout_marginBottom="@dimen/dp10"
                    android:background="@drawable/background_yellow_radius_4dp"
                    android:fontFamily="@font/roboto_bold"
                    android:gravity="center"
                    android:letterSpacing="0.2"
                    android:padding="@dimen/dp18"
                    android:text="@string/buat_room"
                    android:textAllCaps="true"
                    android:textColor="@color/blackish_blue"
                    android:textSize="@dimen/sp12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/imgVip" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolBar" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>