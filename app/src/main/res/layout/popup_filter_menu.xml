<?xml version="1.0" encoding="utf-8"?>

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:translationZ="@dimen/dp10">

    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/dp164"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp16"
        app:cardCornerRadius="@dimen/dp8"
        android:outlineSpotShadowColor="@color/grey"
        app:cardElevation="@dimen/dp5"
        app:cardBackgroundColor="#202020">

        <LinearLayout
            android:orientation="vertical"
            android:paddingStart="@dimen/dp16"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RadioButton
                android:id="@+id/rbTerbaru"
                android:text="@string/latest"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:checked="true"
                app:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <RadioButton
                android:id="@+id/rbTerlama"
                android:text="@string/earlier"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/white"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout>