<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/backBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginStart="@dimen/dp14"
        android:padding="@dimen/dp8"
        android:src="@drawable/ic_left_angle_white"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/view1"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp27"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_48dp"
        app:layout_constraintStart_toEndOf="@id/backBtn"
        app:layout_constraintTop_toTopOf="@id/backBtn"
        app:layout_constraintBottom_toBottomOf="@id/backBtn"/>

    <View
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp27"
        android:layout_marginEnd="@dimen/dp14"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/backBtn"
        app:layout_constraintBottom_toBottomOf="@id/backBtn"/>

    <View
        android:id="@+id/view2"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:layout_marginTop="@dimen/dp31"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view1"
        app:layout_constraintEnd_toStartOf="@id/view3"/>

    <View
        android:id="@id/view3"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/view2"
        app:layout_constraintStart_toEndOf="@id/view2"
        app:layout_constraintEnd_toStartOf="@id/view4"/>

    <View
        android:id="@id/view4"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/view2"
        app:layout_constraintStart_toEndOf="@id/view3"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/view5"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginEnd="@dimen/dp8"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/view2"
        app:layout_constraintTop_toBottomOf="@id/view2"
        app:layout_constraintEnd_toStartOf="@id/view6"/>

    <View
        android:id="@+id/view6"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/view5"
        app:layout_constraintStart_toEndOf="@id/view5"
        app:layout_constraintEnd_toStartOf="@id/view7"/>

    <View
        android:id="@+id/view7"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp140"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/view6"
        app:layout_constraintTop_toTopOf="@id/view5"
        app:layout_constraintEnd_toEndOf="@id/view4"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="@dimen/dp76"
        android:background="@color/grey_333"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:id="@+id/view8"
            android:layout_width="@dimen/dp71"
            android:layout_height="@dimen/dp16"
            android:layout_marginStart="@dimen/dp16"
            android:background="@drawable/background_skeleton_48dp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/view9"/>

        <View
            android:id="@+id/view9"
            android:layout_width="@dimen/dp88"
            android:layout_height="@dimen/dp16"
            android:layout_marginTop="@dimen/dp6"
            android:background="@drawable/background_skeleton_48dp"
            app:layout_constraintStart_toStartOf="@id/view8"
            app:layout_constraintTop_toBottomOf="@id/view8"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/view10"
            android:layout_width="@dimen/dp94"
            android:layout_height="@dimen/dp32"
            android:layout_marginTop="@dimen/dp6"
            android:layout_marginEnd="@dimen/dp16"
            android:background="@drawable/background_skeleton_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>