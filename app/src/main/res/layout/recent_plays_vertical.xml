<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recentPlaysView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp3"
    android:paddingTop="@dimen/dp3"
    tools:background="@color/black">

    <FrameLayout
        android:id="@+id/imgMainLayout"
        android:layout_width="@dimen/dp64"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/contentLayout"
        app:layout_constraintBottom_toBottomOf="@id/contentLayout">

        <androidx.cardview.widget.CardView
            android:id="@+id/imgLayout"
            android:layout_width="@dimen/dp64"
            android:layout_height="@dimen/dp64"
            android:layout_gravity="center"
            app:cardCornerRadius="@dimen/dp8"
            app:cardElevation="@dimen/size_0dp"
            android:layout_marginTop="@dimen/dp4">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/coverImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/equalizer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/dp14"
                android:layout_gravity="center"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:background="@color/black30"
                android:visibility="gone"/>

        </androidx.cardview.widget.CardView>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:orientation="vertical"
        app:layout_constraintStart_toEndOf="@id/imgMainLayout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/actionButton"
        app:layout_constraintBottom_toTopOf="@+id/divider">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:fontFamily="@font/readex_pro"
            android:includeFontPadding="false"
            android:textSize="@dimen/sp14"
            android:textColor="@color/dull_white"
            android:lineSpacingExtra="@dimen/sp6"
            tools:text="Laki-Laki Juga Bisa Insecure Ternyata yah?" />

        <TextView
            android:id="@+id/subTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:fontFamily="@font/readex_pro"
            android:includeFontPadding="false"
            android:textStyle="normal"
            android:textSize="@dimen/sp12"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="@color/white80"
            android:lineSpacingExtra="@dimen/sp5"
            tools:text="Episode · Musuh Masyarakat"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintEnd_toEndOf="@id/title"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp12">

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="@dimen/dp80"
                android:layout_height="@dimen/dp3"
                android:layout_marginEnd="@dimen/dp10"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                tools:progress="70"
                android:layout_gravity="center_vertical"
                android:progressDrawable="@drawable/custom_progress_drawable" />

            <TextView
                android:id="@+id/duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:textSize="@dimen/sp10"
                android:textColor="#b3fafafa"
                android:letterSpacing="0.04"
                android:lineSpacingExtra="@dimen/sp4"
                tools:text="Tersisa 33 Menit"/>
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/actionButton"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_gravity="center"
        android:background="@drawable/custom_ripple_bg"
        android:src="@drawable/ic_play_transparent_white"
        android:contentDescription="@string/app_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/contentLayout"
        app:layout_constraintBottom_toBottomOf="@id/contentLayout" />

    <ProgressBar
        android:id="@+id/loader"
        android:layout_width="@dimen/dp44"
        android:layout_height="@dimen/dp44"
        android:visibility="gone"
        android:indeterminateTint="@color/white50"
        app:layout_constraintStart_toStartOf="@id/actionButton"
        app:layout_constraintEnd_toEndOf="@id/actionButton"
        app:layout_constraintTop_toTopOf="@id/actionButton"
        app:layout_constraintBottom_toBottomOf="@id/actionButton"/>

    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp16"
        android:background="@color/white10"
        app:layout_constraintTop_toBottomOf="@id/contentLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>