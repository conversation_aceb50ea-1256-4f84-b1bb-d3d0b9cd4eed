<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="label"
            type="String" />
        <variable
            name="selectedLabel"
            type="String" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp8"
            android:paddingBottom="@dimen/dp8"
            android:text="@{label}"
            android:textAlignment="center"
            android:background="@color/blackish_grey"
            android:textColor="@{selectedLabel.equalsIgnoreCase(label) ? @color/dull_yellow : @color/white}"
            android:textSize="@dimen/sp16"
            app:fontFamily="@font/roboto_med"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="10"
            tools:textColor="@color/white" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>