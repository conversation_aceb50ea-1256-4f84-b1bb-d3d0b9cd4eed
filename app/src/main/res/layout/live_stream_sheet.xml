<?xml version="1.0" encoding="UTF-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="noice.app.R"/>
        <import type="androidx.databinding.ObservableArrayList"/>
        <import type="noice.app.modules.live.model.gift.slide.GiftSlide"/>
        <import type="noice.app.modules.live.stream.common.VideoState"/>

        <variable
            name="viewModel"
            type="noice.app.modules.live.stream.player.LiveStreamViewModel" />

        <variable
            name="coverPhotoUrl"
            type="String" />

        <variable
            name="giftSlidingList"
            type="ObservableArrayList&lt;GiftSlide&gt;"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:keepScreenOn="true">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBlur"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_0dp"
            app:blurSrcUrl="@{viewModel.viewState.coverPhotoUrl}"
            app:placeholder="@{R.drawable.gradient_overlay}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:scaleType="centerCrop" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <FrameLayout
            android:id="@+id/videContainer"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            android:background="@android:color/transparent"
            app:attachTextureView="@{viewModel.viewState.video.textureView}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleIf="@{viewModel.viewState.video.state == VideoState.FROZEN || viewModel.viewState.video.state == VideoState.STREAMING}" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/compose_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/giftSlideRecyclerview"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp45"
            android:clipToPadding="false"
            android:orientation="vertical"
            app:items="@{giftSlidingList}"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/view_mock_regular_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            tools:visibility="visible"
            tools:itemCount="3"
            tools:listitem="@layout/item_gift_slide" />

        <View
            android:id="@+id/view_mock_regular_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp290"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ViewStub
            android:id="@+id/layout_gift_lottie_viewStub"
            android:layout="@layout/layout_gift_lottie"
            android:inflatedId="@+id/layout_gift_lottie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
