<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/custom_ripple_grey"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp48"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp16">

    <ImageView
        android:id="@+id/tick"
        android:layout_width="@dimen/dp15"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_tick_yellow"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/qualityValue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp20"
        android:gravity="center_vertical"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp16"
        android:textColor="@color/neutral_90"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="0sp"
        tools:text="1080"
        app:layout_constraintStart_toEndOf="@id/tick"
        app:layout_constraintTop_toTopOf="@id/tick"
        app:layout_constraintBottom_toBottomOf="@id/tick"/>

    <TextView
        android:id="@+id/quality"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp6"
        android:layout_marginBottom="@dimen/dp2"
        android:gravity="center_vertical"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textSize="@dimen/sp12"
        android:textColor="@color/pinkish_red"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="0sp"
        android:textAllCaps="true"
        tools:text="HD"
        android:drawablePadding="@dimen/dp20"
        app:layout_constraintStart_toEndOf="@id/qualityValue"
        app:layout_constraintBottom_toBottomOf="@id/qualityValue"/>
</androidx.constraintlayout.widget.ConstraintLayout>