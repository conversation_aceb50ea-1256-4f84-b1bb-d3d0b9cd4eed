<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp20"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp16"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/headerText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:text="@string/noice_live"
        android:textColor="@color/white"
        android:textSize="@dimen/sp20"
        android:textStyle="bold"
        android:fontFamily="sans-serif"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/txtMoon"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:text="@string/no_live_scheduled_yet"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        android:drawablePadding="@dimen/dp16"
        android:textStyle="bold"
        android:fontFamily="@font/roboto_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText"
        app:drawableTopCompat="@drawable/ic_no_live_schedule" />

    <TextView
        android:id="@+id/txtDes"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:text="@string/be_the_first_to_get_busy"
        android:textColor="@color/white80"
        android:textSize="@dimen/sp14"
        android:gravity="center"
        android:fontFamily="@font/roboto"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtMoon"
        />

    <TextView
        android:id="@+id/txtLive"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:text="@string/start_live"
        android:padding="@dimen/dp3"
        android:gravity="center"
        android:background="@drawable/custom_ripple"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        android:fontFamily="@font/roboto_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtDes" />
</androidx.constraintlayout.widget.ConstraintLayout>