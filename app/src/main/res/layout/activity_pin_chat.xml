<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:clickable="true"
    android:focusable="true"
    tools:context=".modules.live.activity.PinChatActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolBar"
        android:layout_width="match_parent"
        app:layout_collapseMode="pin"
        android:layout_height="?attr/actionBarSize"
        app:contentInsetStartWithNavigation="0dp"
        app:theme="@style/ThemeOverlay.AppCompat.Dark"
        app:navigationIcon="@drawable/ic_left_arrow_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@color/neutral_30"
        app:layout_constraintTop_toTopOf="parent"
        app:title="">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtToolbarTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/update_pin_chat"
                android:layout_gravity="center"
                android:fontFamily="@font/readex_pro_regular"
                android:textColor="@android:color/white"
                android:textSize="@dimen/sp16" />


    </androidx.appcompat.widget.Toolbar>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_gravity="bottom"
        android:background="@color/neutral_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolBar" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/edtLayoutPin"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp16"
        android:textColorHint="@color/white70"
        app:boxBackgroundColor="@color/black600"
        app:boxBackgroundMode="filled"
        app:boxCornerRadiusBottomEnd="@dimen/dp8"
        app:boxCornerRadiusBottomStart="@dimen/dp8"
        app:boxCornerRadiusTopEnd="@dimen/dp8"
        app:boxCornerRadiusTopStart="@dimen/dp8"
        app:boxStrokeColor="@color/white"
        app:boxStrokeWidth="@dimen/size_0dp"
        app:boxStrokeWidthFocused="@dimen/size_0dp"
        app:hintEnabled="false"
        app:hintTextColor="@color/white70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolBar">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edtPin"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readex_pro_regular"
            android:gravity="start|top"
            android:hint="@string/add_pin_hint"
            android:maxLength="2000"
            android:maxLines="4"
            android:minLines="4"
            android:textColor="@color/white"
            android:textColorHint="@color/white_600"
            android:textSize="@dimen/sp16" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/textErrorPin"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="sans-serif-medium"
        android:text="@string/msg_room_name_info"
        android:textColor="@color/red"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/edtLayoutPin"
        app:layout_constraintStart_toStartOf="@id/edtLayoutPin"
        app:layout_constraintTop_toBottomOf="@id/edtLayoutPin" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/submitButton"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginHorizontal="@dimen/dp16"
        android:layout_marginVertical="@dimen/dp32"
        android:fontFamily="@font/readex_pro_semi_bold"
        android:gravity="center"
        android:insetLeft="@dimen/size_0dp"
        android:insetTop="@dimen/size_0dp"
        android:insetRight="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:letterSpacing="0"
        android:minHeight="@dimen/size_0dp"
        android:text="@string/update_live_pin_chat"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        app:backgroundTint="@color/dull_yellow"
        app:cornerRadius="@dimen/dp14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolBar" />


</androidx.constraintlayout.widget.ConstraintLayout>