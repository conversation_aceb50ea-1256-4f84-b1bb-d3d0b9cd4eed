<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentHorizontalSegment"
    android:layout_width="@dimen/dp264"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/hostProfileImage"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp10"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_user_profile"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <LinearLayout
        android:id="@+id/scheduleLayout"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp18"
        android:layout_marginStart="@dimen/dp16"
        app:layout_constraintStart_toEndOf="@+id/hostProfileImage"
        app:layout_constraintTop_toTopOf="@+id/hostProfileImage"
        android:orientation="horizontal"
        >

        <TextView
            android:id="@+id/txtReplay"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:fontFamily="@font/readex_pro"
            android:text="@string/replay_live"
            android:visibility="gone"
            tools:visibility="gone"
            android:paddingStart="@dimen/dp4"
            android:paddingEnd="@dimen/dp4"
            android:background="@drawable/background_blackish_grey_4dp"
            android:textSize="@dimen/sp12"
            android:drawablePadding="@dimen/dp3"
            app:drawableStartCompat="@drawable/ic_replay" />


        <TextView
            android:id="@+id/txtSchedule"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textColor="@color/white"
            android:fontFamily="@font/readex_pro"
            tools:text="21 Mei - 8.30"
            android:visibility="gone"
            tools:visibility="visible"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp4"
            android:paddingEnd="@dimen/dp4"
            android:background="@drawable/background_blackish_grey_4dp"
            android:textSize="@dimen/sp12"
            android:drawablePadding="@dimen/dp3"
            app:drawableStartCompat="@drawable/ic_schedule" />
        <LinearLayout
            android:id="@+id/liveBadge"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="@dimen/dp4"
            android:paddingEnd="@dimen/dp4"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_gravity="center_vertical"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@drawable/background_blackish_grey_4dp"
            >
            <ImageView
                android:id="@+id/equalizer"
                android:layout_width="@dimen/dp10"
                android:gravity="center_vertical"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:src="@drawable/live_equalizer"
                tools:ignore="ContentDescription" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/live"
                android:gravity="center_vertical"
                android:textColor="@color/white"
                android:textAllCaps="true"
                android:textSize="@dimen/sp12"
                android:layout_marginStart="@dimen/dp3"
                android:fontFamily="@font/roboto_bold"
                />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/imgRecording"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:paddingStart="@dimen/dp2"
            android:layout_marginStart="@dimen/dp4"
            tools:visibility="visible"
            android:background="@drawable/background_blackish_grey_4dp"
            >

            <ImageView
                android:id="@+id/imgDot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/dp2"
                app:srcCompat="@drawable/ic_red_dot" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/rec"
                android:gravity="center_vertical"
                android:paddingEnd="@dimen/dp2"
                android:textColor="@color/white"
                android:textAllCaps="true"
                android:textSize="@dimen/sp12"
                android:layout_marginStart="@dimen/dp3"
                android:fontFamily="@font/roboto_bold" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/imgVipBadge"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:layout_gravity="center_vertical"
            tools:visibility="visible"
            android:layout_marginStart="@dimen/dp4"
            >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                app:srcCompat="@drawable/ic_vip_badge"
                android:layout_gravity="center_vertical"
                />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp12"
        android:layout_marginTop="@dimen/dp4"
        android:fontFamily="@font/readix_pro_bold"
        android:maxLines="1"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        android:includeFontPadding="false"
        app:layout_constraintStart_toStartOf="@+id/scheduleLayout"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintTop_toBottomOf="@+id/scheduleLayout"
        tools:text="Musuh Masyarakat"/>

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:fontFamily="@font/readex_pro"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintStart_toStartOf="@+id/title"
        app:layout_goneMarginTop="@dimen/dp2"
        tools:text="Coki Pardede, Tretan Muslim" />

    <ImageView
        android:id="@+id/cross"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginEnd="@dimen/dp5"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp10"
        android:src="@drawable/ic_close_without_square"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/white10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/subTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>