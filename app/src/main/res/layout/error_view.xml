<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/errorViewLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/overlay">

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:indeterminateTint="@color/orange_yellow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noInternetLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:background="@color/black700"
        android:clickable="true"
        android:focusable="true"
        android:paddingBottom="@dimen/dp24"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/noInternetImage"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginBottom="@dimen/dp24"
            android:contentDescription="@string/app_name"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toTopOf="@id/noInternetText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_error_no_signal" />

        <TextView
            android:id="@+id/noInternetText"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal"
            android:text="@string/no_internet_connection"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/noInternetMsg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/noInternetMsg"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal"
            android:text="@string/no_internet_msg"
            android:textColor="@color/white80"
            android:textSize="@dimen/sp16"
            android:textStyle="normal"
            app:layout_constraintBottom_toTopOf="@id/returnBtn"
            app:layout_constraintEnd_toEndOf="@id/noInternetText"
            app:layout_constraintStart_toStartOf="@id/noInternetText" />

        <TextView
            android:id="@+id/returnBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp24"
            android:background="@drawable/selector_yellow_btn"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp16"
            android:text="@string/returned"
            android:textAllCaps="true"
            android:textColor="@color/blackish_blue"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/goToDownloads"
            app:layout_constraintEnd_toEndOf="@id/noInternetText"
            app:layout_constraintStart_toStartOf="@id/noInternetText" />

        <TextView
            android:id="@+id/goToDownloads"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:background="@drawable/border_dull_yellow_dark_radius_4dp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp16"
            android:text="@string/lihat_download"
            android:textAllCaps="true"
            android:textColor="@color/dull_yellow_dark"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/noInternetText"
            app:layout_constraintStart_toStartOf="@id/noInternetText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/errorLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:background="@color/black700"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical"
        android:paddingBottom="@dimen/dp24"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/errorImage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp10"
            android:layout_marginBottom="@dimen/dp24"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_error"
            app:layout_constraintBottom_toTopOf="@+id/errorText"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/errorText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal"
            android:text="@string/ouch_error"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/errorMsg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/errorMsg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal"
            android:text="@string/something_is_wrong"
            android:textColor="@color/white80"
            android:textSize="@dimen/sp16"
            android:textStyle="normal"
            app:layout_constraintBottom_toTopOf="@+id/errorReturnBtn"
            app:layout_constraintEnd_toEndOf="@id/errorText"
            app:layout_constraintStart_toStartOf="@id/errorText" />

        <TextView
            android:id="@+id/errorReturnBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp24"
            android:background="@drawable/selector_yellow_btn"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp16"
            android:text="@string/returned"
            android:textAllCaps="true"
            android:textColor="@color/blackish_blue"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/returnToHome"
            app:layout_constraintEnd_toEndOf="@id/errorText"
            app:layout_constraintStart_toStartOf="@id/errorText" />

        <TextView
            android:id="@+id/returnToHome"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:background="@drawable/border_dull_yellow_dark_radius_4dp"
            android:clickable="true"
            android:focusable="true"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:padding="@dimen/dp16"
            android:text="@string/return_to_home"
            android:textAllCaps="true"
            android:textColor="@color/orange_yellow"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/errorText"
            app:layout_constraintStart_toStartOf="@id/errorText" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.faltenreich.skeletonlayout.SkeletonLayout
        android:id="@+id/skeletonLoader"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black700"
        android:visibility="gone"
        app:maskColor="@color/skeleton_color"
        app:shimmerColor="@color/skeleton_shimmer"
        app:shimmerDurationInMillis="1000" />
</androidx.constraintlayout.widget.ConstraintLayout>