<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="title"
            type="String" />

        <variable
            name="drawable"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="description"
            type="String" />
    </data>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp32"
            android:layout_marginTop="@dimen/dp32"
            android:layout_marginEnd="@dimen/dp32"
            android:adjustViewBounds="true"
            android:src="@{drawable}"
            tools:src="@drawable/ic_blocked_empty" />

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp32"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp32"
            android:fontFamily="@font/roboto_bold"
            android:text="@{title}"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            tools:text="@string/empty_blocked_users_title" />


        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/dp32"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp32"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="@dimen/dp6"
            android:text="@{description}"
            android:textAlignment="center"
            android:textColor="@color/white80"
            android:textSize="@dimen/sp14"
            tools:text="@string/empty_blocked_users_description" />

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>