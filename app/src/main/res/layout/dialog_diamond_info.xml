<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_rounded_black700_24dp">

        <View
            android:id="@+id/line"
            android:layout_width="@dimen/dp86"
            android:layout_height="@dimen/dp4"
            android:layout_marginTop="@dimen/dp16"
            android:background="@color/color_sheet_drag_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            app:fontFamily="@font/roboto"
            android:text="@string/tutup"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp14"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:text="@string/diamond"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/ic_diamond_hand"
            android:layout_marginTop="@dimen/dp27"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtDescription"
            app:textHtml="@{@string/diamond_earned_description}"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp34"
            android:layout_marginEnd="@dimen/dp24"
            android:textAlignment="center"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp14"
            app:fontFamily="@font/roboto"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/img"
            tools:text="@string/diamond_earned_description" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnOkay"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp48"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp32"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp16"
            android:backgroundTint="@color/dull_yellow"
            android:insetLeft="@dimen/size_0dp"
            android:insetTop="@dimen/size_0dp"
            android:insetRight="@dimen/size_0dp"
            android:insetBottom="@dimen/size_0dp"
            android:letterSpacing=".2"
            android:minWidth="@dimen/size_0dp"
            android:minHeight="@dimen/size_0dp"
            android:paddingStart="@dimen/dp24"
            android:paddingTop="@dimen/dp9"
            android:paddingEnd="@dimen/dp24"
            android:paddingBottom="@dimen/dp9"
            android:text="@string/okay"
            android:textColor="@color/blackish_blue"
            android:textSize="@dimen/sp12"
            app:cornerRadius="@dimen/dp4"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtDescription" />



        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>