<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/forYouSkeleton"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black700">

    <View
        android:id="@+id/v0"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp40"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v0"/>

    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp28"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp28"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp28"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp80"
        android:layout_height="@dimen/dp28"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v4"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v6"
        android:layout_width="@dimen/dp300"
        android:layout_height="@dimen/dp128"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v1"/>

    <View
        android:id="@+id/v7"
        android:layout_width="@dimen/dp300"
        android:layout_height="@dimen/dp128"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v6"
        app:layout_constraintTop_toTopOf="@id/v6"/>

    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/dp8"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v7"
        app:layout_constraintEnd_toStartOf="@id/v9"/>

    <View
        android:id="@+id/v9"
        android:layout_width="@dimen/dp8"
        android:layout_height="@dimen/dp8"
        android:layout_marginStart="@dimen/dp4"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v8"
        app:layout_constraintTop_toTopOf="@id/v8"
        app:layout_constraintEnd_toStartOf="@id/v10"/>

    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/dp8"
        android:layout_height="@dimen/dp8"
        android:layout_marginStart="@dimen/dp4"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v9"
        app:layout_constraintTop_toTopOf="@id/v8"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/v11"
        android:layout_width="@dimen/dp144"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v8"/>

    <View
        android:id="@+id/v12"
        android:layout_width="@dimen/dp78"
        android:layout_height="@dimen/dp20"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v11"/>

    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v12"/>

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/dp96"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v13"/>

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v14"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/dp53"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v15"/>

    <View
        android:id="@+id/v17"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="8dp"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v13"
        app:layout_constraintTop_toTopOf="@id/v13"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp96"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintTop_toBottomOf="@id/v17"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintTop_toBottomOf="@id/v18"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp53"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintTop_toBottomOf="@id/v19"/>

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="8dp"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v17"
        app:layout_constraintTop_toTopOf="@id/v17"/>

    <View
        android:id="@+id/v22"
        android:layout_width="@dimen/dp96"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintTop_toBottomOf="@id/v21"/>

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintTop_toBottomOf="@id/v22"/>

    <View
        android:id="@+id/v24"
        android:layout_width="@dimen/dp53"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintTop_toBottomOf="@id/v23"/>

    <View
        android:id="@+id/v25"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="8dp"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v21"
        app:layout_constraintTop_toTopOf="@id/v17"/>

    <View
        android:id="@+id/v26"
        android:layout_width="@dimen/dp96"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@id/v25"/>

    <View
        android:id="@+id/v27"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@id/v26"/>

    <View
        android:id="@+id/v28"
        android:layout_width="@dimen/dp53"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@id/v27"/>

    <View
        android:id="@+id/v29"
        android:layout_width="@dimen/dp144"
        android:layout_height="@dimen/dp28"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v16"/>

    <View
        android:id="@+id/v30"
        android:layout_width="@dimen/dp78"
        android:layout_height="@dimen/dp20"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v29"/>

    <View
        android:id="@+id/v31"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v29"/>

    <View
        android:id="@+id/v32"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v31"
        app:layout_constraintTop_toTopOf="@id/v31"/>

    <View
        android:id="@+id/v33"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v32"
        app:layout_constraintTop_toTopOf="@id/v31"/>

    <View
        android:id="@+id/v34"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp112"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toEndOf="@id/v33"
        app:layout_constraintTop_toTopOf="@id/v31"/>
</androidx.constraintlayout.widget.ConstraintLayout>