<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="@dimen/dp8"
    app:cardElevation="@dimen/dp5"
    app:cardBackgroundColor="@color/black500"
    android:background="@drawable/rounded_dialog_bg">
    <FrameLayout
        android:id="@+id/adFrame"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        />
</androidx.cardview.widget.CardView>