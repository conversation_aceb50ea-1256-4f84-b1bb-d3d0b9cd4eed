<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/black700"
    tools:context=".modules.onboarding.fragments.ChooseGenreFragment">

    <FrameLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_alignParentTop="true">

        <TextView
            android:id="@+id/skipButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center"
            android:fontFamily="@font/readex_pro"
            android:includeFontPadding="false"
            android:gravity="center"
            android:letterSpacing="0.4"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            android:paddingVertical="@dimen/dp8"
            android:background="@drawable/custom_ripple_bg"
            android:text="@string/skip"
            android:textAllCaps="true"
            android:textColor="@color/grey"
            android:textSize="@dimen/sp12"
            android:textStyle="bold" />
    </FrameLayout>

    <TextView
        android:id="@+id/heading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/toolbar"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/sp5"
        android:text="@string/choose_3_genre"
        android:textColor="@color/white"
        android:textSize="@dimen/sp24"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/subHeading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/heading"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:gravity="center"
        android:text="@string/choose_genre_desc"
        android:textColor="@color/greyText"
        android:textSize="@dimen/sp14"
        android:textStyle="normal" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/genreRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@+id/subHeading"
        android:layout_alignParentBottom="true"
        android:layout_marginTop="@dimen/dp10"
        android:clipToPadding="false"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:paddingBottom="@dimen/dp50" />

    <View
        android:id="@+id/view_shadow_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp80"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shadow_gradient_bottom" />

    <TextView
        android:id="@+id/continueBtn"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp48"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_gravity="center"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16"
        android:background="@drawable/selector_yellow_button"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:paddingStart="@dimen/dp38"
        android:paddingEnd="@dimen/dp38"
        android:text="@string/continue_txt"
        android:textAllCaps="true"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp12"
        android:textStyle="bold" />

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@+id/subHeading"
        android:layout_alignParentBottom="true" />
</RelativeLayout>