<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/readex_pro_light"
        android:paddingTop="@dimen/dp16"
        android:paddingBottom="@dimen/dp16"
        android:text="Lorem Ipsum"
        android:textColor="@color/white"
        android:textSize="@dimen/dp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/dp16"
        android:layout_height="@dimen/dp16"
        app:layout_constraintBottom_toBottomOf="@+id/txtTitle"
        android:src="@drawable/ic_arrow_right_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/txtTitle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        app:layout_constraintTop_toBottomOf="@id/txtTitle"
        android:background="@color/white_12"/>

</androidx.constraintlayout.widget.ConstraintLayout>