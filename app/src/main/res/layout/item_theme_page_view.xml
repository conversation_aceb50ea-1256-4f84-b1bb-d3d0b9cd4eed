<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/dp6"
    app:cardBackgroundColor="@android:color/transparent"
    android:foreground="@drawable/custom_ripple_bg_4dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/image_theme"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"/>
</androidx.cardview.widget.CardView>