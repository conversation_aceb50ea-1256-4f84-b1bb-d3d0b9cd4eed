<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24"
    tools:context=".modules.live.fragment.UpdateDialog">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/cancelBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginTop="@dimen/dp32"
        android:paddingStart="@dimen/dp4"
        android:paddingEnd="@dimen/dp4"
        android:paddingTop="@dimen/dp2"
        android:paddingBottom="@dimen/dp2"
        android:background="@drawable/custom_ripple"
        android:fontFamily="sans-serif"
        android:text="@string/tutup"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp32"
        android:drawablePadding="@dimen/dp3"
        android:fontFamily="sans-serif"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        android:text="@string/ada_noice_v" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgRocket"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_update_image"
        android:layout_marginTop="@dimen/dp24"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/banyak_fitu"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:textColor="@color/white"
        android:fontFamily="@font/roboto"
        android:layout_marginEnd="@dimen/dp32"
        android:layout_marginStart="@dimen/dp32"
        android:textSize="@dimen/sp14"
        app:layout_constraintTop_toBottomOf="@+id/imgRocket"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="@string/force_update_description"
        />
    <TextView
        android:id="@+id/submitButton"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp25"
        android:background="@drawable/background_yellow_radius_4dp"
        android:fontFamily="@font/roboto_bold"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:text="@string/update"
        android:textAllCaps="true"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp14"
        android:layout_marginStart="@dimen/dp32"
        android:layout_marginEnd="@dimen/dp32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/banyak_fitu" />

    <TextView
        android:id="@+id/txtNanti"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/submitButton"
        app:layout_constraintEnd_toEndOf="@+id/submitButton"
       app:layout_constraintTop_toBottomOf="@+id/submitButton"
        android:layout_marginTop="@dimen/dp20"
        android:textColor="@color/dull_yellow"
        android:padding="@dimen/dp4"
        android:visibility="gone"
        android:gravity="center_horizontal"
        android:textSize="@dimen/sp16"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/later"
        />
</androidx.constraintlayout.widget.ConstraintLayout>