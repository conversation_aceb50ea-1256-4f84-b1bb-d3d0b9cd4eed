<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="noice.app.modules.coins.model.SubscriptionPackage.SubscriptionBenefit" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UseCompoundDrawables">

        <ImageView
            android:layout_width="@dimen/dp18"
            android:layout_height="@dimen/dp18"
            app:loadBenefitIcon="@{item}"
            android:src="@drawable/content_download_sub"
            android:contentDescription="@string/app_name"/>

        <com.google.android.material.textview.MaterialTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            app:textHtml="@{item.desc}"
            app:linkUnderline="@{false}"
            tools:text="@string/content_can_be_downloaded"
            android:textSize="@dimen/sp12"
            android:textColor="@color/dull_white"
            android:textColorLink="@color/dull_yellow"
            android:letterSpacing="0.04"
            android:lineSpacingExtra="0sp"
            android:fontFamily="@font/readex_pro"/>
    </LinearLayout>
</layout>