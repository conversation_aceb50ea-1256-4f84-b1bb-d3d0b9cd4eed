<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".modules.onboarding.fragments.FeedbackFragment">

    <TextView
        android:id="@+id/headingText"
        android:layout_width="@dimen/size_0dp"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp40"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@color/white"
        android:fontFamily="@font/roboto"
        android:textStyle="bold"
        android:textSize="@dimen/sp18"
        android:text="@string/account_deleted_successfully"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:id="@+id/feedbackIllustration"
        android:layout_width="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp40"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_feedback_illustration"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/headingText"
        app:layout_constraintEnd_toEndOf="@id/headingText"
        app:layout_constraintTop_toBottomOf="@id/headingText"/>

    <TextView
        android:id="@+id/feedback1"
        android:layout_width="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp16"
        android:layout_height="wrap_content"
        android:textColor="@color/white_600"
        android:fontFamily="@font/roboto"
        android:textStyle="normal"
        android:textSize="@dimen/sp14"
        android:text="@string/if_there_is_a_well_in_the_farm"
        app:layout_constraintStart_toStartOf="@id/headingText"
        app:layout_constraintEnd_toEndOf="@id/headingText"
        app:layout_constraintTop_toBottomOf="@id/feedbackIllustration"/>

    <TextView
        android:id="@+id/mailForHelp"
        android:layout_width="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp16"
        android:layout_height="wrap_content"
        android:textColor="@color/white_600"
        android:fontFamily="@font/roboto"
        android:textStyle="normal"
        android:textSize="@dimen/sp14"
        android:text="@string/need_help_emailto_noice"
        app:layout_constraintStart_toStartOf="@id/headingText"
        app:layout_constraintEnd_toEndOf="@id/headingText"
        app:layout_constraintTop_toBottomOf="@id/feedback1"/>

    <TextView
        android:id="@+id/continueBtn"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:layout_gravity="center"
        android:background="@drawable/selector_yellow_button"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0.2"
        android:text="@string/done"
        android:textAllCaps="true"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>