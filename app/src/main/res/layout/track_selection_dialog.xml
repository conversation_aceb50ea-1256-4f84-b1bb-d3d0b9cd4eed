<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2018 The Android Open Source Project
     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
          http://www.apache.org/licenses/LICENSE-2.0
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/track_selection_dialog_view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/track_selection_dialog_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabGravity="fill"
            app:tabMode="fixed"/>

    </androidx.viewpager.widget.ViewPager>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end">

        <Button
            android:id="@+id/track_selection_dialog_cancel_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@android:string/cancel"
            android:foreground="@drawable/custom_ripple_grey"
            style="?android:attr/borderlessButtonStyle"/>

        <Button
            android:id="@+id/track_selection_dialog_ok_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@android:string/ok"
            android:foreground="@drawable/custom_ripple_grey"
            style="?android:attr/borderlessButtonStyle"/>

    </LinearLayout>

</LinearLayout>