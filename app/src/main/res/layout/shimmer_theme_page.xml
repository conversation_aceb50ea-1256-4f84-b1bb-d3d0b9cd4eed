<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/forYouSkeleton"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black700">

    <View
        android:id="@+id/v1"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp120"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/dp144"
        android:layout_height="@dimen/dp35"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v1" />

    <View
        android:id="@+id/v3"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v2" />

    <View
        android:id="@+id/v4"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp20"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v3" />

    <View
        android:id="@+id/v5"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp24"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v4" />

    <View
        android:id="@+id/v6"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v5" />

    <View
        android:id="@+id/v7"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp20"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v6" />

    <View
        android:id="@+id/v8"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp24"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v7" />

    <View
        android:id="@+id/v9"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/v8" />

</androidx.constraintlayout.widget.ConstraintLayout>