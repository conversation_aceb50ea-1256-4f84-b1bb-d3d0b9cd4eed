<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/v1"
        android:layout_width="@dimen/size_0dp"
        android:background="@android:color/transparent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/v2"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v1"/>

    <View
        android:id="@+id/v17"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v16"/>

    <ImageView
        android:id="@+id/v2"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintTop_toTopOf="@id/v1"
        app:layout_constraintEnd_toStartOf="@id/v3"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v2"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v18"/>

    <ImageView
        android:id="@+id/v3"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp16"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v1"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v3"/>

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toBottomOf="@id/v20"/>

    <ImageView
        android:id="@+id/v4"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v17"/>

    <View
        android:id="@+id/v22"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v4"
        app:layout_constraintEnd_toEndOf="@id/v4"
        app:layout_constraintTop_toBottomOf="@id/v4"/>

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v4"
        app:layout_constraintEnd_toEndOf="@id/v4"
        app:layout_constraintTop_toBottomOf="@id/v22"/>

    <ImageView
        android:id="@+id/v5"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v4"/>

    <View
        android:id="@+id/v24"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v5"
        app:layout_constraintEnd_toEndOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@id/v5"/>

    <View
        android:id="@+id/v25"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v5"
        app:layout_constraintEnd_toEndOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@id/v24"/>

    <ImageView
        android:id="@+id/v6"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v4"/>

    <View
        android:id="@+id/v26"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintEnd_toEndOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v6"/>

    <View
        android:id="@+id/v27"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v6"
        app:layout_constraintEnd_toEndOf="@id/v6"
        app:layout_constraintTop_toBottomOf="@id/v26"/>

    <ImageView
        android:id="@+id/v7"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v23"/>

    <View
        android:id="@+id/v28"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v7"
        app:layout_constraintEnd_toEndOf="@id/v7"
        app:layout_constraintTop_toBottomOf="@id/v7"/>

    <View
        android:id="@+id/v29"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v7"
        app:layout_constraintEnd_toEndOf="@id/v7"
        app:layout_constraintTop_toBottomOf="@id/v28"/>

    <ImageView
        android:id="@+id/v8"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v7"/>

    <View
        android:id="@+id/v30"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v8"
        app:layout_constraintEnd_toEndOf="@id/v8"
        app:layout_constraintTop_toBottomOf="@id/v8"/>

    <View
        android:id="@+id/v31"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v8"
        app:layout_constraintEnd_toEndOf="@id/v8"
        app:layout_constraintTop_toBottomOf="@id/v30"/>

    <ImageView
        android:id="@+id/v9"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v7"/>

    <View
        android:id="@+id/v32"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v9"
        app:layout_constraintEnd_toEndOf="@id/v9"
        app:layout_constraintTop_toBottomOf="@id/v9"/>

    <View
        android:id="@+id/v33"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v9"
        app:layout_constraintEnd_toEndOf="@id/v9"
        app:layout_constraintTop_toBottomOf="@id/v32"/>

    <ImageView
        android:id="@+id/v10"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v29"/>

    <View
        android:id="@+id/v34"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintEnd_toEndOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v10"/>

    <View
        android:id="@+id/v35"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v10"
        app:layout_constraintEnd_toEndOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@id/v34"/>

    <ImageView
        android:id="@+id/v11"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v10"/>

    <View
        android:id="@+id/v36"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v11"
        app:layout_constraintEnd_toEndOf="@id/v11"
        app:layout_constraintTop_toBottomOf="@id/v11"/>

    <View
        android:id="@+id/v37"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v11"
        app:layout_constraintEnd_toEndOf="@id/v11"
        app:layout_constraintTop_toBottomOf="@id/v36"/>

    <ImageView
        android:id="@+id/v12"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v10"/>

    <View
        android:id="@+id/v38"
        android:layout_width="@dimen/dp91"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v12"
        app:layout_constraintEnd_toEndOf="@id/v12"
        app:layout_constraintTop_toBottomOf="@id/v12"/>

    <View
        android:id="@+id/v39"
        android:layout_width="@dimen/dp71"
        android:layout_height="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v12"
        app:layout_constraintEnd_toEndOf="@id/v12"
        app:layout_constraintTop_toBottomOf="@id/v38"/>

    <ImageView
        android:id="@+id/v13"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintEnd_toEndOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v35"/>

    <ImageView
        android:id="@+id/v14"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v13"/>

    <ImageView
        android:id="@+id/v15"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_artist_skeleton_object"
        app:layout_constraintStart_toStartOf="@id/v3"
        app:layout_constraintEnd_toEndOf="@id/v3"
        app:layout_constraintTop_toTopOf="@id/v13"/>
</androidx.constraintlayout.widget.ConstraintLayout>