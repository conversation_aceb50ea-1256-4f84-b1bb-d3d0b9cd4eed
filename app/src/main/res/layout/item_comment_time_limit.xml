<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/playlistViewLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/home_search"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingStart="@dimen/dp8"
    android:paddingTop="@dimen/dp10"
    android:paddingEnd="@dimen/dp8"
    android:paddingBottom="@dimen/dp10">

    <TextView
        android:id="@+id/text_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="6sp"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        tools:text="15 Detik" />

    <ImageView
        android:id="@+id/image_selected_tick"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_tick_green"
        android:visibility="gone" />
</RelativeLayout>