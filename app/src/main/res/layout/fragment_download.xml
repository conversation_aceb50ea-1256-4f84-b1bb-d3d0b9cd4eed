<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    tools:context=".modules.dashboard.collection.fragment.DownloadFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"
        app:toolbarViewTitle="@string/download" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/restoreLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:background="@drawable/background_blackish_grey_8dp"
                android:paddingBottom="@dimen/dp16"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgDownload"
                    android:layout_width="@dimen/dp36"
                    android:layout_height="@dimen/dp36"
                    android:layout_margin="@dimen/dp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_download_black" />
                <!-- Mau re-download 12 konten sebelumnya? -->
                <TextView
                    android:id="@+id/txtHeading"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp16"
                    android:fontFamily="@font/readix_pro_bold"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/imgDownload"
                    tools:text="Mau re-download 12 konten sebelumnya?" />

                <TextView
                    android:id="@+id/txtDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp8"
                    android:layout_marginEnd="@dimen/dp16"
                    android:fontFamily="@font/readex_pro"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal"
                    android:text="@string/restore_download_description"
                    android:textColor="#8F8F8F"
                    android:textSize="@dimen/sp14"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtHeading" />

                <TextView
                    android:id="@+id/txtCount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp3"
                    android:layout_marginEnd="@dimen/dp16"
                    android:fontFamily="@font/readex_pro"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal"
                    android:textColor="#8F8F8F"
                    android:textSize="@dimen/sp14"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtDescription"
                    tools:text="1 of 9 is downloading..." />


                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/txtCount">

                    <TextView
                        android:id="@+id/txtMau"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/custom_ripple"
                        android:fontFamily="@font/readix_pro_bold"
                        android:includeFontPadding="false"
                        android:gravity="center_horizontal"
                        android:letterSpacing="0.2"
                        android:padding="@dimen/dp8"
                        android:text="@string/want"
                        android:textAllCaps="true"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp14" />

                    <TextView
                        android:id="@+id/txtGak"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp32"
                        android:background="@drawable/custom_ripple"
                        android:fontFamily="@font/readix_pro_bold"
                        android:includeFontPadding="false"
                        android:gravity="center_horizontal"
                        android:letterSpacing="0.2"
                        android:padding="@dimen/dp8"
                        android:text="@string/no_need_2"
                        android:textAllCaps="true"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp14" />


                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/downloadRecycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/emptyView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp28"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_bg_emptystate" />

                    <ImageView
                        android:id="@+id/iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/dp59"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_download_empty"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/dp16"
                        android:fontFamily="@font/readex_pro"
                        android:includeFontPadding="false"
                        android:text="@string/download_empty"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp18"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/iv" />

                    <TextView
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="@dimen/dp24"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp24"
                        android:fontFamily="@font/readex_pro"
                        android:includeFontPadding="false"
                        android:gravity="center"
                        android:text="@string/download_empty_text"
                        android:textColor="@color/white80"
                        android:textSize="@dimen/sp16"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</androidx.constraintlayout.widget.ConstraintLayout>