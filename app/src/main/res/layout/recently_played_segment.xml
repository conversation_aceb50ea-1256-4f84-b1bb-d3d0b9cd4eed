<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rvQueue"
    android:layout_width="@dimen/dp130"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:background="@color/black">

    <noice.app.views.SquareCardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp112"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <FrameLayout
            android:id="@+id/audioBookLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/coverBackgroundImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:contentDescription="@string/app_name"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black60"/>

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp6"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp">

                <ImageView
                    android:id="@+id/coverImageAudioBook"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>
            </androidx.cardview.widget.CardView>
        </FrameLayout>

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"/>

        <FrameLayout
            android:id="@+id/playButtonLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/playButton"
                android:layout_width="@dimen/dp52"
                android:layout_height="@dimen/dp52"
                android:layout_gravity="center"
                android:background="@drawable/custom_ripple_bg_circle"
                android:contentDescription="@string/app_name"
                app:srcCompat="@drawable/ic_play_white_transgrey" />

            <View
                android:id="@+id/playButtonClickArea"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/transparent" />
        </FrameLayout>

        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="@dimen/dp56"
            android:layout_height="@dimen/dp56"
            android:layout_gravity="center"
            android:visibility="gone"
            android:indeterminateTint="@color/dull_yellow"
            app:layout_constraintStart_toStartOf="@id/actionButton"
            app:layout_constraintEnd_toEndOf="@id/actionButton"
            app:layout_constraintTop_toTopOf="@id/actionButton"
            app:layout_constraintBottom_toBottomOf="@id/actionButton"/>

        <FrameLayout
            android:id="@+id/relative_equalizer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black30">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/equalizer"
                android:layout_width="@dimen/dp48"
                android:layout_height="@dimen/dp48"
                android:layout_gravity="center"
                android:contentDescription="@string/app_name"
                app:srcCompat="@drawable/equalizer"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </FrameLayout>

        <ImageView
            android:id="@+id/origExc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            tools:src="@drawable/ic_premium"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="4dp"
            android:contentDescription="@string/original" />
    </noice.app.views.SquareCardView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp3"
        android:layout_marginTop="@dimen/dp8"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        tools:progress="70"
        android:progressDrawable="@drawable/custom_progress_drawable"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:maxLines="2"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:fontFamily="@font/readix_pro_bold"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="Eps  9: Berdoa Tidak Ada Guna..." />

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp12"
        android:textColor="@color/neutral_80"
        android:lineSpacingExtra="-3.6sp"
        tools:text="Musuh Masyarakat"/>
</LinearLayout>