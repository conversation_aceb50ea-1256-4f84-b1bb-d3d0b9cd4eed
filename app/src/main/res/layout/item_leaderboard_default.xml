<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.utils.HandleValues"/>
        <import type="noice.app.utils.Constants"/>
        <variable
            name="viewModel"
            type="noice.app.modules.live.stream.feature.leaderboard.LeaderboardViewModel" />
        <variable
            name="item"
            type="noice.app.modules.live.model.leaderboard.LeaderBoardItemViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:onClick="@{()-> viewModel.onNavigateToProfile(item.userId, Constants.GIFT_LEADERBOARD_PAGE)}">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_badge_rank_one"
            android:gravity="center"
            tools:text="1"
            android:text="@{HandleValues.getHyphenForNull(item.self &amp;&amp; item.rank > 99 ? item.rank.toString() + `+` : item.rank.toString())}"
            handleBadgeBackgroundForRank="@{item.rank}"
            android:textColor="@color/color_rank_text"
            android:textSize="@dimen/sp12"
            app:fontFamily="@font/roboto_bold"
            android:layout_marginStart="@dimen/dp20"
            app:layout_constraintBottom_toBottomOf="@+id/imgProfile"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imgProfile" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imgProfile"
            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.Round"
            android:layout_width="@dimen/dp36"
            android:layout_height="@dimen/dp36"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginBottom="@dimen/dp4"
            app:srcUrl="@{item.profilePic}"
            app:circleCrop="@{true}"
            app:placeholder="@{@drawable/ic_user_profile}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/txtBadge"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_user_profile" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintHorizontal_bias="0"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp8"
            tools:text="Florentia"
            android:text="@{item.userName}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            app:fontFamily="@font/roboto"
            app:layout_constraintBottom_toBottomOf="@+id/imgProfile"
            app:layout_constraintEnd_toStartOf="@+id/txtCoins"
            app:layout_constraintStart_toEndOf="@+id/imgProfile"
            app:layout_constraintTop_toTopOf="@+id/imgProfile" />


        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtLabelSelf"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:text="@{` `+ @string/self_label}"
            tools:text=" (Kamu)"
            android:visibility="@{item.self ? View.VISIBLE : View.GONE}"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp12"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintBottom_toBottomOf="@+id/imgProfile"
            app:layout_constraintEnd_toStartOf="@+id/txtCoins"
            app:layout_constraintStart_toEndOf="@+id/txtName"
            app:layout_constraintTop_toTopOf="@+id/imgProfile" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtCoins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp24"
            android:drawableStart="@drawable/ic_coins"
            android:drawablePadding="@dimen/dp4"
            android:foregroundGravity="center"
            android:gravity="center"
            android:includeFontPadding="false"
            tools:text="12.400.000"
            android:text="@{HandleValues.convertNumberToLocaleIndonesia(item.coins.toString())}"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp16"
            app:fontFamily="@font/roboto"
            app:layout_constraintBaseline_toBaselineOf="@+id/txtName"
            app:layout_constraintEnd_toEndOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>