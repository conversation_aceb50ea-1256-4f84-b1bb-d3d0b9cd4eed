<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/customPlayerView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:paddingBottom="@dimen/dp16">

    <TextView
        android:id="@+id/txtPreview"
        android:layout_width="@dimen/dp90"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp6"
        android:background="@drawable/background_white_22dp"
        android:fontFamily="@font/readex_pro"
        android:gravity="center"
        android:paddingVertical="@dimen/dp3"
        android:textColor="@color/black"
        android:textSize="@dimen/sp11"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/exo_progress"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="04:59 tersisa"
        tools:visibility="visible" />


    <TextView
        android:id="@id/exo_position"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="4.8sp"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1:11" />

    <TextView
        android:id="@id/txtDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp24"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="4.8sp"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/exo_position"
        tools:text="-32:44" />


    <TextView
        android:id="@id/exo_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="4.8sp"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/exo_position"
        tools:text="-32:44" />

    <noice.app.CustomTimeBar
        android:id="@id/exo_progress"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp18"
        android:layout_marginHorizontal="@dimen/dp17"
        app:ad_marker_color="@color/transparent"
        app:bar_height="@dimen/dp4"
        app:buffered_color="@color/white10"
        app:layout_constraintTop_toBottomOf="@id/exo_position"
        app:played_ad_marker_color="@color/transparent"
        app:played_color="@color/white"
        app:unplayed_color="@color/white20" />

    <FrameLayout
        android:id="@+id/btnLayout"
        android:layout_width="@dimen/dp57"
        android:layout_height="@dimen/dp57"
        android:layout_marginTop="@dimen/dp24"
        android:background="@drawable/circuler_white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/exo_progress">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@id/exo_play_pause"
            android:layout_width="@dimen/dp56"
            android:layout_height="@dimen/dp56"
            android:layout_gravity="center"
            android:adjustViewBounds="true"
            android:background="@null"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_black_play" />

        <!--        <androidx.appcompat.widget.AppCompatImageButton-->
        <!--            android:id="@id/exo_pause"-->
        <!--            android:layout_width="@dimen/dp56"-->
        <!--            android:layout_height="@dimen/dp56"-->
        <!--            android:layout_gravity="center"-->
        <!--            android:contentDescription="@string/app_name"-->
        <!--            app:srcCompat="@drawable/ic_pause_grey_white"-->
        <!--            android:visibility="gone"-->
        <!--            android:background="@null" />-->

        <View
            android:id="@+id/preClickPlayPause"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"
            android:foreground="@drawable/circuler_white_transparent" />

        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:indeterminateTint="@color/black"
            android:visibility="gone" />
    </FrameLayout>

    <!--    prev for vertical video only-->
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/custom_prev"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp50"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/custom_ripple_bg"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp8"
        android:scaleType="fitCenter"
        android:tintMode="src_in"
        app:layout_constraintBottom_toBottomOf="@id/btnLayout"
        app:layout_constraintStart_toStartOf="@id/exo_progress"
        app:layout_constraintTop_toTopOf="@id/btnLayout"
        app:srcCompat="@drawable/ic_previous_white" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/exo_rew"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp50"
        android:background="@drawable/custom_ripple_bg"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp10"
        android:scaleType="fitCenter"
        android:tintMode="src_in"
        app:layout_constraintBottom_toBottomOf="@id/btnLayout"
        app:layout_constraintEnd_toStartOf="@id/btnLayout"
        app:layout_constraintStart_toEndOf="@id/custom_prev"
        app:layout_constraintTop_toTopOf="@id/btnLayout"
        app:srcCompat="@drawable/ic_backward_10s" />

    <!--    next for vertical video only-->
    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@id/exo_next"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp50"
        android:layout_marginEnd="@dimen/dp8"
        android:background="@drawable/custom_ripple_bg"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp8"
        android:scaleType="fitCenter"
        android:tintMode="src_in"
        app:layout_constraintBottom_toBottomOf="@id/btnLayout"
        app:layout_constraintEnd_toEndOf="@id/exo_progress"
        app:layout_constraintTop_toTopOf="@id/btnLayout"
        app:srcCompat="@drawable/ic_next_white"
        app:tint="@color/white" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@id/exo_ffwd"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp50"
        android:background="@drawable/custom_ripple_bg"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp10"
        android:scaleType="fitCenter"
        android:tintMode="src_in"
        app:layout_constraintBottom_toBottomOf="@id/btnLayout"
        app:layout_constraintEnd_toStartOf="@id/exo_next"
        app:layout_constraintStart_toEndOf="@id/btnLayout"
        app:layout_constraintTop_toTopOf="@id/btnLayout"
        app:srcCompat="@drawable/ic_forward_10s" />

</androidx.constraintlayout.widget.ConstraintLayout>