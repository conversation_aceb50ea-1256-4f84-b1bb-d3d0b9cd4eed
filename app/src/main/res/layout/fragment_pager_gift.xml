<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewState"
            type="noice.app.modules.live.model.gift.viewstate.GiftViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutGifts"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp350">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerview"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_0dp"
            android:layout_marginTop="@dimen/dp8"
            android:clipToPadding="false"
            android:nestedScrollingEnabled="true"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginBottom="@dimen/dp8"
            app:spanCount="4"
            tools:listitem="@layout/item_gift" />

        <ProgressBar
            android:id="@+id/progressbar_gift"
            android:layout_width="@dimen/dp25"
            android:layout_height="@dimen/dp25"
            android:indeterminateTint="@color/dull_yellow"
            app:layout_constraintBottom_toBottomOf="@id/recyclerview"
            app:layout_constraintEnd_toEndOf="@id/recyclerview"
            app:layout_constraintStart_toStartOf="@id/recyclerview"
            app:layout_constraintTop_toTopOf="@id/recyclerview"
            app:visibleIf="@{viewState.loading &amp;&amp; viewState.items.empty}" />

        <include
            android:id="@+id/layout_empty"
            layout="@layout/layout_gift_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:drawable="@{@drawable/ic_gift_error}"
            app:label="@{@string/text_gift_load_error}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="parent"
            app:layout_constraintStart_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visible="@{false}"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>