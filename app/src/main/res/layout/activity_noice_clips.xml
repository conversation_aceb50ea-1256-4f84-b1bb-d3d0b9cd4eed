<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:id="@+id/mainLayout"
    tools:context=".modules.clips.activities.ClipsActivity">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager_clips"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewShadowTop"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp170"
        android:background="@drawable/clip_shadow_gradient_top"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewShadowBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp250"
        android:background="@drawable/clip_shadow_gradient_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/linear_progress_bars"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/image_play"
        android:layout_width="@dimen/dp50"
        android:layout_height="@dimen/dp50"
        android:src="@drawable/ic_play_clip"
        android:visibility="gone"
        android:contentDescription="@string/app_name"
        app:layout_constraintBottom_toTopOf="@+id/label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_clip_title" />

    <FrameLayout
        android:id="@+id/frame_left"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toTopOf="@id/label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/frame_right"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toTopOf="@id/label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/image_cross"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginBottom="@dimen/dp4"
        android:contentDescription="@string/cross"
        android:padding="@dimen/dp8"
        android:src="@drawable/ic_close_without_square"
        android:background="@drawable/custom_ripple_bg_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/rssPremiumTag"
        app:layout_constraintTop_toBottomOf="@id/linear_progress_bars"/>

    <TextView
        android:id="@+id/rssPremiumTag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:contentDescription="@string/app_name"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="@font/readex_pro"
        android:gravity="center"
        android:paddingEnd="@dimen/dp4"
        android:shadowColor="@color/blackish_grey"
        android:shadowDx="2"
        android:letterSpacing="0.1"
        android:shadowDy="2"
        android:shadowRadius="3"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:textSize="@dimen/sp11"
        android:textStyle="bold"
        app:drawableStartCompat="@drawable/ic_vip_badge"
        app:layout_constraintBottom_toTopOf="@id/text_clip_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_cross"
        tools:text="PODCAST" />

    <TextView
        android:id="@+id/text_clip_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp16"
        android:fontFamily="sans-serif"
        android:textColor="@color/white"
        android:textSize="22sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rssPremiumTag"
        tools:text="Udahlah ga usah doa" />

    <ImageView
        android:id="@+id/imageMenu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:contentDescription="@string/cross"
        android:padding="@dimen/dp8"
        android:foreground="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_three_dots_ws"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/linear_progress_bars" />

    <TextView
        android:id="@+id/label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:paddingEnd="@dimen/dp4"
        android:shadowColor="@color/grey500"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="3"
        android:text="@string/clip_from"
        android:textColor="@color/white"
        android:textSize="@dimen/sp11"
        app:layout_constraintBottom_toTopOf="@id/clipPlayerView"
        app:layout_constraintStart_toStartOf="@id/clipPlayerView" />

    <noice.app.views.ClipPlayerView
        android:id="@+id/clipPlayerView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/dp25"
        android:layout_height="@dimen/dp25"
        android:visibility="gone"
        android:indeterminateTint="@color/orange_yellow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>