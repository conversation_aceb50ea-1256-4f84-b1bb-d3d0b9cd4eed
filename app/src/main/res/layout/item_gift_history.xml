<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.utils.HandleValues"/>
        <variable
            name="item"
            type="noice.app.modules.live.model.gift.history.viewstate.GiftHistoryItemViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imgProfile"
            app:circleCrop="@{true}"
            app:placeholder="@{@drawable/ic_user_profile}"
            app:srcUrl="@{item.item.sender.smallImage}"
            android:layout_width="@dimen/dp36"
            android:layout_height="@dimen/dp36"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp17"
            android:layout_marginBottom="@dimen/dp17"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.Round"
            app:srcCompat="@drawable/ic_user_profile" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtProfileName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp8"
            android:ellipsize="end"
            android:maxLines="1"
            android:includeFontPadding="false"
            app:layout_constraintHorizontal_bias="0"
            android:text="@{item.item.sender.userName}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:fontFamily="@font/roboto"
            app:layout_constraintEnd_toStartOf="@+id/appCompatImageView2"
            app:layout_constraintStart_toEndOf="@+id/imgProfile"
            app:layout_constraintTop_toTopOf="@+id/imgProfile"
            tools:text="MooreJeane" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img_verified"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp2"
            android:visibility="@{item.item.sender.isVerified ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@+id/txtProfileName"
            app:layout_constraintStart_toEndOf="@+id/txtProfileName"
            app:layout_constraintTop_toTopOf="@+id/txtProfileName"
            app:srcCompat="@drawable/ic_verified_tag" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtGiftName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{@string/sent_pre_text_string(item.item.gift.name)}"
            android:textColor="@color/white_900"
            android:textSize="@dimen/sp12"
            app:fontFamily="@font/roboto"
            app:layout_constraintBottom_toBottomOf="@+id/imgProfile"
            app:layout_constraintEnd_toStartOf="@+id/imgGift"
            app:layout_constraintStart_toEndOf="@+id/imgProfile"
            app:layout_constraintStart_toStartOf="@+id/txtProfileName"
            tools:text="Ngirim Shoeees" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgGift"
            app:placeholder="@{@drawable/ic_thumb_default}"
            app:srcUrl="@{item.item.gift.config.image.listUrl}"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            android:layout_marginStart="@dimen/dp4"
            android:src="@drawable/ic_thumb_default"
            app:layout_constraintBottom_toBottomOf="@+id/txtGiftName"
            app:layout_constraintStart_toEndOf="@+id/txtGiftName"
            app:layout_constraintTop_toTopOf="@+id/txtGiftName" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtGiftQuantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp4"
            android:fontFamily="@font/roboto_bold_italic"
            android:includeFontPadding="false"
            android:text="@{`x`+item.item.quantity}"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp14"
            android:visibility="@{HandleValues.isGreaterThanOne(item.item.quantity) ? View.VISIBLE : View.GONE}"
            app:layout_constraintBaseline_toBaselineOf="@+id/txtGiftName"
            app:layout_constraintStart_toEndOf="@+id/imgGift"
            tools:text="x99"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/appCompatImageView2"
            android:layout_width="@dimen/dp12"
            android:layout_height="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp4"
            app:layout_constraintBottom_toBottomOf="@+id/txtCoins"
            app:layout_constraintEnd_toStartOf="@+id/txtCoins"
            app:layout_constraintTop_toTopOf="@+id/txtCoins"
            app:srcCompat="@drawable/ic_coins" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtCoins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp24"
            android:foregroundGravity="center"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@{HandleValues.convertNumberToLocaleIndonesia(HandleValues.getMultiplicationOfTwoValues(item.item.gift.priceInCoins.toString(), item.item.quantity))}"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp16"
            app:fontFamily="@font/roboto"
            app:layout_constraintBottom_toBottomOf="@+id/imgProfile"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/imgProfile"
            tools:text="12.400.000" />

        <View
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp1"
            android:background="@color/black600"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/txtCoins"
            app:layout_constraintStart_toStartOf="@+id/imgProfile" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>