<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginHorizontal="@dimen/dp24"
    android:background="@drawable/rounded_dialog_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:minHeight="@dimen/dp270"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="vertical">

                <com.google.android.gms.ads.nativead.MediaView
                    android:id="@+id/ad_media"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:insetRight="0dp"
                    android:insetLeft="0dp"
                    android:layout_gravity="center_horizontal"
                    android:clipToPadding="true"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp312" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:layout_marginHorizontal="@dimen/dp16"
                    app:layout_constraintTop_toBottomOf="@+id/adLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textSize="@dimen/sp16"
                    android:fontFamily="@font/readex_pro"
                    android:includeFontPadding="false"
                    android:textColor="@color/neutral_grey"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:orientation="horizontal"
                    android:paddingBottom="@dimen/dp10"
                    android:paddingTop="@dimen/dp10">

                    <TextView
                        android:id="@+id/ad_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="@dimen/dp5"
                        android:paddingStart="@dimen/dp5"
                        android:paddingRight="@dimen/dp5"
                        android:paddingEnd="@dimen/dp5"
                        android:textSize="@dimen/sp12"
                        android:layout_marginEnd="@dimen/dp6"
                        app:layout_constraintTop_toTopOf="@+id/ad_store"
                        app:layout_constraintEnd_toStartOf="@+id/ad_store"
                        android:textColor="@color/neutral_grey"
                        />

                    <TextView
                        android:id="@+id/ad_store"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="@dimen/dp5"
                        android:paddingStart="@dimen/dp5"
                        android:paddingRight="@dimen/dp5"
                        android:paddingEnd="@dimen/dp5"
                        android:textSize="@dimen/sp12"
                        android:layout_marginEnd="@dimen/dp6"
                        app:layout_constraintEnd_toStartOf="@+id/ad_call_to_action"
                        app:layout_constraintTop_toTopOf="@+id/ad_call_to_action"
                        app:layout_constraintBottom_toBottomOf="@+id/ad_call_to_action"
                        android:textColor="@color/neutral_grey"
                        />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/ad_call_to_action"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp48"
                        app:backgroundTint="@color/dull_yellow"
                        android:textColor="@color/black"
                        android:text="@string/action_button"
                        android:textAllCaps="false"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginHorizontal="@dimen/dp16"
                        android:layout_marginVertical="@dimen/dp24"
                        app:cornerRadius="@dimen/dp24"
                        android:textSize="@dimen/sp14"
                        android:fontFamily="@font/readex_pro"
                        android:includeFontPadding="false"
                        />


                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </LinearLayout>
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/imgCross"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:src="@drawable/admob_close_button_black_circle_white_cross"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_margin="@dimen/dp12"
            android:background="@color/grey"
            android:layout_gravity="right"
            app:shapeAppearanceOverlay="@style/roundedImageViewRounded"
            />

    </FrameLayout>
</com.google.android.gms.ads.nativead.NativeAdView>
