<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700"
    tools:context=".modules.notification.fragment.NotificationFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarSkipVisibility="gone"
        app:toolbarRightButtonText="@string/delete"
        app:toolbarRightTextColor="@color/dull_yellow"
        app:toolbarRightTextCaps="false"
        app:toolbarRightTextStyle="normal"
        app:toolbarRightTextSize="@dimen/sp14"
        app:toolbarViewTitle="@string/notification_ind"
        app:toolbarSecondaryText="@string/select_all"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/notificationRecycler"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/emptyView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <ImageView
            android:id="@+id/iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp59"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_no_notification"
            android:background="?selectableItemBackgroundBorderless"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_gravity="center_horizontal"
            android:textSize="@dimen/sp18"
            android:textColor="@color/white"
            android:text="@string/blank_notification"
            android:fontFamily="sans-serif"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv"/>

        <TextView
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white80"
            android:text="@string/blank_notification_text"
            android:fontFamily="sans-serif"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>