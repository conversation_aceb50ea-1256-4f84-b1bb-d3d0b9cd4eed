<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="noice.app.utils.filter.FilterViewModel" />

        <variable
            name="item"
            type="noice.app.utils.filter.PageFilter" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.google.android.material.chip.Chip
            android:id="@+id/chipSort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{()-> viewModel.onSelected(item)}"
            android:paddingVertical="@dimen/dp8"
            android:text="@{item.title}"
            android:textAlignment="center"
            android:textAppearance="@style/chipTextSemiBoldStyle"
            android:textColor="@{item.isSelected ? @color/black : @color/neutral_90}"
            android:textSize="@dimen/sp14"
            app:chipBackgroundColor="@{item.isSelected ? @color/white : @android:color/transparent}"
            app:chipEndPadding="@dimen/dp16"
            app:chipMinTouchTargetSize="0dp"
            app:chipStartPadding="@dimen/dp16"
            app:chipStrokeColor="@{item.isSelected ? @color/white : @color/neutral_70}"
            app:chipStrokeWidth="@dimen/dp1"
            app:chipSurfaceColor="@android:color/transparent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:textEndPadding="@dimen/size_0dp"
            app:textStartPadding="@dimen/size_0dp"
            tools:chipStrokeColor="@color/neutral_70"
            tools:chipSurfaceColor="@android:color/transparent"
            tools:text="Semua"
            tools:textColor="@color/neutral_90" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>