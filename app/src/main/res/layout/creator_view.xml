<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/creatorView"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp8"
    android:background="@drawable/custom_ripple_bg_4dp">

    <FrameLayout
        android:id="@+id/artistImageLayout"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgArtist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_user_profile" />
    </FrameLayout>

    <TextView
        android:id="@+id/userName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:fontFamily="sans-serif"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/artistImageLayout"
        tools:text=" @surajpaln2011" />

    <TextView
        android:id="@+id/txtType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/background_blackish_grey_22dp"
        android:fontFamily="sans-serif"
        tools:text="Host"
        android:layout_marginStart="@dimen/dp8"
        android:paddingStart="@dimen/dp6"
        android:paddingTop="@dimen/dp2"
        android:paddingEnd="@dimen/dp6"
        android:paddingBottom="@dimen/dp2"
        android:textColor="@color/white_600"
        android:textSize="@dimen/sp9"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/userName"
        app:layout_constraintTop_toTopOf="@+id/userName"
        app:layout_constraintBottom_toBottomOf="@id/userName"/>

    <TextView
        android:id="@+id/txtName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:textColor="@color/medium_greyed"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/userName"
        app:layout_constraintTop_toBottomOf="@+id/userName"
        tools:text="Suraj Pal" />
</androidx.constraintlayout.widget.ConstraintLayout>