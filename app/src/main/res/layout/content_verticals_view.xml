<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentVerticalSegment"
    android:layout_width="@dimen/dp56"
    android:layout_height="wrap_content"
    android:background="@color/black"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/baruLayout"
        android:layout_width="@dimen/dp47"
        android:layout_height="@dimen/dp20"
        android:layout_gravity="center|top"
        android:visibility="gone"
        tools:visibility="visible"
        android:elevation="1dp">

        <TextView
            android:id="@+id/newVertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:text="@string/new_vertical"
            android:background="@drawable/background_white_4dp_round"
            android:textColor="@color/black600"
            android:textSize="@dimen/sp8"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingStart="@dimen/dp5"
            android:paddingEnd="@dimen/dp5"
            android:textAllCaps="true"
            android:lineSpacingExtra="0sp"
            app:drawableStartCompat="@drawable/ic_star"/>
    </FrameLayout>

    <noice.app.views.SquareCardView
        android:id="@+id/squareCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-12dp"
        app:cardBackgroundColor="@color/home_search"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <ImageView
            android:id="@+id/imageIcon"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_gravity="center"
            android:adjustViewBounds="true"
            android:background="@color/home_search"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_mic_on" />

    </noice.app.views.SquareCardView>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="0sp"
        android:maxLines="1"
        android:letterSpacing="0.01"
        android:textAlignment="center"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp10"
        tools:text="Audiobook" />
</LinearLayout>