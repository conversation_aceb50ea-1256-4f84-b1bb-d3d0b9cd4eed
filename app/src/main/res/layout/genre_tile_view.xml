<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tile"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp72"
    app:cardBackgroundColor="@color/black"
    app:cardCornerRadius="@dimen/dp8"
    app:cardElevation="0dp">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/genre_gradient"/>

    <TextView
        android:id="@+id/genreName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="@dimen/dp8"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:lineSpacingExtra="@dimen/sp4"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="Genre"
        android:textSize="@dimen/sp16"
        android:textColor="@color/white"
        android:fontFamily="sans-serif" />

    <ImageView
        android:id="@+id/image"
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp14"
        android:layout_gravity="end"
        android:visibility="gone"
        android:adjustViewBounds="true"
        android:contentDescription="@string/select_genre"
        tools:src="@drawable/ic_checked_circle_yellow"/>

    <FrameLayout
        android:id="@+id/selectedOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@color/black50">

        <ImageView
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginTop="@dimen/dp14"
            android:adjustViewBounds="true"
            android:contentDescription="@string/select_genre"
            android:src="@drawable/ic_checked_circle_yellow"/>
    </FrameLayout>
</androidx.cardview.widget.CardView>