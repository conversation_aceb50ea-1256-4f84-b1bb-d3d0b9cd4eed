<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.enums.TransactionStatus"/>
        <import type="noice.app.enums.TransactionType"/>
        <import type="noice.app.enums.MonthFormat"/>

        <variable
            name="viewModel"
            type="noice.app.modules.coins.viewmodel.CoinHistoryViewModel" />

        <variable
            name="dataItem"
            type="noice.app.modules.coins.model.CoinTransactionHistory" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/thumbnail"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
        
        <ImageView
            android:id="@+id/txnTypeImg"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:visibility="@{(dataItem.meta.txnTypeImage == null || dataItem.meta.txnTypeImage.empty) ? View.GONE : View.VISIBLE}"
            app:loadImageUrl="@{dataItem.meta.txnTypeImage}"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            app:layout_constraintEnd_toEndOf="@id/thumbnail"
            app:layout_constraintBottom_toBottomOf="@id/thumbnail"/>

        <TextView
            android:id="@+id/transactionText"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:text="@{viewModel.getTitle(dataItem)}"
            tools:text="Top up + Bonus Coin"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp12"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:letterSpacing="0"
            android:lineSpacingExtra="@dimen/sp3"
            android:fontFamily="sans-serif"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintEnd_toEndOf="@id/transactionTime"
            app:layout_constraintStart_toEndOf="@id/thumbnail"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/transactionTime"/>

        <TextView
            android:id="@+id/transactionTime"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginEnd="@dimen/dp8"
            monthFormat="@{MonthFormat.FULL}"
            createdAt="@{dataItem.createdAt}"
            tools:text="20 Agustus 2022 20:30"
            android:textColor="@color/medium_greyed"
            android:textSize="@dimen/sp12"
            android:letterSpacing="0"
            android:lineSpacingExtra="@dimen/sp3"
            android:fontFamily="sans-serif"
            app:layout_constraintEnd_toStartOf="@id/transactionInfo"
            app:layout_constraintStart_toStartOf="@id/transactionText"
            app:layout_constraintTop_toBottomOf="@id/transactionText"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/transactionAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{viewModel.getTransactionValue(dataItem)}"
            tools:text="+20"
            android:textColor="@{viewModel.getTransactionValueColor(dataItem)}"
            android:textSize="@dimen/sp12"
            android:letterSpacing="0"
            android:lineSpacingExtra="@dimen/sp3"
            android:fontFamily="sans-serif"
            app:layout_constraintTop_toTopOf="@id/transactionText"
            app:layout_constraintBottom_toBottomOf="@id/transactionText"
            app:layout_constraintEnd_toEndOf="parent"/>

        <FrameLayout
            android:id="@+id/transactionInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@id/transactionTime"
            app:layout_constraintBottom_toBottomOf="@id/transactionTime"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/transactionType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="@{viewModel.getTypeVisibility(dataItem)}"
                tools:visibility="gone"
                android:text="@{viewModel.getTypeValue(dataItem)}"
                android:textColor="@color/medium_greyed"
                android:textSize="@dimen/sp12"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/transactionStatus"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp20"
                android:layout_gravity="center"
                android:visibility="@{viewModel.getStatusVisibility(dataItem)}"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp8"
                android:gravity="center"
                tools:text="Coin Masuk"
                android:background="@{viewModel.getStatusBackground(dataItem)}"
                android:text="@{viewModel.getTransactionStatus(dataItem)}"
                android:textColor="@{viewModel.getStatusTextColor(dataItem)}"
                android:textSize="@dimen/sp10"
                android:textStyle="bold"
                android:letterSpacing="0.01"
                android:lineSpacingExtra="0sp"
                android:fontFamily="sans-serif" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>