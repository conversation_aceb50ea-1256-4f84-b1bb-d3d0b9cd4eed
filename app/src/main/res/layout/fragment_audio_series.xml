<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:clickable="true"
    android:focusable="true"
    tools:context=".modules.podcast.fragment.ChannelPodcastFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:fitsSystemWindows="true"
        android:background="@drawable/channel_gradiant">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="@color/black700"
            app:expandedTitleMarginStart="@dimen/dp16"
            app:expandedTitleTextAppearance="@style/CollapsingToolbarLayoutExpandedTextStyle"
            app:collapsedTitleTextAppearance="@style/CollapsingToolbarLayoutCollapsedTextStyle"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                app:layout_collapseMode="pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black700">

                <ImageView
                    android:id="@+id/imgCover"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:scaleType="fitXY"
                    app:layout_constraintBottom_toBottomOf="@+id/headerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    tools:ignore="ContentDescription" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_0dp"
                    android:background="@color/whitish_grey"
                    android:alpha=".6"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/headerLayout" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_0dp"
                    android:background="@drawable/channel_gradient_dark"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="@+id/headerLayout" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/headerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="?actionBarSize"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/dp16"
                    android:paddingEnd="@dimen/dp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/imgCard"
                        android:layout_width="@dimen/dp180"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        app:cardCornerRadius="@dimen/dp8"
                        app:cardElevation="@dimen/size_0dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/imgPodcast"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:adjustViewBounds="true"
                            android:contentDescription="@string/app_name"
                            android:src="@drawable/ic_thumb_default" />

                        <ImageView
                            android:id="@+id/noiceOriginal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:contentDescription="@string/original"
                            android:src="@drawable/ic_noice_original"
                            android:visibility="gone" />
                    </androidx.cardview.widget.CardView>


                    <TextView
                        android:id="@+id/origExc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:ellipsize="end"
                        android:fontFamily="@font/roboto"
                        android:gravity="start"
                        android:letterSpacing="0.15"
                        android:lineSpacingExtra="@dimen/sp2"
                        android:maxLines="1"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp10"
                        android:textStyle="normal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/imgCard"
                        tools:text="EXCLUSIVE" />

                    <TextView
                        android:id="@+id/txtTitle"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginTop="@dimen/dp5"
                        android:layout_marginEnd="@dimen/dp16"
                        android:fontFamily="@font/readex_pro"
                        android:gravity="center_horizontal"
                        android:lineSpacingExtra="@dimen/dp8"
                        android:textColor="@color/dull_white"
                        android:textSize="@dimen/sp22"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/origExc"
                        tools:text="Eps 9 : Berdoa Tidak Ada Gunanya (bersama Habib Jafar, Cania Citta)" />
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/durationCountLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
                        >
                        <TextView
                            android:id="@+id/txtEpsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:letterSpacing="0.04"
                            android:lineSpacingExtra="@dimen/sp4"
                            android:textColor="@color/dull_white"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/txtEpsUnit"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            tools:text="33" />
                        <TextView
                            android:id="@+id/txtEpsUnit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:textFontWeight="400"
                            android:letterSpacing="0.04"
                            android:lineSpacingExtra="4sp"
                            android:textColor="@color/text_grey2"
                            android:textSize="@dimen/sp14"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@+id/txtEpsCount"
                            app:layout_constraintEnd_toStartOf="@+id/txtCount"
                            android:text=" Episode " />

                        <TextView
                            android:id="@+id/txtCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:letterSpacing="0.04"
                            android:lineSpacingExtra="4sp"
                            android:textColor="@color/dull_white"
                            android:textSize="@dimen/sp14"
                            android:textStyle="bold"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/txtCountUnit"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@+id/txtEpsUnit"
                            tools:text=" 2.6 rb " />

                        <TextView
                            android:id="@+id/txtCountUnit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/readex_pro"
                            android:letterSpacing="0.04"
                            android:lineSpacingExtra="4sp"
                            android:textColor="@color/text_grey2"
                            android:textSize="@dimen/sp14"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@+id/txtCount"
                            android:text=" Subscribers" />


                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <noice.app.views.PlayerMediaButton
                        android:id="@+id/mediaButton"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp52"
                        android:layout_marginTop="@dimen/dp12"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/durationCountLayout" />


                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/layoutFollow"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp52"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/mediaButton"
                        android:layout_weight="1"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/border_dark_yellow_14dp"
                        >
                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/imgFollow"
                            android:layout_width="@dimen/dp30"
                            android:layout_height="@dimen/dp30"
                            android:layout_gravity="center_vertical"
                            app:srcCompat="@drawable/ic_follow_yellow"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/txtFollow"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"

                            />
                        <TextView
                            android:id="@+id/txtFollow"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dp52"
                            android:layout_gravity="center"
                            android:visibility="visible"
                            android:fontFamily="@font/readex_pro"
                            android:gravity="center"
                            android:layout_marginStart="@dimen/dp8"
                            android:lineSpacingExtra="@dimen/sp3"
                            android:textColor="@color/dull_yellow"
                            android:textSize="@dimen/sp16"
                            android:letterSpacing="0"
                            android:textStyle="bold"
                            android:text="@string/subscribe"
                            app:drawableTint="@color/dull_yellow"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toEndOf="@+id/imgFollow"
                            app:layout_constraintEnd_toEndOf="parent"

                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <FrameLayout
                        android:id="@+id/frameAdView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layoutFollow" />


                    <noice.app.views.ReadMoreTextView
                        android:id="@+id/txtEpisodDesc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/frameAdView"
                        tools:text="Ngobrolin kontroversi biar jadi musuh masyarakat bareng Coki Pardede dan Tretan Muslim. Episode baru tayang setiap hari Senin." />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvGenre"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/dp8"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/txtEpisodDesc" />

                    <noice.app.views.DonateView
                        android:id="@+id/donateView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/rvGenre" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/view13"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:layout_marginTop="@dimen/dp24"
                    android:background="@color/white100"
                    app:layout_constraintTop_toBottomOf="@+id/headerLayout"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <noice.app.views.RootTitleRecycler
                    android:id="@+id/rvArtist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    app:listPaddingStart="@dimen/dp8"
                    android:visibility="gone"
                    app:headingSize="@dimen/dp20"
                    app:headingText="@string/artist_ind"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/view13"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                app:layout_collapseMode="pin"
                android:layout_height="?attr/actionBarSize"
                app:contentInsetStartWithNavigation="0dp"
                app:layout_anchor="@id/collapsingToolbar"
                app:theme="@style/ThemeOverlay.AppCompat.Dark"
                app:navigationIcon="@drawable/ic_left_arrow_white"
                app:title="">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/txtToolbarTitle"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:fontFamily="sans-serif"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/sp14" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/imgShare"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp16"
                        android:background="@drawable/custom_ripple_bg"
                        app:srcCompat="@drawable/ic_share_white" />
                </LinearLayout>
            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <LinearLayout
            android:id="@+id/filterLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp20"
            android:paddingBottom="@dimen/dp8"
            android:orientation="horizontal"
            android:background="@color/black700">

            <TextView
                android:id="@+id/headerText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/dp8"
                android:maxLines="1"
                android:ellipsize="end"
                android:layout_gravity="center_vertical"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:text="@string/episode"
                android:textColor="@color/white80"
                android:textSize="@dimen/sp20"
                android:textStyle="bold"
                android:fontFamily="sans-serif" />

            <LinearLayout
                android:id="@+id/layoutFilter"
                android:layout_width="@dimen/dp95"
                android:layout_height="@dimen/dp32"
                android:layout_marginEnd="@dimen/dp16"
                android:background="@drawable/border_white_radius_4dp">

                <TextView
                    android:id="@+id/txtFilter"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:fontFamily="sans-serif"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    android:layout_weight="0.8"
                    android:visibility="visible"
                    android:text="@string/latest"
                    app:fontFamily="sans-serif" />

                <ImageView
                    android:id="@+id/hairTypeFilterArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.2"
                    android:layout_marginEnd="@dimen/dp8"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_arrow_down"
                    tools:ignore="ContentDescription" />
            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black700"
            android:paddingTop="@dimen/dp10"
            android:paddingStart="@dimen/dp2"
            android:paddingEnd="@dimen/dp2"
            app:tabBackground="@color/black700"
            app:tabGravity="start"
            app:tabIndicator="@drawable/custom_tab_indicator"
            app:tabIndicatorColor="@color/dull_yellow"
            app:tabIndicatorFullWidth="false"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/white"
            app:tabTextAppearance="@style/NoiceTabLayoutStyle"
            app:tabTextColor="@color/white50" />

        <View
            android:id="@+id/tabLayoutLine"
            android:layout_width="match_parent"
            android:layout_height=".8dp"
            android:background="@color/home_search" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollDefault"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="@dimen/dp1"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvEpisode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <TextView
                android:id="@+id/txtLihatSemua"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp16"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:textAllCaps="true"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp12"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="Lihat semua episode (45)" />

            <TextView
                android:id="@+id/addInfoTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:layout_marginStart="@dimen/dp16"
                android:visibility="gone"
                android:text="@string/additional_info"
                android:textSize="@dimen/sp20"
                android:textColor="@color/dull_white"
                android:textStyle="bold"
                android:fontFamily="sans-serif" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/authorGridRecycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"
                android:visibility="gone"
                android:orientation="vertical"
                app:spanCount="2"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:clickable="true"
        android:layout_marginTop="?actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>