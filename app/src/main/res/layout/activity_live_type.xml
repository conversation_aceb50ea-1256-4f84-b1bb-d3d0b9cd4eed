<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".modules.live.activity.LiveTypeActivity">
    <noice.app.views.ToolbarView
        android:id="@+id/toolBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:toolbarViewTitle="@string/vip_room"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/switchPrivate"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:checked="false"
        android:layout_margin="@dimen/dp16"
        android:lineSpacingExtra="@dimen/sp4"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolBar" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:clickable="true"
        android:layout_margin="@dimen/dp16"
        android:lineSpacingExtra="@dimen/sp4"
        android:text="@string/private_room"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp16"
        android:drawablePadding="@dimen/dp5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/switchPrivate"
        app:layout_constraintBottom_toBottomOf="@+id/switchPrivate"
        android:focusable="true" />



    <com.google.android.material.switchmaterial.SwitchMaterial
        android:id="@+id/switchTicket"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:checked="false"
        android:layout_margin="@dimen/dp16"
        android:lineSpacingExtra="@dimen/sp4"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/switchPrivate" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:clickable="true"
        android:layout_margin="@dimen/dp16"
        android:lineSpacingExtra="@dimen/sp4"
        android:text="@string/live_rrom_ticketed"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp16"
        android:drawablePadding="@dimen/dp5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/switchTicket"
        app:layout_constraintBottom_toBottomOf="@+id/switchTicket"
        android:focusable="true" />

</androidx.constraintlayout.widget.ConstraintLayout>