<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black700">

    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp26"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_48dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp26"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_48dp"
        app:layout_constraintTop_toTopOf="@id/v1"
        app:layout_constraintStart_toEndOf="@id/v1"/>

    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp26"
        android:layout_marginStart="@dimen/dp8"
        android:background="@drawable/background_skeleton_48dp"
        app:layout_constraintTop_toTopOf="@id/v1"
        app:layout_constraintStart_toEndOf="@id/v2"/>

    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v1"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v4"
        app:layout_constraintStart_toEndOf="@id/v4"/>

    <View
        android:id="@+id/v6"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@id/v5"/>

    <View
        android:id="@+id/v7"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v4"/>

    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v4"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v9"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v8"
        app:layout_constraintStart_toEndOf="@id/v8"/>

    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v9"
        app:layout_constraintTop_toBottomOf="@id/v9"/>

    <View
        android:id="@+id/v11"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v9"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v8"/>

    <View
        android:id="@+id/v12"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v8"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v12"
        app:layout_constraintStart_toEndOf="@id/v12"/>

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v13"
        app:layout_constraintTop_toBottomOf="@id/v13"/>

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v12"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v12"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v17"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v16"
        app:layout_constraintStart_toEndOf="@id/v16"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintTop_toBottomOf="@id/v17"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v16"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v16"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v21"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v20"
        app:layout_constraintStart_toEndOf="@id/v20"/>

    <View
        android:id="@+id/v22"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintTop_toBottomOf="@id/v21"/>

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v21"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v20"/>

    <View
        android:id="@+id/v24"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v20"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v25"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v24"
        app:layout_constraintStart_toEndOf="@id/v24"/>

    <View
        android:id="@+id/v26"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@id/v25"/>

    <View
        android:id="@+id/v27"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v25"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v24"/>

    <View
        android:id="@+id/v28"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v24"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v29"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v28"
        app:layout_constraintStart_toEndOf="@id/v28"/>

    <View
        android:id="@+id/v30"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v29"
        app:layout_constraintTop_toBottomOf="@id/v29"/>

    <View
        android:id="@+id/v31"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v29"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v28"/>

    <View
        android:id="@+id/v32"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v28"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v33"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v32"
        app:layout_constraintStart_toEndOf="@id/v32"/>

    <View
        android:id="@+id/v34"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v33"
        app:layout_constraintTop_toBottomOf="@id/v33"/>

    <View
        android:id="@+id/v35"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v33"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v32"/>

    <View
        android:id="@+id/v36"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toBottomOf="@id/v32"
        app:layout_constraintStart_toStartOf="@id/v1"/>

    <View
        android:id="@+id/v37"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@id/v36"
        app:layout_constraintStart_toEndOf="@id/v36"/>

    <View
        android:id="@+id/v38"
        android:layout_width="@dimen/dp176"
        android:layout_height="@dimen/dp12"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v37"
        app:layout_constraintTop_toBottomOf="@id/v37"/>

    <View
        android:id="@+id/v39"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v37"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/v36"/>
</androidx.constraintlayout.widget.ConstraintLayout>