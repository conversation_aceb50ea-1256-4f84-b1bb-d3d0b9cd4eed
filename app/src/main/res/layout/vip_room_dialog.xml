<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24"
    android:paddingStart="@dimen/dp32"
    android:paddingEnd="@dimen/dp32">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/txtPrivate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:text="@string/vip_room"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/sp18"
        android:fontFamily="@font/roboto_bold"
        android:layout_marginTop="@dimen/dp32"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/txtCancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tutup"
        android:padding="@dimen/dp3"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        android:fontFamily="@font/roboto"
        app:layout_constraintTop_toTopOf="@+id/txtPrivate"
        app:layout_constraintBottom_toBottomOf="@+id/txtPrivate"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp39"
        app:layout_constraintTop_toBottomOf="@+id/txtPrivate"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgVip"
            android:layout_width="@dimen/dp119"
            android:layout_height="@dimen/dp119"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:srcCompat="@drawable/ic_vip"
            />



        <TextView
            android:id="@+id/txtVipDesc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/tersedia_kh"
            android:lineSpacingExtra="@dimen/sp3"
            android:fontFamily="@font/roboto"
            android:textSize="@dimen/sp14"
            android:layout_marginTop="@dimen/dp16"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imgVip"
            />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnMengerti"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp48"
            android:background="@drawable/background_yellow_radius_4dp"
            android:layout_marginTop="@dimen/dp24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtVipDesc"
            android:text="@string/mengerti"
            android:fontFamily="@font/roboto_bold"
            android:textSize="@dimen/sp14"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>