<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/miniPlayerView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/home_search">

    <androidx.cardview.widget.CardView
        android:id="@+id/imgSongLayout"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginBottom="@dimen/dp8"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/progressBar">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgSong"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:adjustViewBounds="true"
            tools:srcCompat="@mipmap/ic_launcher" />

        <noice.app.exoplayer.CompanionAdView
            android:id="@+id/miniCompanionAdSlot"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            app:bgShape="square"
            android:visibility="gone"
            android:gravity="center"
            android:textAlignment="center" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:textSize="@dimen/sp11"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textColor="@color/dull_white"
        android:letterSpacing="0.04"
        tools:text="Eps 9 : Berdoa Tidak Ada Gunanya"
        android:maxLines="1"
        android:ellipsize="end"
        android:drawablePadding="@dimen/dp5"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintTop_toTopOf="@id/imgSongLayout"
        app:layout_constraintStart_toEndOf="@id/imgSongLayout"
        app:layout_constraintEnd_toStartOf="@+id/backwardBtn"
        app:layout_constraintBottom_toTopOf="@id/txtSong"/>

    <TextView
        android:id="@+id/txtSong"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        tools:text="Musuh Masyarakat"
        android:textColor="@color/white80"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="@dimen/sp11"
        android:fontFamily="@font/readex_pro"
        android:textStyle="normal"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        app:layout_constraintBottom_toBottomOf="@id/imgSongLayout"
        app:layout_constraintStart_toStartOf="@+id/txtTitle"
        app:layout_constraintEnd_toEndOf="@id/txtTitle"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/backwardBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp22"
        app:srcCompat="@drawable/ic_backward_10s"
        android:background="@drawable/custom_ripple_bg"
        app:layout_constraintStart_toEndOf="@id/txtTitle"
        app:layout_constraintEnd_toStartOf="@id/actionButton"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/progressBar"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/actionButton"
        android:layout_width="@dimen/dp32"
        android:layout_height="@dimen/dp32"
        android:layout_marginEnd="@dimen/dp12"
        app:srcCompat="@drawable/ic_play_grey_white"
        android:background="@drawable/custom_ripple_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/progressBar"/>

    <ProgressBar
        android:id="@+id/loader"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:visibility="gone"
        android:indeterminateTint="@color/white"
        android:padding="@dimen/dp3"
        app:layout_constraintStart_toStartOf="@id/actionButton"
        app:layout_constraintEnd_toEndOf="@id/actionButton"
        app:layout_constraintTop_toTopOf="@id/actionButton"
        app:layout_constraintBottom_toBottomOf="@id/actionButton"/>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp2"
        android:progressTint="@color/white"
        android:progressBackgroundTint="@color/neutral_grey_900"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        tools:progress="70"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>