<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp42"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/bannerRecyclerView"
        android:layout_width="@dimen/dp42"
        android:layout_height="@dimen/dp45"
        android:orientation="horizontal"/>

    <me.relex.circleindicator.CircleIndicator2
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp6"
        android:layout_gravity="bottom|center"
        app:ci_height="@dimen/dp3"
        app:ci_width="@dimen/dp3"
        app:ci_margin="@dimen/dp1"
        app:ci_animator="@anim/scale_with_alpha_custom"
        app:ci_drawable="@drawable/circle_indicator_selected"
        app:ci_drawable_unselected="@drawable/circle_indicator_unselected"/>
</LinearLayout>