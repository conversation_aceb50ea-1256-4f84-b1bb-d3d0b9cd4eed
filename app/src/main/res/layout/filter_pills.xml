<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/linearRoot"
    android:background="@drawable/background_blackish_grey_8dp"
    android:gravity="center"
    android:paddingStart="@dimen/dp12"
    android:paddingTop="@dimen/dp5"
    android:paddingEnd="@dimen/dp12"
    android:paddingBottom="@dimen/dp5">

    <TextView
        android:id="@+id/genreName"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp25"
        android:fontFamily="@font/readex_pro_regular"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lineSpacingExtra="@dimen/sp5"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        tools:text="Filter" />

    <ImageView
        android:id="@+id/imgTrailing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:visibility="gone"
        app:srcCompat="@drawable/ic_filter_white"
        tools:visibility="visible" />
</LinearLayout>