<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="noice.app.utils.HandleValues"/>
        <import type="android.widget.ImageView.ScaleType"/>
        <variable
            name="item"
            type="noice.app.modules.live.model.gift.viewstate.GiftItemViewState" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/holder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@{item.selected ? @drawable/border_dull_yellow_dark_radius_8dp : @drawable/transparent}"
        tools:background="@drawable/border_dull_yellow_dark_radius_8dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgGift"
            android:layout_width="@dimen/dp41"
            android:layout_height="@dimen/dp41"
            android:layout_marginStart="@dimen/dp20"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp20"
            android:scaleType="@{item.gift.campaignId != null? ScaleType.CENTER_CROP : ScaleType.FIT_CENTER}"
            app:circleCrop="@{item.gift.campaignId != null}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:placeholder="@{@drawable/ic_thumb_default}"
            app:srcCompat="@drawable/ic_support_image"
            app:srcUrl="@{item.gift.config.image.listUrl}" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginTop="@dimen/dp6"
            android:layout_marginEnd="@dimen/dp8"
            android:text="@{item.gift.name}"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp10"
            app:fontFamily="@font/roboto"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imgGift"
            tools:text="Shoeees" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtCoins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginBottom="@dimen/dp8"
            android:drawableStart="@drawable/ic_coins"
            android:drawablePadding="@dimen/dp3"
            android:gravity="center"
            android:includeFontPadding="false"
            tools:text="120"
            android:text="@{HandleValues.convertNumberToLocaleIndonesia(item.gift.priceInCoins.toString())}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp10"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/imgGift"
            app:layout_constraintStart_toStartOf="@+id/imgGift"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>