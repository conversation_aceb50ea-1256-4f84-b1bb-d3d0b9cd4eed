<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">

    <LinearLayout
        android:id="@+id/liveHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:paddingTop="@dimen/dp16">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="@dimen/dp27"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_weight="1"
            android:src="@drawable/ic_skeleton_square_8dp" />


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp27"
            android:layout_gravity="center_vertical"
            android:paddingEnd="@dimen/dp16"
            android:paddingStart="@dimen/dp16"
            android:paddingTop="@dimen/dp8"
            android:paddingBottom="@dimen/dp8"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_arrow_right_white" />
    </LinearLayout>


    <ImageView
        android:id="@+id/v1"
        android:layout_width="@dimen/dp156"
        android:layout_height="@dimen/dp180"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/liveHeading"
        app:layout_constraintEnd_toStartOf="@id/v2"/>
    <ImageView
        android:id="@+id/v2"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp180"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v1"/>


    <LinearLayout
        android:id="@+id/scheduleHeading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@+id/v1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:paddingTop="@dimen/dp16">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="@dimen/dp27"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_weight="1"
            android:src="@drawable/ic_skeleton_square_8dp" />


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp27"
            android:layout_gravity="center_vertical"
            android:paddingEnd="@dimen/dp16"
            android:paddingStart="@dimen/dp16"
            android:paddingTop="@dimen/dp8"
            android:paddingBottom="@dimen/dp8"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_arrow_right_white" />

    </LinearLayout>



    <ImageView
        android:id="@+id/v3"
        android:layout_width="@dimen/dp156"
        android:layout_height="@dimen/dp180"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scheduleHeading"
        app:layout_constraintEnd_toStartOf="@id/v4"/>
    <ImageView
        android:id="@+id/v4"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp180"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/v3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v3"/>



</androidx.constraintlayout.widget.ConstraintLayout>