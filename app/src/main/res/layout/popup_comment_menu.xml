<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/deleteView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="@dimen/dp8"
    app:cardElevation="@dimen/dp5"
    app:cardBackgroundColor="@color/home_search">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/pinComment"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp32"
            android:paddingStart="@dimen/dp3"
            android:paddingEnd="@dimen/dp9"
            android:visibility="gone"
            android:orientation="horizontal"
            android:foreground="?android:attr/selectableItemBackgroundBorderless">

            <ImageView
                android:id="@+id/pinIcon"
                android:layout_width="@dimen/dp16"
                android:layout_height="@dimen/dp16"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_gravity="center_vertical"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_pinned"/>

            <TextView
                android:id="@+id/pinText"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/pin"
                android:checked="true"
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/text_grey3" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/black" />

        <LinearLayout
            android:id="@+id/firstMenu"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp32"
            android:paddingStart="@dimen/dp3"
            android:paddingEnd="@dimen/dp9"
            android:orientation="horizontal"
            android:foreground="?android:attr/selectableItemBackgroundBorderless">

            <ImageView
                android:id="@+id/imgFirst"
                android:layout_width="@dimen/dp12"
                android:layout_height="@dimen/dp12"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_gravity="center_vertical"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_cross_red"/>

            <TextView
                android:id="@+id/rbFirst"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/remove_commenter"
                android:checked="true"
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/red" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/black" />

        <LinearLayout
            android:id="@+id/secMenu"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp32"
            android:paddingStart="@dimen/dp3"
            android:paddingEnd="@dimen/dp9"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imgSec"
                android:layout_width="@dimen/dp12"
                android:layout_height="@dimen/dp12"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginEnd="@dimen/dp8"
                android:layout_gravity="center_vertical"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_report"/>

            <TextView
                android:id="@+id/rbSecond"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/delete"
                android:checked="true"
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/red" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
