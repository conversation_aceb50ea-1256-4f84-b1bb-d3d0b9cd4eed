<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.utils.HandleValues"/>
        <variable
            name="item"
            type="noice.app.modules.live.model.gift.slide.GiftSlide" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_gift_lottie"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/ic_background_lottie_gift"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieGiftsView"
            android:layout_width="match_parent"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_height="@dimen/size_0dp"
            android:layout_marginBottom="@dimen/dp8"
            android:scaleType="fitEnd"
            android:adjustViewBounds="true"
            app:lottie_imageAssetsFolder="images"
            app:layout_constraintBottom_toTopOf="@+id/constraintLayout3"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp2"
            android:layout_marginBottom="@dimen/dp120"
            android:paddingEnd="@dimen/dp8"
            android:paddingStart="@dimen/size_0dp"
            android:background="@drawable/background_black_40_percent_54dp"
            android:backgroundTint="@color/dull_yellow_dark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgGiftProfile"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginTop="@dimen/dp4"
                android:layout_marginBottom="@dimen/dp4"
                app:srcUrl="@{item.userPic}"
                app:circleCrop="@{true}"
                app:placeholder="@{@drawable/ic_user_profile}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_user_profile" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftProfileName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:fontFamily="@font/readex_pro_semi_bold"
                tools:text="MarkZ"
                android:text="@{item.userName}"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp12"
                android:includeFontPadding="false"
                app:layout_constraintBottom_toBottomOf="@+id/imgGiftProfile"
                app:layout_constraintStart_toEndOf="@+id/imgGiftProfile"
                app:layout_constraintTop_toTopOf="@+id/imgGiftProfile" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/verifiedBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp2"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_verified_tag"
                android:visibility="@{item.verifiedUser? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="@+id/txtGiftProfileName"
                app:layout_constraintStart_toEndOf="@+id/txtGiftProfileName"
                app:layout_constraintTop_toTopOf="@+id/txtGiftProfileName"
                tools:visibility="visible" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp2"
                android:fontFamily="@font/readex_pro_regular"
                tools:text="Ngirim Stik PS"
                android:text="@{@string/sent_pre_text_string(item.giftName)}"
                android:textColor="@color/white300"
                android:textSize="@dimen/sp12"
                app:layout_constraintBaseline_toBaselineOf="@+id/txtGiftProfileName"
                app:layout_constraintStart_toEndOf="@+id/verifiedBadge" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftQuantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:fontFamily="@font/readex_pro_semi_bold"
                android:includeFontPadding="false"
                android:text="@{`x`+item.quantity}"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp20"
                android:visibility="@{HandleValues.isGreaterThanOne(item.quantity) ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="@+id/txtGiftDetail"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/txtGiftDetail"
                app:layout_constraintTop_toTopOf="@+id/txtGiftDetail"
                tools:text="x99"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>