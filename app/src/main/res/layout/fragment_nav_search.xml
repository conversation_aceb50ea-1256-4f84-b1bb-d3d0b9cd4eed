<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700">

    <TextView
        android:id="@+id/textSearch"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp45"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:padding="@dimen/dp14"
        android:text="@string/cari_podcast_radio_atau_audiobook"
        android:textColor="@color/medium_grey"
        android:textSize="17sp"
        app:drawableEndCompat="@drawable/ic_search"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textSearch">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerSearch"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/emptyView"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                android:background="@color/black"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:padding="@dimen/dp16"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/emptyMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/readex_pro"
                    android:lineSpacingExtra="@dimen/sp6"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    android:fontFamily="@font/readex_pro"
                    android:lineSpacingExtra="@dimen/sp6"
                    android:text="@string/no_search_result"
                    android:textColor="@color/white70"
                    android:textSize="@dimen/sp14" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/homeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="@dimen/dp48"
        android:fitsSystemWindows="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0" />
</androidx.constraintlayout.widget.ConstraintLayout>