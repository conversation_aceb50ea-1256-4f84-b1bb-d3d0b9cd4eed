<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".modules.coins.activity.CoinTopUpActivity">

    <data>
        <import type="noice.app.R"/>
        <import type="android.view.View"/>
        <variable
            name="ctx"
            type="android.content.Context" />
        <variable
            name="viewModel"
            type="noice.app.modules.coins.viewmodel.CoinTopUpViewModel"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/toolbar"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp56"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/backBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:padding="@dimen/dp8"
                android:layout_gravity="center"
                android:src="@drawable/ic_left_angle_white"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"/>

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:text="@string/top_up"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp18"
                android:textStyle="bold"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp4"/>

            <TextView
                android:id="@+id/coins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginEnd="@dimen/dp16"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp8"
                android:paddingTop="@dimen/dp4"
                android:paddingBottom="@dimen/dp4"
                android:background="@drawable/background_blackish_grey_22dp"
                android:gravity="center"
                android:text="@string/hyphen"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="bold"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:fontFamily="sans-serif"
                android:drawablePadding="@dimen/dp8"
                app:drawableStartCompat="@drawable/ic_noice_coin" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/packageRecycler"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="@dimen/dp8"
            android:visibility="gone"
            android:orientation="vertical"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"
            app:items="@{viewModel.availableCoinPackages}"
            tools:itemCount="6"
            tools:listitem="@layout/coin_package_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toTopOf="@id/bottomLayout"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:padding="@dimen/dp16"
            android:background="@color/home_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/coinImg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@{ctx.getString(R.string.xx_coin, viewModel.selectedPackage.formattedTotalCoins)}"
                tools:text="46 Coins"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="bold"
                android:fontFamily="sans-serif"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:drawablePadding="@dimen/dp8"
                app:drawableStartCompat="@drawable/ic_noice_coin_24"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/noOfCoinTitle" />

            <TextView
                android:id="@+id/noOfCoinTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:text="@{ctx.getString(R.string.total_coin_price, viewModel.selectedPackage.priceIDR.priceAfterDiscount)}"
                tools:text="Total : Rp16.000"
                android:textColor="@color/white"
                android:textSize="@dimen/sp12"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:fontFamily="sans-serif"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@id/coinImg"
                app:layout_constraintTop_toBottomOf="@id/coinImg"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <FrameLayout
                android:layout_width="@dimen/dp94"
                android:layout_height="@dimen/dp32"
                android:background="@drawable/background_yellow_radius_4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <ProgressBar
                    android:id="@+id/btnProgressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp25"
                    android:layout_gravity="center"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.processingPurchases ? View.VISIBLE : View.GONE}"
                    android:indeterminateTint="@color/black600"/>

                <TextView
                    android:id="@+id/topUpBtn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:visibility="@{viewModel.processingPurchases ? View.GONE : View.VISIBLE}"
                    android:text="@string/top_up"
                    android:textColor="@color/black600"
                    android:textSize="@dimen/sp12"
                    android:textStyle="bold"
                    android:textAllCaps="true"
                    android:letterSpacing="0.2"
                    android:lineSpacingExtra="@dimen/sp1"
                    android:fontFamily="sans-serif" />
            </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/emptyView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/black"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <ImageView
                android:id="@+id/coinStack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_error_coins"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/emptyTitle"/>

            <TextView
                android:id="@+id/emptyTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:gravity="center"
                android:text="@string/coin_not_available"
                android:textSize="@dimen/sp18"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/coinStack"
                app:layout_constraintBottom_toTopOf="@id/emptyDesc"/>

            <TextView
                android:id="@+id/emptyDesc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:gravity="center"
                android:text="@string/coin_empty_desc"
                android:textSize="@dimen/sp16"
                android:textColor="@color/white80"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/emptyTitle"
                app:layout_constraintBottom_toTopOf="@id/reload"/>

            <TextView
                android:id="@+id/reload"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:background="@drawable/background_yellow_radius_4dp"
                android:gravity="center"
                android:text="@string/returned"
                android:textSize="@dimen/sp12"
                android:textColor="@color/blackish_blue"
                android:textStyle="bold"
                android:letterSpacing="0.2"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/emptyDesc"
                app:layout_constraintBottom_toTopOf="@id/helpStr"/>

            <TextView
                android:id="@+id/helpStr"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center"
                android:text="@string/topup_help_str"
                android:textSize="@dimen/sp14"
                android:textColor="@color/medium_greyed"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="@id/reload"
                app:layout_constraintEnd_toEndOf="@id/reload"
                app:layout_constraintTop_toBottomOf="@id/reload"
                app:layout_constraintBottom_toTopOf="@id/supportEmail"/>

            <TextView
                android:id="@+id/supportEmail"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/dp6"
                android:text="@string/support_noice_id"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_yellow"
                android:letterSpacing="0"
                android:lineSpacingExtra="@dimen/sp3"
                android:fontFamily="sans-serif"
                app:layout_constraintStart_toStartOf="@id/reload"
                app:layout_constraintEnd_toEndOf="@id/reload"
                app:layout_constraintTop_toBottomOf="@id/helpStr"
                app:layout_constraintBottom_toBottomOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>