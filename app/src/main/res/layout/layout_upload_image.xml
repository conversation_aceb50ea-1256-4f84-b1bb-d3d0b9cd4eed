<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="state"
            type="noice.app.modules.live.viewmodel.UploadViewModel.State" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/label"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp16"
            android:fontFamily="@font/roboto"
            android:lineSpacingExtra="@dimen/sp4"
            android:text="@string/add_image"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_upload"
            android:layout_width="@dimen/dp126"
            android:layout_height="@dimen/dp140"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/label">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/img_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:placeholder="@{@drawable/ic_user_profile}"
                app:shapeAppearance="@style/ShapeAppearanceOverlay.App.RoundedCorners"
                app:srcUrl="@{state.url}"
                tools:srcCompat="@color/persian_blue" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/img_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/overlay"
                android:clickable="true"
                android:elevation="@dimen/dp1"
                android:focusable="true"
                android:scaleType="centerCrop"
                app:shapeAppearance="@style/ShapeAppearanceOverlay.App.RoundedCorners"
                app:visibleIf="@{state.uploading}" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txt_label_change"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:background="@color/color_overlay"
                android:padding="@dimen/dp4"
                android:text="@string/change_image"
                android:textAlignment="center"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp12"
                app:layout_constraintBottom_toBottomOf="@+id/img_cover"
                app:layout_constraintEnd_toEndOf="@+id/img_cover"
                app:layout_constraintStart_toStartOf="@+id/img_cover"
                app:visibleIf="@{!state.uploading}" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_upload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                app:trackThickness="@dimen/dp2"
                android:indeterminateTint="@color/dull_yellow"
                app:indicatorSize="@dimen/dp10"
                app:layout_constraintBottom_toBottomOf="@+id/img_cover"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/img_cover"
                app:visibleIf="@{state.uploading}" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>