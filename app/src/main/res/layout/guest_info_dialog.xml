<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingBottom="@dimen/dp24">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textTutup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp8"
        android:padding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:text="@string/tutup"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        android:background="@drawable/custom_ripple"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:fontFamily="sans-serif"
        android:text="@string/visitor"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:id="@+id/dialogHeading"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:text="@string/user_dont_want_to_register"
        android:textColor="@color/white80"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toStartOf="@id/textTutup"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textTutup"
        />

    <TextView
        android:id="@+id/textOk"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_yellow_radius_4dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/okay"
        android:textColor="@color/black"
        android:textSize="@dimen/sp12"
        android:textStyle="bold"
        android:letterSpacing="0.2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialogHeading" />

</androidx.constraintlayout.widget.ConstraintLayout>