<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/notificationView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black700"
    android:paddingStart="@dimen/dp8"
    android:paddingEnd="@dimen/dp8">

    <CheckBox
        android:id="@+id/selectView"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:visibility="gone"
        android:theme="@style/checkBoxStyle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/imageContainer"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:layout_marginStart="@dimen/dp8"
        app:cardCornerRadius="@dimen/dp8"
        app:cardBackgroundColor="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/selectView"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:background="@color/black700"
            android:src="@drawable/ic_thumb_default"
            android:contentDescription="@string/app_name" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:maxLength="50"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/rightAngle"
        app:layout_constraintStart_toEndOf="@id/imageContainer"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/body"
        tools:text="Episode baru dari Musuh Masyarakat" />

    <TextView
        android:id="@+id/body"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:visibility="gone"
        android:ellipsize="end"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/text_grey3"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0"
        android:autoLink="all"
        app:layout_constraintBottom_toTopOf="@id/notificationTime"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="Season 4 episode 10, Setan tidak perlu disalahkan oleh kamu" />

    <TextView
        android:id="@+id/notificationTime"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:visibility="gone"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="@color/text_grey2"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0"
        tools:text="29 Maret 2022 . 10.00 WIB"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/body" />

    <ImageView
        android:id="@+id/rightAngle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp8"
        android:src="@drawable/ic_angle_right_grey"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp16"
        android:background="@color/skeleton_color"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/imageContainer"
        app:layout_constraintTop_toBottomOf="@id/notificationTime" />
</androidx.constraintlayout.widget.ConstraintLayout>