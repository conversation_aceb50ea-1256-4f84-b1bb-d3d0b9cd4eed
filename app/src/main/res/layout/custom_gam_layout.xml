<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp24"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout
        android:id="@+id/adLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp296"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />



    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txtDescription"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:layout_marginHorizontal="@dimen/dp16"
        app:layout_constraintTop_toBottomOf="@+id/adLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="@dimen/sp16"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        />
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAction"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp48"
        app:backgroundTint="@color/dull_yellow"
        android:textColor="@color/black"
        android:text="@string/action_button"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@+id/txtDescription"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginHorizontal="@dimen/dp16"
        android:layout_marginVertical="@dimen/dp24"
        app:cornerRadius="@dimen/dp24"
        android:textSize="@dimen/sp14"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        />
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/imgCross"
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"
        android:src="@drawable/admob_close_button_black_circle_white_cross"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/dp12"
        android:background="@color/grey"
        app:shapeAppearanceOverlay="@style/roundedImageViewRounded"
        />

</androidx.constraintlayout.widget.ConstraintLayout>