<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="noice.app.R"/>
        <import type="android.view.View"/>
        <import type="noice.app.utils.HandleValues"/>
        <variable
            name="viewModel"
            type="noice.app.modules.live.stream.feature.gift.GiftViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_rounded_black700_24dp">

        <include
            android:id="@+id/layout_input_gift_quantity"
            layout="@layout/layout_input_gift_quantity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/consLayoutHolder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <View
                android:id="@+id/line"
                android:layout_width="@dimen/dp56"
                android:layout_height="@dimen/dp4"
                android:layout_marginTop="@dimen/dp16"
                android:background="@color/color_sheet_drag_bar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/txtTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:fontFamily="sans-serif"
                android:text="@string/give_gift"
                android:textColor="@color/white"
                android:textSize="@dimen/sp18"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtBalCoins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp4"
                android:background="@drawable/background_black_40_percent_54dp"
                android:drawablePadding="@dimen/dp3"
                android:gravity="center"
                android:paddingStart="@dimen/dp12"
                android:paddingTop="@dimen/dp6"
                android:paddingEnd="@dimen/dp12"
                android:paddingBottom="@dimen/dp6"
                tools:text="20.182.224"
                android:text="@{viewModel.state.walletCoins}"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                app:drawableLeftCompat="@drawable/ic_coins"
                app:fontFamily="@font/roboto_bold"
                app:layout_constraintBottom_toBottomOf="@+id/txtTitle"
                app:layout_constraintEnd_toStartOf="@+id/imgNavigateTopUP"
                app:layout_constraintTop_toTopOf="@+id/txtTitle" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgNavigateTopUP"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                android:layout_marginTop="@dimen/dp4"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginBottom="@dimen/dp4"
                app:layout_constraintBottom_toBottomOf="@+id/txtBalCoins"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/txtBalCoins"
                app:srcCompat="@drawable/ic_arrow_forward_filled_yellow" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layoutGifts"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp350"
                android:layout_marginTop="@dimen/dp24"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/txtTitle"
                tools:visibility="visible">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tabIndicator="@drawable/custom_tab_indicator_width_12px"
                    app:tabIndicatorColor="@color/dull_yellow"
                    app:tabIndicatorFullWidth="false"
                    app:tabIndicatorHeight="@dimen/dp2"
                    app:tabMode="scrollable"
                    app:tabPaddingBottom="@dimen/dp4"
                    app:tabPaddingEnd="@dimen/dp8"
                    app:tabPaddingStart="@dimen/dp8"
                    app:tabPaddingTop="@dimen/dp4"
                    app:tabRippleColor="@android:color/transparent"
                    app:tabSelectedTextColor="@color/white"
                    app:tabTextAppearance="@style/NoiceTabLayoutGiftStyle"
                    app:tabTextColor="@color/neutral_grey_900" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewPager"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/size_0dp"
                    app:layout_constraintBottom_toTopOf="@+id/consBottom"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tabLayout"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/consBottom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_marginBottom="@dimen/dp24"
                    android:minHeight="@dimen/dp48"
                    app:visibleIf="@{viewModel.state.selectedItem != null}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible"
                    app:layout_constraintTop_toBottomOf="@+id/viewPager">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/consDropDown"
                        android:layout_width="@dimen/dp83"
                        android:layout_height="@dimen/size_0dp"
                        android:background="@drawable/background_black600_radius_4dp"
                        android:backgroundTint="@color/blackish_grey"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.textview.MaterialTextView
                            android:id="@+id/txtGiftCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp16"
                            android:layout_marginEnd="@dimen/dp8"
                            android:text="@{viewModel.state.sendGiftQuantity}"
                            android:textColor="@color/white300"
                            android:textSize="@dimen/sp16"
                            app:fontFamily="@font/roboto_med"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/imgDrop"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="1" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/imgDrop"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp16"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_arrow_drop_down" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnGiftSend"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="@dimen/size_0dp"
                        android:layout_marginStart="@dimen/dp8"
                        android:backgroundTint="@color/dull_yellow"
                        android:insetLeft="@dimen/size_0dp"
                        android:insetTop="@dimen/size_0dp"
                        android:insetRight="@dimen/size_0dp"
                        android:insetBottom="@dimen/size_0dp"
                        android:letterSpacing=".2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        app:cornerRadius="@dimen/dp4"
                        app:layout_constraintBottom_toBottomOf="@+id/consDropDown"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/consDropDown"
                        app:layout_constraintTop_toTopOf="@+id/consDropDown"
                        app:mpbActionLoading="@{viewModel.state.buttonActionLoading}"
                        app:mpbProgressTint="@{R.color.black90}"
                        app:mpbStartIcon="@{null}"
                        app:mpbTextLabel="@{@string/send_gift}"
                        app:toggleButtonState="@{viewModel.state.selectedItem != null}"
                        tools:text="@string/send_gift" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:indeterminate="true"
                    app:visibleIf="@{viewModel.state.loading &amp;&amp; !viewModel.state.items.empty}"
                    app:indicatorColor="@color/dull_yellow"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/viewBottom"
                android:layout_width="@dimen/dp2"
                android:layout_height="@dimen/dp8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <noice.app.views.ErrorView
                android:id="@+id/errorView"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp200"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>