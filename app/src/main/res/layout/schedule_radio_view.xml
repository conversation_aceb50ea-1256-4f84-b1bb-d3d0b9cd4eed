<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/radioScheduleView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp16"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <TextView
        android:id="@+id/txtSchedule"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp36"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:background="@color/home_search"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:paddingEnd="@dimen/dp16"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="10.00 WIB - 13.00 WIB"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgSchedule"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        android:background="@drawable/round_outline_8"
        android:layout_margin="@dimen/dp16"
        app:layout_constraintTop_toBottomOf="@+id/txtSchedule"
        app:layout_constraintStart_toStartOf="parent"
        app:srcCompat="@drawable/ic_thumb_square"
        />
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/imgSchedule"
        app:layout_constraintTop_toTopOf="@+id/imgSchedule"
        app:layout_constraintBottom_toBottomOf="@+id/imgSchedule"
        >
        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:fontFamily="sans-serif"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAllCaps="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Sarapan Seru"
            />
        <TextView
            android:id="@+id/txtDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/white70"
            android:textSize="@dimen/sp12"
            android:fontFamily="sans-serif"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="@dimen/dp4"
            android:lineHeight="@dimen/dp17"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Sarapan Seru"
            />


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>