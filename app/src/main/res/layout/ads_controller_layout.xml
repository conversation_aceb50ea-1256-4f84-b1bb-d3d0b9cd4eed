<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:padding="@dimen/dp8">

<!--    now in media3player using exo_play_pause button id-->

    <View
        android:id="@+id/preClickPlayPause"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:background="@drawable/circuler_white_transparent"
        android:clickable="true"
        android:focusable="true"/>

    <ImageButton
        android:id="@id/exo_play_pause"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_gravity="center"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        android:background="@drawable/circuler_white"
        android:src="@drawable/ic_black_play" />

    <ProgressBar
        android:id="@+id/loader"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:padding="@dimen/dp3"
        android:visibility="gone"
        android:indeterminateTint="@color/white"/>
</FrameLayout>