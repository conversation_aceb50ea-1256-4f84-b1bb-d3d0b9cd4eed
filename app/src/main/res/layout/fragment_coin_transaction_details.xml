<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".modules.coins.fragments.CoinTransactionDetails">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.enums.TransactionStatus"/>
        <import type="noice.app.enums.PurchaseSubTypes"/>
        <import type="noice.app.enums.TransactionType"/>
        <import type="noice.app.enums.MonthFormat"/>
        <import type="noice.app.utils.Constants" />

        <variable
            name="viewModel"
            type="noice.app.modules.coins.viewmodel.TransactionDetailsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bottom_sheet_rounded_24dp_black"
        android:paddingBottom="@dimen/dp24">

        <View
            android:id="@+id/line"
            android:layout_width="@dimen/dp86"
            android:layout_height="@dimen/dp4"
            android:layout_marginTop="@dimen/dp8"
            android:background="#303030"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textTutup"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginStart="@dimen/dp8"
            android:padding="@dimen/dp8"
            android:fontFamily="@font/readex_pro"
            android:text="@string/tutup"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp14"
            android:background="@drawable/custom_ripple"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <TextView
            android:id="@+id/dialogTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:fontFamily="@font/readex_pro"
            tools:text="@string/status_top_up"
            android:text="@{viewModel.getDialogTitle(viewModel.transaction)}"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp16"
            app:layout_constraintTop_toBottomOf="@id/textTutup"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/okBtn">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/transactionAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:fontFamily="@font/readex_pro"
                    android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                    tools:text="+20"
                    tools:textColor="@color/light_green"
                    android:textColor="@{viewModel.getTxnValueColor(viewModel.transaction)}"
                    android:textSize="@dimen/sp24"
                    android:textStyle="bold"
                    android:lineSpacingExtra="@dimen/sp1"
                    android:drawablePadding="@dimen/dp10"
                    app:drawableStartCompat="@drawable/ic_noice_coin_24"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintEnd_toStartOf="@id/transactionPending"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <ImageView
                    android:id="@+id/transactionPending"
                    android:layout_width="@dimen/dp24"
                    android:layout_height="@dimen/dp24"
                    android:layout_marginStart="@dimen/dp10"
                    android:visibility="@{viewModel.transaction.transactionStatus.equals(TransactionStatus.PENDING.name) ? View.VISIBLE : View.GONE}"
                    android:src="@drawable/ic_transaction_in_progress"
                    android:contentDescription="@string/app_name"
                    app:layout_constraintStart_toEndOf="@id/transactionAmount"
                    app:layout_constraintTop_toTopOf="@id/transactionAmount"
                    app:layout_constraintBottom_toBottomOf="@id/transactionAmount"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <TextView
                    android:id="@+id/transactionTitle"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_marginTop="@dimen/dp24"
                    android:gravity="center"
                    android:text="@{viewModel.getTitle(viewModel.transaction)}"
                    tools:text="Berhasil Top Up Coin"
                    android:fontFamily="@font/readex_pro"
                    android:textColor="@color/dull_white"
                    android:textSize="@dimen/sp18"
                    android:textStyle="bold"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="@dimen/sp4"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transactionAmount" />

                <TextView
                    android:id="@+id/transactionDesc"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:gravity="center"
                    android:text="@{viewModel.getTransactionDesc(viewModel.transaction)}"
                    tools:text="Jumlah coin kamu bertambah"
                    android:fontFamily="@font/readex_pro"
                    android:textColor="@color/medium_greyed"
                    android:textSize="@dimen/sp14"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="@dimen/sp3"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transactionTitle" />

                <FrameLayout
                    android:id="@+id/actionLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transactionDesc">

                    <TextView
                        android:id="@+id/retry"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        tools:visibility="gone"
                        android:visibility="@{viewModel.transaction.transactionStatus.equals(TransactionStatus.PENDING.name) ? View.VISIBLE : View.GONE}"
                        android:gravity="center"
                        android:text="@string/returned"
                        android:fontFamily="@font/readex_pro"
                        android:textStyle="bold"
                        android:textColor="@color/dull_yellow"
                        android:textSize="@dimen/sp12"
                        android:letterSpacing="0.2"
                        android:lineSpacingExtra="0sp" />

                    <TextView
                        android:id="@+id/openAction"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp32"
                        android:layout_marginTop="@dimen/dp8"
                        android:paddingStart="@dimen/dp16"
                        android:paddingEnd="@dimen/dp16"
                        android:background="@drawable/background_white_4dp_round"
                        tools:visibility="gone"
                        android:visibility="@{viewModel.transaction.type.equals(TransactionType.PURCHASE.name) &amp;&amp; viewModel.transaction.purchase.entityType.equals(`content`) ? View.VISIBLE : View.GONE}"
                        android:gravity="center"
                        android:text="@string/open_content"
                        android:fontFamily="@font/readex_pro"
                        android:textStyle="bold"
                        android:textAllCaps="true"
                        android:textColor="@color/black"
                        android:textSize="@dimen/sp12"
                        android:letterSpacing="0.2"
                        android:lineSpacingExtra="0sp" />
                </FrameLayout>

                <View
                    android:id="@+id/line1"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp1"
                    android:layout_marginTop="@dimen/dp24"
                    android:background="@color/blackish_grey"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/actionLayout"/>

                <TextView
                    android:id="@+id/coinDetailsTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    android:layout_marginStart="@dimen/dp16"
                    app:goneIf="@{viewModel.transaction.type.equals(TransactionType.PURCHASE.name) &amp;&amp; viewModel.transaction.purchase.entityType.equals(PurchaseSubTypes.SUBSCRIPTION.value)}"
                    tools:text="@string/login_coin_details"
                    android:text="@{viewModel.getCoinsDetailTitle(viewModel.transaction)}"
                    android:fontFamily="@font/readex_pro"
                    android:textStyle="bold"
                    android:textColor="@color/medium_greyed"
                    android:textSize="@dimen/sp12"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="0sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/line1"/>

                <TextView
                    android:id="@+id/transactionDBId"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp18"
                    android:layout_marginStart="@dimen/dp16"
                    android:text="@string/id_transaction"
                    android:fontFamily="@font/readex_pro"
                    android:textColor="@color/medium_greyed"
                    android:textSize="@dimen/sp14"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="@dimen/sp3"
                    app:layout_constraintWidth_percent="0.38"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/coinDetailsTitle" />

                <TextView
                    android:id="@+id/transactionDBIdValue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp4"
                    android:layout_marginEnd="@dimen/dp6"
                    android:gravity="end"
                    tools:text="1054587014"
                    android:text="@{viewModel.transaction.transactionId}"
                    android:fontFamily="@font/readex_pro"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp14"
                    android:textStyle="bold"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="@dimen/sp3"
                    android:maxLines="1"
                    android:ellipsize="end"
                    app:layout_constraintStart_toEndOf="@id/transactionDBId"
                    app:layout_constraintTop_toTopOf="@id/transactionDBId"
                    app:layout_constraintEnd_toStartOf="@id/copyTransactionDBId"/>

                <TextView
                    android:id="@+id/copyTransactionDBId"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp24"
                    android:layout_marginEnd="@dimen/dp16"
                    android:paddingStart="@dimen/dp8"
                    android:paddingEnd="@dimen/dp8"
                    android:gravity="center"
                    android:textAllCaps="true"
                    android:text="@string/copy"
                    android:textSize="@dimen/sp10"
                    android:textColor="@color/text_grey3"
                    android:textStyle="bold"
                    android:fontFamily="@font/readex_pro"
                    android:letterSpacing="0.2"
                    android:lineSpacingExtra="0sp"
                    android:background="@drawable/background_blackish_grey_4dp"
                    android:drawablePadding="@dimen/dp8"
                    app:drawableStartCompat="@drawable/ic_copy"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintTop_toTopOf="@id/transactionDBIdValue"
                    app:layout_constraintBottom_toBottomOf="@id/transactionDBIdValue"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/transactionDBIdValue" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/refundTxnDBIdLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp17"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.type.equals(TransactionType.REFUND.name) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/transactionDBId">

                    <TextView
                        android:id="@+id/refundTxnDBId"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/cancelled_txn_id"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <TextView
                        android:id="@+id/refundTxnDBIdValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp6"
                        android:gravity="end"
                        tools:text="1054587014"
                        android:text="@{viewModel.transaction.refund.forTransactionId}"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:layout_constraintStart_toEndOf="@id/refundTxnDBId"
                        app:layout_constraintTop_toTopOf="@id/refundTxnDBId"
                        app:layout_constraintEnd_toStartOf="@id/copyRefundTxnDBId"/>

                    <TextView
                        android:id="@+id/copyRefundTxnDBId"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp24"
                        android:layout_marginEnd="@dimen/dp16"
                        android:paddingStart="@dimen/dp8"
                        android:paddingEnd="@dimen/dp8"
                        android:gravity="center"
                        android:textAllCaps="true"
                        android:text="@string/copy"
                        android:textSize="@dimen/sp10"
                        android:textColor="@color/text_grey3"
                        android:textStyle="bold"
                        android:fontFamily="@font/readex_pro"
                        android:letterSpacing="0.2"
                        android:lineSpacingExtra="0sp"
                        android:background="@drawable/background_blackish_grey_4dp"
                        android:drawablePadding="@dimen/dp8"
                        app:drawableStartCompat="@drawable/ic_copy"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintTop_toTopOf="@id/refundTxnDBIdValue"
                        app:layout_constraintBottom_toBottomOf="@id/refundTxnDBIdValue"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/refundTxnDBIdValue" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/dateLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    app:visibleIf="@{viewModel.dateLayoutVisibility(viewModel.transaction)}"
                    tools:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/refundTxnDBIdLayout">

                    <TextView
                        android:id="@+id/transactionDate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp18"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/date"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/transactionDateValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="end"
                        tools:text="20 Okt 2022 18:58"
                        monthFormat="@{MonthFormat.THREE_LETTER}"
                        createdAt="@{viewModel.transaction.createdAt}"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/transactionDate"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/transactionDate"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/topUpLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.type.equals(TransactionType.TOP_UP.name) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/dateLayout">

                    <TextView
                        android:id="@+id/paymentMethod"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/payment_method"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/paymentMethodValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="end"
                        android:text="@{viewModel.transaction.topup.paymentMethod}"
                        tools:text="Google Play Store"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/paymentMethod"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/paymentMethod"/>

                    <TextView
                        android:id="@+id/totalPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/total"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/paymentMethod"
                        app:layout_constraintStart_toStartOf="@id/paymentMethod"/>

                    <TextView
                        android:id="@+id/totalPriceValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="end"
                        android:text="@{viewModel.transaction.topup.storePriceFormat}"
                        tools:text="Rp16.000"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/totalPrice"
                        app:layout_constraintTop_toTopOf="@id/totalPrice"
                        app:layout_constraintEnd_toEndOf="@id/paymentMethodValue"/>

                    <TextView
                        android:id="@+id/totalCoins"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/coin"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/totalPrice"
                        app:layout_constraintStart_toStartOf="@id/paymentMethod"/>

                    <TextView
                        android:id="@+id/totalCoinsValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        android:text="@{viewModel.getFormattedCoins(viewModel.transaction)}"
                        tools:text="+12"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/light_green"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/totalCoins"
                        app:layout_constraintEnd_toEndOf="@id/paymentMethodValue"/>

                    <TextView
                        android:id="@+id/bonusCoins"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/bonus_coin"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/totalCoins"
                        app:layout_constraintStart_toStartOf="@id/paymentMethod"/>

                    <TextView
                        android:id="@+id/bonusCoinsValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        android:text="@{viewModel.getFormattedFreeCoins(viewModel.transaction)}"
                        tools:text="+8"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/light_green"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/bonusCoins"
                        app:layout_constraintEnd_toEndOf="@id/paymentMethodValue"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/virtualGiftLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.type.equals(TransactionType.GIFT.name) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/topUpLayout">

                    <TextView
                        android:id="@+id/giftPurchase"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/pembelian_hadiah"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/giftPurchaseValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="end"
                        android:text="@{viewModel.getGift(viewModel.transaction)}"
                        tools:text="Martabak x 2"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/giftPurchase"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/giftPurchase"/>

                    <TextView
                        android:id="@+id/coinUsed"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/coins_used"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/giftPurchaseValue"
                        app:layout_constraintStart_toStartOf="@id/giftPurchase"/>

                    <TextView
                        android:id="@+id/coinUsedValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                        tools:text="-12"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/red"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/coinUsed"
                        app:layout_constraintEnd_toEndOf="@id/giftPurchaseValue"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/rewardLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.type.equals(TransactionType.REWARD.name) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/virtualGiftLayout">

                    <TextView
                        android:id="@+id/coinReward"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/coin_reward"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/coinRewardValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="center"
                        android:text="@{viewModel.getFormattedFreeCoins(viewModel.transaction)}"
                        tools:text="+12"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/light_green"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/coinReward"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ppcLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:visibility="gone"
                    app:visibleIf="@{viewModel.transaction.type.equals(TransactionType.PURCHASE.name) &amp;&amp; viewModel.transaction.purchase.entityType.equals(PurchaseSubTypes.CONTENT.value)}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/rewardLayout">

                    <TextView
                        android:id="@+id/totalContentPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/total"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/totalContentPriceValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                        tools:text="+12"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/red"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/totalContentPrice"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/refundLayout"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.type.equals(TransactionType.REFUND.name) ? View.VISIBLE : View.GONE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ppcLayout">

                    <TextView
                        android:id="@+id/deductedCoins"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/coins_used"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/deductedCoinsValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                        tools:text="-12"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/red"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/deductedCoins"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/subLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    tools:visibility="gone"
                    android:paddingStart="@dimen/dp16"
                    android:paddingEnd="@dimen/dp16"
                    app:visibleIf="@{viewModel.transaction.type.equals(TransactionType.PURCHASE.name) &amp;&amp; viewModel.transaction.purchase.entityType.equals(PurchaseSubTypes.SUBSCRIPTION.value)}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/refundLayout">

                    <TextView
                        android:id="@+id/packageName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/package_ind"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        app:goneIf="@{viewModel.transaction.packageType.equals(Constants.Subscription.PACKAGE_TYPE_LITE)}"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/packageNameValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="end"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:text="@{viewModel.transaction.purchase.entityTitle}"
                        app:goneIf="@{viewModel.transaction.packageType.equals(Constants.Subscription.PACKAGE_TYPE_LITE)}"
                        tools:text="Title"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintStart_toEndOf="@id/packageName"
                        app:layout_constraintTop_toTopOf="@id/packageName"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <TextView
                        android:id="@+id/packagePrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/price"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/packageNameValue"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/packagePriceValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValueWithoutSign(viewModel.transaction) + ` Coins`}"
                        tools:text="129 Coins"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/packagePrice"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <TextView
                        android:id="@+id/purchaseDate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/tanggal_"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/packagePriceValue"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/purchaseDateValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        tools:text="20 Okt 2022 18:58"
                        monthFormat="@{MonthFormat.THREE_LETTER}"
                        createdAt="@{viewModel.transaction.purchase.createdAt}"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/purchaseDate"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <TextView
                        android:id="@+id/expiryDate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:text="@string/package_preriod"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        app:goneIf="@{viewModel.transaction.packageType.equals(Constants.Subscription.PACKAGE_TYPE_LITE)}"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toBottomOf="@id/purchaseDateValue"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/expiryDateValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:gravity="center"
                        tools:text="20 Okt 2022 18:58"
                        app:goneIf="@{viewModel.transaction.packageType.equals(Constants.Subscription.PACKAGE_TYPE_LITE)}"
                        monthFormat="@{MonthFormat.THREE_LETTER}"
                        createdAt="@{viewModel.transaction.purchase.expiryTime}"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/expiryDate"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/liveStreamLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    tools:visibility="visible"
                    app:visibleIf="@{viewModel.transaction.type.equals(TransactionType.PURCHASE.name) &amp;&amp; viewModel.transaction.purchase.entityType.equals(PurchaseSubTypes.LIVE_STREAM.value)}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/subLayout">

                    <TextView
                        android:id="@+id/liveCoinUsed"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/coins_used"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/liveCoinUsedValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                        tools:text="-120"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/red"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/liveCoinUsed"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/defaultLayout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    tools:visibility="gone"
                    android:visibility="@{viewModel.transaction.isHandledTxn ? View.GONE : View.VISIBLE}"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/liveStreamLayout">

                    <TextView
                        android:id="@+id/defaultCoin"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:text="@string/coin"
                        android:fontFamily="@font/readex_pro"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintWidth_percent="0.38"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/defaultCoinValue"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginEnd="@dimen/dp16"
                        android:gravity="center"
                        android:text="@{viewModel.getTxnValue(viewModel.transaction)}"
                        tools:text="+12"
                        android:fontFamily="@font/readex_pro"
                        tools:textColor="@color/light_green"
                        android:textColor="@{viewModel.getTxnValueColor(viewModel.transaction)}"
                        android:textSize="@dimen/sp14"
                        android:textStyle="bold"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:drawableStartCompat="@drawable/ic_noice_coin"
                        android:drawablePadding="@dimen/dp8"
                        app:layout_constraintTop_toTopOf="@id/defaultCoin"
                        app:layout_constraintEnd_toEndOf="parent"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/line2"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp1"
                    android:layout_marginTop="@dimen/dp32"
                    android:background="@color/blackish_grey"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/defaultLayout"/>

                <TextView
                    android:id="@+id/needHelp"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp24"
                    android:text="@string/need_help"
                    android:fontFamily="@font/readex_pro"
                    android:textColor="@color/neutral_90"
                    android:textSize="@dimen/sp12"
                    android:textStyle="bold"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="0sp"
                    app:layout_constraintTop_toBottomOf="@id/line2"
                    app:layout_constraintStart_toStartOf="@id/transactionDBId"/>

                <TextView
                    android:id="@+id/helpStr"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    android:layout_marginEnd="@dimen/dp16"
                    android:fontFamily="@font/readex_pro"
                    android:textColorLink="@color/neutral_90"
                    android:text="@string/coin_detail_help_str"
                    android:textColor="@color/neutral_90"
                    android:textSize="@dimen/sp14"
                    android:letterSpacing="0"
                    android:lineSpacingExtra="@dimen/sp3"
                    app:layout_constraintTop_toBottomOf="@id/needHelp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/transactionDBId"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/defaultHelpSection"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp8"
                    android:layout_marginEnd="@dimen/dp16"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/transactionDBId"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/needHelp">

                    <TextView
                        android:id="@+id/dot1"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp8"
                        android:fontFamily="@font/readex_pro"
                        android:text="@string/bullet"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"/>

                    <TextView
                        android:id="@+id/helpStr1"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp8"
                        android:fontFamily="@font/readex_pro"
                        android:textColorLink="@color/dull_yellow"
                        tools:text="@string/coin_detail_help_str_default1"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/dot1"
                        app:layout_constraintTop_toTopOf="@id/dot1"/>

                    <TextView
                        android:id="@+id/dot2"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp6"
                        android:fontFamily="@font/readex_pro"
                        android:text="@string/bullet"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toStartOf="@id/dot1"
                        app:layout_constraintTop_toBottomOf="@id/helpStr1"/>

                    <TextView
                        android:id="@+id/helpStr2"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp8"
                        android:fontFamily="@font/readex_pro"
                        android:textColorLink="@color/dull_yellow"
                        tools:text="@string/coin_detail_help_str_default1"
                        android:textColor="@color/medium_greyed"
                        android:textSize="@dimen/sp14"
                        android:letterSpacing="0"
                        android:lineSpacingExtra="@dimen/sp3"
                        app:layout_constraintStart_toEndOf="@id/dot2"
                        app:layout_constraintTop_toTopOf="@id/dot2"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/okBtn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp52"
            android:layout_marginTop="@dimen/dp22"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp16"
            android:insetTop="@dimen/size_0dp"
            android:insetBottom="@dimen/size_0dp"
            android:gravity="center"
            app:cornerRadius="@dimen/dp14"
            android:backgroundTint="@color/dull_yellow"
            android:text="@{viewModel.getOkButtonText(viewModel.transaction)}"
            tools:text="@string/okay"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="@dimen/sp14"
            android:lineSpacingExtra="0sp"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:elevation="@dimen/dp24"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textTutup"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>