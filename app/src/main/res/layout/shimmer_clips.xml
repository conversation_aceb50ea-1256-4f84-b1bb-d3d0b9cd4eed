<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".modules.clips.activities.ClipsActivity">

    <View
        android:id="@+id/linear_progress_bars"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp8"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/text_clip_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp25"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_cross"
        tools:text="Udahlah ga usah doa" />

    <View
        android:id="@+id/text_clip_title_2"
        android:layout_width="@dimen/dp190"
        android:layout_height="@dimen/dp25"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_clip_title" />

    <ImageView
        android:id="@+id/image_play"
        android:layout_width="@dimen/dp50"
        android:layout_height="@dimen/dp50"
        android:src="@drawable/ic_play_clip"
        android:visibility="gone"
        android:contentDescription="@string/app_name"
        app:layout_constraintBottom_toTopOf="@+id/label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_clip_title" />

    <FrameLayout
        android:id="@+id/frame_left"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toTopOf="@id/label"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/frame_right"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toTopOf="@id/label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/image_cross"
        android:layout_width="@dimen/dp25"
        android:layout_height="@dimen/dp25"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:contentDescription="@string/cross"
        android:background="@drawable/background_skeleton_8dp"
        android:src="@drawable/ic_close_without_square"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/linear_progress_bars" />

    <TextView
        android:id="@+id/label"
        android:layout_width="@dimen/dp70"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        android:fontFamily="@font/roboto_medium_numbers"
        app:layout_constraintBottom_toTopOf="@id/clipPlayerView"
        app:layout_constraintStart_toStartOf="@id/clipPlayerView" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clipPlayerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp16"
        android:background="@drawable/background_blackish_grey_8dp"
        android:padding="@dimen/dp16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/imageCard"
            android:layout_width="@dimen/dp48"
            android:layout_height="@dimen/dp48"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp12"
            app:layout_constraintEnd_toStartOf="@id/txtTitle"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:background="@drawable/background_skeleton_8dp" />

        <View
            android:id="@+id/txtTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp15"
            android:layout_marginStart="@dimen/dp8"
            android:background="@drawable/background_skeleton_8dp"
            app:layout_constraintBottom_toTopOf="@id/txtCatalog"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imageCard"
            app:layout_constraintTop_toTopOf="@+id/imageCard" />

        <View
            android:id="@+id/txtCatalog"
            android:layout_width="@dimen/dp190"
            android:layout_height="@dimen/dp15"
            android:background="@drawable/background_skeleton_8dp"
            android:fontFamily="@font/roboto"
            android:textColor="@color/grey"
            app:layout_constraintBottom_toBottomOf="@id/imageCard"
            app:layout_constraintStart_toStartOf="@+id/txtTitle"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtCatalog"
            android:baselineAligned="false">

            <RelativeLayout
                android:id="@+id/relative_play"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp12"
                android:layout_weight="1">

                <View
                    android:id="@+id/imgPlay"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp12"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_play_button"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@id/imgPlay"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/background_skeleton_8dp"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="@id/imgPlay"
                    app:layout_constraintStart_toStartOf="@+id/imgPlay" />

                <View
                    android:id="@+id/text_play_button_2"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@id/text_play_button"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/background_skeleton_8dp"
                    android:gravity="center" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/relative_download"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp12"
                android:layout_weight="1">

                <View
                    android:id="@+id/image_download"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp12"
                    android:adjustViewBounds="true"
                    android:background="@drawable/background_skeleton_8dp"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_download" />

                <View
                    android:id="@+id/text_download"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@+id/image_download"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_download_2"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@id/text_download"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/background_skeleton_8dp"
                    android:gravity="center" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/relative_add_to_queue"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp12"
                android:layout_weight="1">

                <View
                    android:id="@+id/image_add_to_queue"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp12"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_add_to_queue"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@+id/image_add_to_queue"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_add_to_queue_2"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@id/text_add_to_queue"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/background_skeleton_8dp"
                    android:gravity="center" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/relative_add_to_playlist"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp12"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_add_to_playlist"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp12"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_add_to_playlist"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@+id/image_add_to_playlist"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/background_skeleton_8dp" />

                <View
                    android:id="@+id/text_add_to_playlist_2"
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp10"
                    android:layout_below="@id/text_add_to_playlist"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/background_skeleton_8dp"
                    android:gravity="center" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>