<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/label"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp20"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:text="@string/my_collection"
                android:textColor="@color/white"
                android:textSize="@dimen/sp24"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <noice.app.views.RootTitleRecycler
                android:id="@+id/continueListening"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:listPaddingStart="@dimen/dp8"
                app:headingPaddingTop="@dimen/dp16"
                app:headingSize="@dimen/sp20"
                app:headingText="@string/continue_listening"
                app:headingTextColor="@color/white80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/label" />

            <TextView
                android:id="@+id/history"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/history"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/continueListening"
                app:drawableStartCompat="@drawable/ic_clock_white" />

            <TextView
                android:id="@+id/likedContent"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/liked_content"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/history"
                app:drawableStartCompat="@drawable/ic_thumb_up_without_square" />

            <TextView
                android:id="@+id/downloads"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/download"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/likedContent"
                app:drawableStartCompat="@drawable/ic_download_16dp" />

            <TextView
                android:id="@+id/yourPodCast"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/podcast"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/downloads"
                app:drawableStartCompat="@drawable/ic_podcast_white" />

            <TextView
                android:id="@+id/yourRadio"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp10"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/radio"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/yourPodCast"
                app:drawableStartCompat="@drawable/ic_radio_white" />

            <TextView
                android:id="@+id/yourNoiceBook"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp12"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/audio_book"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/yourRadio"
                app:drawableStartCompat="@drawable/ic_noice_book_icon" />

            <TextView
                android:id="@+id/yourOriginalSeries"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/dp48"
                android:layout_marginTop="@dimen/dp16"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:drawablePadding="@dimen/dp8"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:gravity="center_vertical"
                android:lineSpacingExtra="@dimen/sp5"
                android:paddingStart="@dimen/dp14"
                android:paddingEnd="@dimen/dp14"
                android:text="@string/audio_series"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp14"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/yourNoiceBook"
                app:drawableStartCompat="@drawable/ic_audio_series_icon" />

            <TextView
                android:id="@+id/plTitle"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp30"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:lineSpacingExtra="@dimen/sp8"
                android:text="@string/playlist"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp20"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="@id/history"
                app:layout_constraintStart_toStartOf="@id/history"
                app:layout_constraintTop_toBottomOf="@id/yourOriginalSeries" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/playListRv"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"

                android:nestedScrollingEnabled="false"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/plTitle"
                app:spanCount="2" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/emptyView"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp12"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/plTitle">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp59"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"
                    app:srcCompat="@drawable/ic_empty_playlist"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp16"
                    android:fontFamily="@font/readex_pro"
                    android:includeFontPadding="false"
                    android:text="@string/playlist_empty"
                    android:textColor="@color/white"
                    android:textSize="@dimen/sp18"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv" />

                <TextView
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginStart="@dimen/dp24"
                    android:layout_marginTop="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp24"
                    android:fontFamily="@font/readex_pro"
                    android:includeFontPadding="false"
                    android:gravity="center"
                    android:paddingBottom="@dimen/dp63"
                    android:text="@string/playlist_empty_text"
                    android:textColor="@color/white80"
                    android:textSize="@dimen/sp16"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/playlistBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:text="@string/playlist"
        android:textColor="@color/black"
        android:textSize="@dimen/sp12"
        android:textStyle="bold"
        app:backgroundTint="@color/dull_yellow"
        app:icon="@drawable/ic_plus_black"
        app:iconSize="@dimen/dp11"
        app:layout_anchor="@id/nestedScrollView"
        app:layout_anchorGravity="bottom|end"
        app:rippleColor="@color/white" />

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/homeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="@dimen/dp48"
        android:fitsSystemWindows="false" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>