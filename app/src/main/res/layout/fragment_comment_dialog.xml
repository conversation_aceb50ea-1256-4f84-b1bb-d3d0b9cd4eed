<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black700">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp56"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgClose"
            android:layout_width="@dimen/dp20"
            android:layout_height="@dimen/dp20"
            android:layout_marginStart="@dimen/dp16"
            android:background="?selectableItemBackgroundBorderless"
            android:padding="@dimen/dp4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_close_without_square" />

        <TextView
            android:id="@+id/txtComments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:background="@drawable/custom_ripple"
            android:fontFamily="sans-serif"
            android:gravity="end"
            android:text="@string/see_all_comment"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/imgClose"
            app:layout_constraintStart_toEndOf="@+id/imgClose"
            app:layout_constraintTop_toTopOf="@+id/imgClose" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layoutFilter"
        android:layout_width="@dimen/dp118"
        android:layout_height="@dimen/dp32"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/border_white_radius_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/txtFilter"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="0.8"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:visibility="visible"
            app:fontFamily="sans-serif" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/hairTypeFilterArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_weight="0.2"
            app:srcCompat="@drawable/ic_arrow_down"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/commentRecyclerView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp16"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutFilter"
        tools:itemCount="5"
        tools:listitem="@layout/comment_view" />

    <LinearLayout
        android:id="@+id/emptyView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black700"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp24"
        android:paddingBottom="@dimen/dp25"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_min="@dimen/dp200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_no_comment_yet" />

        <TextView
            android:id="@+id/emptyViewHeading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:fontFamily="sans-serif"
            android:gravity="end"
            android:text="@string/no_comments_yet"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/emptyViewMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp30"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp30"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:text="@string/comment_empty_message"
            android:textColor="@color/white70"
            android:textSize="@dimen/sp14" />
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/chatEmptyView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_min="@dimen/dp280"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/txtHeading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dp16"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center"
            android:lineHeight="@dimen/dp20"
            android:text="@string/no_live_chat_yet"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            app:drawableTopCompat="@drawable/ic_message"
            app:layout_constraintBottom_toTopOf="@id/txtDesc"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/txtDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:drawablePadding="@dimen/dp16"
            android:fontFamily="@font/roboto"
            android:gravity="center_horizontal"
            android:lineHeight="@dimen/dp20"
            android:text="@string/so_lets_get_started_first"
            android:textColor="@color/white70"
            android:textSize="@dimen/sp14"
            app:layout_constraintBottom_toTopOf="@id/txtRefresh"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtHeading" />

        <TextView
            android:id="@+id/txtRefresh"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp32"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/border_dull_yellow_dark_radius_4dp"
            android:drawablePadding="@dimen/dp6"
            android:fontFamily="@font/roboto"
            android:gravity="center"
            android:paddingStart="@dimen/dp12"
            android:paddingEnd="@dimen/dp12"
            android:text="@string/refresh"
            android:textColor="@color/dull_yellow_dark"
            android:textSize="@dimen/sp16"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_refresh"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtDesc" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/disableView"
        layout="@layout/comment_disable_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_min="@dimen/dp200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black700"
        android:clickable="true"
        android:focusable="true"
        app:cardBackgroundColor="@color/black700"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/dp8"
        app:layout_constraintBottom_toTopOf="@id/line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintVertical_bias="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvUser"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:nestedScrollingEnabled="false"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp16"
            android:paddingTop="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            android:paddingBottom="@dimen/dp16"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="3"
            tools:listitem="@layout/content_horizontal_segment" />

        <LinearLayout
            android:id="@+id/userEmptyView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp200"
            android:background="@color/black700"
            android:clickable="true"
            android:focusable="true"
            android:orientation="vertical"
            android:padding="@dimen/dp16"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/emptyMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp6"
                android:text="@string/couldnt_find_shdiahduw"
                android:textColor="@color/white"
                android:textSize="@dimen/sp14"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/sp6"
                android:text="@string/no_user_search_result"
                android:textColor="@color/white70"
                android:textSize="@dimen/sp14" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp1"
        android:background="@color/white20"
        app:layout_constraintBottom_toTopOf="@id/commentLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/commentLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/dp16"
        android:paddingTop="@dimen/dp12"
        android:paddingEnd="@dimen/dp16"
        android:paddingBottom="@dimen/dp12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <noice.app.views.CreateCommentView
            android:id="@+id/createCommentView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/notLoggedIn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp48"
            android:background="@drawable/background_blackish_blue_4dp"
            android:clickable="true"
            android:focusable="true"
            android:fontFamily="sans-serif"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            android:text="@string/login_to_comment"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:visibility="gone" />
    </FrameLayout>

    <RelativeLayout
        android:id="@+id/userBanLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp50"
        android:background="@color/neutral_30"
        android:clickable="true"
        android:focusable="true"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/imgComment"
            android:layout_width="@dimen/dp20"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            android:src="@drawable/ic_comment_white"
            android:tintMode="src_atop"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/txtUserBan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/imgComment"
            android:fontFamily="sans-serif"
            android:ellipsize="end"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/txtUserBanButton"
            android:text="@string/comment_ban_text"
            android:textColor="@color/white"
            android:layout_marginStart="@dimen/dp16"
            android:textSize="@dimen/sp14" />

        <TextView
            android:id="@+id/txtUserBanButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/custom_ripple"
            android:fontFamily="sans-serif"
            android:text="@string/selengkapnya"
            android:padding="@dimen/dp5"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp14" />
    </RelativeLayout>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_min="@dimen/dp300"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>