<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/contentHorizontalSegment"
    android:layout_width="@dimen/dp264"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    android:orientation="horizontal">

    <androidx.cardview.widget.CardView
        android:id="@+id/imageCard"
        android:layout_width="@dimen/dp64"
        android:layout_height="@dimen/dp64"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"/>

        <noice.app.views.EqualizerView
            android:id="@+id/equalizer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:marginLeft="@dimen/dp3"
            app:marginRight="@dimen/dp3"
            app:barHeight="@dimen/dp40"
            android:background="@color/black30"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/image_noice_original"
            android:layout_width="@dimen/dp16"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|start"
            android:layout_marginStart="2dp"
            android:layout_marginBottom="2dp"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            tools:src="@drawable/ic_noice_original"
            android:visibility="gone"
            tools:visibility="visible" />

    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/origExc"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp12"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp2"
        android:maxLines="1"
        android:visibility="gone"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp8"
        android:textStyle="normal"
        app:layout_constraintStart_toEndOf="@id/imageCard"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintTop_toTopOf="@id/imageCard"
        tools:text="EXCLUSIVE" />

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:maxLines="1"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp6"
        tools:text="Musuh Masyarakat"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/imageCard"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintTop_toBottomOf="@id/origExc"
        app:layout_constraintBottom_toTopOf="@id/subTitle"/>

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:maxLines="1"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp12"
        android:textColor="@color/medium_grey"
        tools:text="Coki Pardede, Tretan Muslim"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintBottom_toBottomOf="@id/imageCard" />

    <ImageView
        android:id="@+id/cross"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginEnd="@dimen/dp5"
        android:visibility="gone"
        android:padding="@dimen/dp10"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_close_without_square"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp16"
        android:background="@color/white10"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imageCard"/>
</androidx.constraintlayout.widget.ConstraintLayout>