<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linearYou"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black"
    android:gravity="center_vertical"
    app:layout_constraintStart_toStartOf="parent"
    android:layout_marginBottom="@dimen/dp20"
    app:layout_constraintTop_toBottomOf="@+id/textCariHostAtauTamuLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imageHost"
        android:layout_width="@dimen/dp45"
        android:layout_height="@dimen/dp45"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp10"
        android:background="@drawable/round_outline"
        app:srcCompat="@drawable/ic_user_profile" />

    <ImageView
        android:id="@+id/imageShield"
        android:layout_width="@dimen/dp15"
        android:layout_height="@dimen/dp15"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp3"
        android:layout_toEndOf="@+id/imageHost"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_shield" />

    <TextView
        android:id="@+id/textHostName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/imageShield"
        android:fontFamily="@font/roboto"
        android:maxLines="2"
        android:text="@string/you"
        android:textColor="@color/light_blue"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/imageVerified"
        android:layout_width="@dimen/dp20"
        android:layout_height="@dimen/dp20"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp3"
        android:layout_toEndOf="@+id/textHostName"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_verified_tag" />

    <TextView
        android:id="@+id/textHupus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp30"
        android:drawablePadding="@dimen/dp10"
        android:fontFamily="@font/roboto"
        android:text="@string/delete"
        android:textColor="@color/dull_yellow"
        android:textSize="16sp"/>
</RelativeLayout>