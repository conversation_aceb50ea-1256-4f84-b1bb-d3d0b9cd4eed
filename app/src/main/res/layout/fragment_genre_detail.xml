<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.indexpages.fragment.GenreDetailFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black700">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:collapsedTitleTextAppearance="@style/CollapsedAppBar"
            app:expandedTitleTextAppearance="@style/ExpandedAppBar"
            app:expandedTitleGravity="center"
            app:contentScrim="@color/black700"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_collapseMode="parallax">

                <ImageView
                    android:id="@+id/imgCover"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/channel_gradiant" />
            </FrameLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:contentInsetStartWithNavigation="0dp"
                app:navigationIcon="@drawable/ic_left_arrow_white"
                app:layout_anchor="@id/collapsingToolbar"
                app:theme="@style/ThemeOverlay.AppCompat.Dark"
                app:title=""
                android:background="@color/black700">

                <ImageView
                    android:id="@+id/search"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"
                    android:layout_marginEnd="@dimen/dp12"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_search_white"
                    android:background="?selectableItemBackgroundBorderless"
                    android:contentDescription="@string/app_name"/>
            </androidx.appcompat.widget.Toolbar>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/podCastRecycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?actionBarSize"/>
</androidx.coordinatorlayout.widget.CoordinatorLayout>