<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black700">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed|enterAlways|snap">

                <noice.app.views.ToolbarView
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:clickable="true"
                    app:titleTextSize="@dimen/sp15"
                    app:toolbarBackIcon="@drawable/ic_left_angle_white"
                    app:toolbarViewTitle="Top Ranking"
                    app:toolbarBackgroundColor="@color/black700"/>
            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black700"
                app:tabBackground="@color/black700"
                app:tabGravity="start"
                app:tabIndicator="@drawable/custom_tab_indicator"
                app:tabIndicatorColor="@color/dull_yellow"
                app:tabIndicatorFullWidth="false"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/white"
                app:tabTextAppearance="@style/NoiceTabLayoutStyle"
                app:tabTextColor="@color/white50" />

            <View
                android:layout_width="match_parent"
                android:layout_height=".8dp"
                android:background="@color/home_search" />
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <noice.app.views.ToolbarView
            android:id="@+id/toolbar_loading"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:titleTextSize="@dimen/sp15"
            app:toolbarBackIcon="@drawable/ic_left_angle_white"
            android:visibility="invisible"
            app:toolbarViewTitle="Top Ranking" />

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</RelativeLayout>