<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tabRootLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tabTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:textColor="@color/neutral_90"
        android:textSize="16sp"
        tools:text="episode" />

    <ImageView
        android:id="@+id/tabBadge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp5"
        android:layout_toEndOf="@id/tabTitle"
        android:src="@drawable/badge_baru"
        android:visibility="gone" />

</RelativeLayout>