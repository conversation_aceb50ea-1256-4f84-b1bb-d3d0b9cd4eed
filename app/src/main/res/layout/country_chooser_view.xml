<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="country"
            type="noice.app.modules.onboarding.models.Country" />
    </data>

    <LinearLayout
        android:id="@+id/countryLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:paddingStart="@dimen/dp12"
        android:paddingEnd="@dimen/dp12"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/flag"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:fontFamily="@font/readex_pro"
            android:text="@{country.flag}"
            android:textSize="@dimen/sp20" />

        <TextView
            android:id="@+id/countryName"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp16"
            android:gravity="center_vertical"
            android:fontFamily="@font/readex_pro"
            tools:text="India"
            android:text="@{country.name + ` (` + country.dialingCode + `)`}"
            android:textSize="@dimen/sp16"
            android:textColor="@color/neutral_90"
            android:letterSpacing="0.01"
            android:lineSpacingExtra="0sp" />
    </LinearLayout>
</layout>