<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relative_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/image_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/app_name"
        android:scaleType="centerCrop"
        android:visibility="gone"
        android:src="@drawable/ic_banner_default" />

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerViewClip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:show_timeout="0"
        android:visibility="gone"
        app:use_controller="false" />
</RelativeLayout>

