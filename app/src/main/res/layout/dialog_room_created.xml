<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    android:paddingStart="@dimen/dp20"
    android:paddingEnd="@dimen/dp20"
    android:paddingBottom="@dimen/dp24"
    tools:context=".modules.dashboard.home.fragment.ShareDialog">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/cancelBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp18"
        android:fontFamily="sans-serif"
        android:padding="@dimen/dp8"
        android:text="@string/tutup"
        android:background="@drawable/custom_ripple"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp14"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp26"
        android:fontFamily="sans-serif"
        android:text="@string/room_berhasil_dibuat"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imageDialogRoomCreated"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp185"
        android:layout_marginTop="@dimen/dp30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cancelBtn"
        app:srcCompat="@drawable/ic_room_created" />

    <TextView
        android:id="@+id/textBagikanLewat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp26"
        android:fontFamily="sans-serif"
        android:text="@string/share_via_indo"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imageDialogRoomCreated" />

    <TextView
        android:id="@+id/instagram"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp45"
        android:layout_marginTop="@dimen/dp10"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/instagram_story"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        app:drawableStartCompat="@drawable/ic_share_circle_36dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textBagikanLewat" />

    <TextView
        android:id="@+id/whatsapp"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp45"
        android:layout_marginTop="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:drawablePadding="@dimen/dp13"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp13"
        android:paddingEnd="@dimen/dp13"
        android:text="@string/whatsapp"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        app:drawableStartCompat="@drawable/ic_whatsapp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/instagram" />

    <TextView
        android:id="@+id/textCopyLink"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp45"
        android:layout_marginTop="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:drawablePadding="@dimen/dp13"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp13"
        android:paddingEnd="@dimen/dp13"
        android:text="@string/copy_link"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        app:drawableStartCompat="@drawable/ic_link"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/whatsapp" />

    <TextView
        android:id="@+id/textButtonEnterRoom"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp48"
        android:layout_marginTop="@dimen/dp17"
        android:background="@drawable/background_yellow_radius_4dp"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:text="@string/masuk_room"
        android:textAllCaps="true"
        android:textColor="@color/black"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0.2"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textCopyLink" />

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>