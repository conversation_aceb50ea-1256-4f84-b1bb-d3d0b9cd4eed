<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />
        <import type="noice.app.utils.HandleValues"/>

        <variable
            name="item"
            type="noice.app.modules.live.model.gift.slide.GiftSlide" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp8"
            android:paddingEnd="@dimen/dp2"
            android:paddingStart="@dimen/size_0dp"
            android:background="@drawable/gift_slide_bg"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgGiftProfile"
                android:layout_width="@dimen/dp40"
                android:layout_height="@dimen/dp40"
                android:layout_marginStart="@dimen/dp2"
                android:layout_marginTop="@dimen/dp2"
                android:layout_marginBottom="@dimen/dp2"
                app:circleCrop="@{true}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholder="@{@drawable/ic_user_profile}"
                app:srcUrl="@{item.userPic}"
                tools:srcCompat="@drawable/ic_user_profile"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftProfileName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginTop="@dimen/dp5"
                android:fontFamily="@font/readex_pro_semi_bold"
                android:textStyle="bold"
                android:includeFontPadding="false"
                android:text="@{item.userName}"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp12"
                app:layout_constraintStart_toEndOf="@+id/imgGiftProfile"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="MarkZ" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/verifiedBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp2"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_verified_tag"
                android:visibility="@{item.verifiedUser? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="@+id/txtGiftProfileName"
                app:layout_constraintStart_toEndOf="@+id/txtGiftProfileName"
                app:layout_constraintTop_toTopOf="@+id/txtGiftProfileName"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:barrierDirection="right"
                app:constraint_referenced_ids="verifiedBadge, txtGiftDetail"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp5"
                android:fontFamily="@font/readex_pro_regular"
                android:text="@{@string/sent_pre_text_string(item.giftName) }"
                android:textColor="@color/white300"
                android:textSize="@dimen/sp12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@+id/txtGiftProfileName"
                tools:text="Ngirim Stik PS" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgGift"
                android:layout_width="@dimen/dp40"
                android:layout_height="@dimen/dp40"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginTop="@dimen/dp2"
                android:layout_marginBottom="@dimen/dp2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/barrier"
                app:layout_constraintTop_toTopOf="parent"
                app:placeholder="@{@drawable/ic_gift_thumb}"
                app:srcUrl="@{item.url}"
                tools:srcCompat="@drawable/ic_gift_thumb"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/txtGiftQuantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:layout_marginEnd="@dimen/dp2"
                android:fontFamily="@font/readex_pro_semi_bold"
                android:text="@{`x`.concat(item.quantity)}"
                android:textColor="@color/dull_yellow"
                android:textSize="@dimen/sp24"
                android:visibility="@{HandleValues.isGreaterThanOne(item.quantity) ? View.VISIBLE : View.GONE}"
                app:layout_constraintBottom_toBottomOf="@+id/imgGift"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/imgGift"
                app:layout_constraintTop_toTopOf="@+id/imgGift"
                tools:text="x99" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>