<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:toolbarViewTitle="@string/semua_chapter"
        app:rightIcon="@drawable/ic_search_white_16dp"
        app:rightIconVisibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/pageTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:padding="@dimen/dp8"
        android:visibility="gone"
        android:text="@string/chapter"
        android:textSize="@dimen/sp20"
        android:textColor="@color/dull_white"
        android:fontFamily="sans-serif"
        android:textStyle="bold"
        android:lineSpacingExtra="@dimen/sp5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvEpisode"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:nestedScrollingEnabled="false"
        android:orientation="vertical"
        android:scrollIndicators="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pageTitle"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>