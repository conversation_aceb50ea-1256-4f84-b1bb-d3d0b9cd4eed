<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentHorizontalSegment"
    android:layout_width="@dimen/dp264"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/custom_ripple_bg_4dp"
    tools:background="@color/black">

    <FrameLayout
        android:id="@+id/imageLayout"
        android:layout_width="@dimen/dp48"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/imageCard"
            android:layout_width="@dimen/dp48"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="@dimen/dp8"
            app:cardElevation="@dimen/size_0dp">

            <ImageView
                android:id="@+id/coverImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_thumb_default"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name" />

            <noice.app.views.EqualizerView
                android:id="@+id/equalizer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:barHeight="@dimen/dp40"
                android:background="@color/black30"
                android:visibility="gone"
                android:layout_gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:marginLeft="@dimen/dp2"
                app:marginRight="@dimen/dp2" />

            <RelativeLayout
                android:id="@+id/relative_equalizer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:background="@color/black30">

                <noice.app.views.EqualizerView
                    android:id="@+id/equalizer_top_episode"
                    android:layout_width="@dimen/dp48"
                    android:layout_height="@dimen/dp48"
                    android:layout_gravity="center"
                    android:paddingTop="@dimen/dp12"
                    android:paddingBottom="@dimen/dp12"
                    android:layout_centerInParent="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:marginLeft="@dimen/dp2"
                    app:marginRight="@dimen/dp2" />
            </RelativeLayout>

            <ImageView
                android:id="@+id/image_noice_original"
                android:layout_width="@dimen/dp25"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:visibility="gone"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_noice_original" />
        </androidx.cardview.widget.CardView>
    </FrameLayout>

    <TextView
        android:id="@+id/origExc"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp12"
        android:visibility="gone"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp2"
        android:maxLines="1"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp8"
        android:textStyle="normal"
        tools:text="EXCLUSIVE"
        app:layout_constraintStart_toEndOf="@id/imageLayout"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/title"/>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:fontFamily="@font/readix_pro_bold"
        android:drawablePadding="@dimen/dp5"
        android:lineSpacingExtra="@dimen/sp6"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp14"
        android:textStyle="normal"
        tools:text="Musuh Masyarakat"
        app:layout_constraintStart_toEndOf="@id/imageLayout"
        app:layout_constraintTop_toBottomOf="@id/origExc"
        app:layout_constraintBottom_toTopOf="@id/text_user_name"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipToOutline="true"
        android:orientation="horizontal"
        app:layout_constraintStart_toEndOf="@id/title"
        app:layout_constraintTop_toTopOf="@id/title"
        app:layout_constraintBottom_toBottomOf="@id/title"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgVerified"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            app:srcCompat="@drawable/ic_verified_tag"
            android:layout_marginStart="@dimen/dp3"
            android:visibility="gone"
            />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgNplus"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            app:srcCompat="@drawable/ic_n_plus_badge"
            android:layout_marginStart="@dimen/dp3"
            android:visibility="gone"
            />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgVip"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            app:srcCompat="@drawable/vip_badge"
            android:layout_marginStart="@dimen/dp3"
            android:visibility="gone"
            />
    </LinearLayout>


    <TextView
        android:id="@+id/text_user_name"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginStart="@dimen/dp12"
        android:visibility="gone"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="-3.6sp"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        tools:text="Username"
        app:layout_goneMarginTop="@dimen/dp5"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintStart_toEndOf="@id/imageLayout"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintBottom_toTopOf="@id/subTitle"/>

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp2"
        android:fontFamily="@font/readex_pro"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_goneMarginTop="@dimen/dp2"
        tools:text="Coki Pardede, Tretan Muslim"
        app:layout_constraintEnd_toStartOf="@id/cross"
        app:layout_constraintStart_toEndOf="@id/imageLayout"
        app:layout_constraintTop_toBottomOf="@id/text_user_name"
        app:layout_constraintBottom_toTopOf="@id/view_divider"/>

    <ImageView
        android:id="@+id/cross"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginEnd="@dimen/dp5"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/dp10"
        android:src="@drawable/ic_close_without_square"
        android:background="@drawable/custom_ripple_bg_4dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/white10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/subTitle"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>