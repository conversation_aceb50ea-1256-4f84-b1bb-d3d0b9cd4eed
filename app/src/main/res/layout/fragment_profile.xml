<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:clickable="true"
    android:focusable="true"
    tools:context=".modules.profile.fragment.ProfileFragment">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/pullToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black700">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:id="@+id/collapsingToolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:contentScrim="@color/black700"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_collapseMode="pin">

                        <ImageView
                            android:id="@+id/userProfileImageBack"
                            android:layout_width="@dimen/size_0dp"
                            android:layout_height="@dimen/size_0dp"
                            android:scaleType="centerCrop"
                            android:contentDescription="@string/app_name"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="@id/headerLayout"/>

                        <View
                            android:layout_width="@dimen/size_0dp"
                            android:layout_height="@dimen/size_0dp"
                            android:background="@drawable/profile_gradient"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="@id/headerLayout"/>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/headerLayout"
                            android:layout_width="@dimen/size_0dp"
                            android:background="@color/black700"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="?actionBarSize"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent">

                            <ImageView
                                android:id="@+id/animatedRing"
                                android:layout_width="@dimen/dp102"
                                android:layout_height="@dimen/dp102"
                                android:layout_marginTop="@dimen/dp16"
                                android:layout_marginStart="@dimen/dp16"
                                android:contentDescription="@string/app_name"
                                android:visibility="gone"
                                tools:visibility="visible"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/userProfileLayout"
                                android:layout_width="@dimen/dp92"
                                android:layout_height="@dimen/dp102"
                                android:layout_marginTop="@dimen/dp22"
                                android:layout_marginStart="@dimen/dp22"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent">

                                <ImageView
                                    android:id="@+id/userProfileImage"
                                    android:layout_width="@dimen/dp90"
                                    android:layout_height="@dimen/dp90"
                                    android:src="@drawable/ic_user_profile"
                                    android:adjustViewBounds="true"
                                    android:contentDescription="@string/app_name"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <TextView
                                    android:id="@+id/liveTag"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:visibility="gone"
                                    android:paddingStart="@dimen/dp10"
                                    android:paddingEnd="@dimen/dp10"
                                    android:paddingTop="@dimen/dp5"
                                    android:paddingBottom="@dimen/dp5"
                                    android:background="@drawable/live_profile_tag"
                                    android:textSize="@dimen/sp10"
                                    android:textColor="@color/black"
                                    android:textStyle="bold"
                                    android:fontFamily="sans-serif"
                                    android:letterSpacing="0.2"
                                    android:textAllCaps="true"
                                    android:lineSpacingExtra="0sp"
                                    android:text="@string/live"
                                    app:layout_constraintStart_toStartOf="@id/userProfileImage"
                                    app:layout_constraintEnd_toEndOf="@id/userProfileImage"
                                    app:layout_constraintBottom_toBottomOf="@id/userProfileImage"
                                    app:layout_constraintTop_toBottomOf="@id/userProfileImage"/>
                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <FrameLayout
                                android:id="@+id/layoutFollow"
                                android:layout_width="@dimen/dp131"
                                android:layout_height="@dimen/dp36"
                                app:layout_constraintBottom_toBottomOf="@id/userProfileLayout"
                                app:layout_constraintStart_toEndOf="@id/userProfileLayout"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:background="@drawable/follow_button_bg">

                                <TextView
                                    android:id="@+id/txtFollow"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/follow"
                                    android:textSize="@dimen/sp12"
                                    android:fontFamily="@font/roboto_bold"
                                    android:layout_gravity="center"
                                    android:textColor="@color/black_n_white"
                                    android:gravity="center"
                                    android:textAllCaps="true"
                                    android:drawablePadding="@dimen/dp8"
                                    app:drawableStartCompat="@drawable/follow_selector" />

                            </FrameLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:orientation="horizontal"
                                app:layout_constraintBottom_toTopOf="@+id/layoutFollow"
                                app:layout_constraintStart_toEndOf="@id/userProfileLayout"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:weightSum="3"
                                android:layout_marginBottom="@dimen/dp16"
                                android:paddingStart="@dimen/dp16"
                                android:paddingEnd="@dimen/dp16"
                                android:baselineAligned="false">
                                <LinearLayout
                                    android:id="@+id/layoutFollower"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:foreground="?android:attr/selectableItemBackgroundBorderless"
                                    android:orientation="vertical"
                                    android:layout_gravity="bottom"
                                    >
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:layout_gravity="center_horizontal|bottom"
                                        >

                                        <TextView
                                            android:id="@+id/txtFollowers"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/zero"
                                            android:textSize="@dimen/sp16"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_gravity="bottom"
                                            />

                                        <TextView
                                            android:id="@+id/txtFollowerUnit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            tools:text="jt"
                                            android:textSize="@dimen/sp12"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_marginStart="@dimen/dp3"
                                            android:layout_gravity="bottom"
                                            />
                                    </LinearLayout>
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp4"
                                        android:text="@string/followers"
                                        android:textSize="@dimen/sp10"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:letterSpacing="0.04"
                                        android:fontFamily="sans-serif"
                                        android:gravity="center"
                                        android:layout_gravity="center_horizontal"/>
                                </LinearLayout>
                                <LinearLayout
                                    android:id="@+id/layoutFollowing"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:foreground="?android:attr/selectableItemBackgroundBorderless"
                                    android:orientation="vertical"
                                    android:layout_gravity="bottom"
                                    >
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:layout_gravity="center_horizontal|bottom"
                                        >

                                        <TextView
                                            android:id="@+id/txtFollowe"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/zero"
                                            android:textSize="@dimen/sp16"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_gravity="bottom"
                                            />

                                        <TextView
                                            android:id="@+id/txtUnitFollowee"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            tools:text="rb"
                                            android:textSize="@dimen/sp12"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_marginStart="@dimen/dp3"
                                            android:layout_gravity="bottom"
                                            />
                                    </LinearLayout>
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp4"
                                        android:text="@string/following"
                                        android:textSize="@dimen/sp10"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:letterSpacing="0.04"
                                        android:fontFamily="sans-serif"
                                        android:gravity="center"
                                        android:layout_gravity="center_horizontal"/>
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:layout_gravity="bottom"
                                    >
                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:layout_gravity="center_horizontal|bottom"
                                        >

                                        <TextView
                                            android:id="@+id/minutesListened"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/zero"
                                            android:textSize="@dimen/sp16"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_gravity="bottom"
                                            />

                                        <TextView
                                            android:id="@+id/txtListenUnit"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="@string/mnt"
                                            android:textSize="@dimen/sp12"
                                            android:textColor="@color/white"
                                            android:fontFamily="sans-serif"
                                            android:layout_marginStart="@dimen/dp3"
                                            android:layout_gravity="bottom"
                                            />
                                    </LinearLayout>
                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="@dimen/dp4"
                                        android:text="@string/mendengar"
                                        android:textSize="@dimen/sp10"
                                        android:textColor="@color/white"
                                        android:textStyle="bold"
                                        android:letterSpacing="0.04"
                                        android:fontFamily="sans-serif"
                                        android:gravity="center"
                                        android:layout_gravity="center_horizontal"/>
                                </LinearLayout>
                            </LinearLayout>

                            <TextView
                                android:id="@+id/userFullName"
                                android:layout_width="@dimen/size_0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp16"
                                android:layout_marginEnd="@dimen/dp16"
                                android:layout_marginStart="@dimen/dp16"
                                android:ellipsize="end"
                                android:maxLines="1"
                                tools:text="Vaibhav Pal"
                                android:textSize="@dimen/sp14"
                                android:textColor="@color/dull_white"
                                android:textStyle="bold"
                                android:lineSpacingExtra="@dimen/sp3"
                                android:fontFamily="sans-serif"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/userProfileLayout"/>

                            <TextView
                                android:id="@+id/userName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp16"
                                android:layout_marginTop="@dimen/dp4"
                                tools:text="palvaibhav89"
                                android:textSize="@dimen/sp14"
                                android:drawablePadding="@dimen/dp2"
                                android:letterSpacing="0.01"
                                android:textColor="@color/dull_white"
                                android:fontFamily="sans-serif"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/userFullName"/>

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/verifiedBadge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp4"
                                android:contentDescription="@string/app_name"
                                android:visibility="gone"
                                tools:visibility="visible"
                                app:srcCompat="@drawable/ic_verified_tag"
                                app:layout_constraintStart_toEndOf="@id/userName"
                                app:layout_constraintTop_toTopOf="@id/userName"
                                app:layout_constraintBottom_toBottomOf="@id/userName" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/noicemakerBadge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp4"
                                android:contentDescription="@string/app_name"
                                android:visibility="gone"
                                tools:visibility="visible"
                                app:srcCompat="@drawable/ic_n_plus_badge"
                                app:layout_constraintStart_toEndOf="@id/verifiedBadge"
                                app:layout_constraintTop_toTopOf="@id/verifiedBadge"
                                app:layout_constraintBottom_toBottomOf="@id/verifiedBadge"/>

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/vipBadge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp4"
                                android:contentDescription="@string/app_name"
                                android:visibility="gone"
                                tools:visibility="visible"
                                app:srcCompat="@drawable/vip_badge"
                                app:layout_constraintStart_toEndOf="@id/noicemakerBadge"
                                app:layout_constraintTop_toTopOf="@id/noicemakerBadge"
                                app:layout_constraintBottom_toBottomOf="@id/noicemakerBadge"/>

                            <TextView
                                android:id="@+id/description"
                                android:layout_width="@dimen/size_0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp16"
                                android:layout_marginEnd="@dimen/dp20"
                                android:layout_marginTop="@dimen/dp4"
                                tools:text="Description"
                                android:maxLines="2"
                                android:textSize="@dimen/sp14"
                                android:textColor="@color/white"
                                android:fontFamily="sans-serif"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/userName"/>

                            <TextView
                                android:id="@+id/editButton"
                                android:layout_width="@dimen/size_0dp"
                                android:layout_height="@dimen/dp30"
                                android:layout_marginStart="@dimen/dp16"
                                android:layout_marginTop="@dimen/dp20"
                                android:layout_marginEnd="@dimen/dp20"
                                android:background="@drawable/border_white_radius_4dp"
                                android:gravity="center"
                                android:text="@string/edit_profile"
                                android:textSize="@dimen/sp12"
                                android:textColor="@color/white"
                                android:textStyle="bold"
                                android:textAllCaps="true"
                                android:letterSpacing="0.02"
                                android:lineSpacingExtra="@dimen/sp3"
                                android:fontFamily="sans-serif"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/description"/>

                            <noice.app.views.RootTitleRecycler
                                android:id="@+id/upcomingRoomRecycler"
                                android:layout_width="@dimen/size_0dp"
                                android:layout_height="wrap_content"
                                android:visibility="gone"
                                tools:visibility="visible"
                                android:layout_marginTop="@dimen/dp16"
                                app:headingPaddingTop="@dimen/size_0dp"
                                app:headerTextPaddingStart="@dimen/dp16"
                                app:headingText="@string/room_schedule"
                                app:headingSize="@dimen/sp14"
                                app:listPaddingStart="@dimen/dp8"
                                app:listPaddingEnd="@dimen/dp8"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/editButton" >

                                <FrameLayout
                                    android:id="@+id/homeContainer"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:elevation="@dimen/dp6" />
                            </noice.app.views.RootTitleRecycler>

                            <View
                                android:id="@+id/line"
                                android:layout_width="@dimen/size_0dp"
                                android:layout_height="@dimen/dp1"
                                android:layout_marginTop="@dimen/dp20"
                                android:background="@color/white10"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/upcomingRoomRecycler"/>
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.appcompat.widget.Toolbar
                        android:id="@+id/toolbar"
                        android:layout_width="match_parent"
                        android:layout_height="?attr/actionBarSize"
                        app:layout_collapseMode="pin"
                        app:contentInsetStartWithNavigation="0dp"
                        app:layout_anchor="@id/collapsingToolbar"
                        app:theme="@style/ThemeOverlay.AppCompat.Dark"
                        app:title="">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/title"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="@dimen/dp8"
                                android:layout_gravity="center_vertical"
                                android:maxLines="1"
                                android:ellipsize="end"
                                android:visibility="invisible"
                                tools:text="Vaibhav Pal"
                                android:textSize="@dimen/sp14"
                                android:textColor="@color/white"
                                android:fontFamily="sans-serif"
                                android:textStyle="bold"/>
                            
                            <TextView
                                android:id="@+id/coins"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="@dimen/dp4"
                                android:visibility="gone"
                                android:padding="@dimen/dp8"
                                android:foreground="?android:attr/selectableItemBackground"
                                android:background="@drawable/background_blackish_grey_22dp"
                                android:gravity="center"
                                android:text="@string/hyphen"
                                android:textColor="@color/white"
                                android:textSize="@dimen/sp14"
                                android:textStyle="bold"
                                android:letterSpacing="0"
                                android:lineSpacingExtra="@dimen/sp3"
                                android:fontFamily="sans-serif"
                                android:drawablePadding="@dimen/dp8"
                                app:drawableStartCompat="@drawable/ic_noice_coin" />

                            <ImageView
                                android:id="@+id/addCoins"
                                android:layout_width="@dimen/dp16"
                                android:layout_height="@dimen/dp16"
                                android:layout_marginEnd="@dimen/dp16"
                                android:layout_gravity="center_vertical"
                                android:foreground="?android:attr/selectableItemBackground"
                                android:visibility="gone"
                                android:src="@drawable/ic_add_coin"
                                android:adjustViewBounds="true"
                                android:contentDescription="@string/app_name" />

                            <TextView
                                android:id="@+id/diamonds"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="@dimen/dp8"
                                android:visibility="gone"
                                android:padding="@dimen/dp8"
                                android:foreground="?android:attr/selectableItemBackground"
                                android:background="@drawable/background_blackish_grey_22dp"
                                android:gravity="center"
                                android:text="@string/hyphen"
                                android:textColor="@color/white"
                                android:textSize="@dimen/sp14"
                                android:textStyle="bold"
                                android:letterSpacing="0"
                                android:lineSpacingExtra="@dimen/sp3"
                                android:fontFamily="sans-serif"
                                android:drawablePadding="@dimen/dp8"
                                app:drawableStartCompat="@drawable/ic_diamond_16dp" />

                            <ImageView
                                android:id="@+id/toolbarActionButton"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:visibility="gone"
                                android:foreground="?android:attr/selectableItemBackgroundBorderless"
                                android:layout_marginEnd="@dimen/dp8"
                                android:layout_gravity="center_vertical"
                                android:padding="@dimen/dp8"
                                android:src="@drawable/ic_search_white_16dp"
                                android:contentDescription="@string/app_name"/>

                            <ImageView
                                android:id="@+id/threeDotMenu"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="@dimen/dp8"
                                android:visibility="gone"
                                android:paddingBottom="@dimen/dp8"
                                android:paddingTop="@dimen/dp8"
                                android:paddingStart="@dimen/sp16"
                                android:paddingEnd="@dimen/dp8"
                                android:src="@drawable/ic_three_dots_ws"
                                android:contentDescription="@string/app_name"/>
                        </LinearLayout>
                    </androidx.appcompat.widget.Toolbar>
                </com.google.android.material.appbar.CollapsingToolbarLayout>

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/black700"
                    android:paddingStart="@dimen/dp2"
                    android:paddingEnd="@dimen/dp2"
                    app:tabBackground="@color/black"
                    app:tabGravity="start"
                    app:tabIndicator="@drawable/custom_tab_indicator"
                    app:tabIndicatorColor="@color/dull_yellow"
                    app:tabIndicatorFullWidth="false"
                    app:tabMode="scrollable"
                    app:tabSelectedTextColor="@color/white"
                    app:tabTextAppearance="@style/NoiceTabLayoutStyle"
                    app:tabTextColor="@color/white50"
                    />

                <View
                    android:id="@+id/tabLayoutLine"
                    android:layout_width="match_parent"
                    android:layout_height=".8dp"
                    android:background="@color/home_search"/>

            </com.google.android.material.appbar.AppBarLayout>

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

            <noice.app.views.ErrorView
                android:id="@+id/errorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="?actionBarSize"/>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/fabCreateKarya"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:paddingStart="@dimen/dp18"
        android:paddingEnd="@dimen/dp18"
        android:gravity="center"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp16"
        android:visibility="gone"
        tools:visibility="visible"
        android:fontFamily="sans-serif"
        android:text="@string/karya"
        android:textColor="@color/black"
        android:textSize="@dimen/sp12"
        android:textStyle="bold"
        app:backgroundTint="@color/dull_yellow"
        app:icon="@drawable/ic_plus_black"
        app:iconSize="@dimen/dp11"
        app:rippleColor="@color/white" />

</FrameLayout>
