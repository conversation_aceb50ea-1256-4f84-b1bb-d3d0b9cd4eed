<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View"/>
        <import type="noice.app.utils.HandleValues"/>
        <import type="noice.app.utils.Constants"/>
        <variable
            name="viewModel"
            type="noice.app.modules.live.viewmodel.LiveBlockListViewModel" />
        <variable
            name="item"
            type="noice.app.modules.live.model.block.LiveBlockItemViewState" />
    </data>
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp70"
    android:orientation="horizontal"
    tools:background="@color/black">

     <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/coverImage"
        android:layout_width="@dimen/dp50"
        android:layout_height="@dimen/dp50"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
         app:srcUrl="@{item.item.user.smallImage}"
         app:circleCrop="@{true}"
         app:placeholder="@{@drawable/ic_user_profile}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearUsername"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp19"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp5"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toStartOf="@id/text_invite"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toTopOf="@id/coverImage">

        <TextView
            android:id="@+id/txtUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dp3"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center_vertical"
            android:maxLength="15"
            android:text="@{item.item.user.userName}"
            android:textAllCaps="false"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            tools:text="\@sahiljeet_noice" />

        <ImageView
            android:id="@+id/noicemakerBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp4"
            android:visibility="@{item.item.user.creatorProfile.isActive ? View.VISIBLE : View.GONE}"
            tools:visibility="visible"
            android:src="@drawable/ic_noicemaker_badge"
            android:contentDescription="@string/app_name"/>

    </LinearLayout>

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp3"
        android:layout_marginEnd="@dimen/dp5"
        android:fontFamily="@font/roboto"
        android:maxLines="1"
        android:text="@{item.item.user.displayName}"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toStartOf="@id/text_invite"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toBottomOf="@id/linearUsername"
        app:layout_goneMarginTop="@dimen/dp2"
        tools:text="Noice DEV" />

    <TextView
        android:id="@+id/text_invite"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/selector_follow_btn_white"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/unblock"
        android:drawablePadding="@dimen/dp5"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:onClick="@{()-> viewModel.onUnblockUserPrompt(item)}"/>


    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/white10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/linearUsername" />
</androidx.constraintlayout.widget.ConstraintLayout>
</layout>
