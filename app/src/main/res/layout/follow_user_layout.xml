<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgUser"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/bg_skeleton_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/txtUserName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp6"
        android:fontFamily="@font/readex_pro"
        tools:text="atiq"
        android:textColor="@color/light_blue"
        android:textSize="@dimen/sp14"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/imgUser"
        app:layout_constraintTop_toTopOf="@id/imgUser"
        app:layout_constraintBottom_toTopOf="@id/txtName"/>

    <ImageView
        android:id="@+id/verifiedBadge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_verified_tag"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toEndOf="@id/txtUserName"
        app:layout_constraintTop_toTopOf="@id/txtUserName"
        app:layout_constraintBottom_toBottomOf="@id/txtUserName"/>

    <ImageView
        android:id="@+id/noicemakerBadge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_noicemaker_badge"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/txtUserName"
        app:layout_constraintStart_toEndOf="@id/verifiedBadge"
        app:layout_constraintTop_toTopOf="@id/txtUserName" />
    <ImageView
        android:id="@+id/imgVip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        android:contentDescription="@string/app_name"
        android:src="@drawable/vip_badge"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@id/txtUserName"
        app:layout_constraintStart_toEndOf="@id/noicemakerBadge"
        app:layout_constraintTop_toTopOf="@id/txtUserName" />


    <TextView
        android:id="@+id/txtName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:layout_marginEnd="@dimen/dp16"
        android:ellipsize="end"
        android:maxLines="1"
        android:fontFamily="@font/readex_pro"
        android:gravity="start"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp14"
        tools:text="atiq"
        app:layout_constraintStart_toStartOf="@id/txtUserName"
        app:layout_constraintTop_toBottomOf="@id/txtUserName"
        app:layout_constraintBottom_toBottomOf="@id/imgUser"
        app:layout_constraintEnd_toStartOf="@id/followBtn"/>

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp12"
        android:background="@color/white10"
        app:layout_constraintStart_toStartOf="@+id/txtUserName"
        app:layout_constraintTop_toBottomOf="@id/imgUser"
        app:layout_constraintEnd_toEndOf="@id/followBtn"/>

    <TextView
        android:id="@+id/followBtn"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp21"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp12"
        android:background="@drawable/small_follow_button_bg"
        android:drawablePadding="@dimen/dp5"
        android:fontFamily="@font/readex_pro"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/follow"
        android:textColor="@color/follow_text_color"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:drawableStartCompat="@drawable/ic_follower_plus"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>