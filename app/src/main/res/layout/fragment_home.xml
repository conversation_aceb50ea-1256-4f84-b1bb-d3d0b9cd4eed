<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/extendSubLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/heading"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp16"
            android:gravity="center"
            tools:text="Yang mau berakhir adalah paketmu "
            android:textSize="@dimen/sp14"
            android:textColor="@color/black"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:letterSpacing="0.01"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/desc"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:gravity="center"
            tools:text="Buat lanjutin paket, kamu punya waktu [xx] hari untuk memperbarui"
            android:textSize="@dimen/sp14"
            android:textColor="@color/neutral_grey_60"
            android:fontFamily="@font/readex_pro"
            android:letterSpacing="0.01"
            app:layout_constraintStart_toStartOf="@id/heading"
            app:layout_constraintEnd_toEndOf="@id/heading"
            app:layout_constraintTop_toBottomOf="@id/heading"/>

        <TextView
            android:id="@+id/btnOkay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp16"
            android:padding="@dimen/dp4"
            android:gravity="center"
            android:text="@string/oke"
            android:textSize="@dimen/sp14"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:fontFamily="@font/readex_pro"
            android:letterSpacing="0.01"
            app:layout_constraintTop_toTopOf="@id/proceedToSub"
            app:layout_constraintBottom_toBottomOf="@id/proceedToSub"
            app:layout_constraintEnd_toStartOf="@id/proceedToSub"/>

        <TextView
            android:id="@+id/proceedToSub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginBottom="@dimen/dp16"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:background="@drawable/background_dull_yellow_20dp"
            android:padding="@dimen/dp4"
            android:gravity="center"
            android:text="@string/extend"
            android:textSize="@dimen/sp14"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:fontFamily="@font/readex_pro"
            android:letterSpacing="0.01"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/desc"
            app:layout_constraintEnd_toEndOf="@id/heading"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:background="@color/black700"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/extendSubLayout"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|exitUntilCollapsed|enterAlways|snap">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/black700"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/imgLogo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/dp16"
                        android:adjustViewBounds="true"
                        android:layout_centerVertical="true"
                        android:contentDescription="@string/app_name"
                        android:layout_alignParentStart="true"
                        android:src="@drawable/ic_noice_home_logo"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <TextView
                        android:id="@+id/searchBar"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginTop="@dimen/dp8"
                        android:layout_marginEnd="@dimen/dp6"
                        android:layout_marginBottom="@dimen/dp8"
                        android:alpha="0.7"
                        android:background="@drawable/background_blackish_grey_4dp"
                        android:fontFamily="@font/readex_pro"
                        android:lineSpacingExtra="6.4sp"
                        android:paddingStart="@dimen/dp16"
                        android:paddingTop="@dimen/dp10"
                        android:paddingEnd="@dimen/dp16"
                        android:paddingBottom="@dimen/dp10"
                        android:text="@string/search_for_latest_podcast"
                        android:textColor="@color/dull_white"
                        android:textSize="@dimen/sp16"
                        android:textStyle="normal"
                        app:layout_constraintStart_toEndOf="@id/imgLogo"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/icNotification"
                        app:drawableEndCompat="@drawable/ic_search_grey" />


                    <androidx.appcompat.widget.AppCompatImageButton
                        android:id="@+id/icNotification"
                        android:layout_width="@dimen/dp48"
                        android:layout_height="@dimen/dp48"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="@dimen/dp5"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_notification"
                        android:background="@drawable/custom_ripple_bg_4dp"
                        app:layout_constraintStart_toEndOf="@id/searchBar"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                    <ImageView
                        android:id="@+id/newNotifications"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginEnd="@dimen/dp4"
                        android:layout_marginTop="@dimen/dp4"
                        android:src="@drawable/ic_red_circle"
                        android:contentDescription="@string/app_name"
                        app:layout_constraintEnd_toEndOf="@id/icNotification"
                        app:layout_constraintTop_toTopOf="@id/icNotification"/>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.appbar.CollapsingToolbarLayout>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/black700"
                android:paddingStart="@dimen/dp2"
                android:paddingEnd="@dimen/size_0dp"
                app:tabBackground="@color/black700"
                app:tabGravity="start"
                app:tabIndicator="@drawable/custom_tab_indicator"
                app:tabIndicatorColor="@color/dull_yellow"
                app:tabIndicatorFullWidth="false"
                app:tabMode="scrollable"
                app:tabSelectedTextColor="@color/white"
                app:tabTextAppearance="@style/NoiceTabLayoutStyle"
                app:tabTextColor="@color/white50" />

            <View
                android:layout_width="match_parent"
                android:layout_height=".8dp"
                android:background="@color/home_search" />
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <FrameLayout
        android:id="@+id/homeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
