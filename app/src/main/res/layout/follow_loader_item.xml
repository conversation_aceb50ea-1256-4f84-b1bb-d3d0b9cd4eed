<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <View
        android:id="@+id/v1"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:background="@drawable/background_skeleton_20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dp15"
        />
    <View
        android:id="@+id/v2"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp16"
        android:layout_marginStart="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp14"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintTop_toTopOf="@+id/v1"
        app:layout_constraintStart_toEndOf="@+id/v1"
        app:layout_constraintEnd_toStartOf="@+id/v3"
        />
    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/dp65"
        android:layout_height="@dimen/dp27"
        android:layout_marginEnd="@dimen/dp6"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />
    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/dp94"
        android:layout_height="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp14"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@+id/v2"
        app:layout_constraintTop_toBottomOf="@+id/v2"
        android:layout_marginTop="@dimen/dp7"
        />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        app:layout_constraintTop_toBottomOf="@+id/v1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp15"
        android:background="@color/white10"
        />
</androidx.constraintlayout.widget.ConstraintLayout>