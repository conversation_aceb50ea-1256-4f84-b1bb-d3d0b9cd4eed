<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="noice.app.modules.live.stream.feature.leaderboard.LeaderboardViewModel" />

        <variable
            name="viewState"
            type="noice.app.modules.live.model.leaderboard.LeaderBoardViewState" />

        <variable
            name="isReceiver"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bottom_sheet_rounded_black700_24dp">

        <View
            android:id="@+id/line"
            android:layout_width="@dimen/dp56"
            android:layout_height="@dimen/dp4"
            android:layout_marginTop="@dimen/dp16"
            android:background="@color/color_sheet_drag_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/txtTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            android:fontFamily="sans-serif"
            android:text="@string/top_suporter"
            android:textColor="@color/white"
            android:textSize="@dimen/sp18"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp350"
            android:layout_marginTop="@dimen/dp20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp28"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp24"
                android:background="@android:color/transparent"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tabIndicator="@drawable/custom_tab_indicator_width_12px"
                app:tabIndicatorColor="@color/dull_yellow"
                app:tabIndicatorFullWidth="false"
                app:tabMode="fixed"
                app:tabPaddingBottom="@dimen/dp4"
                app:tabPaddingEnd="@dimen/dp8"
                app:tabPaddingStart="@dimen/dp8"
                app:tabPaddingTop="@dimen/dp4"
                app:tabRippleColor="@android:color/transparent"
                app:tabSelectedTextColor="@color/white"
                app:tabTextAppearance="@style/NoiceTabLayoutGiftStyle"
                app:tabTextColor="@color/white50" />

            <ProgressBar
                android:id="@+id/progressbar"
                android:layout_width="@dimen/dp25"
                android:layout_height="@dimen/dp25"
                android:indeterminateTint="@color/dull_yellow"
                app:visibleIf="@{viewState.loading &amp;&amp; viewState.items.empty}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerview"
                app:items="@{viewState.items}"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tabLayout"
                tools:listitem="@layout/item_leaderboard_default" />

            <ViewStub
                android:id="@+id/viewStubEmpty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout="@layout/layout_empty_live_leaderboard"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tabLayout"
                app:layout_constraintBottom_toBottomOf="parent"
                app:inflatedVisibility="@{viewState.items.empty &amp;&amp; !viewState.loading}"
                android:visibility="@{viewState.items.empty &amp;&amp; !viewState.loading ? View.VISIBLE : View.GONE}"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/layoutRankSelf"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black700"
            android:elevation="@dimen/dp2"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp1"
            android:paddingBottom="@dimen/dp12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:showDividers="beginning"
            app:divider="@color/grey20"
            app:dividerPadding="@dimen/size_0dp"
            app:visibleIf="@{!isReceiver}"
            app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

            <ViewStub
                android:id="@+id/layoutRankSelfViewStub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:layout="@layout/item_leaderboard_default"
                android:inflatedId="@+id/item_rank_self"
                app:item="@{viewState.itemSelf}"
                app:viewModel="@{viewModel}"
                tools:visibility="visible"/>

        </androidx.appcompat.widget.LinearLayoutCompat>

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:elevation="@dimen/dp3"
            android:indeterminate="true"
            app:indicatorColor="@color/dull_yellow"
            app:layout_constraintBottom_toBottomOf="@id/layoutRankSelf"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/constraintLayout"
            app:layout_constraintVertical_bias="1"
            app:visibleIf="@{viewState.loading &amp;&amp; !viewState.items.empty}" />


        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/dp200"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>