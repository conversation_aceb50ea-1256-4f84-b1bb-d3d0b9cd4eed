<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/similarCatalogSkeleton"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black700">

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp90"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v6"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v5"
        app:layout_constraintTop_toTopOf="@id/v5"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v7"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@+id/v6" />

    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        app:layout_constraintEnd_toEndOf="@+id/v7"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toBottomOf="@id/v5"
        app:layout_constraintStart_toEndOf="@id/v5"
        app:layout_constraintTop_toBottomOf="@+id/v7" />


    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp90"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v5" />

    <View
        android:id="@+id/v11"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v10"
        app:layout_constraintTop_toTopOf="@id/v10"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v12"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v13"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@+id/v11" />

    <View
        android:id="@+id/v13"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        app:layout_constraintEnd_toEndOf="@+id/v12"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toBottomOf="@id/v10"
        app:layout_constraintStart_toEndOf="@id/v10"
        app:layout_constraintTop_toBottomOf="@+id/v12" />

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp90"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v10" />

    <View
        android:id="@+id/v16"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v17"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v15"
        app:layout_constraintTop_toTopOf="@id/v15"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v17"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v15"
        app:layout_constraintTop_toBottomOf="@+id/v16" />

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        app:layout_constraintEnd_toEndOf="@+id/v17"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toBottomOf="@id/v15"
        app:layout_constraintStart_toEndOf="@id/v15"
        app:layout_constraintTop_toBottomOf="@+id/v17" />

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp90"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v15" />

    <View
        android:id="@+id/v21"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v22"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v20"
        app:layout_constraintTop_toTopOf="@id/v20"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v22"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v23"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v20"
        app:layout_constraintTop_toBottomOf="@+id/v21" />

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        app:layout_constraintEnd_toEndOf="@+id/v22"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toBottomOf="@id/v20"
        app:layout_constraintStart_toEndOf="@id/v20"
        app:layout_constraintTop_toBottomOf="@+id/v22" />

    <View
        android:id="@+id/v25"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp90"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v20" />

    <View
        android:id="@+id/v26"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp15"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v27"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v25"
        app:layout_constraintTop_toTopOf="@id/v25"
        app:layout_constraintVertical_chainStyle="packed" />

    <View
        android:id="@+id/v27"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toTopOf="@+id/v28"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@+id/v26" />

    <View
        android:id="@+id/v28"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/background_white_round_4dp"
        app:layout_constraintBottom_toBottomOf="@id/v25"
        app:layout_constraintEnd_toEndOf="@+id/v27"
        app:layout_constraintStart_toEndOf="@id/v25"
        app:layout_constraintTop_toBottomOf="@+id/v27" />

</androidx.constraintlayout.widget.ConstraintLayout>