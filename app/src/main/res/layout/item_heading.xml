<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/text_heading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="4sp"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="@dimen/sp16"
        android:textStyle="normal"
        android:translationY="-1.82sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="musuh_masya" />
</LinearLayout>
