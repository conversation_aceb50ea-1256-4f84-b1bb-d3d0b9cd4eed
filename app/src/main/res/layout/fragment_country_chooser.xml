<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/dp16"
    android:orientation="vertical"
    android:background="@color/black700"
    tools:context=".modules.onboarding.activity.CountryChooserFragment">
    
    <LinearLayout
        android:id="@+id/searchLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:background="@drawable/background_black600_radius_4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/backBtn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="@dimen/dp20"
            android:paddingEnd="@dimen/dp12"
            android:src="@drawable/arrow_back"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>

        <EditText
            android:id="@+id/countryEditText"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp44"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp4"
            android:inputType="text"
            android:textSize="@dimen/sp14"
            android:textColor="@color/white"
            android:textColorHint="@color/greyText1"
            android:hint="@string/search_country"
            android:letterSpacing="0.02"
            android:background="@null"
            android:importantForAutofill="no" />

        <ImageView
            android:id="@+id/clearText"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:paddingEnd="@dimen/dp12"
            android:paddingStart="@dimen/dp8"
            android:src="@drawable/ic_clear_textfield"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>
    </LinearLayout>
    
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/countryRecyclerView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp8"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <LinearLayout
        android:id="@+id/searchEmpty"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp24"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchLayout"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:src="@drawable/country_not_found"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:gravity="center"
            android:fontFamily="@font/readex_pro"
            android:text="@string/country_empty_title"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white"
            android:letterSpacing="0.01"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:gravity="center"
            android:fontFamily="@font/readex_pro"
            android:text="@string/country_empty_desc"
            android:textSize="@dimen/sp16"
            android:textColor="@color/neutral_grey"
            android:letterSpacing="0.01"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>