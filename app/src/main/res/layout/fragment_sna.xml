<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/dp16"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:background="@color/black700"
    tools:context=".modules.onboarding.fragments.SnaFragment">

    <ProgressBar
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"
        android:layout_marginTop="@dimen/dp16"
        android:indeterminateTint="@color/white"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:gravity="center"
        android:fontFamily="@font/readex_pro"
        android:text="@string/matching_your_number"
        android:textStyle="bold"
        android:textSize="@dimen/sp24"
        android:textColor="@color/white"
        android:lineSpacingExtra="0sp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:gravity="center"
        android:fontFamily="@font/readex_pro"
        android:text="@string/wait_a_minute"
        android:textSize="@dimen/sp16"
        android:textColor="@color/black900"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="@dimen/sp6"/>
</LinearLayout>