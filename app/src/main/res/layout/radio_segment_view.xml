<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/radioSegmentView"
    android:layout_width="@dimen/dp185"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_name"
            android:adjustViewBounds="true"/>

        <TextView
            android:id="@+id/txtLive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp12"
            android:layout_marginBottom="@dimen/dp12"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp4"
            android:paddingEnd="@dimen/dp8"
            android:paddingTop="@dimen/dp2"
            android:paddingBottom="@dimen/dp2"
            android:drawablePadding="@dimen/dp4"
            android:layout_gravity="bottom|end"
            android:background="@drawable/background_blackish_grey_4dp"
            android:fontFamily="sans-serif"
            android:textAllCaps="true"
            android:textSize="@dimen/sp12"
            android:textColor="@color/dull_yellow"
            android:lineSpacingExtra="@dimen/sp5"
            android:text="@string/live"
            app:drawableStartCompat="@drawable/ic_radio_broadcast" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="@font/readix_pro_bold"
        android:includeFontPadding="false"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="Salah Sambung" />

    <TextView
        android:id="@+id/timing"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp12"
        android:textColor="@color/neutral_80"
        android:alpha="0.5"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="07.00 - 10.00" />
</LinearLayout>