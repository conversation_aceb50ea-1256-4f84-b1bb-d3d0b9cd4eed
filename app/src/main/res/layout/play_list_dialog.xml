<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dp16"
    android:background="@drawable/bottom_sheet_rounded_24dp"
    tools:context=".modules.dashboard.home.fragment.PlayListDialog">

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="#303030"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/cancelBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginStart="@dimen/dp24"
        android:paddingTop="@dimen/dp16"
        android:fontFamily="sans-serif"
        android:text="@string/cancel"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_yellow"
        android:background="@drawable/custom_ripple"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp26"
        android:fontFamily="sans-serif"
        android:textStyle="bold"
        android:text="@string/add_to_playlist"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"/>

    <TextView
        android:id="@+id/createNewPlaylist"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp56"
        android:layout_marginTop="@dimen/dp27"
        android:layout_marginStart="@dimen/dp32"
        android:gravity="center_vertical"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:drawablePadding="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_yellow"
        android:lineSpacingExtra="@dimen/sp6"
        android:text="@string/create_new_list"
        app:layout_constraintTop_toBottomOf="@id/cancelBtn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:drawableStartCompat="@drawable/ic_plus_yellow" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/playListRv"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp8"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/createNewPlaylist"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp200"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cancelBtn"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>