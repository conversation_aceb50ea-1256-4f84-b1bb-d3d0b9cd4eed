<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black">

    <ImageView
        android:id="@+id/userProfileImage"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:src="@drawable/ic_user_profile"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/userDisplayName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        tools:text="Vaibhav Pal"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintStart_toEndOf="@id/userProfileImage"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"/>

    <TextView
        android:id="@+id/userName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp4"
        tools:text="\@palvaibhav89"
        android:textSize="@dimen/sp12"
        android:textColor="@color/white70"
        android:fontFamily="sans-serif"
        android:drawablePadding="@dimen/dp3"
        app:layout_constraintStart_toEndOf="@id/userDisplayName"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"
        app:layout_constraintBottom_toBottomOf="@id/userDisplayName"/>

    <TextView
        android:id="@+id/activityTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="1 jam lalu"
        android:textSize="@dimen/sp10"
        android:textColor="@color/white70"
        android:fontFamily="sans-serif"
        android:letterSpacing="0.04"
        android:lineSpacingExtra="@dimen/sp2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"/>

    <TextView
        android:id="@+id/activity"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        tools:text="Mengikuti:"
        android:textSize="@dimen/sp12"
        android:textColor="@color/white"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintTop_toBottomOf="@id/userDisplayName"
        app:layout_constraintStart_toStartOf="@id/userDisplayName"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/followLayout"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:paddingTop="@dimen/dp14"
        android:paddingBottom="@dimen/dp14"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:background="@drawable/background_blackish_grey_8dp"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintEnd_toEndOf="@id/activityTime"
        app:layout_constraintTop_toBottomOf="@id/activity">

        <androidx.cardview.widget.CardView
            android:id="@+id/imgLayout"
            android:layout_width="@dimen/dp40"
            android:layout_height="wrap_content"
            app:cardCornerRadius="@dimen/dp8"
            app:cardElevation="@dimen/size_0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/coverImageSmall"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/artistName"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:layout_marginEnd="@dimen/dp12"
            android:maxLines="1"
            android:ellipsize="end"
            android:fontFamily="sans-serif"
            android:textStyle="normal"
            android:textSize="@dimen/sp14"
            android:textColor="@color/white"
            android:lineSpacingExtra="@dimen/sp5"
            android:gravity="start"
            tools:text="Musuh Masyarakat"
            app:layout_constraintStart_toEndOf="@id/imgLayout"
            app:layout_constraintEnd_toStartOf="@id/followBtn"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/noOfFollowers"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:fontFamily="sans-serif"
            android:textStyle="normal"
            android:textSize="@dimen/sp12"
            android:textColor="@color/medium_grey"
            android:lineSpacingExtra="4.8sp"
            android:gravity="start"
            tools:text="3.7k Followers"
            app:layout_constraintEnd_toEndOf="@id/artistName"
            app:layout_constraintStart_toStartOf="@id/artistName"
            app:layout_constraintTop_toBottomOf="@id/artistName"/>

        <TextView
            android:id="@+id/followBtn"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp28"
            android:paddingStart="@dimen/dp8"
            android:paddingEnd="@dimen/dp8"
            android:background="@drawable/selector_follow_btn"
            android:gravity="center_vertical"
            android:drawablePadding="@dimen/dp5"
            android:fontFamily="sans-serif"
            android:textStyle="normal"
            android:textSize="@dimen/sp12"
            android:textColor="@color/dull_white"
            android:lineSpacingExtra="5.6sp"
            android:text="@string/subscribe"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:drawableStartCompat="@drawable/ic_follower_plus" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/likes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:drawablePadding="@dimen/dp4"
        tools:text="28"
        android:textSize="@dimen/sp12"
        android:textColor="@color/grey"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintTop_toBottomOf="@id/followLayout"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:drawableStartCompat="@drawable/ic_thumb_up_grey_12dp" />

    <TextView
        android:id="@+id/disLikes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:drawablePadding="@dimen/dp4"
        tools:text="2"
        android:textSize="@dimen/sp12"
        android:textColor="@color/grey"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintStart_toEndOf="@id/likes"
        app:layout_constraintTop_toTopOf="@id/likes"
        app:drawableStartCompat="@drawable/ic_thumb_down_grey_12dp" />

    <TextView
        android:id="@+id/comments"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:visibility="gone"
        android:gravity="center_vertical"
        android:backgroundTint="@color/white"
        android:drawablePadding="@dimen/dp4"
        tools:text="12"
        android:textSize="@dimen/sp12"
        android:textColor="@color/grey"
        android:fontFamily="sans-serif"
        android:lineSpacingExtra="@dimen/sp3"
        app:layout_constraintStart_toEndOf="@id/disLikes"
        app:layout_constraintTop_toTopOf="@id/likes"
        app:drawableStartCompat="@drawable/ic_comment_grey_12dp" />

    <TextView
        android:id="@+id/noOfReplies"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:visibility="gone"
        android:gravity="center_vertical"
        tools:text="12 REPLY"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_yellow"
        android:textStyle="bold"
        android:textAllCaps="true"
        android:letterSpacing="0.2"
        android:lineSpacingExtra="@dimen/sp3"
        android:fontFamily="sans-serif"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/likes" />

    <include
        android:id="@+id/activity_error"
        layout="@layout/item_user_activity_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/activityTime"
        app:layout_constraintStart_toStartOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/activity" />
</androidx.constraintlayout.widget.ConstraintLayout>