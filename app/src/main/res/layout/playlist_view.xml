<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/playlistViewLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="@dimen/dp8"
    android:orientation="horizontal"
    android:background="@color/home_search">

    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/dp32"
        android:layout_height="@dimen/dp32"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"/>
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="6sp"
        tools:text="Malam Minggu"/>
</LinearLayout>