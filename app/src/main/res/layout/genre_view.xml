<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tile"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp86"
    app:cardCornerRadius="@dimen/dp6"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@color/dark_red">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?selectableItemBackground"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/genreName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp7"
            android:ellipsize="end"
            android:includeFontPadding="false"
            android:fontFamily="@font/readex_pro"
            android:lineSpacingExtra="@dimen/sp4"
            android:maxLines="2"
            android:textColor="@color/white"
            android:textSize="@dimen/sp16"
            android:textStyle="bold"
            tools:text="Suraj" />

        <ImageView
            android:id="@+id/genreIcon"
            android:layout_width="@dimen/dp30"
            android:layout_height="@dimen/dp30"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="@dimen/dp16"
            android:adjustViewBounds="true"
            android:contentDescription="@string/select_genre"
            tools:src="@drawable/ic_heart_selected"/>
    </LinearLayout>
</androidx.cardview.widget.CardView>