<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/dp24"
    android:paddingEnd="@dimen/dp24"
    android:paddingStart="@dimen/dp24"
    android:background="@color/black"
    android:fillViewport="true"
    tools:context=".modules.onboarding.fragments.IntroFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/introImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_name"
            tools:srcCompat="@drawable/ic_dengerin"
            android:adjustViewBounds="true"/>

        <TextView
            android:id="@+id/introHeading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp25"
            android:gravity="center"
            android:textSize="@dimen/sp18"
            android:textColor="@color/white"
            android:fontFamily="sans-serif"
            android:textStyle="bold"
            tools:text="@string/intro_screen1_heading"/>

        <TextView
            android:id="@+id/introDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:gravity="center"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white80"
            android:textStyle="normal"
            android:fontFamily="sans-serif"
            tools:text="@string/intro_screen1_desc"/>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
