<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp16"
    android:layout_marginEnd="@dimen/dp8">

    <LinearLayout
        android:id="@+id/layoutBg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp10"
        android:paddingEnd="@dimen/dp10"
        android:paddingTop="@dimen/dp7"
        android:paddingBottom="@dimen/dp7"
        android:foreground="@drawable/custom_ripple_bg"
        android:background="@drawable/pills_gradient_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/pillIcon"
            android:layout_width="@dimen/dp16"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:adjustViewBounds="true"
            android:layout_gravity="center"
            android:src="@drawable/ic_heart_selected"
            android:contentDescription="@string/app_name"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/pillText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Suraj"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white"
            android:fontFamily="@font/readex_pro"
            android:textStyle="normal"
            android:includeFontPadding="false"
            android:lineSpacingExtra="4sp"
            android:textAlignment="center"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>