<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp8"
    tools:background="@color/black700">

    <ImageView
        android:id="@+id/userProfileImage"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        android:layout_marginTop="@dimen/dp16"
        android:contentDescription="@string/app_name"
        android:adjustViewBounds="true"
        android:foreground="@drawable/custom_ripple_bg_circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/pinnedText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:paddingBottom="@dimen/dp8"
        android:text="@string/pinned_text"
        android:textColor="@color/text_grey3"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:drawablePadding="@dimen/dp4"
        android:visibility="gone"
        tools:visibility="visible"
        app:drawableStartCompat="@drawable/ic_pinned_fill"
        app:layout_constraintStart_toEndOf="@id/userProfileImage"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"
        app:layout_constraintBottom_toTopOf="@id/userName"/>

    <TextView
        android:id="@+id/pinnedBy"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp8"
        tools:text=" @surajpaln2011"
        android:textColor="@color/light_blue"
        android:textSize="@dimen/sp12"
        android:drawablePadding="@dimen/dp4"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toEndOf="@id/pinnedText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/userProfileImage"/>

    <TextView
        android:id="@+id/userName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp2"
        app:layout_constraintWidth_max="@dimen/dp110"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:textColor="@color/medium_grey"
        android:lineSpacingExtra="4.8sp"
        tools:text=" @surajpaln2011"
        android:drawablePadding="@dimen/dp4"
        android:maxLines="1"
        app:layout_constraintStart_toEndOf="@id/userProfileImage"
        app:layout_constraintTop_toBottomOf="@id/pinnedText"/>

    <ImageView
        android:id="@+id/verifiedBadge"
        android:layout_width="@dimen/dp10"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp4"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/ic_verified_tag"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/userName"
        app:layout_constraintTop_toTopOf="@id/userName"
        app:layout_constraintBottom_toBottomOf="@id/userName"/>

    <ImageView
        android:id="@+id/noicemakerBadge"
        android:layout_width="@dimen/dp10"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp4"
        android:visibility="gone"
        tools:visibility="visible"
        android:src="@drawable/ic_noicemaker_badge"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/verifiedBadge"
        app:layout_constraintTop_toTopOf="@id/verifiedBadge"
        app:layout_constraintBottom_toBottomOf="@id/verifiedBadge"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/vipBadge"
        android:layout_width="@dimen/dp10"
        android:layout_height="@dimen/dp10"
        android:layout_marginStart="@dimen/dp4"
        android:visibility="gone"
        tools:visibility="visible"
        app:srcCompat="@drawable/vip_badge"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/noicemakerBadge"
        app:layout_constraintTop_toTopOf="@id/noicemakerBadge"
        app:layout_constraintBottom_toBottomOf="@id/noicemakerBadge"/>

    <TextView
        android:id="@+id/txtHost"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        android:textSize="@dimen/sp9"
        android:gravity="center"
        android:textColor="@color/medium_grey"
        android:lineSpacingExtra="4.8sp"
        android:text="@string/host"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@drawable/background_blackish_grey_22dp"
        android:paddingStart="@dimen/dp6"
        android:paddingEnd="@dimen/dp6"
        android:paddingTop="@dimen/dp2"
        android:paddingBottom="@dimen/dp2"
        android:drawablePadding="@dimen/dp4"
        app:layout_constraintStart_toEndOf="@id/vipBadge"
        app:layout_constraintBottom_toBottomOf="@+id/userName"
        app:layout_constraintTop_toTopOf="@id/userName"
        app:layout_constraintEnd_toStartOf="@id/txtTime"/>

    <TextView
        android:id="@+id/txtTime"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp2"
        android:maxLines="1"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        android:textSize="@dimen/sp12"
        android:textColor="@color/medium_grey"
        android:lineSpacingExtra="4.8sp"
        tools:text="15 menit lalu"
        app:layout_constraintStart_toEndOf="@id/txtHost"
        app:layout_constraintEnd_toStartOf="@id/commentMenu"
        app:layout_constraintTop_toTopOf="@id/userName"
        app:layout_constraintBottom_toBottomOf="@id/userName"/>


    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/commentMenu"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:clickable="true"
        android:focusable="true"
        app:srcCompat="@drawable/ic_three_dots_ws"
        android:background="@drawable/custom_ripple_bg_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/comment"
        app:layout_constraintBottom_toBottomOf="@id/comment"/>

    <noice.app.views.ReadMoreTextView
        android:id="@+id/comment"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp4"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="5.6sp"
        android:linksClickable="true"
        android:autoLink="all"
        android:textIsSelectable="true"
        android:textColorLink="@color/dull_yellow_dark"
        tools:text="Lorem Ipsum is simply dummy text of the printing and typesetting industry."
        app:layout_constrainedHeight="true"
        app:layout_constraintHeight="wrap_content"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintWidth_default="wrap"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toStartOf="@id/userName"
        app:layout_constraintTop_toBottomOf="@id/userName"
        app:layout_constraintBottom_toTopOf="@id/barrier"
        app:layout_constraintEnd_toStartOf="@id/commentMenu"/>

    <TextView
        android:id="@+id/txtLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:text="@string/zero"
        android:background="@drawable/custom_ripple_bg_4dp"
        android:padding="@dimen/dp3"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:fontFamily="@font/roboto"
        android:drawablePadding="@dimen/dp4"
        app:drawableStartCompat="@drawable/like_selector_small"
        app:layout_constraintTop_toBottomOf="@+id/comment"
        app:layout_constraintStart_toStartOf="@+id/comment"/>

    <TextView
        android:id="@+id/txtDisLike"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp1"
        android:layout_marginStart="@dimen/dp36"
        android:background="@drawable/custom_ripple_bg_4dp"
        android:padding="@dimen/dp3"
        android:text="@string/zero"
        android:textColor="@color/white"
        android:textSize="@dimen/sp12"
        android:fontFamily="@font/roboto"
        android:drawablePadding="@dimen/dp4"
        app:drawableStartCompat="@drawable/dis_like_selector_small"
        app:layout_constraintTop_toTopOf="@id/txtLike"
        app:layout_constraintStart_toEndOf="@+id/txtLike"
        app:layout_constraintBottom_toBottomOf="@id/txtLike"/>


    <TextView
        android:id="@+id/lihat_komen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/txtLike"
        app:layout_constraintTop_toBottomOf="@+id/txtLike"
        android:layout_marginTop="@dimen/dp8"
        tools:text="@string/lihat_comment"
        android:background="@drawable/custom_ripple"
        android:fontFamily="@font/roboto"
        android:textColor="@color/dull_yellow"
        android:textSize="@dimen/sp12"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/txtReply"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/txtDisLike"
        app:layout_constraintStart_toEndOf="@+id/txtDisLike"
        android:background="@drawable/custom_ripple_grey"
        android:text="@string/balas"
        android:padding="@dimen/dp3"
        android:layout_marginStart="@dimen/dp29"
        android:textColor="@color/white70"
        android:letterSpacing="0.1"
        android:textSize="@dimen/sp12"
        android:fontFamily="@font/roboto_bold"
        />
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="comment, userProfileImage" />

    <View
        android:id="@+id/line"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp12"
        android:background="#1AFFFFFF"
        app:layout_constraintTop_toBottomOf="@id/lihat_komen"
        app:layout_constraintStart_toStartOf="@id/userName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>