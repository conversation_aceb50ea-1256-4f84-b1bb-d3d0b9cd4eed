<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.onboarding.activity.EditGenreActivity">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:toolbarViewTitle="@string/change_genre"
        app:toolbarBackgroundColor="@color/black700"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dp4"
        android:fillViewport="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/saveBtn">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/heading"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp24"
                android:fontFamily="@font/readex_pro"
                android:textStyle="bold"
                android:includeFontPadding="false"
                android:text="@string/choose_genre_you_like"
                android:textColor="@color/white"
                android:textSize="@dimen/sp20"/>

            <TextView
                android:id="@+id/desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:layout_marginTop="@dimen/dp12"
                android:fontFamily="@font/readex_pro"
                android:includeFontPadding="false"
                android:text="@string/get_recommendation_from_genre"
                android:textColor="@color/white80"
                android:textSize="@dimen/sp14"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/genreRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/dp24"
                android:orientation="vertical"
                android:nestedScrollingEnabled="false"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/saveBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dp24"
        android:paddingBottom="@dimen/dp17"
        android:paddingTop="@dimen/dp17"
        android:paddingStart="@dimen/dp21"
        android:paddingEnd="@dimen/dp21"
        android:gravity="center"
        android:background="@drawable/selector_yellow_btn"
        android:textAllCaps="true"
        android:text="@string/save"
        android:textColor="@color/blackish_blue"
        android:textSize="@dimen/sp12"
        android:letterSpacing="0.2"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>