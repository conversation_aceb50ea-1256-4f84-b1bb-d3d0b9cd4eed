<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black700"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgRadio"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp100"
        app:srcCompat="@drawable/ic_thumb_square"
        android:layout_marginTop="@dimen/dp16"
        android:background="@drawable/round_outline_8"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
    <TextView
        android:id="@+id/txtTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="sarapan_ser"
        android:lineSpacingExtra="3sp"
        android:translationY="-1.6sp"
        android:gravity="center_horizontal|top"
        android:fontFamily="@font/roboto"
        android:textSize="@dimen/sp14"
        android:lineHeight="@dimen/dp20"
        android:textColor="@color/white"
        android:layout_marginTop="@dimen/dp16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imgRadio"
        />

    <TextView
        android:id="@+id/txtSubTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="sarapan_ser"
        android:lineSpacingExtra="3sp"
        android:translationY="-1.6sp"
        android:gravity="center_horizontal|top"
        android:fontFamily="@font/roboto"
        android:textSize="@dimen/sp12"
        android:lineHeight="@dimen/dp17"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="@color/white50"
        android:layout_marginTop="@dimen/dp4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtTitle"
        />


    <TextView
        android:id="@+id/txtShare"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp24"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:drawablePadding="@dimen/dp16"
        android:gravity="center_vertical"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp5"
        android:text="@string/share"
        app:layout_constraintTop_toBottomOf="@+id/txtSubTitle"
        app:drawableStartCompat="@drawable/ic_share_large" />

    <TextView
        android:id="@+id/txtLihatSemua"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp48"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/dp16"
        android:drawablePadding="@dimen/dp16"
        android:gravity="center_vertical"
        android:background="@drawable/ripple_background_blackish_grey_4dp"
        android:fontFamily="sans-serif"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp5"
        android:text="@string/lihat_radio"
        app:layout_constraintTop_toBottomOf="@+id/txtShare"
        app:drawableStartCompat="@drawable/ic_radio" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />



</androidx.constraintlayout.widget.ConstraintLayout>