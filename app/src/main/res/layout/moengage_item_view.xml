<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:foreground="@drawable/custom_ripple_bg_4dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/dp8"
    app:cardBackgroundColor="@android:color/transparent">

    <ImageView
        android:id="@+id/bannerImageView"
        android:layout_width="@dimen/dp300"
        android:layout_height="match_parent"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"/>
</androidx.cardview.widget.CardView>