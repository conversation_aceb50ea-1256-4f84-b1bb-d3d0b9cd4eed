<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/audiobookCopyView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp16"
    android:background="@drawable/background_blackish_grey_8dp">

    <ImageView
        android:id="@+id/audiobookDownload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/audio_book"
        android:src="@drawable/ic_donate_me_gray"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/audiobookText"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:text="@string/audiobook_copy"
        android:textSize="@dimen/sp14"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:fontFamily="sans-serif"
        app:layout_constraintStart_toEndOf="@id/audiobookDownload"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/imgArrow"
        app:layout_constraintBottom_toTopOf="@id/exciteCreators"/>

    <TextView
        android:id="@+id/exciteCreators"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:text="@string/get_full_version_in_physical_book"
        android:textColor="@color/white"
        android:textSize="@dimen/sp14"
        android:fontFamily="sans-serif"
        app:layout_constraintEnd_toEndOf="@id/audiobookText"
        app:layout_constraintStart_toStartOf="@id/audiobookText"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/audiobookText"/>

    <ImageView
        android:id="@+id/imgArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/audio_book"
        android:src="@drawable/ic_angle_right_grey"
        app:tint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>