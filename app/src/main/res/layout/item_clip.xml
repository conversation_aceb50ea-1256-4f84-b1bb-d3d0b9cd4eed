<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/relative_root"
    android:layout_width="@dimen/dp90"
    android:layout_height="wrap_content"
    android:background="@drawable/custom_ripple_bg_4dp">

    <FrameLayout
        android:id="@+id/mainFrame"
        android:layout_width="@dimen/dp90"
        android:layout_height="@dimen/dp90">

        <RelativeLayout
            android:id="@+id/relative_border"
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp90"
            android:background="@drawable/bg_clip_colored">

            <androidx.cardview.widget.CardView
                android:id="@+id/card_image"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:cardCornerRadius="@dimen/dp40">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_clip_cover"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp80"
                    app:srcCompat="@drawable/ic_user_profile"
                    android:contentDescription="@string/clip_cover_image"
                    android:scaleType="centerCrop" />
            </androidx.cardview.widget.CardView>
        </RelativeLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/crownBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginStart="@dimen/dp12"
            app:srcCompat="@drawable/ic_crown_badge"
            android:contentDescription="@string/original"
            android:layout_gravity="bottom"/>
    </FrameLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/mainFrame"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:maxLines="2"
        android:textAlignment="center"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp12"
        android:visibility="visible"
        tools:text="Eps  9: Berdoa Tidak Ada Guna.." />

</RelativeLayout>