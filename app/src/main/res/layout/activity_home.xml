<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mainMotionLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:ignore="contentDescription">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/pager"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            android:clipToPadding="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/miniView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <noice.app.views.MiniPlayerView
            android:id="@+id/miniView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/bottomNavigationView"/>

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNavigationView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            app:menu="@menu/navigation"
            android:background="@color/black700"
            app:itemTextColor="@color/bottom_tab_text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:labelVisibilityMode="labeled"
            app:layout_constraintStart_toStartOf="parent"/>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="@dimen/size_0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/bottomSheetContent"
        layout="@layout/custom_player_fragment" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>