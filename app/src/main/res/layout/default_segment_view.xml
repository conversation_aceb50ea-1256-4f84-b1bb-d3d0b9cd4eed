<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/defaultSegmentView"
    android:layout_width="@dimen/dp130"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/custom_ripple_bg_4dp">

    <ImageView
        android:id="@+id/imgPremiumCarLock"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:contentDescription="@string/app_name"
        android:visibility="gone" />

    <noice.app.views.SquareCardView
        android:id="@+id/squareCardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dp8"
        app:cardElevation="@dimen/size_0dp">

        <FrameLayout
            android:id="@+id/audioBookLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/coverBackgroundImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:contentDescription="@string/app_name"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black60"/>

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp6"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp">

                <ImageView
                    android:id="@+id/coverImageAudioBook"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>
            </androidx.cardview.widget.CardView>
        </FrameLayout>

        <ImageView
            android:id="@+id/coverImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"/>

        <noice.app.views.EqualizerView
            android:id="@+id/equalizer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:barHeight="@dimen/dp40"
            android:background="@color/black30"
            android:visibility="gone"
            app:marginLeft="@dimen/dp3"
            app:marginRight="@dimen/dp3"/>

        <ImageView
            android:id="@+id/origExc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/ic_premium"
            android:contentDescription="@string/original"
            android:layout_gravity="bottom"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="4dp"
            android:visibility="gone"/>
    </noice.app.views.SquareCardView>

    <LinearLayout
        android:id="@+id/layoutRanking"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/ranking"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/roboto_bold"
            android:lineSpacingExtra="@dimen/sp3"
            android:textColor="@color/dull_white"
            android:textSize="@dimen/sp20"
            tools:text="10" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/arrow"
            android:layout_width="@dimen/dp10"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp7"
            android:adjustViewBounds="true"
            app:srcCompat="@drawable/ic_trending_arrow_up"
            app:layout_constraintEnd_toEndOf="@+id/ranking"
            app:layout_constraintStart_toStartOf="@+id/ranking"
            app:layout_constraintTop_toBottomOf="@+id/ranking" />
    </LinearLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:maxLines="2"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:fontFamily="@font/readix_pro_bold"
        android:textSize="@dimen/sp12"
        android:textColor="@color/dull_white"
        android:lineSpacingExtra="@dimen/sp3"
        tools:text="Eps  9: Berdoa Tidak Ada Guna..." />

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:ellipsize="end"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp12"
        android:textColor="@color/neutral_80"
        android:lineSpacingExtra="0sp"
        tools:text="Musuh Masyarakat"/>

    <TextView
        android:id="@+id/duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp4"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:includeFontPadding="false"
        android:textSize="@dimen/sp10"
        android:textColor="@color/neutral_80"
        android:letterSpacing="0.04"
        tools:text="39 MIN"/>
</LinearLayout>