<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:orientation="vertical"
    tools:context=".modules.dashboard.home.fragment.NoiceContentMenu">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:paddingBottom="@dimen/dp30"
        android:clipToPadding="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <androidx.cardview.widget.CardView
                android:id="@+id/coverImageLayout"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp100"
                android:layout_marginTop="@dimen/dp16"
                app:cardElevation="@dimen/size_0dp"
                app:cardCornerRadius="@dimen/dp8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/coverImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_user_profile"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"/>
            </androidx.cardview.widget.CardView>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/hostLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <TextView
                    android:id="@+id/textUserName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sp16"
                    android:fontFamily="@font/roboto_med"
                    android:textColor="@color/blue_host"
                    tools:text="Atiqulalam"
                    android:maxLength="20"
                    android:drawablePadding="@dimen/dp4"
                    android:layout_marginTop="@dimen/dp12"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:drawableStartCompat="@drawable/ic_shield" />

                <ImageView
                    android:id="@+id/playButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:visibility="gone"
                    android:src="@drawable/ic_play_black_yellow"
                    android:contentDescription="@string/app_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textUserName"/>

                <TextView
                    android:id="@+id/txtReplay"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:textColor="@color/white"
                    android:fontFamily="@font/roboto"
                    android:text="@string/replay_live"
                    android:visibility="visible"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp4"
                    android:paddingEnd="@dimen/dp4"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/background_blackish_grey_4dp"
                    android:textSize="@dimen/sp12"
                    android:drawablePadding="@dimen/dp3"
                    app:drawableStartCompat="@drawable/ic_replay"
                    app:drawableTint="@color/dull_yellow"
                    android:layout_marginTop="@dimen/dp16"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textUserName"
                    />


                <TextView
                    android:id="@+id/txtHost"
                    android:layout_width="@dimen/size_0dp"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto"
                    android:textStyle="normal"
                    android:textSize="@dimen/sp10"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:text="@string/host"
                    android:paddingStart="@dimen/dp9"
                    android:paddingEnd="@dimen/dp9"
                    android:paddingTop="@dimen/dp2"
                    android:paddingBottom="@dimen/dp2"
                    android:layout_marginStart="@dimen/dp13"
                    app:layout_constraintTop_toTopOf="@+id/textUserName"
                    app:layout_constraintBottom_toBottomOf="@+id/textUserName"
                    app:layout_constraintStart_toEndOf="@+id/textUserName"
                    android:background="@drawable/background_blackish_grey_22dp"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/episodeName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp6"
                android:gravity="center_horizontal"
                tools:text="Eps 9 : Berdoa Tidak Ada Gunanya (bersama Citta  Citata"/>

            <TextView
                android:id="@+id/details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp12"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:gravity="center_horizontal"
                tools:text="33 Menit  · 30 November 2020"/>

            <TextView
                android:id="@+id/artistName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp4"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp12"
                android:textColor="@color/white90"
                android:lineSpacingExtra="@dimen/sp5"
                android:gravity="center_horizontal"
                tools:text="Oleh Musuh Masyarakat"/>

            <TextView
                android:id="@+id/videoQuality"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp24"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp16"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/video_quality"
                app:drawableStartCompat="@drawable/ic_settings" />

            <TextView
                android:id="@+id/addToPlayList"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp16"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/add_to_playlist"
                app:drawableStartCompat="@drawable/add_to_playlist" />

            <LinearLayout
                android:id="@+id/downloadLayout"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp12">

                    <ImageView
                        android:id="@+id/downloadIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_download"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name"/>

                    <ProgressBar
                        android:id="@+id/downloadProgress"
                        android:layout_width="@dimen/dp28"
                        android:layout_height="@dimen/dp28"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        android:indeterminate="false"
                        android:max="100"
                        android:progress="50"
                        android:progressDrawable="@drawable/circular_progress_bar"
                        android:background="@drawable/circular_progress_background"
                        style="?android:attr/progressBarStyleHorizontal"/>
                </FrameLayout>

                <TextView
                    android:id="@+id/download"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:fontFamily="sans-serif"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/dull_white"
                    android:lineSpacingExtra="@dimen/sp5"
                    android:text="@string/download" />
            </LinearLayout>

            <TextView
                android:id="@+id/markAsPlayed"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/mark_as_played"
                app:drawableStartCompat="@drawable/ic_tick_white" />

            <TextView
                android:id="@+id/addToQueue"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/add_to_queue_"
                app:drawableStartCompat="@drawable/ic_add_to_que" />

            <TextView
                android:id="@+id/seeQueue"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp16"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/see_queue"
                app:drawableStartCompat="@drawable/ic_see_queue" />

            <TextView
                android:id="@+id/share"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/share"
                app:drawableStartCompat="@drawable/ic_share_large" />

            <TextView
                android:id="@+id/like"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/like"
                app:drawableStartCompat="@drawable/ic_thumb_up" />

            <TextView
                android:id="@+id/disLike"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/dislike"
                app:drawableStartCompat="@drawable/ic_thumb_down" />

            <TextView
                android:id="@+id/seePodCasts"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/see_podcasts"
                app:drawableStartCompat="@drawable/ic_see_podcast" />

            <TextView
                android:id="@+id/seeEpisodeDetails"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp8"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp8"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/dull_white"
                android:lineSpacingExtra="@dimen/sp5"
                android:text="@string/see_episode_details"
                app:drawableStartCompat="@drawable/ic_see_podcast" />


            <TextView
                android:id="@+id/deleteRecording"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp12"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/red"
                android:visibility="gone"
                android:lineSpacingExtra="@dimen/sp3"
                android:text="@string/delete_this_rebroadcast"
                app:drawableStartCompat="@drawable/ic_cross_red"/>


            <TextView
                android:id="@+id/reportContent"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp48"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginBottom="@dimen/dp60"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:drawablePadding="@dimen/dp12"
                android:gravity="center_vertical"
                android:background="@drawable/ripple_background_blackish_grey_4dp"
                android:fontFamily="sans-serif"
                android:textSize="@dimen/sp14"
                android:textColor="@color/red"
                android:lineSpacingExtra="@dimen/sp3"
                android:text="@string/report_content"
                app:drawableStartCompat="@drawable/ic_information_red"/>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="@dimen/dp40"
        android:layout_height="@dimen/dp40"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        />
</androidx.constraintlayout.widget.ConstraintLayout>