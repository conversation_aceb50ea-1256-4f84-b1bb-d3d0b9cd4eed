<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    android:padding="@dimen/dp16"
    tools:context=".modules.onboarding.fragments.OtpFragment">

    <TextView
        android:id="@+id/txtHeading"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:gravity="center"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textSize="@dimen/sp24"
        android:textColor="@color/dull_white"
        android:text="@string/enter_the_otp"
        android:lineSpacingExtra="0sp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/txtHeading1"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:gravity="center"
        android:letterSpacing="0.01"
        android:lineSpacingExtra="@dimen/sp6"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white_900"
        android:text="@string/enter_otp_sent_to"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtHeading"/>

    <TextView
        android:id="@+id/txtMobile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:fontFamily="@font/readex_pro"
        android:lineSpacingExtra="@dimen/sp6"
        android:textStyle="bold"
        android:textSize="@dimen/sp16"
        android:textColor="@color/white80"
        android:letterSpacing="0.01"
        tools:text="+6281234567890"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/txtHeading1"
        app:layout_constraintEnd_toStartOf="@id/txtEdit"/>

    <TextView
        android:id="@+id/txtEdit"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp40"
        android:layout_marginStart="@dimen/dp16"
        android:paddingStart="@dimen/dp13"
        android:paddingEnd="@dimen/dp13"
        android:background="@drawable/edit_button_drawable"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        android:textSize="@dimen/sp14"
        android:textColor="@color/dull_yellow"
        android:letterSpacing="0.01"
        android:gravity="center"
        android:text="@string/change_"
        app:layout_constraintStart_toEndOf="@id/txtMobile"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/txtMobile"
        app:layout_constraintBottom_toBottomOf="@id/txtMobile"/>

    <noice.app.views.otp.NoiceOtpView
        android:id="@+id/otpView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp30"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@id/txtHeading"
        app:layout_constraintStart_toStartOf="@id/txtHeading"
        app:layout_constraintTop_toBottomOf="@+id/txtEdit" />

    <TextView
        android:id="@+id/txtResendText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp28"
        android:fontFamily="@font/readex_pro"
        android:textSize="@dimen/sp14"
        android:textColor="@color/white500"
        android:lineSpacingExtra="0sp"
        android:letterSpacing="0.01"
        android:text="@string/have_not_recieved_otp"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otpView"
        app:layout_constraintEnd_toStartOf="@id/resendLayout"/>

    <FrameLayout
        android:id="@+id/resendLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp8"
        app:layout_constraintTop_toTopOf="@id/txtResendText"
        app:layout_constraintBottom_toBottomOf="@id/txtResendText"
        app:layout_constraintStart_toEndOf="@id/txtResendText"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/txtCountDown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:textSize="@dimen/sp12"
            android:textColor="@color/white"
            android:letterSpacing="0.04"
            android:text="@string/_00_30"
            android:lineSpacingExtra="0sp" />

        <TextView
            android:id="@+id/txtResend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/custom_ripple"
            android:visibility="gone"
            tools:visibility="visible"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:textSize="@dimen/sp14"
            android:textColor="@color/dull_yellow"
            android:lineSpacingExtra="0sp"
            android:letterSpacing="0.01"
            android:text="@string/resend_otp" />
    </FrameLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/proceed"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp52"
        android:layout_marginTop="@dimen/dp28"
        android:insetTop="@dimen/size_0dp"
        android:insetBottom="@dimen/size_0dp"
        android:gravity="center"
        app:cornerRadius="@dimen/dp14"
        android:backgroundTint="@color/dull_yellow"
        android:text="@string/verifikasi"
        android:textAllCaps="false"
        android:textColor="@color/black"
        android:textSize="@dimen/sp16"
        android:lineSpacingExtra="0sp"
        android:fontFamily="@font/readex_pro"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/txtResendText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        android:elevation="@dimen/dp24"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>