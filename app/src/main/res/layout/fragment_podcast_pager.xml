<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700">

    <noice.app.views.ToolbarView
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:id="@+id/toolbar"
        android:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <LinearLayout
        android:id="@+id/layoutFilter"
        android:layout_width="@dimen/dp95"
        android:layout_height="@dimen/dp32"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/border_white_radius_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/txtFilter"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/sp14"
            android:layout_weight="0.8"
            android:visibility="visible"
            android:text="@string/latest"
            app:fontFamily="sans-serif" />

        <ImageView
            android:id="@+id/hairTypeFilterArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0.2"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_gravity="center"
            android:src="@drawable/ic_arrow_down"
            tools:ignore="ContentDescription" />
    </LinearLayout>

    <TextView
        android:id="@+id/semuaEntity"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp32"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp16"
        android:text="@string/semua_episode"
        android:textSize="@dimen/sp20"
        android:textColor="@color/white"
        android:lineSpacingExtra="@dimen/dp5"
        android:gravity="center"
        android:fontFamily="sans-serif"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp16"
        android:layout_marginStart="@dimen/dp4"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/black700"
        app:tabTextAppearance="@style/NoiceTabLayoutStyle"
        app:tabGravity="start"
        app:tabMode="scrollable"
        app:tabBackground="@color/black700"
        app:tabIndicatorColor="@color/dull_yellow"
        app:tabTextColor="@color/white50"
        app:tabSelectedTextColor="@color/white"
        app:layout_constraintTop_toBottomOf="@id/semuaEntity"/>

    <noice.app.views.NonSwipeableViewPager
        android:id="@+id/viewPager"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>