<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".modules.indexpages.fragment.GenreDetailFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:collapsedTitleTextAppearance="@style/RobotoBoldTextAppearance"
            app:contentScrim="@color/black"
            app:expandedTitleGravity="center"
            app:expandedTitleTextAppearance="@style/ExpandedAppBar"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_collapseMode="parallax">

                <View
                    android:id="@+id/imgCover"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp180"
                    android:adjustViewBounds="true"
                    android:background="@color/persian_blue"
                    android:contentDescription="@string/app_name" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/channel_gradiant" />
            </FrameLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:contentInsetStartWithNavigation="0dp"
                app:layout_anchor="@id/collapsingToolbar"
                app:layout_collapseMode="pin"
                app:navigationIcon="@drawable/ic_left_angle_white"
                app:theme="@style/ThemeOverlay.AppCompat.Dark"
                app:title="" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/pullToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:context=".modules.dashboard.home.fragment.RadioHomeFragment">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/radioRecycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />

            <noice.app.views.ErrorView
                android:id="@+id/errorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:error_padding_bottom="@dimen/dp60" />
        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>