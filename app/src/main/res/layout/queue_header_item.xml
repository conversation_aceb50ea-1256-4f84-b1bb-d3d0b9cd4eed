<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/dp5"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView
        android:id="@+id/header_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineHeight="@dimen/dp21"
        tools:text="Sedang Diputar"
        android:background="@color/black700"
        android:textColor="@color/white"
        android:maxLines="2"
        android:textStyle="bold"
        android:textSize="@dimen/sp18"
        android:ellipsize="end"
        android:fontFamily="sans-serif"
        xmlns:tools="http://schemas.android.com/tools" />
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp10"
        android:background="@color/black700"/>

</LinearLayout>
