<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black700"
    android:paddingTop="@dimen/dp6"
    android:paddingBottom="@dimen/dp6">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgCheck"
        android:layout_width="@dimen/dp30"
        android:layout_height="@dimen/dp30"
        android:layout_marginStart="@dimen/dp2"
        android:paddingTop="@dimen/dp5"
        android:paddingBottom="@dimen/dp5"
        android:paddingStart="@dimen/dp5"
        android:paddingEnd="@dimen/dp11"
        app:srcCompat="@drawable/check_box_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/txtTitleItem"
        app:layout_constraintBottom_toBottomOf="@id/txtSubtitle"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/img"
        android:layout_width="@dimen/dp36"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:layout_marginStart="@dimen/dp10"
        android:background="@drawable/round_outline_8"
        app:srcCompat="@drawable/ic_thumb_default"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/imgCheck"/>

    <TextView
        android:id="@+id/txtTitleItem"
        app:layout_constraintEnd_toStartOf="@id/imgMenu"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/img"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp16"
        android:ellipsize="end"
        android:textSize="@dimen/sp14"
        android:fontFamily="sans-serif"
        android:textColor="@color/white"
        tools:text="Eps 9 : Berdoa Tidak Ada Gunanya ..."
        android:lineSpacingExtra="@dimen/dp3"
        android:translationY="-1.6sp"
        android:gravity="top"
        android:layout_marginEnd="@dimen/dp16"
        />

    <TextView
        app:layout_constraintEnd_toStartOf="@id/imgMenu"
        app:layout_constraintTop_toBottomOf="@id/txtTitleItem"
        app:layout_constraintStart_toStartOf="@id/txtTitleItem"
        android:id="@+id/txtSubtitle"
        android:layout_marginTop="@dimen/dp6"
        android:layout_width="0dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp12"
        android:textColor="#CC999999"
        tools:text="Eps 9 : Berdoa Tidak Ada Gunanya ..."
        android:lineSpacingExtra="0sp"
        android:gravity="center_vertical"
        android:layout_marginEnd="@dimen/dp16"
        />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgMenu"
        android:padding="@dimen/dp3"
        android:layout_marginEnd="@dimen/dp2"
        app:layout_constraintTop_toTopOf="@id/txtTitleItem"
        app:layout_constraintBottom_toBottomOf="@id/txtSubtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:srcCompat="@drawable/ic_hamburger_menu"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <View
        android:layout_marginTop="@dimen/dp18"
        android:background="@color/white10"
        app:layout_constraintEnd_toEndOf="@id/imgMenu"
        app:layout_constraintStart_toStartOf="@id/img"
        app:layout_constraintTop_toBottomOf="@id/txtSubtitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"/>

</androidx.constraintlayout.widget.ConstraintLayout>