<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".modules.trending.fragment.TrendingEpisodeFragment">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="UselessParent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_trending"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:error_padding_bottom="@dimen/dp60" />
    </FrameLayout>

</RelativeLayout>