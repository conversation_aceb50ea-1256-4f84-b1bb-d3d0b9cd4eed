<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp70"
    android:orientation="horizontal"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/coverImage"
        android:layout_width="@dimen/dp50"
        android:layout_height="@dimen/dp50"
        android:adjustViewBounds="true"
        android:contentDescription="@string/app_name"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearUsername"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp19"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginEnd="@dimen/dp5"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toStartOf="@id/text_invite"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toTopOf="@id/coverImage">

        <TextView
            android:id="@+id/txtUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="@dimen/dp3"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center_vertical"
            android:maxLength="15"
            android:textAllCaps="false"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:textSize="@dimen/sp12"
            tools:text="\@sahiljeet_noice" />

        <ImageView
            android:id="@+id/noicemakerBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp4"
            android:visibility="gone"
            tools:visibility="visible"
            android:src="@drawable/ic_noicemaker_badge"
            android:contentDescription="@string/app_name"/>

        <TextView
            android:id="@+id/txtHost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp2"
            android:background="@drawable/background_blackish_grey_4dp"
            android:drawablePadding="@dimen/dp4"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:paddingStart="@dimen/dp6"
            android:paddingTop="@dimen/dp2"
            android:paddingEnd="@dimen/dp6"
            android:paddingBottom="@dimen/dp2"
            android:textColor="@color/white700"
            android:textSize="@dimen/sp9"
            android:textStyle="normal"
            android:visibility="gone"
            tools:text="@string/host"
            tools:visibility="visible" />
    </LinearLayout>

    <TextView
        android:id="@+id/subTitle"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp12"
        android:layout_marginTop="@dimen/dp3"
        android:layout_marginEnd="@dimen/dp5"
        android:fontFamily="@font/roboto"
        android:maxLines="1"
        android:textColor="@color/medium_grey"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toStartOf="@id/text_invite"
        app:layout_constraintStart_toEndOf="@id/coverImage"
        app:layout_constraintTop_toBottomOf="@id/linearUsername"
        app:layout_goneMarginTop="@dimen/dp2"
        tools:text="Noice DEV" />

    <TextView
        android:id="@+id/text_invite"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp16"
        android:background="@drawable/selector_follow_btn_white"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical"
        android:lineSpacingExtra="5.6sp"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:text="@string/undang"
        android:drawablePadding="@dimen/dp5"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp12"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/image_three_dots"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/image_three_dots"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp28"
        android:foreground="?attr/selectableItemBackgroundBorderless"
        android:paddingStart="@dimen/dp8"
        android:paddingEnd="@dimen/dp8"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_three_dots_filled"
        app:layout_constraintBottom_toBottomOf="@+id/text_invite"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/text_invite" />

    <View
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp8"
        android:background="@color/white10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/linearUsername" />
</androidx.constraintlayout.widget.ConstraintLayout>
