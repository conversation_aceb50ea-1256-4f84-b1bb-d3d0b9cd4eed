<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearRoot"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/dp40"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_user_profile" />

    <TextView
        android:id="@+id/title"
        android:layout_width="@dimen/dp60"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:ellipsize="end"
        android:fontFamily="@font/roboto_bold"
        android:maxLines="2"
        android:gravity="center_horizontal"
        android:textColor="@color/dull_white"
        android:textSize="@dimen/sp11"
        tools:text="Whatsapp Status" />

</LinearLayout>