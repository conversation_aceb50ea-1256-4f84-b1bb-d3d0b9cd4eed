<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black700"
    tools:context=".modules.onboarding.activity.EditProfileActivity">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="16dp"
        app:toolbarViewTitle="Lengkapi Profilmu"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:toolbarBackgroundColor="@color/black700"/>

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/profilePic"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp100"
                android:layout_marginTop="@dimen/dp24"
                android:layout_marginStart="@dimen/dp16"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_user_profile"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <TextView
                android:id="@+id/uploadPicBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp24"
                android:fontFamily="sans-serif"
                android:textStyle="bold"
                android:background="@drawable/border_dull_yellow_dark_radius_4dp"
                android:padding="@dimen/dp12"
                android:textAllCaps="true"
                android:text="@string/upload_photo"
                android:textSize="@dimen/sp12"
                android:textColor="@color/dull_yellow_dark"
                android:letterSpacing="0.2"
                app:layout_constraintStart_toEndOf="@id/profilePic"
                app:layout_constraintTop_toTopOf="@id/profilePic"
                app:layout_constraintBottom_toBottomOf="@id/profilePic"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/fullNameInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp16"
                android:hint="@string/full_name_star"
                android:textColorHint="@color/white70"
                app:hintTextColor="@color/white70"
                app:boxBackgroundColor="@color/blackish_blue"
                app:boxBackgroundMode="filled"
                app:boxCornerRadiusBottomEnd="@dimen/dp4"
                app:boxCornerRadiusBottomStart="@dimen/dp4"
                app:boxCornerRadiusTopEnd="@dimen/dp4"
                app:boxCornerRadiusTopStart="@dimen/dp4"
                app:boxStrokeColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/profilePic">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/fullNameEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLength="80"
                    android:textSize="@dimen/sp14"
                    android:fontFamily="sans-serif"
                    android:textColor="@color/white"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <FrameLayout
                android:id="@+id/userNameLayout"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:orientation="horizontal"
                android:baselineAligned="false"
                android:background="@drawable/background_blackish_blue_4dp"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintTop_toBottomOf="@id/fullNameInputLayout">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/userNameInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/username"
                    android:textColorHint="@color/white70"
                    app:boxBackgroundMode="filled"
                    app:boxBackgroundColor="@color/blackish_blue"
                    app:boxCornerRadiusBottomEnd="@dimen/dp4"
                    app:boxCornerRadiusBottomStart="@dimen/dp4"
                    app:boxCornerRadiusTopEnd="@dimen/dp4"
                    app:boxCornerRadiusTopStart="@dimen/dp4"
                    app:hintTextColor="@color/white70"
                    app:boxStrokeColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/userNameEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingEnd="@dimen/dp50"
                        android:textSize="@dimen/sp14"
                        android:fontFamily="sans-serif"
                        android:textColor="@color/white"
                        android:inputType="textPersonName"/>
                </com.google.android.material.textfield.TextInputLayout>

                <ProgressBar
                    android:id="@+id/userProgressBar"
                    android:layout_width="@dimen/dp16"
                    android:layout_height="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    android:indeterminateTint="@color/dull_yellow"/>

                <ImageView
                    android:id="@+id/userNameStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_gravity="center_vertical|end"
                    android:visibility="gone"
                    android:contentDescription="@string/app_name"
                    android:adjustViewBounds="true"/>
            </FrameLayout>

            <TextView
                android:id="@+id/nameDisclaimer"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:layout_marginTop="@dimen/dp8"
                android:fontFamily="sans-serif-medium"
                android:textStyle="normal"
                android:textSize="@dimen/sp12"
                android:textColor="@color/whitish_grey"
                android:text="@string/name_disclaimer"
                app:layout_constraintStart_toStartOf="@id/userNameLayout"
                app:layout_constraintEnd_toEndOf="@id/userNameLayout"
                app:layout_constraintTop_toBottomOf="@id/userNameLayout"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/userBioLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:hint="@string/description"
                android:textColorHint="@color/white70"
                app:hintTextColor="@color/white70"
                app:boxBackgroundColor="@color/blackish_blue"
                app:boxBackgroundMode="filled"
                app:boxCornerRadiusBottomEnd="@dimen/dp4"
                app:boxCornerRadiusBottomStart="@dimen/dp4"
                app:boxCornerRadiusTopEnd="@dimen/dp4"
                app:boxCornerRadiusTopStart="@dimen/dp4"
                app:boxStrokeColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintTop_toBottomOf="@id/nameDisclaimer">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/userBio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLength="100"
                    android:textSize="@dimen/sp14"
                    android:fontFamily="sans-serif"
                    android:textColor="@color/white"
                    android:inputType="textMultiLine"/>
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/yearInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:hint="@string/year_of_birth_star"
                android:textColorHint="@color/white70"
                app:hintTextColor="@color/white70"
                app:boxBackgroundColor="@color/blackish_blue"
                app:boxBackgroundMode="filled"
                app:boxCornerRadiusBottomEnd="@dimen/dp4"
                app:boxCornerRadiusBottomStart="@dimen/dp4"
                app:boxCornerRadiusTopEnd="@dimen/dp4"
                app:boxCornerRadiusTopStart="@dimen/dp4"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintTop_toBottomOf="@id/userBioLayout">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/yearEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLength="4"
                    android:longClickable="false"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/white"
                    android:inputType="number"/>
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/genderLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:fontFamily="sans-serif-medium"
                android:textStyle="normal"
                android:textSize="@dimen/sp12"
                android:textColor="@color/white"
                android:text="@string/gender"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintTop_toBottomOf="@id/yearInputLayout"/>

            <RadioGroup
                android:id="@+id/genderRadioGroup"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintTop_toBottomOf="@id/genderLabel">

                <RadioButton
                    android:id="@+id/genderMale"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="@dimen/dp12"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/white"
                    android:text="@string/male"
                    android:fontFamily="sans-serif"/>

                <RadioButton
                    android:id="@+id/genderFemale"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="@dimen/dp12"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/white"
                    android:text="@string/female"
                    android:fontFamily="sans-serif"/>
            </RadioGroup>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/locationInputLayout"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:hint="@string/city"
                android:textColorHint="@color/white70"
                app:hintTextColor="@color/white70"
                app:placeholderTextColor="@color/white70"
                app:placeholderText="@string/type_city_you_live_in"
                app:boxBackgroundColor="@color/blackish_blue"
                app:boxBackgroundMode="filled"
                app:boxCornerRadiusBottomEnd="@dimen/dp4"
                app:boxCornerRadiusBottomStart="@dimen/dp4"
                app:boxCornerRadiusTopEnd="@dimen/dp4"
                app:boxCornerRadiusTopStart="@dimen/dp4"
                app:boxStrokeColor="@color/white"
                app:layout_constraintStart_toStartOf="@id/genderRadioGroup"
                app:layout_constraintEnd_toEndOf="@id/genderRadioGroup"
                app:layout_constraintTop_toBottomOf="@id/genderRadioGroup">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/locationEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sp14"
                    android:textColor="@color/white"
                    android:inputType="textPersonName"/>
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/kotaDisclaimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:fontFamily="sans-serif-medium"
                android:textStyle="normal"
                android:textSize="@dimen/sp12"
                android:textColor="@color/whitish_grey"
                android:text="@string/kota_disclaimer"
                app:layout_constraintStart_toStartOf="@id/locationInputLayout"
                app:layout_constraintTop_toBottomOf="@id/locationInputLayout"/>

            <TextView
                app:layout_constraintTop_toBottomOf="@+id/kotaDisclaimer"
                app:layout_constraintLeft_toLeftOf="@+id/kotaDisclaimer"
                android:id="@+id/txtCurrent"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp48"
                android:fontFamily="sans-serif"
                android:textStyle="normal"
                android:textSize="@dimen/sp14"
                android:drawablePadding="11dp"
                android:gravity="center_vertical"
                android:textColor="#f9cf5f"
                android:text="@string/use_my_location"
                app:drawableStartCompat="@drawable/ic_pin" />

            <RelativeLayout
                android:id="@+id/pbLayout"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="@+id/locationInputLayout"
                app:layout_constraintBottom_toBottomOf="@+id/locationInputLayout"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:background="@android:color/transparent"
                android:layout_width="match_parent"
                android:clickable="true"
                android:layout_height="0dp"
                android:focusable="true">
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="30dp"
                    android:layout_width="@dimen/dp24"
                    android:layout_height="@dimen/dp24"/>
            </RelativeLayout>

            <TextView
                android:id="@+id/mandatoryFields"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp25"
                android:textSize="@dimen/sp12"
                android:textColor="@color/white"
                android:text="@string/required_fields"
                android:fontFamily="sans-serif-medium"
                app:layout_constraintStart_toStartOf="@id/submitButton"
                app:layout_constraintTop_toBottomOf="@id/txtCurrent"/>

            <TextView
                android:id="@+id/submitButton"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:padding="@dimen/dp18"
                android:background="@drawable/background_yellow_radius_4dp"
                android:gravity="center"
                android:textSize="@dimen/sp12"
                android:textColor="@color/blackish_blue"
                android:text="@string/done"
                android:textAllCaps="true"
                android:letterSpacing="0.2"
                android:fontFamily="sans-serif-medium"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintTop_toBottomOf="@id/mandatoryFields"/>

            <TextView
                android:id="@+id/privacyPolicy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp18"
                android:layout_marginBottom="@dimen/dp30"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:text="@string/privacy_policy_text"
                android:textColor="@color/white80"
                android:textSize="@dimen/sp12"
                android:textStyle="normal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/fullNameInputLayout"
                app:layout_constraintStart_toStartOf="@id/profilePic"
                app:layout_constraintTop_toBottomOf="@id/submitButton"
                app:layout_constraintVertical_bias="0.0" />

            <androidx.cardview.widget.CardView
                android:id="@+id/locationLayout"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toBottomOf="@id/locationInputLayout"
                app:cardUseCompatPadding="false"
                app:cardCornerRadius="@dimen/dp4"
                app:cardElevation="0dp"
                app:cardBackgroundColor="#1e222c"
                android:visibility="gone"
                tools:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvAddress"
                    app:layout_constraintTop_toBottomOf="@id/locationInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"/>
            </androidx.cardview.widget.CardView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"/>
</androidx.constraintlayout.widget.ConstraintLayout>