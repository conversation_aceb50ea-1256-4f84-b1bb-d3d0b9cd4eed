<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/black700"
    android:orientation="vertical"
    tools:context=".modules.onboarding.fragments.MyGenreFragment">

    <noice.app.views.ToolbarView
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:toolbarBackPaddingStart="@dimen/dp24"
        app:toolbarViewTitle="@string/your_genre"
        app:toolbarSkipVisibility="visible"
        app:toolbarRightTextColor="@color/white"
        app:skipPaddingEnd="@dimen/dp16"
        app:toolbarBackgroundColor="@color/black700"
        app:toolbarRightButtonText="@string/change"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/genreRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/emptyView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:visibility="gone">

        <ImageView
            android:id="@+id/noGenreImageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_no_genre"
            app:layout_constraintTop_toTopOf="parent"/>


        <TextView
            android:id="@+id/noGenreTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:layout_gravity="center_horizontal"
            android:textSize="@dimen/sp18"
            android:textColor="@color/white"
            android:text="@string/no_activities"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            android:includeFontPadding="false"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noGenreImageView"/>

        <TextView
            android:id="@+id/noGenreDescription"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_gravity="center_horizontal"
            android:gravity="center"
            android:textSize="@dimen/sp16"
            android:textColor="@color/white80"
            android:text="@string/not_activities_text"
            android:fontFamily="@font/readex_pro"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noGenreTitle"/>

        <TextView
            android:id="@+id/editGenre"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp24"
            android:layout_marginEnd="@dimen/dp24"
            android:layout_marginTop="@dimen/dp24"
            android:paddingTop="@dimen/dp16"
            android:paddingBottom="@dimen/dp18"
            android:gravity="center"
            android:background="@drawable/selector_yellow_btn"
            android:textAllCaps="true"
            android:text="@string/select_genre"
            android:textColor="@color/blackish_blue"
            android:textSize="@dimen/sp12"
            android:letterSpacing="0.2"
            android:fontFamily="@font/readex_pro"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/noGenreDescription"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <noice.app.views.ErrorView
        android:id="@+id/errorView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp300"/>

</LinearLayout>