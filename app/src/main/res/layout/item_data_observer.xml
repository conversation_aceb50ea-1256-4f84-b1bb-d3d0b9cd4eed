<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="noice.app.utils.Utils" />

        <variable
            name="item"
            type="noice.app.services.observer.InterceptorStatTrace" />
    </data>

    <androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@{`Type: `.concat(item.type.toString())}"
            android:textSize="@dimen/sp16"
            android:textStyle="bold"
            tools:text="Type: IMAGE" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp10"
            android:text="@{`URL: `.concat(item.url)}"
            android:textSize="@dimen/sp16"
            tools:text="URL: https://noice.id/" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/dp10"
            android:text="@{`Size: `.concat(Utils.INSTANCE.bytesToReadable(item.responseSize))}"
            android:textSize="@dimen/sp16"
            tools:text="Size: 1 MB" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/black"
            android:paddingTop="@dimen/dp20"
            android:alpha="0.3"/>
    </androidx.appcompat.widget.LinearLayoutCompat>

</layout>