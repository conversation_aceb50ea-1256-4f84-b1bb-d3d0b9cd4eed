<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recommendationSegment"
    android:layout_width="@dimen/dp221"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/home_search"
    app:cardCornerRadius="@dimen/dp8"
    app:cardElevation="@dimen/size_0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/imgLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/imageLayout"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <noice.app.views.SquareCardView
                android:id="@+id/squareCardAudiobook"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp"
                android:visibility="gone">

                <FrameLayout
                    android:id="@+id/audioBookLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/coverBackgroundImage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:contentDescription="@string/app_name"
                        android:scaleType="centerCrop" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/black60" />

                    <androidx.cardview.widget.CardView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_margin="@dimen/dp14"
                        app:cardCornerRadius="@dimen/dp8"
                        app:cardElevation="@dimen/size_0dp">

                        <ImageView
                            android:id="@+id/coverImageAudioBook"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:adjustViewBounds="true"
                            android:contentDescription="@string/app_name"
                            tools:src="@tools:sample/backgrounds/scenic" />
                    </androidx.cardview.widget.CardView>
                </FrameLayout>
            </noice.app.views.SquareCardView>

            <ImageView
                android:id="@+id/coverImage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:contentDescription="@string/app_name" />

            <ImageView
                android:id="@+id/image_noice_original"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:layout_marginStart="2dp"
                android:layout_marginTop="2dp"
                android:contentDescription="@string/app_name"
                tools:src="@drawable/ic_noice_original"
                android:visibility="gone" />

            <noice.app.views.SquareCardView
                android:id="@+id/cardLayoutLive"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/hostBgBlur"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:adjustViewBounds="true"
                    android:contentDescription="@string/app_name"
                    android:scaleType="centerCrop" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/black80" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/thumbnailLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingStart="@dimen/dp5"
                    android:paddingEnd="@dimen/dp5">

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideLine5"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />

                    <TextView
                        android:id="@+id/noiceLive"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp16"
                        android:layout_marginTop="@dimen/dp16"
                        app:drawableStartCompat="@drawable/ic_live_icon"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/noiceLiveTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:fontFamily="@font/roboto_bold"
                        android:textAlignment="viewStart"
                        android:textColor="@color/grey"
                        android:textSize="@dimen/sp8"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="18.30 WIB" />

                    <TextView
                        android:id="@+id/liveListenersCount"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp16"
                        android:layout_marginEnd="@dimen/dp16"
                        android:drawablePadding="@dimen/dp6"
                        android:textColor="@color/light_grey"
                        android:textSize="@dimen/sp12"
                        app:drawableStartCompat="@drawable/ic_listener_icon"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="124" />

                    <ImageView
                        android:id="@+id/hostProfileImage"
                        android:layout_width="@dimen/dp60"
                        android:layout_height="@dimen/dp60"
                        android:layout_marginEnd="@dimen/dp4"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_user_profile"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/guideLine5"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/hostUsername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp4"
                        android:drawablePadding="@dimen/dp2"
                        android:ellipsize="end"
                        android:gravity="center_horizontal"
                        android:maxLength="7"
                        android:maxLines="1"
                        android:textColor="@color/light_blue"
                        android:textSize="@dimen/sp10"
                        app:drawableStartCompat="@drawable/ic_live_host_icon"
                        app:layout_constraintEnd_toEndOf="@id/hostProfileImage"
                        app:layout_constraintStart_toStartOf="@id/hostProfileImage"
                        app:layout_constraintTop_toBottomOf="@id/hostProfileImage"
                        tools:text="\@surajpaln2011" />

                    <ImageView
                        android:id="@+id/speakerImage1"
                        android:layout_width="@dimen/dp45"
                        android:layout_height="@dimen/dp45"
                        android:layout_marginStart="@dimen/dp4"
                        android:layout_marginTop="@dimen/dp3"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_user_profile"
                        android:visibility="gone"
                        app:layout_constraintStart_toEndOf="@id/guideLine5"
                        app:layout_constraintTop_toTopOf="@id/hostProfileImage" />

                    <ImageView
                        android:id="@+id/speakerImage2"
                        android:layout_width="@dimen/dp45"
                        android:layout_height="@dimen/dp45"
                        android:layout_marginEnd="@dimen/dp4"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_user_profile"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/hostProfileImage"
                        app:layout_constraintCircle="@id/speakerImage1"
                        app:layout_constraintCircleAngle="120"
                        app:layout_constraintCircleRadius="@dimen/dp30"
                        app:layout_constraintEnd_toEndOf="parent"
                        tools:ignore="MissingConstraints" />

                    <ImageView
                        android:id="@+id/speakerImageBig"
                        android:layout_width="@dimen/dp60"
                        android:layout_height="@dimen/dp60"
                        android:layout_marginBottom="@dimen/dp4"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/ic_user_profile"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/guideLine5"
                        app:layout_constraintTop_toTopOf="@id/hostProfileImage" />

                    <TextView
                        android:id="@+id/totalSpeakers"
                        android:layout_width="@dimen/size_0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp4"
                        android:gravity="center"
                        android:maxLines="2"
                        android:textColor="@color/white"
                        android:textSize="@dimen/sp10"
                        app:layout_constraintEnd_toEndOf="@id/speakerImage2"
                        app:layout_constraintStart_toStartOf="@id/speakerImage1"
                        app:layout_constraintTop_toBottomOf="@id/speakerImage2"
                        tools:text="+ 8 Orang" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </noice.app.views.SquareCardView>
        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/actionLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:paddingStart="@dimen/dp16"
            android:paddingEnd="@dimen/dp16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imageLayout">

            <TextView
                android:id="@+id/filterBy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp6"
                android:background="@drawable/bg_label_content_highlight"
                android:fontFamily="@font/readex_pro"
                android:lineSpacingExtra="0sp"
                android:paddingStart="@dimen/dp8"
                android:paddingTop="@dimen/dp6"
                android:paddingEnd="@dimen/dp8"
                android:paddingBottom="@dimen/dp6"
                android:textColor="@color/black"
                android:textSize="@dimen/sp10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Suraj" />

            <TextView
                android:id="@+id/title"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginEnd="@dimen/dp14"
                android:ellipsize="end"
                android:fontFamily="@font/readix_pro_bold"
                android:lineSpacingExtra="7.2sp"
                android:maxLines="1"
                android:includeFontPadding="false"
                android:textColor="@color/dull_white"
                android:textSize="@dimen/sp12"
                android:textStyle="bold"
                app:layout_constraintEnd_toStartOf="@id/playButton"
                app:layout_constraintStart_toStartOf="@id/filterBy"
                app:layout_constraintTop_toBottomOf="@id/filterBy"
                tools:text="Kembalikan Kedaulatan Bapak Bapak!" />

            <TextView
                android:id="@+id/subTitle"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginEnd="@dimen/dp14"
                android:ellipsize="end"
                android:fontFamily="@font/readex_pro"
                android:maxLines="1"
                android:includeFontPadding="false"
                android:textColor="@color/neutral_80"
                android:textSize="@dimen/sp12"
                app:layout_constraintEnd_toStartOf="@id/playButton"
                app:layout_constraintStart_toStartOf="@id/filterBy"
                app:layout_constraintTop_toBottomOf="@id/title"
                tools:text="Suraj asdfsafasfsadfdfasdfasdfdasfdfsdafdasfsfasdfsadf" />

            <TextView
                android:id="@+id/type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:layout_marginBottom="@dimen/dp16"
                android:ellipsize="end"
                android:fontFamily="@font/readex_pro"
                android:maxLines="1"
                android:includeFontPadding="false"
                android:textColor="@color/neutral_80"
                android:textSize="@dimen/sp12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/title"
                app:layout_constraintStart_toStartOf="@id/filterBy"
                app:layout_constraintTop_toBottomOf="@id/subTitle"
                tools:text="Type" />

            <ImageView
                android:id="@+id/playButton"
                android:layout_width="@dimen/dp40"
                android:layout_height="@dimen/dp40"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_play_black"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/title"
                app:layout_constraintTop_toTopOf="parent" />

            <ProgressBar
                android:id="@+id/loader"
                android:layout_width="@dimen/dp44"
                android:layout_height="@dimen/dp44"
                android:indeterminateTint="@color/dull_yellow"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/playButton"
                app:layout_constraintEnd_toEndOf="@id/playButton"
                app:layout_constraintStart_toStartOf="@id/playButton"
                app:layout_constraintTop_toTopOf="@id/playButton" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
