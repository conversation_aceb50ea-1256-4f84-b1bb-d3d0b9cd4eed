<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/v1"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp8"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/v2"/>

    <View
        android:id="@+id/v3"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v1"/>

    <View
        android:id="@+id/v4"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v3"/>

    <View
        android:id="@+id/v5"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v4"/>

    <ImageView
        android:id="@+id/v2"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:layout_marginStart="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toEndOf="@id/v1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/v1"/>

    <View
        android:id="@+id/v6"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v2"/>

    <View
        android:id="@+id/v7"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v6"/>

    <View
        android:id="@+id/v8"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v7"/>

    <ImageView
        android:id="@+id/v9"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:layout_marginTop="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v8"
        app:layout_constraintEnd_toEndOf="@id/v1"/>

    <View
        android:id="@+id/v10"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v9"/>

    <View
        android:id="@+id/v11"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v10"/>

    <View
        android:id="@+id/v12"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v11"/>

    <ImageView
        android:id="@+id/v13"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v9"/>

    <View
        android:id="@+id/v14"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v13"/>

    <View
        android:id="@+id/v15"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v14"/>

    <View
        android:id="@+id/v16"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v15"/>

    <ImageView
        android:id="@+id/v17"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:layout_marginTop="@dimen/dp16"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v16"
        app:layout_constraintEnd_toEndOf="@id/v1"/>

    <View
        android:id="@+id/v18"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v17"/>

    <View
        android:id="@+id/v19"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v18"/>

    <View
        android:id="@+id/v20"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v1"
        app:layout_constraintTop_toBottomOf="@id/v19"/>

    <ImageView
        android:id="@+id/v21"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/dp208"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_skeleton_square_8dp"
        android:contentDescription="@string/app_name"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintEnd_toEndOf="@id/v2"
        app:layout_constraintTop_toTopOf="@id/v17"/>

    <View
        android:id="@+id/v22"
        android:layout_width="@dimen/dp112"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v21"/>

    <View
        android:id="@+id/v23"
        android:layout_width="@dimen/dp81"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v22"/>

    <View
        android:id="@+id/v24"
        android:layout_width="@dimen/dp104"
        android:layout_height="@dimen/dp8"
        android:layout_marginTop="@dimen/dp8"
        android:background="@drawable/background_skeleton_8dp"
        app:layout_constraintStart_toStartOf="@id/v2"
        app:layout_constraintTop_toBottomOf="@id/v23"/>
</androidx.constraintlayout.widget.ConstraintLayout>