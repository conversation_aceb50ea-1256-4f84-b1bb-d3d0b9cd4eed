<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp10"
        android:paddingBottom="@dimen/dp10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/imgLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp16"
            android:adjustViewBounds="true"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_noice_home_logo_yellow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/icNotification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp16"
            android:adjustViewBounds="true"
            android:background="?selectableItemBackgroundBorderless"
            android:contentDescription="@string/app_name"
            android:padding="@dimen/dp3"
            android:src="@drawable/ic_notification"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/newNotifications"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp4"
            android:layout_marginEnd="@dimen/dp4"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_red_circle"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="@id/icNotification"
            app:layout_constraintTop_toTopOf="@id/icNotification" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="@dimen/size_0dp"
        android:layout_height="@dimen/size_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/constraint_toolbar">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/forYouRecycler"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <noice.app.views.ErrorView
                android:id="@+id/errorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <FrameLayout
        android:id="@+id/homeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false" />
</androidx.constraintlayout.widget.ConstraintLayout>

