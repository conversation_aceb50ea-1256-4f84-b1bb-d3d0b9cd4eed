<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:width="300dp"
            android:height="300dp"
            android:viewportWidth="800"
            android:viewportHeight="800">
            <group android:name="_R_G">
                <group
                    android:name="_R_G_L_3_G"
                    android:translateX="384"
                    android:translateY="394">
                    <path
                        android:name="_R_G_L_3_G_D_0_P_0"
                        android:pathData=" M-256 -320 C-256,-320 -256,332 -256,332 "
                        android:strokeWidth="104"
                        android:strokeAlpha="1"
                        android:strokeColor="#FAD810"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:trimPathStart="0.99"
                        android:trimPathEnd="1"
                        android:trimPathOffset="0" />
                </group>
                <group
                    android:name="_R_G_L_2_G"
                    android:translateX="566.6669999999999"
                    android:translateY="394">
                    <path
                        android:name="_R_G_L_2_G_D_0_P_0"
                        android:pathData=" M-256 -320 C-256,-320 -256,332 -256,332 "
                        android:strokeWidth="104"
                        android:strokeAlpha="1"
                        android:strokeColor="#FAD810"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:trimPathStart="0"
                        android:trimPathEnd="1"
                        android:trimPathOffset="0" />
                </group>
                <group
                    android:name="_R_G_L_1_G"
                    android:translateX="749.3330000000001"
                    android:translateY="394">
                    <path
                        android:name="_R_G_L_1_G_D_0_P_0"
                        android:pathData=" M-256 -320 C-256,-320 -256,332 -256,332 "
                        android:strokeWidth="104"
                        android:strokeAlpha="1"
                        android:strokeColor="#FAD810"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:trimPathStart="0.5"
                        android:trimPathEnd="1"
                        android:trimPathOffset="0" />
                </group>
                <group
                    android:name="_R_G_L_0_G"
                    android:translateX="932"
                    android:translateY="394">
                    <path
                        android:name="_R_G_L_0_G_D_0_P_0"
                        android:pathData=" M-256 -320 C-256,-320 -256,332 -256,332 "
                        android:strokeWidth="104"
                        android:strokeAlpha="1"
                        android:strokeColor="#FAD810"
                        android:strokeLineCap="round"
                        android:strokeLineJoin="round"
                        android:trimPathStart="0.99"
                        android:trimPathEnd="1"
                        android:trimPathOffset="0" />
                </group>
            </group>
            <group android:name="time_group" />
        </vector>
    </aapt:attr>
    <target android:name="_R_G_L_3_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="0"
                    android:valueFrom="0.99"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="133"
                    android:valueFrom="0.25"
                    android:valueTo="0.7000000000000001"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="300"
                    android:valueFrom="0.7000000000000001"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="467"
                    android:valueFrom="0.25"
                    android:valueTo="0.79"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="601"
                    android:valueFrom="0.79"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="734"
                    android:valueFrom="0"
                    android:valueTo="0.77"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="901"
                    android:valueFrom="0.77"
                    android:valueTo="0.98"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1001"
                    android:valueFrom="0.98"
                    android:valueTo="0.67"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="trimPathStart"
                    android:startOffset="1134"
                    android:valueFrom="0.67"
                    android:valueTo="0.7000000000000001"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1201"
                    android:valueFrom="0.7000000000000001"
                    android:valueTo="0.44"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1301"
                    android:valueFrom="0.44"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1468"
                    android:valueFrom="0"
                    android:valueTo="0.38"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1635"
                    android:valueFrom="0.38"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="300"
                    android:propertyName="trimPathStart"
                    android:startOffset="1735"
                    android:valueFrom="0"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2035"
                    android:valueFrom="0.99"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="33"
                    android:propertyName="trimPathStart"
                    android:startOffset="2169"
                    android:valueFrom="0.5"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2202"
                    android:valueFrom="0.5"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="2336"
                    android:valueFrom="0.25"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="2536"
                    android:valueFrom="0.75"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="267"
                    android:propertyName="trimPathStart"
                    android:startOffset="2736"
                    android:valueFrom="0.5"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="167"
                    android:valueFrom="0.5"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="300"
                    android:valueFrom="0.25"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="501"
                    android:valueFrom="0.75"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="634"
                    android:valueFrom="0"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="767"
                    android:valueFrom="0.75"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="901"
                    android:valueFrom="0.99"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1001"
                    android:valueFrom="0.75"
                    android:valueTo="0.9"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1101"
                    android:valueFrom="0.9"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1235"
                    android:valueFrom="0.25"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1401"
                    android:valueFrom="0.75"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1535"
                    android:valueFrom="0.5"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1668"
                    android:valueFrom="0.75"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="1835"
                    android:valueFrom="0"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2035"
                    android:valueFrom="0.75"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2135"
                    android:valueFrom="0.5"
                    android:valueTo="0.8"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2269"
                    android:valueFrom="0.8"
                    android:valueTo="0.35000000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2402"
                    android:valueFrom="0.35000000000000003"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2536"
                    android:valueFrom="0.75"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2636"
                    android:valueFrom="0.5"
                    android:valueTo="0.65"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2736"
                    android:valueFrom="0.65"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="2836"
                    android:valueFrom="0.25"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="0"
                    android:valueFrom="0.5"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="167"
                    android:valueFrom="0.25"
                    android:valueTo="0.65"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="267"
                    android:valueFrom="0.65"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="367"
                    android:valueFrom="0.5"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="467"
                    android:valueFrom="0.75"
                    android:valueTo="0.35000000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="601"
                    android:valueFrom="0.35000000000000003"
                    android:valueTo="0.8"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="734"
                    android:valueFrom="0.8"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="868"
                    android:valueFrom="0.5"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="968"
                    android:valueFrom="0.75"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1168"
                    android:valueFrom="0"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1335"
                    android:valueFrom="0.75"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1468"
                    android:valueFrom="0.5"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1602"
                    android:valueFrom="0.75"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1768"
                    android:valueFrom="0.25"
                    android:valueTo="0.9"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1902"
                    android:valueFrom="0.9"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2002"
                    android:valueFrom="0.75"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2102"
                    android:valueFrom="0.99"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2236"
                    android:valueFrom="0.75"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2369"
                    android:valueFrom="0"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="2503"
                    android:valueFrom="0.75"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2703"
                    android:valueFrom="0.25"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="2836"
                    android:valueFrom="0.5"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="267"
                    android:propertyName="trimPathStart"
                    android:startOffset="0"
                    android:valueFrom="0.99"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="267"
                    android:valueFrom="0.5"
                    android:valueTo="0.75"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="200"
                    android:propertyName="trimPathStart"
                    android:startOffset="467"
                    android:valueFrom="0.75"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="667"
                    android:valueFrom="0.25"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="33"
                    android:propertyName="trimPathStart"
                    android:startOffset="801"
                    android:valueFrom="0.5"
                    android:valueTo="0.5"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="834"
                    android:valueFrom="0.5"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="300"
                    android:propertyName="trimPathStart"
                    android:startOffset="968"
                    android:valueFrom="0.99"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1268"
                    android:valueFrom="0"
                    android:valueTo="0.38"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1368"
                    android:valueFrom="0.38"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="1535"
                    android:valueFrom="0"
                    android:valueTo="0.44"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="1702"
                    android:valueFrom="0.44"
                    android:valueTo="0.7000000000000001"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="trimPathStart"
                    android:startOffset="1802"
                    android:valueFrom="0.7000000000000001"
                    android:valueTo="0.67"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="1869"
                    android:valueFrom="0.67"
                    android:valueTo="0.98"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="100"
                    android:propertyName="trimPathStart"
                    android:startOffset="2002"
                    android:valueFrom="0.98"
                    android:valueTo="0.77"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="2102"
                    android:valueFrom="0.77"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2269"
                    android:valueFrom="0"
                    android:valueTo="0.79"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2402"
                    android:valueFrom="0.79"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="2536"
                    android:valueFrom="0.25"
                    android:valueTo="0.7000000000000001"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="167"
                    android:propertyName="trimPathStart"
                    android:startOffset="2703"
                    android:valueFrom="0.7000000000000001"
                    android:valueTo="0.25"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="133"
                    android:propertyName="trimPathStart"
                    android:startOffset="2870"
                    android:valueFrom="0.25"
                    android:valueTo="0.99"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="3003"
                    android:propertyName="translateX"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
</animated-vector>