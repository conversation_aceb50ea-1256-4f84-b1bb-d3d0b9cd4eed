package noice.app

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.CountDownTimer
import android.util.Base64
import android.util.Log
import androidx.annotation.OptIn
import androidx.appcompat.AppCompatViewHelper
import androidx.appcompat.view.PKeys
import androidx.media3.common.util.UnstableApi
import androidx.multidex.MultiDexApplication
import androidx.room.Room
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.appsflyer.share.LinkGenerator
import com.appsflyer.share.ShareInviteHelper
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.scheduler.Requirements
import com.google.android.gms.ads.MobileAds
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.crashlytics.ktx.setCustomKeys
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.ktx.Firebase
import com.google.firebase.ktx.app
import com.google.firebase.ktx.initialize
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.moengage.core.MoEngage
import com.moengage.core.analytics.MoEAnalyticsHelper
import com.moengage.core.config.FcmConfig
import com.moengage.core.config.NotificationConfig
import com.moengage.core.model.AppStatus
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import noice.app.data.DataController
import noice.app.di.CommonEntryPoint
import noice.app.exoplayer.DownloadQueue
import noice.app.exoplayer.ExoPlayerDownloadTracker
import noice.app.exoplayer.ExoplayerUtils
import noice.app.listner.InAppClickListener
import noice.app.model.ExoNotificationData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.live.event.AppStateTrigger
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.util.LiveUtils
import noice.app.modules.onboarding.models.GuestTokenRequest
import noice.app.player.managers.QueueManager
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.ServerInterface
import noice.app.room.AppDatabase
import noice.app.room.DbMigrations
import noice.app.services.observer.NetworkDataObserver
import noice.app.utils.AnalyticsUtil
import noice.app.utils.AppSignatureHelper
import noice.app.utils.AppVisibilityDetector.AppVisibilityCallback
import noice.app.utils.AppVisibilityDetector.init
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.DATA_VERSION_OLD
import noice.app.utils.ExperimentUtils
import noice.app.utils.FacebookAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.workmanager.UpdateQueueWorker
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import org.json.JSONTokener
import retrofit2.Response
import java.nio.charset.StandardCharsets
import java.util.UUID
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit


@HiltAndroidApp
class BaseApplication : MultiDexApplication() {

    private lateinit var downloadTracker: ExoPlayerDownloadTracker

    @OptIn(UnstableApi::class)
    private lateinit var downloadManager: DownloadManager
    private lateinit var downloadQueue: DownloadQueue
    private lateinit var secondaryFireStoreApp: FirebaseApp

    @Volatile
    private lateinit var appDb: AppDatabase
    private var chatTimer: CountDownTimer? = null
    var appsFlyerDeepLinkIntent: Intent? = null
    var chatRemainingTime = 0L
    private var appsFlyerLinkGenerator: LinkGenerator? = null

    override fun onCreate() {
        super.onCreate()

        application = this
        AppCompatViewHelper.init()

        /*if (BuildConfig.DEBUG) {
            StrictMode.setVmPolicy(
                VmPolicy.Builder()
                    .detectAll()
                    .penaltyLog()
                    .build()
            )
        }*/

        /* initializing MoEngage SDK & parameters */
        setMoEngage()
        FacebookAnalytics.initialize(this)
        if (!Utils.isProductionRelease()) {
            NetworkDataObserver.withContext(this).start()
        }

        CoroutineScope(Dispatchers.IO).launch {
            firebaseAnalytics = FirebaseAnalytics.getInstance(getBaseAppContext())
            setAppsFlyer()

            init(this@BaseApplication, object : AppVisibilityCallback {
                override fun onAppGotoForeground() {
                    Log.d("visibility -", "app is from background to foreground")
                    DataController.extendSession = 0
                    setUniqueId()
                    LiveUtils.checkForPubNubToken()
                    AppStateTrigger.onAppForeground()
                }

                override fun onAppGotoBackground() {
                    Log.d("visibility -", "app is from foreground to background")
                    DataController.extendSession = 1
                    EventBus.getDefault().post(EventMessage(null, Constants.UPDATE_QUEUE))
                    AppStateTrigger.onAppBackground()
                }
            })

            if (!PrefUtils.referralUserId.isNullOrEmpty()) {
                firebaseAnalytics.setUserProperty("referrerUserId", PrefUtils.referralUserId)
            }

            if (PrefUtils.isLoggedIn) {
                AnalyticsUtil.setLoginProperties()
                Firebase.crashlytics.setCustomKeys {
                    key("userName", PrefUtils.userDetails?.userName.orEmpty())
                    key("displayName", PrefUtils.userDetails?.displayName.orEmpty())
                    key("userId", PrefUtils.userDetails?.id.orEmpty())
                }

                if (PrefUtils.appsFlyerId.isNullOrEmpty()) {
                    PrefUtils.appsFlyerId = AppsFlyerLib.getInstance().getAppsFlyerUID(application)
                }
            } else {
                AnalyticsUtil.setGuestProperties()
            }

            firebaseAnalytics.setUserProperty("sessionId", PrefUtils.uniqueId.toString())

            firebaseAnalytics.setUserProperty("fcmToken", PrefUtils.fcmToken ?: "")

            firebaseAnalytics.setAnalyticsCollectionEnabled(true)

            if (PrefUtils.isLoggedIn && (PrefUtils.userDetails?.id == "0f5a28a7-b311-4ba4-b2eb-0301bd4fe53c" ||
                        PrefUtils.userDetails?.userName == "palvaibhav89" ||
                        PrefUtils.userDetails?.userName == "atiqulalam")
            ) {
                Log.d(
                    "AppSignatureHelper - ",
                    AppSignatureHelper(this@BaseApplication).appSignatures.toString()
                )
            }

            DataController.fireStoreChat.collection("Timer").document("RadioUser")
                .get()
                .addOnCompleteListener {
                    if (it.isSuccessful) {
                        it.result?.getLong("interval")?.let { result ->
                            DataController.radioUpdateInterval = TimeUnit.SECONDS.toMillis(result)
                        }
                    }
                }


            /* initializing MoEngage SDK & parameters */

            DataController.useAdProxyServer = ExperimentUtils.useAdProxyServer()

            /* initializing Ad Manger SDK */
            MobileAds.initialize(this@BaseApplication)
            //MobileAds.setRequestConfiguration(RequestConfiguration.Builder().setTestDeviceIds(Arrays.asList("1466FF6931E92F5C923FD447882FCD0A")).build())
        }

        val crashlyticsExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { t, e ->
            e.printStackTrace()
            crashlyticsExceptionHandler?.uncaughtException(t, e)
        }
    }

    fun startChatTimer(chatWaitTime: Long) {
        if (chatTimer != null) {
            chatTimer?.cancel()
            chatTimer = null
        }

        chatTimer = object : CountDownTimer(chatWaitTime, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val sec = millisUntilFinished / 1000

                val seconds = Utils.formatLiveChatTimeLimit(sec, true)
                chatRemainingTime = sec
                EventBus.getDefault().post(EventMessage(seconds, Constants.CHAT_TIMER))
                LiveTrigger.onChatTimer(seconds)
            }

            override fun onFinish() {
                chatRemainingTime = 0L
                EventBus.getDefault().post(EventMessage("", Constants.CHAT_TIMER_FINISHED))
                LiveTrigger.onChatTimerFinished()
            }
        }.start()
    }

    fun stopChatTimer() {
        chatRemainingTime = 0L
        LiveTrigger.onChatTimerFinished()
        chatTimer?.cancel()
    }

    private fun setAppsFlyer() {
        AppsFlyerLib.getInstance().subscribeForDeepLink { deepLinkResult ->
            Utils.handleAppsFlyerDeepLinkData(deepLinkResult.deepLink.valueOf)
        }
        AppsFlyerLib.getInstance().waitForCustomerUserId(true)

        /**  Set Up Conversion Listener to get attribution data  */
        val conversionListener: AppsFlyerConversionListener = object : AppsFlyerConversionListener {
            /* Returns the attribution data. Note - the same conversion data is returned every time per install */
            override fun onConversionDataSuccess(conversionData: Map<String, Any>) {
                for (attrName in conversionData.keys) {
                    Log.d(
                        TAG,
                        "attribute: " + attrName + " = " + conversionData[attrName]
                    )
                }
                setInstallData(conversionData)
            }

            override fun onConversionDataFail(errorMessage: String) {
                Log.d(TAG, "error getting conversion data: $errorMessage")
            }

            /* Called only when a Deep Link is opened */
            override fun onAppOpenAttribution(conversionData: Map<String, String>) {
                for (attrName in conversionData.keys) {
                    Log.d(
                        TAG,
                        "attribute: " + attrName + " = " + conversionData[attrName]
                    )
                }
            }

            override fun onAttributionFailure(errorMessage: String) {
                Log.d(TAG, "error onAttributionFailure : $errorMessage")
            }
        }


        /* Set to true to see the debug logs. Comment out or set to false to stop the function */
        if (BuildConfig.DEBUG) {
            AppsFlyerLib.getInstance().setDebugLog(true)
        }

        if (PrefUtils.isLoggedIn) {
            AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.userDetails?.id)
            AppsFlyerLib.getInstance().setCustomerIdAndLogSession(PrefUtils.userDetails?.id, this)
            AppsFlyerLib.getInstance().setAdditionalData(HashMap<String, Any>().apply {
                put("userId", PrefUtils.userDetails?.id.toString())
                put("buildNumber", BuildConfig.VERSION_NAME)
            })
        } else if (!PrefUtils.guestId.isNullOrEmpty()) {
            AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.guestId)
            AppsFlyerLib.getInstance().setCustomerIdAndLogSession(PrefUtils.guestId, this)
            AppsFlyerLib.getInstance().setAdditionalData(HashMap<String, Any>().apply {
                put("userId", PrefUtils.guestId.toString())
                put("buildNumber", BuildConfig.VERSION_NAME)
            })
        }

        /* custom share links */
        AppsFlyerLib.getInstance()
            .setAppInviteOneLink("cyOg") //should be above - AppsFlyerLib.getInstance().start(this)
        appsFlyerLinkGenerator = ShareInviteHelper.generateInviteUrl(applicationContext)

        AppsFlyerLib.getInstance()
            .init(PKeys.appsflyer_api_key.toString(), conversionListener, this)
        AppsFlyerLib.getInstance().start(this)
        AppsFlyerLib.getInstance().setOneLinkCustomDomain("listen.noice.id")
    }


    fun getLinkGen() = appsFlyerLinkGenerator

    @Synchronized
    fun getAppDb(): AppDatabase {
        if (!::appDb.isInitialized) {
            val migrations = DbMigrations().getMigrations()
            appDb = Room
                .databaseBuilder(applicationContext, AppDatabase::class.java, "noice-db")
                .addMigrations(*migrations)
                .build()
        }
        return appDb
    }

    @Synchronized
    fun getDownloadQueue(): DownloadQueue {
        if (!::downloadQueue.isInitialized) {
            downloadQueue = DownloadQueue()
        }
        return downloadQueue
    }

    @OptIn(UnstableApi::class)
    @Synchronized
    fun getDownloadManager(): DownloadManager {
        ensureDownloadManagerInitialized()
        return downloadManager
    }

    @Synchronized
    fun getDownloadTracker(): ExoPlayerDownloadTracker {
        ensureDownloadManagerInitialized()
        return downloadTracker
    }

    fun getSecondaryFireStore(context: Context): FirebaseFirestore {
        if (!::secondaryFireStoreApp.isInitialized) {

            val ai = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            val bundle = ai.metaData

            val gcpProjectId = bundle.getString("gcp_project_id", "")
            val gcpAppId = bundle.getString("gcp_app_id", "")
            val gcpApiKey = bundle.getString("gcp_api_key", "")

            val options = FirebaseOptions.Builder()
                .setProjectId(gcpProjectId)
                .setApplicationId(gcpAppId)
                .setApiKey(gcpApiKey)
                .build()
            Firebase.initialize(context, options, "secondary")
            secondaryFireStoreApp = Firebase.app("secondary")
        }
        return FirebaseFirestore.getInstance(secondaryFireStoreApp).apply {
            val settings = FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(false)
                .build()
            firestoreSettings = settings
        }
    }

    @Synchronized
    @OptIn(UnstableApi::class)
    private fun ensureDownloadManagerInitialized() {
        if (!::downloadManager.isInitialized) {
            downloadManager = DownloadManager(
                applicationContext,
                ExoplayerUtils.getDatabaseProvider(),
                ExoplayerUtils.getDownloadCache(),
                ExoplayerUtils.getHttpDataSourceFactory(),
                Executors.newFixedThreadPool(6)
            )
            downloadManager.requirements = Requirements(Requirements.NETWORK)
            downloadManager.maxParallelDownloads = 1

            downloadTracker = ExoPlayerDownloadTracker(applicationContext, downloadManager)
        }
    }

    companion object {

        lateinit var firebaseAnalytics: FirebaseAnalytics
        lateinit var application: BaseApplication
        private val TAG = BaseApplication::class.simpleName
        val commonEntryPoint by lazy {
            EntryPointAccessors.fromApplication(
                application.applicationContext,
                CommonEntryPoint::class.java
            )
        }

        fun getSecondaryFireStore() =
            application.getSecondaryFireStore(application.applicationContext)


        fun getBaseAppContext(): Context = application.applicationContext

        private fun <T> getCoroutineExceptionHandler(serverInterface: ServerInterface<T>?): CoroutineExceptionHandler {
            return CoroutineExceptionHandler { _, exception ->
                serverInterface?.onError(exception)
            }
        }

        fun <T> doServerCall(
            method: suspend () -> Response<T>,
            serverInterface: ServerInterface<T>?
        ): Job? {

            if (!NetworkUtils.isNetworkConnected(getBaseAppContext())) {
                serverInterface?.onCustomError(ApiError.getConnectionError())
                return null
            }

            return CoroutineScope(Dispatchers.Main + getCoroutineExceptionHandler(serverInterface)).launch(
                getCoroutineExceptionHandler(serverInterface)
            ) {

                if (PrefUtils.token.isNullOrEmpty()) {
                    if (PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) {

                        val jwt = Utils.getJwtToken()
                        val tokenResponse = invokeApi({
                            NetworkRequests.getGuestToken(
                                GuestTokenRequest(
                                    jwt,
                                    "dasd",
                                    PrefUtils.fcmToken,
                                    Utils.getDeviceId(),
                                    Utils.getDeviceIdOld(),
                                    Utils.getAppInstalledTime(getBaseAppContext())
                                )
                            )
                        }, null)
                        if (tokenResponse.isSuccessful && !tokenResponse.body()?.data?.accessToken.isNullOrEmpty() &&
                            tokenResponse.body()?.data?.accessToken?.contains(".") == true
                        ) {

                            PrefUtils.token = tokenResponse.body()?.data?.accessToken.toString()
                            PrefUtils.dataVersion = DATA_VERSION_OLD
                            PrefUtils.isNewAcquiredUser =
                                tokenResponse.body()?.data?.data?.isOnboardingComplete == false

                            val getId: String = PrefUtils.token.toString().split(".")[1]
                            val data: ByteArray = Base64.decode(getId, Base64.DEFAULT)
                            val text = String(data, StandardCharsets.UTF_8)

                            val json = JSONTokener(text).nextValue() as JSONObject
                            if (json.has("id"))
                                PrefUtils.guestId = json.getString("id")

                            AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.guestId)

                            AppsFlyerLib.getInstance()
                                .setAdditionalData(HashMap<String, Any>().apply {
                                    put("userId", PrefUtils.guestId.toString())
                                    put("buildNumber", BuildConfig.VERSION_NAME)
                                })

                            firebaseAnalytics.setUserProperty(
                                "guest_user_id",
                                PrefUtils.guestId ?: ""
                            )
                            firebaseAnalytics.setUserProperty("device_id", Utils.getDeviceId())
                            firebaseAnalytics.setUserId(PrefUtils.guestId)
                            firebaseAnalytics.setUserProperty("appUserType", "guest")

                            AppsFlyerLib.getInstance()
                                .setCustomerIdAndLogSession(PrefUtils.guestId, application)

                            val response = invokeApi(method, serverInterface)
                            handleMainResponse(response, serverInterface)
                        } else {
                            serverInterface?.onCustomError(ApiError.getTokenError(tokenResponse.message()))
                        }
                    } else {
                        PrefUtils.logout(baseContext = getBaseAppContext())
                    }
                } else {
                    val response = invokeApi(method, serverInterface)
                    handleMainResponse(response, serverInterface)
                }
            }
        }

        private fun <T> handleMainResponse(
            response: Response<T>,
            serverInterface: ServerInterface<T>?
        ) {
            val responseBody = response.body()
            if (response.isSuccessful && responseBody != null) {
                serverInterface?.onSuccess(responseBody, false)
            } else if (!response.isSuccessful && response.errorBody() != null) {
                handleError(response, serverInterface)
            }
        }

        fun setUniqueId() {
            val pseudoId =
                Build.BOARD.length % 10 + Build.BRAND.length % 10 + Build.DEVICE.length % 10 + Build.DISPLAY.length % 10 + Build.HOST.length % 10 + Build.ID.length % 10 + Build.MANUFACTURER.length % 10 + Build.MODEL.length % 10 + Build.PRODUCT.length % 10 + Build.TAGS.length % 10 + Build.TYPE.length + Build.USER.length % 10
            val uniqueId: String =
                UUID.randomUUID().toString() + System.currentTimeMillis().toString().plus(
                    pseudoId
                )
            PrefUtils.uniqueId = uniqueId
        }

        private suspend fun <T> invokeApi(
            method: suspend () -> Response<T>,
            serverInterface: ServerInterface<T>?
        ) = withContext(
            Dispatchers.IO + getCoroutineExceptionHandler(serverInterface)
        ) {
            return@withContext method.invoke()
        }

        private fun <T> handleError(
            response: Response<T>,
            serverInterface: ServerInterface<T>?
        ) {

            if (response.code() != 500) {
                try {
                    serverInterface?.onCustomError(
                        ApiError(
                            response.message().toString(),
                            response.code(),
                            response.body().toString()
                        )
                    )
                } catch (e: Exception) {
                    serverInterface?.onCustomError(
                        ApiError(
                            getBaseAppContext().resources?.getString(R.string.general_error) ?: "",
                            response.code(),
                            response.body()?.toString()
                        )
                    )
                }
            } else {
                serverInterface?.onCustomError(
                    ApiError(
                        getBaseAppContext().resources?.getString(R.string.general_error) ?: "",
                        response.code(),
                        response.body()?.toString()
                    )
                )
            }
        }

        @Synchronized
        fun fetchAndReplaceQueue() {
            commonEntryPoint.onBoardingApiRepository.fetchAndReplaceQueue()
        }

        @Synchronized
        fun updateLocalQueue() {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val listType = object : TypeToken<List<ExoNotificationData>?>() {}.type
                    PrefUtils.queueJson = Gson().toJson(QueueManager.queue, listType)
                } catch (e: ConcurrentModificationException) {
                    e.printStackTrace()
                }
            }
        }

        /**
         *  This method should not be called directly. Call via BasePlayerActivity EventBus [Constants.UPDATE_QUEUE]
         *  Will update on server side.
         *  See [UpdateQueueWorker],
         */
        fun updateQueue() {
            //user should be register as guest or user and
            //queue list should not be empty
            if (!(PrefUtils.isLoggedIn || PrefUtils.isLoggedInAsGuest) || QueueManager.queue.isEmpty()) return

            updateLocalQueue()

            // starting work manager to update queue on
            // local storage (preferences) as well as on server side
            UpdateQueueWorker.beginWork()
        }
    }

    /* IGNORE - USED TO DISPLAY INSTALL DATA */
    private var installConversionData = ""
    private var sessionCount = 0
    fun setInstallData(conversionData: Map<String, Any>) {
        if (sessionCount == 0) {
            val installType = """
            Install Type: ${conversionData["af_status"]}
            
            """.trimIndent()
            val mediaSource = """
            Media Source: ${conversionData["media_source"]}
            
            """.trimIndent()
            val installTime = """
            Install Time(GMT): ${conversionData["install_time"]}
            
            """.trimIndent()
            val clickTime = """
            Click Time(GMT): ${conversionData["click_time"]}
            
            """.trimIndent()
            val isFirstLaunch = """
            Is First Launch: ${conversionData["is_first_launch"]}
            
            """.trimIndent()
            installConversionData += installType + mediaSource + installTime + clickTime + isFirstLaunch
            sessionCount++
        }
    }

    /* setting-up MoEngage SDK */
    private fun setMoEngage() {

        val monEngage = MoEngage.Builder(this, PKeys.moengage_app_id.toString())
            .configureNotificationMetaData(
                NotificationConfig(
                    R.drawable.ic_noice_logo_notification,
                    R.mipmap.ic_launcher,
                    R.color.white,
                    isMultipleNotificationInDrawerEnabled = true,
                    isBuildingBackStackEnabled = true,
                    isLargeIconDisplayEnabled = true
                )
            )
            .configureFcm(FcmConfig(false))
            .build()

        MoEngage.initialiseDefaultInstance(monEngage)

        MoEInAppHelper.getInstance().setClickActionListener(InAppClickListener(applicationContext))

        trackInstallOrUpdate()
    }

    /**
     * Tell MoEngage SDK whether the user is a new user of the application or an existing user.
     */
    private fun trackInstallOrUpdate() {
        val preferences = getSharedPreferences("noice-moengage-prefs", 0)
        var appStatus = AppStatus.INSTALL

        if (!preferences.getBoolean("has_sent_install", false)) {
            MoEAnalyticsHelper.setAppStatus(applicationContext, appStatus)
            preferences.edit().putBoolean("has_sent_install", true).apply()
            preferences.edit().putInt("app_version_code", BuildConfig.VERSION_CODE).apply()
            preferences.edit().putBoolean("existing", true).apply()
        } else {
            if (preferences.getBoolean("existing", false)) {
                if (preferences.getInt(
                        "app_version_code",
                        BuildConfig.VERSION_CODE
                    ) < BuildConfig.VERSION_CODE
                ) {
                    appStatus = AppStatus.UPDATE
                    MoEAnalyticsHelper.setAppStatus(applicationContext, appStatus)
                }
            }
        }
    }

}