package noice.app.player.managers

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.OptIn
import com.appsflyer.AppsFlyerLib
import androidx.media3.common.PlaybackException
import androidx.media3.common.util.UnstableApi
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.SetOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.data.DataController
import noice.app.exoplayer.BasePlayerActivity
import noice.app.exoplayer.ExoplayerUtils.isContentDownloaded
import noice.app.exoplayer.ExoplayerUtils.mediaDurationMs
import noice.app.exoplayer.PlayerDetailFragment.Companion.PORTRAIT
import noice.app.exoplayer.cast.CastManager
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.model.eventbus.PostAction
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.home.fragment.SleepTimeDialog
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.model.Payload
import noice.app.modules.podcast.repository.ChannelPodcastApiRepository
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlaybackLatency
import noice.app.player.models.PlayerEvent
import noice.app.player.models.SeekData
import noice.app.player.playrequests.PlayRequest
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.ServerInterface
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.EventConstant
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.isTrue
import noice.app.utils.putAnalyticsKey
import java.util.Timer
import java.util.TimerTask

class PlayerAnalyticsManager {

    companion object {
        private val EVENT_LIST = listOf(PlayerEvent.STATE_READY_PLAY, PlayerEvent.STATE_READY_PAUSE, PlayerEvent.PLAYER_ERROR, PlayerEvent.SHOW_LOADER, PlayerEvent.END_LOADER, PlayerEvent.SEEK, PlayerEvent.SLEEP_TIMER, PlayerEvent.ON_NEXT, PlayerEvent.ON_QUEUE_ENDED, PlayerEvent.ON_MEDIA_CHANGED)
    }

    var totalTimeElapsed = 0 // seconds
    private var sessionTimeElapsed = 0L // milliseconds
    private val playerEventJob: Job
    private var sleepTime = SleepTimeDialog.SLEEP_TIME_0
    private var timer: Timer? = null
    private val repository = ChannelPodcastApiRepository()
    private var playbackLatency = PlaybackLatency()
    private var lastPlayerEvent = ""

    init {
        playerEventJob = CoroutineScope(Dispatchers.IO).launch {
            GlobalObservers.playerEventObserver
                .subscribePlayerEvents(EVENT_LIST) { playerEvent ->
                    onPlayerEvent(playerEvent)
                }
        }
    }

    @OptIn(UnstableApi::class)
    fun onPlayerEvent(playerEvent: PlayerEvent) {
        val currentData = playerEvent.exoData
        val currentContentPositionMs = playerEvent.currentContentPositionMs ?: 0

        when (playerEvent.event) {
            PlayerEvent.PLAYER_ERROR -> {
                handlePlayerError(playerEvent.error, playerEvent.exoData, currentContentPositionMs)
            }
            PlayerEvent.STATE_READY_PLAY -> {
                if (DataController.isAdPlaying) {
                    lastPlayerEvent = playerEvent.event
                    return
                }
                setSessionTimeStartMs(currentData, currentContentPositionMs)
                if (currentData?.isRadio == true) {
                    if (DataController.isPlaying || totalTimeElapsed > 0) {
                        updateRadioCounter(1, currentData, 0)
                    }
                } else {
                    sendPlayEventForPodcast(
                        currentData = currentData,
                        currentPosMs = currentContentPositionMs,
                        totalMs = playerEvent.contentDurationMs ?: 0L
                    )
                }
            }
            PlayerEvent.STATE_READY_PAUSE -> {
                if (DataController.isAdPlaying) {
                    lastPlayerEvent = playerEvent.event
                    return
                }
                val elapsedTimeMs = getMediaSessionTimeMs(currentData, currentContentPositionMs)
                if (lastPlayerEvent != PlayerEvent.STATE_READY_PAUSE) {
                    //For handle situations in which events like Notification pause click event is triggered 2 times
                    totalTimeElapsed += elapsedTimeMs.msToSeconds()
                }

                if (playerEvent.reason != PlayRequest.REASON_MEDIA_BUTTON_CLICK && playerEvent.reason != PlayRequest.REASON_SWITCH_CONTENT_TYPE) {
                    return
                }

                if (currentData?.isRadio == true) {
                    updateRadioCounter(-1, currentData, elapsedTimeMs)
                } else {
                    sendPauseEventForContent(
                        currentData = currentData,
                        totalDuration = playerEvent.contentDurationMs ?: 0L,
                        elapsedTime = elapsedTimeMs,
                        contentPosition = playerEvent.currentContentPositionMs ?: 0L
                    )
                }
            }
            PlayerEvent.SHOW_LOADER -> {
                playbackLatency.start(currentData)
                sendBufferEvent("buffer_start", currentData, currentContentPositionMs)
            }
            PlayerEvent.END_LOADER -> {
                playbackLatency.checkAndSendEvent()
                sendBufferEvent("buffer_completed", currentData, currentContentPositionMs)
            }
            PlayerEvent.SEEK -> {
                val seekData = playerEvent.genericData as SeekData

                if (seekData.reason == PlayerManager.SEEK_REASON_CLICK) {
                    val action = if (seekData.forward) {
                        sessionTimeElapsed += seekData.seekTimeMs
                        "skip_10s"
                    } else {
                        sessionTimeElapsed -= seekData.seekTimeMs
                        "rewind_10s"
                    }
                    sendPlayerAnalytics(
                        action = action,
                        currentData = currentData,
                        currentPos = playerEvent.currentContentPositionMs ?: 0L,
                        total = playerEvent.contentDurationMs ?: 0L
                    )
                } else if (seekData.reason == PlayerManager.SEEK_REASON_SLIDE) {
                    if (seekData.forward) {
                        sessionTimeElapsed += seekData.seekTimeMs
                    } else {
                        sessionTimeElapsed -= seekData.seekTimeMs
                    }
                    sendPlayerAnalytics(
                        action = "slider_adjusted",
                        currentData = currentData,
                        currentPos = playerEvent.currentContentPositionMs ?: 0L,
                        total = playerEvent.contentDurationMs ?: 0L
                    )
                }
            }
            PlayerEvent.SLEEP_TIMER -> {
                val sleepTimeEvent = playerEvent.genericData as Int
                if (sleepTimeEvent == SleepTimeDialog.SLEEP_TIME_0) {
                    val elapsedTimeMs = getMediaSessionTimeMs(currentData, currentContentPositionMs)
                    sendPauseEventForContent(
                        currentData = currentData,
                        totalDuration = playerEvent.contentDurationMs ?: 0L,
                        elapsedTime = elapsedTimeMs,
                        contentPosition = playerEvent.currentContentPositionMs ?: 0L,
                        trigger = "sleep timer", reason = "sleep timer"
                    )
                }
                sleepTime = sleepTimeEvent
            }
            PlayerEvent.ON_NEXT -> {
                if (playerEvent.reason == PlayerManager.NEXT_REASON_START_SERVICE) {
                    lastPlayerEvent = playerEvent.event
                    return
                }
                setSessionTimeStartMs(currentData, currentContentPositionMs)
                totalTimeElapsed = 0
                if (DataController.isAdPlaying){
                    lastPlayerEvent = playerEvent.event
                    return
                }
                //New Item
                if (currentData?.isRadio == true) {
                    updateRadioCounter(1, currentData, 0)
                } else {
                    sendPlayEventForPodcast(
                        currentData = currentData,
                        currentPosMs = currentContentPositionMs,
                        totalMs = playerEvent.contentDurationMs ?: 0L
                    )
                }
            }
            PlayerEvent.ON_QUEUE_ENDED -> {
                val elapsedTimeMs = getMediaSessionTimeMs(currentData, currentContentPositionMs)
                sendFinishEvent(currentData,
                    playerEvent.contentDurationMs ?: ((currentData?.duration ?: 0) * 1000), elapsedTimeMs)
                totalTimeElapsed = 0
            }
            PlayerEvent.ON_MEDIA_CHANGED -> {
                //Previous Item
                val elapsedTimeMs = getMediaSessionTimeMs(currentData, currentContentPositionMs)
                if (currentData?.isRadio == true && lastPlayerEvent != PlayerEvent.STATE_READY_PAUSE) {
                    updateRadioCounter(-1, currentData, elapsedTimeMs)
                } else {
                    if (playerEvent.currentContentPositionMs == playerEvent.contentDurationMs){
                        sendFinishEvent(currentData,
                            playerEvent.contentDurationMs ?: ((currentData?.duration ?: 0) * 1000), elapsedTimeMs)
                    } else if (lastPlayerEvent != PlayerEvent.STATE_READY_PAUSE && lastPlayerEvent != PlayerEvent.END_LOADER) {
                        lastPlayerEvent = ""
                        sendPauseEventForContent(
                            currentData = currentData,
                            totalDuration = playerEvent.contentDurationMs ?: 0L,
                            elapsedTime = elapsedTimeMs,
                            contentPosition = playerEvent.currentContentPositionMs ?: 0L
                        )
                    }
                }
            }
        }

        lastPlayerEvent = playerEvent.event
    }

    fun updateElapsedTime(currentData: ExoNotificationData?, currentContentPositionMs: Long) {
        val elapsedTimeMs = getMediaSessionTimeMs(currentData, currentContentPositionMs)
        totalTimeElapsed += elapsedTimeMs.msToSeconds()
    }

    private fun sendBufferEvent(eventName: String, mediaData: ExoNotificationData?, elapsedTime: Long) {

        val streamType = if (isContentDownloaded(mediaData)) {
            "downloaded"
        } else {
            "streamed"
        }

        AnalyticsUtil.sendEvent(eventName, Bundle().apply {
            putAnalyticsKey("contentId", mediaData?.id)
            if (CastManager.isCastConnected){
                putAnalyticsKey("castPlatform","chromecast")
                putAnalyticsKey("devicePlatform",CastManager.castDevice?.name.orEmpty())
            } else {
                putAnalyticsKey("castPlatform","null")
            }
            putAnalyticsKey("contentTitle", mediaData?.title)
            putAnalyticsKey("contentDuration", mediaData.mediaDurationMs.msToSeconds())
            putAnalyticsKey("vertical", mediaData?.entitySubType)
            putAnalyticsKey("networkType", NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()))
            putAnalyticsKey("source", mediaData?.pageSource)
            putAnalyticsKey("catalogId", mediaData?.catalogId)
            putAnalyticsKey("catalogTitle", mediaData?.catalogTitle)
            putAnalyticsKey("catalogSource", mediaData?.source)
            putAnalyticsKey("elapsedTime", elapsedTime)
            putAnalyticsKey("streamType", streamType)
            putAnalyticsKey("contentFormat", if (DataController.isPlayingVideo) "video"  else "audio")
        })
    }

    private fun setSessionTimeStartMs(currentData: ExoNotificationData?, currentContentPositionMs: Long) {
        sessionTimeElapsed = if (currentData?.isRadio == true) {
            System.currentTimeMillis()
        } else {
            currentContentPositionMs
        }
    }

    fun getMediaSessionTimeMs(currentData: ExoNotificationData?, contentPosition: Long): Long {
        return if (currentData?.isRadio == true) {
            System.currentTimeMillis() - sessionTimeElapsed
        } else {
            contentPosition - sessionTimeElapsed
        }
    }

    private fun handlePlayerError(
        error: PlaybackException?,
        currentData: ExoNotificationData?,
        elapsedTime: Long = 0
    ) {

        val key = if (currentData?.isRadio == true) {
            "radio catalog id - " + currentData.catalogId.toString()
        } else {
            "content id - " + currentData?.id.toString()
        }

        val message = "$key, message : ${error?.message}, code : ${error?.errorCode}"
        sendPlayerAnalytics(
            "content_play_error",
            currentData = currentData,
            currentPos = elapsedTime,
            total = 0L,
            errorCode = error?.errorCode ?: 0,
            error = message
        )
    }

    fun isEOTSleepActivated() = sleepTime == SleepTimeDialog.END_OF_TRACK

    fun onDestroy() {
        playerEventJob.cancel()
    }

    fun onTaskRemoved(
        taskRemovedListener: () -> Unit,
        contentPosition: Long,
        contentDuration: Long,
    ) {
        MoEngageAnalytics.sendEvent(BaseApplication.getBaseAppContext(), "app closed")

        if (DataController.isAdPlaying){
            taskRemovedListener()
        } else {
            val data = QueueManager.getFirstMedia()
            val elapsedTimeMs = getMediaSessionTimeMs(data, contentPosition)
            if (data?.isRadio == true) {
                updateRadioCounter(-1, data, elapsedTimeMs) {
                    AnalyticsUtil.sendEventForRadio(
                        data.catalogId,
                        "radio_force_stopped",
                        data.catalogTitle ?: "",
                        data.id ?: "",
                        data.title ?: "",
                        "",
                        elapsedTimeMs.msToSeconds().toLong(),
                        extraData = data.extraData
                    )

                    Handler(Looper.getMainLooper()).postDelayed({
                        taskRemovedListener()
                    }, 2000)
                }
            } else {
                sendPauseEventForContent(data, contentDuration, elapsedTimeMs, contentPosition, taskRemovedListener, "quit app", "app closed")
            }
        }
    }

    private fun sendPlayEventForPodcast(
        currentData: ExoNotificationData?,
        currentPosMs: Long = 0,
        totalMs: Long = 0
    ) {

        val streamType = if (isContentDownloaded(currentData)) {
            "downloaded"
        } else {
            "streamed"
        }

        val episodeId = currentData?.id
        val entityType = currentData?.entityType ?: ""
        val entitySubType = currentData?.entitySubType
        val pageSource = currentData?.pageSource
        val catalogId = currentData?.catalogId ?: ""
        var extraData = currentData?.extraData

        if (entityType.isEmpty() || entitySubType.isNullOrEmpty())
            return

        AnalyticsUtil.sendEvent(
            entitySubType,
            episodeId,
            currentData.catalogId,
            "content_played",
            pageSource,
            (currentPosMs / 1000).toString(),
            (totalMs / 1000L).toString(),
            catalogTitle = currentData.catalogTitle ?: "",
            contentTitle = currentData.title ?: "",
            streamType = streamType,
            networkType = NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()),
            contentFormat = if (DataController.isPlayingVideo) {
                if (currentData.orientation == PORTRAIT) {
                    "vertical video"
                } else {
                    "video"
                }
            } else {
                "audio"
            },
            source = currentData.source ?: "",
            extraData = extraData,
            playButtonPosition = DataController.playButtonPosition,
            isAppInBackground = BasePlayerActivity.isAppInBackground,
            isPlayedPreview = currentData.isPurchaseNeeded.isTrue()
        )

        if (!DataController.isAdPlaying) {
            /* when content is played from player queue, this extraData object is null. */
            if (extraData == null) {
                extraData = ExtraData()
            }

            extraData.apply {
                contentLink = "${Constants.WEB_BASE_URL}catalog/$episodeId"
                catalogLink = "${Constants.WEB_BASE_URL}catalog/$catalogId"
                isContentPremium = currentData.isPremium == true
            }

            MoEngageAnalytics.sendEventForCatalogStarted(
                BaseApplication.getBaseAppContext(),
                "listening started",
                (currentPosMs / 1000).toString(),
                currentData,
                extraData
            )
        }

        val payload = Payload(
            (currentPosMs / 1000).toInt(),
            currentData.id ?: "",
            entityType,
            entitySubType,
            pageSource,
            PrefUtils.uniqueId, (totalMs / 1000L).toInt(), 0,
            catalogId = catalogId
        )
        val event = EventPost(EventConstant.MARK_PLAY, payload, contentId = episodeId ?: "")
        if (NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())) {
            repository.updateEvents(listOf(event))
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
        sendAppsFlyerEventForContent(currentData, currentPosMs)

        Log.d("event post - ", "Play")
    }

    private fun sendAppsFlyerEventForContent(
        currentData: ExoNotificationData?,
        contentPosition: Long = 0L
    ) {
        var key = (currentData?.entitySubType ?: "podcast") + "_play"
        if (contentPosition == 0L && PrefUtils.userDetails?.meta?.aggregations?.listeningTime == 0) {
            key = "first_listen"
        }
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue["af_content_id"] = currentData?.id ?: ""
        eventValue["af_content_type"] = currentData?.entitySubType ?: ""
        eventValue["af_content"] = currentData?.title ?: ""
        eventValue["media_parent"] = currentData?.catalogTitle ?: ""
        eventValue["media_duration"] = (currentData?.duration ?: 0)
        AppsFlyerLib.getInstance().logEvent(BaseApplication.getBaseAppContext(), key, eventValue)
    }

    private fun sendAppsFlyerEventForRadio(currentData: ExoNotificationData?) {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue["af_content_id"] = currentData?.id ?: ""
        eventValue["af_content_type"] = "radio"
        eventValue["af_content"] = currentData?.title ?: ""
        eventValue["media_parent"] = currentData?.catalogTitle ?: ""
        AppsFlyerLib.getInstance().logEvent(
            BaseApplication.getBaseAppContext(),
            currentData?.entitySubType + "_play",
            eventValue
        )
    }

    private fun sendAppsFlyerEventForPause(
        currentData: ExoNotificationData?,
        elapsedTime: Long = 0L,
        total: Long = 0
    ) {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue["duration_played"] = (elapsedTime / 1000).toString()
        eventValue["af_content_type"] = currentData?.entitySubType ?: ""
        eventValue["af_content_id"] = currentData?.id ?: ""
        eventValue["af_content"] = currentData?.title ?: ""
        eventValue["media_parent"] = currentData?.catalogTitle ?: ""
        if (currentData?.isRadio == false) {
            eventValue["media_duration"] = total
        }
        AppsFlyerLib.getInstance()
            .logEvent(BaseApplication.getBaseAppContext(), "media_minutes", eventValue)
    }

    private fun sendPauseEventForContent(
        currentData: ExoNotificationData?,
        totalDuration: Long,
        elapsedTime: Long,
        contentPosition: Long = 0,
        taskRemovedListener: (() -> Unit)? = null,
        trigger: String = "",
        reason: String = ""
    ) {
        val streamType = if (isContentDownloaded(currentData)) {
            "downloaded"
        } else {
            "streamed"
        }

        val entityType = currentData?.entityType ?: ""
        val episodeId = currentData?.id
        val entitySubType = currentData?.entitySubType
        val pageSource = currentData?.pageSource
        val catalogId = currentData?.catalogId ?: ""

        if (entityType.isEmpty() || entitySubType.isNullOrEmpty()) {
            taskRemovedListener?.invoke()
            return
        }

        val payload = Payload(
            contentPosition.msToSeconds(), episodeId, entityType, entitySubType, pageSource,
            PrefUtils.uniqueId, totalDuration.msToSeconds(), elapsedTime.msToSeconds(),
            catalogId = catalogId
        )

        currentData.timeElapsed = contentPosition.msToSeconds().toLong()
        val currentIndex = QueueManager.queue.indexOf(currentData)

        if (currentIndex >= 0 && currentIndex < QueueManager.queue.size) {
            QueueManager.queue[currentIndex] = currentData
        }

        val event = EventPost(EventConstant.MARK_PAUSE, payload, contentId = episodeId ?: "")
        val eventName = if (taskRemovedListener != null) {
            "content_forced_stopped"
        } else {
            "content_paused"
        }

        AnalyticsUtil.sendEvent(
            entitySubType,
            episodeId,
            currentData.catalogId,
            eventName,
            pageSource,
            contentPosition.msToSeconds().toString(),
            totalDuration.msToSeconds().toString(),
            elapsedTime.msToSeconds().toString(),
            currentData.catalogTitle ?: "",
            currentData.title ?: "",
            true,
            streamType,
            trigger = trigger,
            source = currentData.source ?: "",
            extraData = currentData.extraData,
            playButtonPosition = DataController.playButtonPosition,
            networkType = NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()),
            contentFormat = if (DataController.isPlayingVideo
                || DataController.wasPlayingVideo
            ) {
                if (currentData.orientation == PORTRAIT) {
                    "vertical video"
                } else {
                    "video"
                }
            } else {
                "audio"
            },
            isPlayedPreview = currentData.isPurchaseNeeded.isTrue()
        )
        // send MoEngage Event
        MoEngageAnalytics.sendEventContentPaused(
            context = BaseApplication.getBaseAppContext(),
            eventName = eventName,
            entitySubType = entitySubType,
            entityId = episodeId,
            parentId = currentData.catalogId,
            pageSource = pageSource,
            durationPlayed = contentPosition.msToSeconds().toString(),
            totalDuration = totalDuration.msToSeconds().toString(),
            listeningTime = elapsedTime.msToSeconds().toString(),
            catalogTitle = currentData.catalogTitle ?: "",
            contentTitle = currentData.title ?: "",
            isPause = true,
            stremType = streamType,
            trigger = trigger,
            source = currentData.source ?: "",
            extraData = currentData.extraData,
            playButtonPosition = DataController.playButtonPosition,
            networkType = NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()),
            contentFormat = if (DataController.isPlayingVideo) "video" else "audio",
            isPlayedPreview = currentData.isPurchaseNeeded.isTrue()
        )
        sendAppsFlyerEventForPause(currentData)
        if (taskRemovedListener == null || DataController.playWhenReady) {
            val perListened = if (totalDuration > 0) {
                (contentPosition * 100 / (totalDuration)).toInt()
            } else {
                0
            }

            currentData.extraData?.apply {
                contentLink = "${Constants.WEB_BASE_URL}catalog/${currentData.id}"
                catalogLink = "${Constants.WEB_BASE_URL}catalog/${currentData.catalogId}"
                isContentPremium = currentData.isPremium == true
            }

            MoEngageAnalytics.sendEventForCatalogEnded(
                BaseApplication.getBaseAppContext(),
                "listening ended",
                contentPosition.msToSeconds().toString(),
                elapsedTime.msToSeconds().toString(),
                perListened,
                currentData,
                currentData.extraData
            )
        }

        if (currentData.isRadio) {
            taskRemovedListener?.invoke()
            return
        }
        Utils.sendActivationEvent(elapsedTime.msToSeconds())

        if (NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())) {
            BaseApplication.doServerCall({
                NetworkRequests.updateActions(
                    PostAction(listOf(event))
                )
            }, object : ServerInterface<BaseModel<Any>> {
                override fun onCustomError(e: ApiError) {
                    Utils.storeEventToLocalDb(event)
                    taskRemovedListener?.invoke()
                }

                override fun onError(e: Throwable) {
                    Utils.storeEventToLocalDb(event)
                    taskRemovedListener?.invoke()
                }

                override fun onSuccess(data: BaseModel<Any>, dataFromCache: Boolean) {
                    taskRemovedListener?.invoke()
                }
            })
        } else {
            Utils.storeEventToLocalDb(event)
            taskRemovedListener?.invoke()
        }
    }

    private fun sendFinishEvent(
        currentData: ExoNotificationData?,
        total: Long,
        elapsedTime: Long
    ) {
        val streamType = if (isContentDownloaded(currentData)) {
            "downloaded"
        } else {
            "streamed"
        }
        val entityType = currentData?.entityType ?: ""
        totalTimeElapsed += elapsedTime.msToSeconds()

        val played = total.msToSeconds()
        val episodeId = currentData?.id
        val entitySubType = currentData?.entitySubType
        val pageSource = currentData?.pageSource

        val catalogId = currentData?.catalogId ?: ""

        if (entityType.isEmpty() || entitySubType.isNullOrEmpty()) {
            return
        }

        Utils.sendActivationEvent(elapsedTime.msToSeconds())

        val payload = Payload(
            played, episodeId, entityType, entitySubType, pageSource,
            PrefUtils.uniqueId, played, elapsedTime.msToSeconds(),
            catalogId = catalogId
        )

        currentData.timeElapsed = played.toLong()

        val event = EventPost(EventConstant.MARK_PAUSE, payload, contentId = episodeId ?: "")
        AnalyticsUtil.sendEvent(
            entitySubType,
            episodeId,
            currentData.catalogId,
            "content_listened",
            pageSource,
            played.toString(),
            total.msToSeconds().toString(),
            elapsedTime.msToSeconds().toString(),
            currentData.catalogTitle ?: "",
            currentData.title ?: "",
            true,
            streamType,
            networkType = NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()),
            contentFormat = if (DataController.isPlayingVideo) {
                if (currentData.orientation == PORTRAIT) {
                    "vertical video"
                } else {
                    "video"
                }
            } else {
                "audio"
            },
            source = currentData.source ?: "",
            extraData = currentData.extraData
        )

        sendAppsFlyerEventForPause(currentData)
        if (NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())) {
            repository.updateEvents(listOf(event))
        } else {
            Utils.storeEventToLocalDb(event)
        }

        /* when content is played from player queue, this extraData object is null. */
        if (currentData.extraData == null) {
            currentData.extraData = ExtraData()
        }

        currentData.extraData?.let {
            it.isContentPremium = currentData.isPremium == true
        }

        if (currentData.isRadio) {
            MoEngageAnalytics.sendEventForRadio(
                BaseApplication.getBaseAppContext(),
                "listening ended",
                elapsedTime.msToSeconds().toString(),
                currentData,
                currentData.extraData
            )
        } else {
            MoEngageAnalytics.sendEventForCatalogEnded(
                BaseApplication.getBaseAppContext(),
                "listening ended",
                elapsedTime.msToSeconds().toString(),
                elapsedTime.msToSeconds().toString(),
                100,
                currentData,
                currentData.extraData
            )
        }
    }

    private fun updateRadioCounter(
        counter: Int,
        currentData: ExoNotificationData,
        elapsedTimeMs: Long,
        forcedStopListener: (() -> Unit)? = null
    ) {
        val elapsedTimeSeconds = elapsedTimeMs.msToSeconds().toLong()

        if (counter == 1) {
            runTimer(currentData)
        } else {
            timer?.cancel()
        }

        if (!DataController.isAdPlaying) {
            /* when content is played from player queue, this extraData object is null. */
            if (currentData.extraData == null) {
                currentData.extraData = ExtraData()
            }

            currentData.extraData?.apply {
                contentLink = "${Constants.WEB_BASE_URL}catalog/${currentData.id}"
                catalogLink = "${Constants.WEB_BASE_URL}catalog/${currentData.catalogId}"
                isContentPremium = currentData.isPremium == true
            }

            if (counter == 1) {
                MoEngageAnalytics.sendEventForRadio(
                    BaseApplication.getBaseAppContext(),
                    "listening started",
                    0.toString(),
                    currentData,
                    currentData.extraData
                )
                AnalyticsUtil.sendEventForRadio(
                    currentData.catalogId,
                    "radio_played",
                    currentData.catalogTitle ?: "",
                    currentData.id ?: "",
                    currentData.subTitle ?: "",
                    "",
                    extraData = currentData.extraData,
                    isAppInBackground = BasePlayerActivity.isAppInBackground
                )
                sendAppsFlyerEventForRadio(currentData)
            } else {
                MoEngageAnalytics.sendEventForRadio(
                    BaseApplication.getBaseAppContext(),
                    "listening ended",
                    elapsedTimeSeconds.toString(),
                    currentData,
                    currentData.extraData
                )
                AnalyticsUtil.sendEventForRadio(
                    currentData.catalogId,
                    "radio_stopped",
                    currentData.catalogTitle ?: "",
                    currentData.id ?: "",
                    currentData.subTitle ?: "",
                    "",
                    elapsedTimeSeconds,
                    extraData = currentData.extraData
                )

                sendAppsFlyerEventForPause(currentData)
            }
        }

        updateRadioEvents(currentData, counter, elapsedTimeSeconds)

        updateTransaction(currentData, counter, forcedStopListener)
    }

    private fun updateRadioEvents(
        currentData: ExoNotificationData,
        counter: Int,
        elapsedTimeSeconds: Long
    ) {
        if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
            return
        }

        val listeningTime = if (counter == 1) {
            0
        } else {
            elapsedTimeSeconds.toInt()
        }

        val payload = Payload(
            0,
            null,
            currentData.entityType,
            currentData.entitySubType,
            currentData.pageSource,
            PrefUtils.uniqueId, -1, listeningTime,
            catalogId = currentData.catalogId ?: ""
        )
        Utils.sendActivationEvent(listeningTime)
        val event =
            if (counter == 1) {
                EventPost(EventConstant.MARK_PLAY, payload, contentId = currentData.catalogId ?: "")
            } else {
                EventPost(
                    EventConstant.MARK_PAUSE,
                    payload,
                    contentId = currentData.catalogId ?: ""
                )
            }
        if (NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())) {
            repository.updateEvents(listOf(event))
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
    }

    private fun updateTransaction(
        currentData: ExoNotificationData,
        counter: Int,
        forcedStopListener: (() -> Unit)?
    ) {
        val docId = Utils.getDeviceId().plus(currentData.id)

        if (docId.isEmpty()) {
            forcedStopListener?.invoke()
            return
        }

        val db = DataController.fireStoreChat

        db.runTransaction { transaction ->

            val recordExists = transaction.get(db.collection("RadioUsers").document(docId)).exists()
            val radioExists =
                transaction.get(db.collection("Content").document(currentData.id ?: "Noice"))
                    .exists()

            var updateCounter = false

            if (recordExists) {
                if (counter < 0) {
                    transaction.delete(db.collection("RadioUsers").document(docId))
                    updateCounter = true
                }
            } else if (counter > 0) {
                val map = HashMap<String, Any>()
                map["radioId"] = currentData.catalogId.toString()
                map["timestamp"] = System.currentTimeMillis() / 1000
                map["deviceId"] = Utils.getDeviceId()

                transaction.set(
                    db.collection("RadioUsers").document(docId),
                    map,
                    SetOptions.merge()
                )
                updateCounter = true
            }

            if (updateCounter) {
                if (radioExists) {
                    transaction.update(
                        db.collection("Content").document(currentData.id ?: "Noice"),
                        "listeners",
                        FieldValue.increment(counter.toLong())
                    )
                } else {
                    val map = HashMap<String, Any>()
                    map["listeners"] = counter
                    transaction.set(
                        db.collection("Content").document(currentData.id ?: "Noice"),
                        map,
                        SetOptions.merge()
                    )
                }
            }
        }.addOnCompleteListener {
            Log.d("radio counter", " updated")
            forcedStopListener?.invoke()
        }
    }

    private fun runTimer(currentData: ExoNotificationData?) {
        timer?.cancel()
        timer = Timer()
        timer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                CoroutineScope(Dispatchers.Main).launch {
                    postRadioInfo(currentData)
                }
            }
        }, DataController.radioUpdateInterval, DataController.radioUpdateInterval)
    }

    private fun postRadioInfo(currentData: ExoNotificationData?) {
        val map = HashMap<String, Any>()
        map["radioId"] = currentData?.catalogId.toString()
        map["timestamp"] = System.currentTimeMillis() / 1000

        Utils.getDeviceId().let { deviceId ->

            map["deviceId"] = deviceId

            DataController.fireStoreChat.collection("RadioUsers")
                .document(deviceId.plus(currentData?.id.toString()))
                .set(map, SetOptions.merge())
                .addOnCompleteListener {

                }
        }
    }
    private fun sendPlayerAnalytics(
        action: String,
        currentData: ExoNotificationData?,
        currentPos: Long = 0,
        total: Long = 0,
        error: String? = null,
        errorCode: Int = 0
    ) {

        val streamType = if (isContentDownloaded(currentData)) {
            "downloaded"
        } else {
            "streamed"
        }

        val episodeId = currentData?.id
        val entityType = currentData?.entityType ?: ""
        val entitySubType = currentData?.entitySubType
        val pageSource = currentData?.pageSource
        val extraData = currentData?.extraData

        if (error.isNullOrEmpty()) {
            AnalyticsUtil.sendEvent(
                entitySubType ?: "",
                episodeId,
                currentData?.catalogId,
                action,
                pageSource,
                (currentPos / 1000).toString(),
                (total / 1000L).toString(),
                catalogTitle = currentData?.catalogTitle ?: "",
                contentTitle = currentData?.title ?: "",
                streamType = streamType,
                source = currentData?.source ?: "",
                extraData = extraData,
                playButtonPosition = DataController.playButtonPosition
            )
        } else {
            AnalyticsUtil.sendEventForPlayerError(
                entitySubType ?: "",
                episodeId,
                currentData?.catalogId,
                action,
                pageSource,
                (currentPos / 1000).toString(),
                (total / 1000L).toString(),
                catalogTitle = currentData?.catalogTitle ?: "",
                contentTitle = currentData?.title ?: "", error = error, stremType = streamType,
                source = currentData?.source ?: "",
                extraData = extraData,
                errorCode = errorCode
            )
        }
    }
}