package noice.app.utils

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.facebook.login.LoginManager
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.moengage.core.MoECoreHelper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import noice.app.BaseApplication
import noice.app.data.DataController
import noice.app.enums.DownloadQuality
import noice.app.exoplayer.ExoplayerUtils
import noice.app.model.AppCDNConfig
import noice.app.model.ExoNotificationData
import noice.app.model.appconfig.AdConfig
import noice.app.model.appconfig.AppConfig
import noice.app.model.appconfig.BottomNavConfigsAos
import noice.app.model.appconfig.FirebaseVariant
import noice.app.model.appconfig.LiveConfig
import noice.app.model.eventbus.EventMessage
import noice.app.model.user.User
import noice.app.model.user.preference.PreferenceData
import noice.app.modules.coins.model.CoinPurchase
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.model.PubNubToken
import noice.app.modules.onboarding.activity.LoginActivity
import noice.app.modules.onboarding.fragments.GuestLoginDialog
import noice.app.modules.onboarding.fragments.GuestLoginDialog.Companion.GUEST_LOGIN
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.LoginResponse
import noice.app.player.managers.QueueManager
import noice.app.utils.ExperimentUtils.FIREBASE_VARIANT_CATALOG_QUICK_FILTERS
import org.greenrobot.eventbus.EventBus
import java.io.IOException
import java.security.GeneralSecurityException

object PrefUtils {

    private const val USER_LEVEL_PREF_ENCRYPTED = "USER_LEVEL_PREF_ENCRYPTED"
    private const val APP_LEVEL_PREF_ENCRYPTED = "APP_LEVEL_PREF_ENCRYPTED"
    private const val OLD_USER_LEVEL_PREF = "Noice_Shared_Pref_Encrypted"
    private const val OLD_APP_LEVEL_PREF = "APP_LEVEL_NOICE_PREF"
    private const val QUEUE_DATA = "QUEUE_DATA"

    private var userLevelPref: SharedPreferences
    private var appLevelPref: SharedPreferences

    init {
        val oldUserLevelPref = createPref(OLD_USER_LEVEL_PREF)
        val oldAppLevelPref = createPref(OLD_APP_LEVEL_PREF)

        try {
            userLevelPref = createEncryptedPref(USER_LEVEL_PREF_ENCRYPTED)
            appLevelPref = createEncryptedPref(APP_LEVEL_PREF_ENCRYPTED)

            if (userLevelPref.all.isEmpty() && oldUserLevelPref.all.isNotEmpty()) {
                oldUserLevelPref.migrateTo(userLevelPref)
            }
            if (appLevelPref.all.isEmpty() && oldAppLevelPref.all.isNotEmpty()) {
                oldAppLevelPref.migrateTo(appLevelPref)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            userLevelPref = oldUserLevelPref
            appLevelPref = oldAppLevelPref
        }

        appLevelPref.migrateKeyTo(userLevelPref, QUEUE_DATA)
    }

    @kotlin.jvm.Throws(GeneralSecurityException::class, IOException::class)
    private fun createEncryptedPref(name: String): SharedPreferences {
        val masterKey = MasterKey.Builder(BaseApplication.getBaseAppContext())
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()

        return EncryptedSharedPreferences.create(
            BaseApplication.getBaseAppContext(),
            name,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }

    private fun createPref(name: String): SharedPreferences {
        return BaseApplication.getBaseAppContext().getSharedPreferences(name, Context.MODE_PRIVATE)
    }

    private const val LOGIN_STATUS = "LOGIN_STATUS"
    private const val LOGIN_TYPE = "LOGIN_TYPE"
    private const val USER_DETAILS = "USER_DETAILS"
    private const val SELECTED_GENRE = "SELECTED_GENRE"
    private const val USER_TOKEN = "USER_TOKEN"
    private const val FIREBASE_TOKEN = "FIREBASE_TOKEN"
    private const val REFRESH_TOKEN = "REFRESH_TOKEN"
    private const val FCM_TOKEN = "FCM_TOKEN"
    private const val GUEST_ID = "GUEST_ID"
    private const val SELECTED_COMMENT_FILTER = "SELECTED_COMMENT_FILTER"
    private const val INTRO_SHOWN = "INTRO_SHOWN"
    private const val ON_BOARDING_STEP = "ON_BOARDING_STEP"
    private const val UNIQUE_ID = "UNIQUE_ID"
    private const val DATA_SAVER = "DATA_SAVER"
    private const val DOWN_LOAD_QUALITY = "DOWN_LOAD_QUALITY"
    private const val QUEUE_TITLE = "QUEUE_TITLE"
    private const val HAS_REVIEWED_APP = "HAS_REVIEWED_APP"
    private const val SHOW_RESTORE_DOWNLOAD = "SHOW_RESTORE_DOWNLOAD"
    private const val IS_INSTALL_REFERRER_CHECKED = "IS_INSTALL_REFERRER_CHECKED"
    private const val REFERRAL_USER_ID = "REFERRAL_USER_ID"
    private const val USER_INTEGRITY_STATE = "USER_INTEGRITY_STATE"
    private const val CLIP_DIALOG = "CLIP_DIALOG"
    private const val APP_CONFIG = "APP_CONFIG"
    private const val LOGIN_COUNTER = "LOGIN_COUNTER"
    private const val APP_CDN_CONFIG = "APP_CDN_CONFIG"
    private const val LIVE_CONFIG = "LIVE_CONFIG"
    private const val NOTIFIED_VERSION_CODE = "NOTIFIED_VERSION_CODE"
    private const val USER_PREFERENCE = "USER_PREFERENCE"
    private const val GUIDELINES_DIALOG = "GUIDELINES_DIALOG"
    private const val LIVE_UPDATE_COUNT = "LIVE_UPDATE_COUNT"
    private const val IS_AB_TEST_PART = "IS_AB_TEST_PART" //used for new onboarding flow
    private const val DATA_VERSION = "DATA_VERSION"
    private const val IS_TOOLTIP_SHOWN = "IS_TOOLTIP_SHOWN"
    private const val LIVE_TOOLTIP_SHOWN = "LIVE_TOOLTIP_SHOWN"
    private const val LIVE_SWIPE_RIGHT_SHOWCASE = "LIVE_SWIPE_RIGHT_SHOWCASE"
    private const val LIVE_NAV_TOOLTIP_SHOWN = "LIVE_TOOLTIP_NAV_SHOWN"
    private const val COINS_INTRO_SHOWN = "COINS_INTRO_SHOWN"
    private const val AD_CONFIG = "AD_CONFIG"
    private const val ADS_IDENTIFIER = "ADS_IDENTIFIER"
    private const val APP_INSTALLED_TIME = "APP_INSTALLED_TIME"
    private const val LAST_ADDED_QUEUE_CONTENT = "LAST_ADDED_QUEUE_CONTENT"
    private const val IS_NEW_VERTICAL_CLICKED = "IS_NEW_VERTICAL_CLICKED"
    private const val PENDING_PURCHASES = "PENDING_PURCHASES"
    private const val IS_SCRIM_VIEW_SHOWN = "IS_SCRIM_VIEW_SHOWN"
    private const val IS_NEW_ACQUIRED_USER = "IS_NEW_ACQUIRED_USER"
    private const val IS_NOTIFICATION_ON_HOME_GRANTED = "IS_NOTIFICATION_ON_HOME_GRANTED"
    private const val LIVE_SO_FILES = "LIVE_SO_FILES"
    private const val VIDEO_INTRO_SHOWN = "VIDEO_INTRO_SHOWN"
    private const val VIDEO_INTRO_SHOWN_IN_PLAYER = "VIDEO_INTRO_SHOWN_IN_PLAYER"
    private const val APPS_FLYER_ID = "APPS_FLYER_ID"
    private const val PUBNUB_TOKEN = "PUBNUB_TOKEN"
    private const val IS_APP_INSTALL = "IS_APP_INSTALL"
    private const val FIREBASE_VARIANT_QUICK_PICKS = "FIREBASE_VARIANT_QUICK_PICKS"
    private const val FIREBASE_VARIANT_SIMILAR_CATALOG = "FIREBASE_VARIANT_SIMILAR_CATALOG"
    private const val IS_SIMILAR_CATALOG_TAB_CLICKED = "IS_SIMILAR_CATALOG_TAB_CLICKED"
    private const val CUSTOM_FORMAT_ID = "CUSTOM_FORMAT_ID"
    private const val FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST = "FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST"
    private const val BOTTOM_NAV_CONFIGS = "BOTTOM_NAV_CONFIGS"
    private const val AUTOMOTIVE_NEW_INSTALLATION_PROMPT = "AUTOMOTIVE_NEW_INSTALLATION_PROMPT"
    private const val AUTOMOTIVE_PREMIUM_DEMO_PROMPT = "AUTOMOTIVE_PREMIUM_DEMO_PROMPT"
    private const val AUTOMOTIVE_NEW_INSTALLATION_REQUIRE_SUBSCRIPTION = "AUTOMOTIVE_NEW_INSTALLATION_REQUIRE_SUBSCRIPTION"
    private var SHOULD_RECREATE_ACTIVITY = "SHOULD_RECREATE_ACTIVITY"
    private var adsConfig : List<AdConfig>? = null
    private var liveConfigs : LiveConfig? = null
    private var adsIdentifierString: String = ""

    private val stateFlow = MutableStateFlow(PrefState())
    val prefStateFlow = stateFlow.asStateFlow()

    private fun SharedPreferences.migrateKeyTo(toPref: SharedPreferences, key: String) {
        if (contains(key) && !toPref.contains(key)) {
            val data = this.all[key]
            if (data != null) {
                toPref.edit().putKey(key, data).apply()
            }
            edit().remove(key).apply()
        }
    }

    private fun SharedPreferences.migrateTo(destinationPref: SharedPreferences) {
        if (all.isEmpty()) {
            return
        }
        destinationPref.edit().let { destinationEditor ->
            all.forEach { (key, value) ->
                destinationEditor.putKey(key, value)
            }
            destinationEditor.apply()
        }
        //edit().clear().apply()
    }

    private fun SharedPreferences.Editor.putKey(key: String, value: Any?): SharedPreferences.Editor {
        when (value) {
            is Boolean -> putBoolean(key, value)
            is Float -> putFloat(key, value)
            is String -> putString(key, value)
            is Int -> putInt(key, value)
            is Long -> putLong(key, value)
            is Set<*> -> putStringSet(key, value.map { it.toString() }.toSet())
        }
        return this
    }

    fun createSession(data: LoginResponse, type: String) {
        tokenImpl = "Bearer ${data.accessToken}"
        userLevelPref.edit().apply {
            putBoolean(LOGIN_STATUS, true)
            putString(USER_TOKEN, tokenImpl)
            putString(REFRESH_TOKEN, data.refreshToken)
            putString(LOGIN_TYPE, type)
            apply()
        }
        if (!data.referralUserId.isNullOrEmpty()) {
            referralUserId = data.referralUserId
        }
    }

    var isLoggedIn:Boolean
        get() = userLevelPref.getBoolean(LOGIN_STATUS, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(LOGIN_STATUS, value)
                apply()
            }
        }

    val loginCounter
        get() = loginCounterPair?.first ?: 0

    var loginCounterPair : Pair<Int, Long>? = null
        get() {
            if (field == null) {
                val config = appLevelPref.getString(LOGIN_COUNTER, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return field
        }
        set(value) {
            field = value
            appLevelPref.edit().apply {
                putString(LOGIN_COUNTER, Gson().toJson(value))
                apply()
            }
        }

    var userPreference : PreferenceData?
        get() = run {
            val userPrefs = userLevelPref.getString(USER_PREFERENCE, "")
            if (!userPrefs.isNullOrEmpty()) {
                return@run Gson().fromJson(userPrefs, PreferenceData::class.java)
            }
            return@run null
        }
        set(value) {
            if (value != null) {
                userLevelPref.edit().apply {
                    putString(USER_PREFERENCE, Gson().toJson(value))
                    apply()
                }
            }
        }

    var notifiedVersionCode : Int
        get() = appLevelPref.getInt(NOTIFIED_VERSION_CODE, 0)
        set(value) {
            appLevelPref.edit().apply {
                putInt(NOTIFIED_VERSION_CODE, value)
                apply()
            }
        }
    var customFormatId : String?
        get() = appLevelPref.getString(CUSTOM_FORMAT_ID, "")
        set(value) {
            appLevelPref.edit().apply {
                putString(CUSTOM_FORMAT_ID, value)
                apply()
            }
        }

    var isInstallReferrerChecked : Boolean
        get() = appLevelPref.getBoolean(IS_INSTALL_REFERRER_CHECKED, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(IS_INSTALL_REFERRER_CHECKED, value)
                apply()
            }
        }


    var isLiveSoFileLoaded : Boolean
        get() = appLevelPref.getBoolean(LIVE_SO_FILES, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(LIVE_SO_FILES, value)
                apply()
            }
        }

    var referralUserId : String? = null
        get() {
            if (field == null) {
                field = userLevelPref.getString(REFERRAL_USER_ID, "")
            }
            return field
        }
        set(value) {
            field = value
            if (value != null) {
                userLevelPref.edit().apply {
                    putString(REFERRAL_USER_ID, value)
                    apply()
                }
            }
        }

    var userIntegrityState : String? = null
        get() {
            if (field == null) {
                field = userLevelPref.getString(USER_INTEGRITY_STATE, "")
            }
            return field
        }
        set(value) {
            field = value
            if (value != null) {
                userLevelPref.edit().apply {
                    putString(USER_INTEGRITY_STATE, value)
                    apply()
                }
            }
        }

    var appConfig : AppConfig? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(APP_CONFIG, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(APP_CONFIG, Gson().toJson(value))
                    apply()
                }
            }
        }

    var appCDNConfig: AppCDNConfig? = null
        get() = run {
            if (field == null || field?.live_android == null) {
                val config = appLevelPref.getString(APP_CDN_CONFIG, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(APP_CDN_CONFIG, Gson().toJson(value))
                    apply()
                }
            }
        }

    var liveConfig : LiveConfig?
        get() = run {
            if (liveConfigs == null) {
                val config = appLevelPref.getString(LIVE_CONFIG, "")
                if (!config.isNullOrEmpty()) {
                    liveConfigs = Utils.getObjectFromJson(config)
                }
            }
            return@run liveConfigs
        }
        set(value) {
            liveConfigs = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(LIVE_CONFIG, Gson().toJson(value))
                    apply()
                }
            }
        }

    var showRestoreDownload : Boolean
        get() = userLevelPref.getBoolean(SHOW_RESTORE_DOWNLOAD, true)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(SHOW_RESTORE_DOWNLOAD, value)
                apply()
            }
        }

    var isLoggedInAsGuest = loginType == GuestLoginDialog.GUEST_LOGIN

    var queueTitle: String?
        get() = run {
            var title = appLevelPref.getString(QUEUE_TITLE, "") ?: ""
            if(title.isEmpty()) {
                title = userLevelPref.getString(QUEUE_TITLE, "") ?: ""
            }
            return@run title
        }
        set(value) {
            appLevelPref.edit().apply {
                putString(QUEUE_TITLE, value)
                apply()
            }
        }

    var updatedTimeStamp: Long
        get() = run {
            return@run appLevelPref.getLong(LIVE_UPDATE_COUNT, 0L)
        }
        set(value) {
            appLevelPref.edit().apply {
                putLong(LIVE_UPDATE_COUNT, value)
                apply()
            }
        }


    var hasReviewedApp: Boolean
        get() = appLevelPref.getBoolean(HAS_REVIEWED_APP, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(HAS_REVIEWED_APP, value)
                apply()
            }
        }

    var fcmToken
        set(token) {
            appLevelPref.edit().apply {
                putString(FCM_TOKEN, token)
                apply()
            }
        }
        get() = run {
            return@run appLevelPref.getString(FCM_TOKEN, "")
        }

    var guestId
        set(id) {
            appLevelPref.edit().apply {
                putString(GUEST_ID, id)
                apply()
            }
        }
        get() = run {
            return@run appLevelPref.getString(GUEST_ID, "")
        }

    var appsFlyerId
        set(value) {
            userLevelPref.edit().apply {
                putString(APPS_FLYER_ID, value)
                apply()
            }
        }
        get() = run {
            return@run userLevelPref.getString(APPS_FLYER_ID, "")
        }

    private var selectedCommentFilterImpl : String? = null
    var selectedCommentFilter
        set(filterOption) {
            selectedCommentFilterImpl = filterOption
            userLevelPref.edit().apply {
                putString(SELECTED_COMMENT_FILTER, filterOption)
                apply()
            }
        }
        get() = run {
            if (selectedCommentFilterImpl == null) {
                selectedCommentFilterImpl = userLevelPref.getString(SELECTED_COMMENT_FILTER, null)
            }
            return@run selectedCommentFilterImpl
        }

    var isClipDialogSeen : Boolean
        get() = appLevelPref.getBoolean(CLIP_DIALOG, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(CLIP_DIALOG, value)
                apply()
            }
        }

    var isGuidelinesDialogSeen : Boolean
        get() = appLevelPref.getBoolean(GUIDELINES_DIALOG, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(GUIDELINES_DIALOG, value)
                apply()
            }
        }

    var lastAddedQueueContent : Pair<String, Int>? = null
        get() = run {
            if (field == null) {
                val lastAddedQueueContent = userLevelPref.getString(LAST_ADDED_QUEUE_CONTENT, "")
                if (!lastAddedQueueContent.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(lastAddedQueueContent)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            userLevelPref.edit().apply {
                putString(LAST_ADDED_QUEUE_CONTENT, Gson().toJson(value))
                apply()
            }
        }

    var queueJson: String? = null
        set(value) {
            field = value
            appLevelPref.edit().apply {
                putString(QUEUE_DATA, value)
                apply()
            }
        }
        get() {
            if (field == null) {
                field = appLevelPref.getString(QUEUE_DATA, "") ?: ""
            }
            return field
        }

    fun getQueueList() : MutableList<ExoNotificationData> {
        val list = Utils.getObjectFromJson<MutableList<ExoNotificationData>>(queueJson)
        return if (!list.isNullOrEmpty()) {
            list
        } else {
            ArrayList()
        }
    }

    fun isIntroShown(value: Boolean? = null) : Boolean {
        return if(value != null) {
            appLevelPref.edit().apply {
                putBoolean(INTRO_SHOWN, value)
                apply()
            }
            true
        } else {
            appLevelPref.getBoolean(INTRO_SHOWN, false)
        }
    }

    fun logout(fullClear : Boolean = false, redirect : Boolean = true, baseContext: Context) {

        LiveTrigger.onRoomEnded()

        QueueManager.clearAndNotify()

        MoECoreHelper.logoutUser(baseContext)

        DataController.clearAll()

        if(loginType == LoginActivity.LOGIN_TYPE_FACEBOOK) {
            LoginManager.getInstance().logOut()
        } else if(loginType == LoginActivity.LOGIN_TYPE_GOOGLE) {
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).build()
            val googleSignInClient = GoogleSignIn.getClient(BaseApplication.getBaseAppContext(), gso)
            googleSignInClient.signOut()
        }

        token.let { userToken ->
            BaseApplication.doServerCall({ BaseApplication.commonEntryPoint.userApiInterface.logout(userToken) }, null)
        }

        userDetails = null
        liveConfigs = null

        clearUserLevelInfo()

        if(fullClear) {
            appLevelPref.edit().apply {
                clear()
                apply()
            }
        }

        BaseApplication.commonEntryPoint.onBoardingApiRepository.callGuestTokenApi()

        CacheUtils.deleteWholeApiCache()

        EventBus.getDefault().post(EventMessage(null, Constants.STOP_AUDIO_SERVICE))

        ExoplayerUtils.stopAllDownloads()

        /* to prevent Tooltip to display again after logout */
        isTooltipShown = true

        AnalyticsUtil.sendEvent("logout")
        MoEngageAnalytics.sendEvent(baseContext, "logged out", "", null)

        if(redirect) {
            val i = Intent(
                baseContext,
                ClassVariantProvider.of(HomeActivity::class.java)
            )
            if (ClassVariantProvider.isToyotaVariant) {
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            } else {
                loginType = GUEST_LOGIN
                shouldRecreateActivity = true
            }
            baseContext.startActivity(i)
        }
    }

    fun getRefreshToken() = userLevelPref.getString(REFRESH_TOKEN, "")?:""

    private var tokenImpl : String? = null
    var token
        set(value) {
            tokenImpl = "Bearer $value"
            userLevelPref.edit().apply {
                putString(USER_TOKEN, tokenImpl)
                apply()
            }
        }
        get() = run {
            if (tokenImpl == null) {
                tokenImpl = userLevelPref.getString(USER_TOKEN, null)
            }
            return@run tokenImpl
        }

    var firebaseToken
        set(value) {
            userLevelPref.edit().apply {
                putString(FIREBASE_TOKEN, value)
                apply()
            }
        }
        get() = run {
            return@run userLevelPref.getString(FIREBASE_TOKEN, null)
        }

    var uniqueId
        set(value) {
            userLevelPref.edit().apply {
                putString(UNIQUE_ID, value)
                apply()
            }
        }
        get() = userLevelPref.getString(UNIQUE_ID, null)


    var userDetails: User? = null
        set(value) {
            field = value
            val str = Gson().toJson(value)
            userLevelPref.edit().apply {
                putString(USER_DETAILS, str)
                apply()
            }
        }
        get() {
            if (field == null || field?.meta == null || field?.imageMeta == null) {
                field = userLevelPref.getString(USER_DETAILS, null)?.let {
                    Utils.getObjectFromJson(it)
                }
            }
            return field
        }

    var pendingPurchases : Set<CoinPurchase>? = null
        set(value) {
            field = value
            val str = Gson().toJson(value)
            userLevelPref.edit().apply {
                putString(PENDING_PURCHASES, str)
                apply()
            }
        }
        get() = run {
            if (field == null) {
                val str = userLevelPref.getString(PENDING_PURCHASES, null)
                field = Utils.getObjectFromJson(str)
            }
            field
        }

    var selectedGenre
        set(value) {
            val str = Gson().toJson(value)
            userLevelPref.edit().apply {
                putString(SELECTED_GENRE, str)
                apply()
            }
        }
        get() = run {
            userLevelPref.getString(SELECTED_GENRE, "[]")?.let {
                val listType = object : TypeToken<ArrayList<Genre>>() {}.type
                return@run Gson().fromJson<ArrayList<Genre>>(it, listType)
            }
        }

    var isDataSaverOn
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(DATA_SAVER, value)
                apply()
            }
        }
        get() = userLevelPref.getBoolean(DATA_SAVER, false)

    var downLoadQuality
        set(value) {
            userLevelPref.edit().apply {
                putString(DOWN_LOAD_QUALITY, value)
                apply()
            }
        }
        get() = userLevelPref.getString(DOWN_LOAD_QUALITY, DownloadQuality.MEDIUM.value) ?: DownloadQuality.MEDIUM.value

    var onBoardingStep
        set(value) {
            userLevelPref.edit().apply {
                putInt(ON_BOARDING_STEP, value)
                apply()
            }
        }
        get() = userLevelPref.getInt(ON_BOARDING_STEP, 0)

    var loginType
        get() = userLevelPref.getString(LOGIN_TYPE, null)
        set(value) {
            userLevelPref.edit().apply {
                putString(LOGIN_TYPE, value)
                apply()
            }
            isLoggedInAsGuest = value == GuestLoginDialog.GUEST_LOGIN
        }

    fun clearQueueData() {
        queueJson = null
        appLevelPref.edit().apply {
            remove(QUEUE_DATA)
            remove(QUEUE_TITLE)
            remove(LAST_ADDED_QUEUE_CONTENT)
            apply()
        }
        userLevelPref.edit().apply {
            remove(QUEUE_DATA)
            remove(QUEUE_TITLE)
            apply()
        }
    }

    //no use of this for current flow, might use it for A/B test later on
    var isPartOfAbTest
        get() = userLevelPref.getBoolean(IS_AB_TEST_PART, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_AB_TEST_PART, value)
                apply()
            }
        }

    //no use for current flow, might use is for A/B later on.
    var dataVersion
        get() = userLevelPref.getString(DATA_VERSION, Constants.DATA_VERSION_OLD) ?: Constants.DATA_VERSION_OLD //represents pre-onboarding flow
        set(value) {
            userLevelPref.edit().apply {
                putString(DATA_VERSION, value)
                apply()
            }
        }

    var isTooltipShown
        get() = userLevelPref.getBoolean(IS_TOOLTIP_SHOWN, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_TOOLTIP_SHOWN, value)
                apply()
            }
        }

    var liveTooltipShown
        get() = appLevelPref.getBoolean(LIVE_TOOLTIP_SHOWN, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(LIVE_TOOLTIP_SHOWN, value)
                apply()
            }
        }

    var liveNavTooltipShown
        get() = appLevelPref.getBoolean(LIVE_NAV_TOOLTIP_SHOWN, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(LIVE_NAV_TOOLTIP_SHOWN, value)
                apply()
            }
        }


    var livePlayerSwipeShowcaseShownRooms: ArrayList<String>?
        get() = run {
            appLevelPref.getString(LIVE_SWIPE_RIGHT_SHOWCASE, null)?.let {
                val type = object : TypeToken<ArrayList<String>>() {}.type
                return@run Gson().fromJson<ArrayList<String>>(it, type)
            }
        }
        set(value) {
            appLevelPref.edit().apply {
                val json = Gson().toJson(value)
                putString(LIVE_SWIPE_RIGHT_SHOWCASE, json)
                apply()
                onState(
                    stateFlow.value.copy(
                        livePlayerSwipeShowcaseShownRoomsState = value ?: arrayListOf()
                    )
                )
            }
        }

    var coinsIntroShown
        get() = appLevelPref.getBoolean(COINS_INTRO_SHOWN, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(COINS_INTRO_SHOWN, value)
                apply()
            }
        }

    var videoIntroShown
        get() = appLevelPref.getBoolean(VIDEO_INTRO_SHOWN, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(VIDEO_INTRO_SHOWN, value)
                apply()
            }
        }

    var videoInPlayerIntroShown
        get() = appLevelPref.getBoolean(VIDEO_INTRO_SHOWN_IN_PLAYER, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(VIDEO_INTRO_SHOWN_IN_PLAYER, value)
                apply()
            }
        }

    var adConfig
        set(value) {
            adsConfig = value
            val str = Gson().toJson(value)
            userLevelPref.edit().apply {
                putString(AD_CONFIG, str)
                apply()
            }
        }
        get() = run {
            if (adsConfig == null) {
                userLevelPref.getString(AD_CONFIG, "")?.let { json ->
                    adsConfig = Utils.getObjectFromJson(json)
                }
            }
            return@run adsConfig
        }

    var isScrimViewShown get() = userLevelPref.getBoolean(IS_SCRIM_VIEW_SHOWN, false)
    set(value) {
        userLevelPref.edit().apply {
                putBoolean(IS_SCRIM_VIEW_SHOWN, value)
                apply()
            }
    }

    var adsIdentifier
        set(value) {
            adsIdentifierString = value
            appLevelPref.edit().apply {
                putString(ADS_IDENTIFIER, value)
                apply()
            }
        }
        get() = run {
            if (adsIdentifierString.isEmpty()) {
                appLevelPref.getString(ADS_IDENTIFIER, "")?.let { identifier ->
                    adsIdentifierString = identifier
                }
            }
            return@run adsIdentifierString
        }

    var appInstalledTime
    set(value) {
        appLevelPref.edit().apply {
            putString(APP_INSTALLED_TIME, value)
            apply()
        }
    }
    get() = appLevelPref.getString(APP_INSTALLED_TIME, "")

    var isNewVerticalClicked
        get() = userLevelPref.getBoolean(IS_NEW_VERTICAL_CLICKED, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_NEW_VERTICAL_CLICKED, value)
                apply()
            }
        }

    /** A flag to know if this user was new or existing user. */
    var isNewAcquiredUser
        get() = userLevelPref.getBoolean(IS_NEW_ACQUIRED_USER, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_NEW_ACQUIRED_USER, value)
                apply()
            }
        }

    var isShowPermissionDialog
        get() = userLevelPref.getBoolean(IS_NOTIFICATION_ON_HOME_GRANTED, true)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_NOTIFICATION_ON_HOME_GRANTED, value)
                apply()
            }
        }

    var isAppInstall
        get() = userLevelPref.getBoolean(IS_APP_INSTALL, true)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_APP_INSTALL, value)
                apply()
            }
        }

    var pubNubToken: PubNubToken? = null
        set(value) {
            field = value
            val str = Gson().toJson(value)
            userLevelPref.edit().apply {
                putString(PUBNUB_TOKEN, str)
                apply()
            }
        }
        get() {
            if (field == null) {
                field = userLevelPref.getString(PUBNUB_TOKEN, null)?.let {
                    Utils.getObjectFromJson(it)
                }
            }
            return field
        }

    var firebaseVariantQuickPicks : FirebaseVariant? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(FIREBASE_VARIANT_QUICK_PICKS, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(FIREBASE_VARIANT_QUICK_PICKS, Gson().toJson(value))
                    apply()
                }
            }
        }

    var firebaseVariantSimilarCatalog : FirebaseVariant? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(FIREBASE_VARIANT_SIMILAR_CATALOG, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(FIREBASE_VARIANT_SIMILAR_CATALOG, Gson().toJson(value))
                    apply()
                }
            }
        }

    var isSimilarCatalogTabClicked
        get() = userLevelPref.getBoolean(IS_SIMILAR_CATALOG_TAB_CLICKED, false)
        set(value) {
            userLevelPref.edit().apply {
                putBoolean(IS_SIMILAR_CATALOG_TAB_CLICKED, value)
                apply()
            }
        }

    var firebaseVariantGenreBasedTopPodcast : FirebaseVariant? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST, Gson().toJson(value))
                    apply()
                }
            }
        }

    var bottomNavConfigsAos : BottomNavConfigsAos? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(BOTTOM_NAV_CONFIGS, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(BOTTOM_NAV_CONFIGS, Gson().toJson(value))
                    apply()
                }
            }
        }

    var firebaseCatalogQuickFilters : FirebaseVariant? = null
        get() = run {
            if (field == null) {
                val config = appLevelPref.getString(FIREBASE_VARIANT_CATALOG_QUICK_FILTERS, "")
                if (!config.isNullOrEmpty()) {
                    field = Utils.getObjectFromJson(config)
                }
            }
            return@run field
        }
        set(value) {
            field = value
            if (value != null) {
                appLevelPref.edit().apply {
                    putString(FIREBASE_VARIANT_CATALOG_QUICK_FILTERS, Gson().toJson(value))
                    apply()
                }
            }
        }

        var isAutomotiveNewInstallationPromptShown
        get() = appLevelPref.getBoolean(AUTOMOTIVE_NEW_INSTALLATION_PROMPT, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(AUTOMOTIVE_NEW_INSTALLATION_PROMPT, value)
                apply()
            }
        }

    var isAutomotivePremiumDemoPromptShown
        get() = appLevelPref.getBoolean(AUTOMOTIVE_PREMIUM_DEMO_PROMPT, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(AUTOMOTIVE_PREMIUM_DEMO_PROMPT, value)
                apply()
            }
        }

    var isAutomotiveNewInstallationRequireSubs
        get() = appLevelPref.getBoolean(AUTOMOTIVE_NEW_INSTALLATION_REQUIRE_SUBSCRIPTION, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(AUTOMOTIVE_NEW_INSTALLATION_REQUIRE_SUBSCRIPTION, value)
                apply()
            }
        }


    var shouldRecreateActivity
        get() = appLevelPref.getBoolean(SHOULD_RECREATE_ACTIVITY, false)
        set(value) {
            appLevelPref.edit().apply {
                putBoolean(SHOULD_RECREATE_ACTIVITY, value)
                apply()
            }
        }

    /*Update the PrefFlow by this fun, if required StateFlow */
    private fun onState(state: PrefState){
        stateFlow.value = state
    }

    /*If we do need flow for pref change, we can use this state */
    data class PrefState(
        val livePlayerSwipeShowcaseShownRoomsState : ArrayList<String> = livePlayerSwipeShowcaseShownRooms?: arrayListOf()
    )



    private fun clearUserLevelInfo() {
        referralUserId = null
        userIntegrityState = null
        lastAddedQueueContent = null
        userDetails = null
        pendingPurchases = null

        userLevelPref.edit().apply {
            clear()
            apply()
        }
    }
}