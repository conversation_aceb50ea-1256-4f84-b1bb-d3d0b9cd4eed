package noice.app.utils.deeplink

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import dagger.hilt.android.AndroidEntryPoint
import noice.app.modules.onboarding.activity.SplashActivity

@AndroidEntryPoint
class DeepLinkDispatcherActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent) // Update the activity's intent to the new one
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent?.data?.let { uri ->
            // Check if the path should be excluded
            val path = uri.path
            if (isExcludedPath(path)) {
                // For excluded paths, just finish without launching anything
                finish()
                return
            }

            val targetIntent = Intent(this, SplashActivity::class.java).apply { // Target SplashActivity directly
                data = uri // Pass the deep link URI to SplashActivity
                action = Intent.ACTION_VIEW // Maintain action for deep link handling
                // Crucial flags to manage the app's own task stack and preserve state
                // FLAG_ACTIVITY_NEW_TASK: Ensures the app launches in its own task or brings it to foreground.
                // FLAG_ACTIVITY_REORDER_TO_FRONT: Brings existing instance of SplashActivity to front, preserving its state.
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            }
            startActivity(targetIntent)
            finish() // Finish the dispatcher activity immediately
        }?: run {
            // Handle cases where intent data is null (e.g., launched without deep link data, though unlikely for this activity)
            // Potentially launch SplashActivity as a default entry point without deep link data
            val defaultIntent = Intent(this, SplashActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            }
            startActivity(defaultIntent)
            finish()
        }
    }

    /**
     * Check if the given path should be excluded from deep link handling using regex
     * @param path The URI path to check
     * @return true if the path should be excluded, false otherwise
     */
    private fun isExcludedPath(path: String?): Boolean {
        if (path == null) return false

        // Regex pattern to match /tv-login or /car-login paths (with optional trailing segments)
        val excludedPathPattern = Regex("^/(tv-login|car-login)(/.*)?$")
        return excludedPathPattern.matches(path)
    }
}