package noice.app.utils

import android.content.Context
import android.os.Bundle
import com.google.gson.Gson
import com.moengage.core.Properties
import com.moengage.core.analytics.MoEAnalyticsHelper
import com.moengage.inapp.MoEInAppHelper
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.data.DataController
import noice.app.enums.Priority
import noice.app.exoplayer.PlayerDetailFragment.Companion.PORTRAIT
import noice.app.exoplayer.cast.CastManager
import noice.app.model.AdEventCustom
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.media.model.Share
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content

object MoEngageAnalytics {

    fun setUniqueId(context: Context, id: String) {
        //UNIQUE_ID is used to uniquely identify a user.
        MoEAnalyticsHelper.setUniqueId(context, id)
    }

    fun setAttributes(context: Context, isLoggedIn: Boolean) {
        if (isLoggedIn) {
            MoEAnalyticsHelper.setUserAttribute(context, "userId", PrefUtils.userDetails?.id ?: "")

            MoEAnalyticsHelper.setFirstName(context, PrefUtils.userDetails?.displayName ?: "")
            MoEAnalyticsHelper.setUserName(context, PrefUtils.userDetails?.userName ?: "")
            MoEAnalyticsHelper.setUserAttribute(
                context,
                "gender",
                PrefUtils.userDetails?.gender ?: ""
            )

            MoEAnalyticsHelper.setUserAttribute(
                context,
                "city",
                PrefUtils.userDetails?.location?.city ?: ""
            )

            MoEAnalyticsHelper.setUserAttribute(
                context,
                "state",
                PrefUtils.userDetails?.location?.state ?: ""
            )

            MoEAnalyticsHelper.setUserAttribute(
                context,
                "dob",
                PrefUtils.userDetails?.dob ?: ""
            )

            val selectedGenresList = arrayListOf<String>()

            PrefUtils.selectedGenre?.forEach { genre ->
                genre.name?.let { name ->
                    selectedGenresList.add(name)
                }
            }

            val selectedGenres = selectedGenresList.toTypedArray()

            if (selectedGenresList.isNotEmpty()) {
                MoEAnalyticsHelper.setUserAttribute(
                    context,
                    "selectedGenres",
                    selectedGenres
                )
            }
        } else {
            MoEAnalyticsHelper.setUserAttribute(
                context,
                "moe_anonymous_id",
                Utils.getDeviceId()
            )

            MoEAnalyticsHelper.setUserAttribute(context, "guestUserId", PrefUtils.guestId ?: "")
        }
    }

    fun setAttributes(context: Context, key: String, value: Any) {
        MoEAnalyticsHelper.setUserAttribute(
            context,
            key,
            value
        )
    }

    private fun createPropertyObject(properties: Properties = Properties()): Properties {

        if (PrefUtils.isLoggedIn) {
            properties.addAttribute(
                "userId",
                PrefUtils.userDetails?.id ?: ""
            )
            properties.addAttribute(
                "userName",
                PrefUtils.userDetails?.userName ?: ""
            )
            properties.addAttribute(
                "displayName",
                PrefUtils.userDetails?.displayName ?: ""
            )
            properties.addAttribute(
                "gender",
                PrefUtils.userDetails?.gender ?: ""
            )
            properties.addAttribute(
                "city",
                PrefUtils.userDetails?.location?.city ?: ""
            )
            properties.addAttribute(
                "state",
                PrefUtils.userDetails?.location?.state ?: ""
            )
            properties.addAttribute(
                "dob",
                PrefUtils.userDetails?.dob ?: ""
            )
        } else {
            properties.addAttribute(
                "moe_anonymous_id",
                Utils.getDeviceId()
            )
        }

        properties.addAttribute(
            "app version name",
            BuildConfig.VERSION_NAME
        )

        return properties
    }

    /* a common method to send final event to MoEngage */
    private fun sendEventToMoEngage(context: Context, eventName: String, properties: Properties?) {
        // passing empty Properties() object when no parameter is required and it's mandatory
        MoEAnalyticsHelper.trackEvent(context, eventName, properties ?: Properties())
    }

    /* sends a single event with no value to MoEngage */
    fun sendEvent(context: Context, eventName: String) {
        sendEventToMoEngage(context, eventName, null)
    }

    /* sends a single event with key-value to MoEngage */
    fun sendEvent(
        context: Context,
        eventName: String,
        propertyKey: String,
        propertyValue: String?
    ) {
        val properties = createPropertyObject()

        if (!propertyValue.isNullOrEmpty()) {
            properties.addAttribute(propertyKey, propertyValue)
        }

        MoEAnalyticsHelper.trackEvent(context, eventName, properties)
    }

    fun sendEventForRadioDetail(context: Context, eventName: String, channel: Channel?, extraData: ExtraData? = null) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", channel?.type ?: "")
        properties.addAttribute("radio channel title", channel?.title ?: "")
        properties.addAttribute("radio channel id", channel?.id ?: "")

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
            properties.addAttribute("source", extraData.source ?: "")
            properties.addAttribute("catalog link", extraData.catalogLink ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForPodcastDetail(
        context: Context,
        eventName: String,
        source: String?,
        pageSource: String?,
        channel: Channel?,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        if (channel != null) {
            properties.addAttribute("vertical", channel.type ?: "")
            properties.addAttribute("category", "catalog")
            properties.addAttribute("catalog title", channel.title ?: "")
            properties.addAttribute("catalog id", channel.id ?: "")
            properties.addAttribute(
                "genre",
                ArrayList(channel.genres?.map { it.name ?: "" } ?: ArrayList()))
            properties.addAttribute("source", source ?: "")
            properties.addAttribute("segment name", pageSource ?: "")
            properties.addAttribute("catalog source", channel.source ?: "")
            properties.addAttribute("created at", channel.createdAt ?: "")
        }

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
            properties.addAttribute("similar catalog", extraData.similarCatalog ?: "")
            properties.addAttribute("source", extraData.source ?: "")
            properties.addAttribute("content link", extraData.contentLink ?: "")
            properties.addAttribute("catalog link", extraData.catalogLink ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForLiveDetail(context: Context, eventName: String, liveRoom: LiveRoom?, extraData: ExtraData? = null) {
        val properties = createPropertyObject()

        if (liveRoom != null) {
            properties.addAttribute("vertical", "live room")
            properties.addAttribute("live room title", liveRoom.title ?: "")
            properties.addAttribute("live room id", liveRoom.id ?: "")
            properties.addAttribute("live host username", liveRoom.host?.userName ?: "")
            properties.addAttribute("live host user id", liveRoom.host?.id ?: "")
            properties.addAttribute("live schedule", liveRoom.scheduledTime ?: "")
            properties.addAttribute("segment name", "Noice Live")
        }

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForCatalogDetail(
        context: Context,
        eventName: String,
        source: String?,
        pageSource: String?,
        episode: Content?,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        if (episode != null) {
            properties.addAttribute(
                "vertical",
                episode.catalog?.type ?: episode.entitySubType ?: ""
            )
            properties.addAttribute("category", "content")
            properties.addAttribute(
                "catalog title",
                episode.catalogTitle ?: episode.catalog?.title ?: ""
            )
            properties.addAttribute("content title", episode.title ?: "")
            properties.addAttribute("catalog id", episode.catalogId ?: episode.catalog?.id ?: "")
            properties.addAttribute("content id", episode.id ?: "")
            properties.addAttribute(
                "genre",
                ArrayList(episode.genres?.map { it.name ?: "" } ?: ArrayList()))
            properties.addAttribute("source", source ?: "")
            properties.addAttribute("segment name", pageSource ?: "")
            properties.addAttribute(
                "catalog source",
                episode.source ?: episode.catalog?.source ?: ""
            )
            properties.addAttribute("created at", episode.publishedAt ?: "")

            if (extraData != null) {
                properties.addAttribute("segment id", extraData.segmentId ?: "")
                properties.addAttribute("segment name", extraData.segmentName ?: "")
                properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
                properties.addAttribute("entity position", extraData.entityPosition ?: -1)
                properties.addAttribute("search Term", extraData.searchTerm ?: "")
                properties.addAttribute("source", extraData.source ?: "")
                properties.addAttribute("content link", extraData.contentLink ?: "")
                properties.addAttribute("catalog link", extraData.catalogLink ?: "")
                properties.addAttribute("is content premium", extraData.isContentPremium ?: "")
            }
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForCatalogStarted(
        context: Context,
        eventName: String,
        startTime: String?,
        data: ExoNotificationData?,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", data?.entitySubType ?: "")
        properties.addAttribute("catalog title", data?.catalogTitle ?: "")
        properties.addAttribute("content title", data?.title ?: "")
        properties.addAttribute("catalog id", data?.catalogId ?: "")
        properties.addAttribute("content id", data?.id ?: "")
        properties.addAttribute("content duration", data?.duration.toString())
        properties.addAttribute("source", data?.pageSource?.lowercase() ?: "")
        properties.addAttribute(
            "genre",
            ArrayList(data?.genres?.map { it.name ?: "" } ?: ArrayList()))
        //putString("no. of contents in catalog")
        properties.addAttribute("catalog source", data?.source ?: "")

        if (data?.isDownloaded == true) {
            properties.addAttribute("stream type", "downloaded")
        } else {
            properties.addAttribute("stream type", "streamed")
        }

        val contentFormat = if (DataController.isPlayingVideo) {
            if (data?.orientation == PORTRAIT) {
                "vertical video"
            } else {
                "video"
            }
        } else {
            "audio"
        }
        properties.addAttribute("content format", contentFormat)

        properties.addAttribute(
            "network type",
            NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext())
        )
        properties.addAttribute("start time", startTime ?: "")

        if (data?.startType == Priority.MANUAL.type) {
            properties.addAttribute("start type", "manual")
        } else {
            properties.addAttribute("start type", "autoplay")
        }

        properties.addAttribute("created at", data?.publishedAt ?: "")

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
            properties.addAttribute("similar catalog", extraData.similarCatalog ?: "")
            properties.addAttribute("source", extraData.source ?: "")
            properties.addAttribute("content link", extraData.contentLink ?: "")
            properties.addAttribute("catalog link", extraData.catalogLink ?: "")
            properties.addAttribute("is content premium", extraData.isContentPremium ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForRadio(
        context: Context,
        eventName: String,
        durationPlayed: String?,
        data: ExoNotificationData,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", data.entitySubType ?: "")
        properties.addAttribute("radio channel title", data.catalogTitle ?: "")
        properties.addAttribute("radio program title", data.title ?: "")
        properties.addAttribute("radio channel id", data.catalogId ?: "")
        properties.addAttribute("radio program id", data.id ?: "")
        if (eventName == "listening ended")
            properties.addAttribute("duration played", durationPlayed ?: "")
        properties.addAttribute(
            "network type",
            NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext())
        )

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
            properties.addAttribute("source", extraData.source ?: "")
            properties.addAttribute("content link", extraData.contentLink ?: "")
            properties.addAttribute("catalog link", extraData.catalogLink ?: "")
            properties.addAttribute("is content premium", extraData.isContentPremium ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendLiveEvent(context: Context, eventName: String, room: LiveRoom, extraData: ExtraData? = null) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", "live room")
        properties.addAttribute("live room title", room.title ?: "")
        properties.addAttribute("live room id", room.id ?: "")
        properties.addAttribute("live host username", room.roomHost?.user?.userName ?: "")
        properties.addAttribute("live host user id", room.roomHost?.user?.id ?: "")
        properties.addAttribute("live room schedule", room.scheduledTime ?: "")
        properties.addAttribute("network type", NetworkUtils.getNetworkType(context))

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForCatalogEnded(
        context: Context,
        eventName: String,
        stopTime: String?,
        durationPlayed: String?,
        perListened: Int?,
        data: ExoNotificationData?,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", data?.entitySubType ?: "")
        properties.addAttribute("catalog title", data?.catalogTitle ?: "")
        properties.addAttribute("content title", data?.title ?: "")
        properties.addAttribute("catalog id", data?.catalogId ?: "")
        properties.addAttribute("content id", data?.id ?: "")
        properties.addAttribute("content duration", data?.duration.toString())
        properties.addAttribute("source", data?.pageSource?.lowercase() ?: "")
        properties.addAttribute(
            "genre",
            ArrayList(data?.genres?.map { it.name ?: "" } ?: ArrayList()))
        //putString("no. of contents in catalog")
        properties.addAttribute("catalog source", data?.source ?: "")

        if (data?.isDownloaded == true) {
            properties.addAttribute("stream type", "downloaded")
        } else {
            properties.addAttribute("stream type", "streamed")
        }

        properties.addAttribute(
            "network type",
            NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext())
        )
        properties.addAttribute("stop time", stopTime ?: "")
        properties.addAttribute("duration played", durationPlayed ?: "")
        properties.addAttribute("percentage listened", perListened ?: 0)

        if (data?.startType == Priority.MANUAL.type) {
            properties.addAttribute("start type", "manual")
        } else {
            properties.addAttribute("start type", "autoplay")
        }

        properties.addAttribute("created at", data?.publishedAt ?: "")

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
            properties.addAttribute("similar catalog", extraData.similarCatalog ?: "")
            properties.addAttribute("source", extraData.source ?: "")
            properties.addAttribute("content link", extraData.contentLink ?: "")
            properties.addAttribute("catalog link", extraData.catalogLink ?: "")
            properties.addAttribute("is content premium", extraData.isContentPremium ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForLiveEnded(
        context: Context,
        eventName: String,
        durationPlayed: Long,
        room: LiveRoom,
        extraData: ExtraData? = null
    ) {
        val properties = createPropertyObject()

        properties.addAttribute("vertical", "live room")
        properties.addAttribute("duration played", (durationPlayed / 1000).toString())
        properties.addAttribute("network type", NetworkUtils.getNetworkType(context))
        properties.addAttribute("live room title", room.title ?: "")
        properties.addAttribute("live room id", room.id ?: "")
        properties.addAttribute("live host username", room.roomHost?.user?.userName ?: "")
        properties.addAttribute("live host user id", room.roomHost?.user?.id ?: "")
        properties.addAttribute("live room schedule", room.scheduledTime ?: "")
        properties.addAttribute("live stop trigger", "leave live room")

        if (extraData != null) {
            properties.addAttribute("segment id", extraData.segmentId ?: "")
            properties.addAttribute("segment name", extraData.segmentName ?: "")
            properties.addAttribute("segment position", extraData.segmentPosition ?: -1)
            properties.addAttribute("entity position", extraData.entityPosition ?: -1)
            properties.addAttribute("search Term", extraData.searchTerm ?: "")
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForClipStarted(context: Context, eventName: String, clip: HomeContent) {
        val properties = createPropertyObject()

        properties.addAttribute("clip title", clip.title ?: "")
        properties.addAttribute("clip id", clip.id)
        properties.addAttribute(
            "clip duration",
            clip.duration.toString()
        ) // at this point clipDuration variable has next audio duration hence this
        properties.addAttribute("catalog title", clip.content?.catalog?.title ?: "")
        properties.addAttribute("content title", clip.content?.title ?: "")
        properties.addAttribute("catalog id", clip.content?.catalog?.id ?: "")
        properties.addAttribute("content id", clip.content?.id ?: "")
        properties.addAttribute("catalog source", clip.content?.source ?: "")
        properties.addAttribute("vertical", clip.content?.catalog?.type ?: "")
        properties.addAttribute(
            "genre",
            ArrayList(clip.genres?.map { it.name ?: "" } ?: ArrayList()))
        //putString("source")
        properties.addAttribute("duration played", clip.duration.toString())
        properties.addAttribute("percentage listened", 100)
        properties.addAttribute("network type", NetworkUtils.getNetworkType(context))

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForClipEnded(context: Context, eventName: String, clip: HomeContent) {
        val properties = createPropertyObject()

        properties.addAttribute("clip title", clip.title ?: "")
        properties.addAttribute("clip id", clip.id)
        properties.addAttribute(
            "clip duration",
            clip.duration.toString()
        ) // at this point clipDuration variable has next audio duration hence this
        properties.addAttribute("catalog title", clip.content?.catalog?.title ?: "")
        properties.addAttribute("content title", clip.content?.title ?: "")
        properties.addAttribute("catalog id", clip.content?.catalog?.id ?: "")
        properties.addAttribute("content id", clip.content?.id ?: "")
        properties.addAttribute("catalog source", clip.content?.source ?: "")
        properties.addAttribute("vertical", clip.content?.catalog?.type ?: "")
        properties.addAttribute(
            "genre",
            ArrayList(clip.genres?.map { it.name ?: "" } ?: ArrayList()))
        //putString("source")
        properties.addAttribute("duration played", clip.duration.toString())
        properties.addAttribute("percentage listened", 100)
        properties.addAttribute("network type", NetworkUtils.getNetworkType(context))

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEvent(context: Context, eventName: String, bundle: Bundle = Bundle()) {
        val properties = createPropertyObject()

        bundle.keySet().forEach {
            it?.let { key ->
                properties.addAttribute(key, bundle[it])
            }
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEvent(context: Context, eventName: String, properties: Properties = Properties()) {
        createPropertyObject()
        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForRadioShare(
        context: Context,
        eventName: String,
        channel: String?,
        share: Share?,
        packageName: String? = null
    ) {
        val properties = createPropertyObject()

        val source: String = if (channel == Constants.INSTAGRAM_STORIES && share?.isFromMediaPlayer == true) {
            "media player"
        } else {
            "detail page"
        }
        properties.addAttribute("vertical", "radio")
        properties.addAttribute("radio title", share?.channel?.title ?: "")
        properties.addAttribute("radio id", share?.channel?.id ?: "")
        properties.addAttribute("source", source)
        properties.addAttribute("channel", channel ?: "")

        if (packageName != null) {
            properties.addAttribute("native channel", packageName)
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForContentShare(
        context: Context,
        eventName: String,
        entitySubType: String?,
        channel: String?,
        share: Share?,
        shareType: Int,
        packageName: String? = null
    ) {
        val properties = createPropertyObject()

        val source: String = if (channel == Constants.INSTAGRAM_STORIES && share?.isFromMediaPlayer == true) {
            "media player"
        } else {
            "detail page"
        }

        properties.addAttribute("vertical", entitySubType ?: "")
        properties.addAttribute("source", source)
        properties.addAttribute("channel", channel ?: "")

        if (shareType == ShareDialog.TYPE_CHANNEL) {
            properties.addAttribute("category", "catalog")
            properties.addAttribute("catalog title", share?.channel?.title ?: "")
            properties.addAttribute("catalog id", share?.channel?.id ?: "")
            properties.addAttribute(
                "genre",
                ArrayList(share?.channel?.genres?.map { it.name ?: "" } ?: ArrayList()))
            properties.addAttribute("catalog source", share?.channel?.source ?: "")
            properties.addAttribute("created at", share?.channel?.createdAt ?: "")
        } else {
            properties.addAttribute("category", "content")
            properties.addAttribute(
                "catalog title",
                share?.content?.catalogTitle ?: share?.content?.catalog?.title ?: ""
            )
            properties.addAttribute("content title", share?.content?.title ?: "")
            properties.addAttribute(
                "catalog id",
                share?.content?.catalogId ?: share?.content?.catalog?.id ?: ""
            )
            properties.addAttribute("content id", share?.content?.id ?: "")
            properties.addAttribute(
                "genre",
                ArrayList(share?.content?.genres?.map { it.name ?: "" } ?: ArrayList()))
            properties.addAttribute("catalog source", share?.content?.source ?: "")
            properties.addAttribute("created at", share?.content?.createdAt ?: "")
        }

        if (packageName != null) {
            properties.addAttribute("native channel", packageName)
        }

        val catalogLink = "${Constants.WEB_BASE_URL}catalog/${share?.exoNotificationData?.catalogId ?: share?.content?.catalogId ?: share?.content?.catalog?.id ?: ""}"
        properties.addAttribute("content link", share?.deepLink ?: "")
        properties.addAttribute("catalog link", catalogLink ?: "")

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendEventForLiveRoom(context: Context, eventName: String, channel: String?, share: Share?, packageName: String? = null) {
        val properties = createPropertyObject()

        val source: String = if (channel == Constants.INSTAGRAM_STORIES && share?.isFromMediaPlayer == true) {
            "media player"
        } else {
            "detail page"
        }
        properties.addAttribute("live room title", share?.liveRoom?.title ?: "")
        properties.addAttribute("live room id", share?.liveRoom?.id ?: "")
        properties.addAttribute(
            "live host username",
            share?.liveRoom?.roomHost?.user?.userName ?: ""
        )
        properties.addAttribute("live host user id", share?.liveRoom?.roomHost?.userId ?: "")
        properties.addAttribute("live schedule", share?.liveRoom?.scheduledTime ?: "")
        properties.addAttribute("source", source)
        properties.addAttribute("channel", channel ?: "")

        if (packageName != null) {
            properties.addAttribute("native channel", packageName)
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun sendAdEvent(
        context: Context,
        eventName: String,
        adEvent: AdEventCustom?,
        currentData: ExoNotificationData?,
        adMediaType: String? = null,
        isPlayedPreview: Boolean? = false
    ) {
        val adConfig = MediaUtil.getAdConfigFor(currentData?.entitySubType ?: "")

        val properties = createPropertyObject()

        properties.addAttribute("ad id", adEvent?.ad?.adId ?: "")
        properties.addAttribute("ad duration", adEvent?.ad?.duration ?: 0.0)
        properties.addAttribute("ad url", adConfig?.url ?: "")
        properties.addAttribute("vertical", currentData?.entitySubType.toString())
        properties.addAttribute("content id", currentData?.id.toString())
        properties.addAttribute("content title", currentData?.title ?: "")
        properties.addAttribute("content duration", currentData?.duration ?: 0L)
        properties.addAttribute("catalog id", currentData?.catalogId ?: "")
        properties.addAttribute("catalog title", currentData?.catalogTitle ?: "")
        properties.addAttribute("catalog source", currentData?.source ?: "")
        properties.addAttribute("ad media type", adMediaType ?: "")
        properties.addAttribute("ad type", Utils.getPlayingAdMediaType(adEvent?.ad?.adPodInfo?.podIndex) ?: "")
        properties.addAttribute("ad start time", adEvent?.ad?.adPodInfo?.timeOffset)
        properties.addAttribute("no of ads", adEvent?.ad?.adPodInfo?.totalAds.toString())
        properties.addAttribute("ad position", adEvent?.ad?.adPodInfo?.adPosition.toString())
        isPlayedPreview?.let {
            properties.addAttribute("isPlayedPreview", isPlayedPreview)
        }

        sendEventToMoEngage(context, eventName, properties)
    }

    fun setOrResetInAppContext(contextValueSet: Set<String>?) {
        MoEInAppHelper.getInstance().resetInAppContext()
        if (contextValueSet != null) {
            MoEInAppHelper.getInstance().setInAppContext(contextValueSet)
        }
    }

    fun Properties.putAnalyticsKey(key : String, value: Any?): Properties {
        if (value == null || (value is String && value.isEmpty())) {
            return this
        }
        var data = value
        if (value is List<*>) {
            if (value.isEmpty()) {
                return this
            } else {
                data = Gson().toJson(value)
            }
        }
        addAttribute(key, data)
        return this
    }

    fun sendEventContentPaused(
        context: Context,
        eventName: String,
        entitySubType: String,
        entityId: String?,
        parentId: String?,
        pageSource: String?,
        durationPlayed: String? = "",
        totalDuration: String? = "",
        listeningTime: String = "0",
        catalogTitle: String = "",
        contentTitle: String = "",
        isPause: Boolean = false,
        stremType: String = "",
        networkType: String = "",
        contentFormat: String? = "",
        trigger: String,
        source: String? = "",
        extraData: ExtraData? = null,
        playButtonPosition: String? = "",
        isPlayedPreview: Boolean?
    ) {
        sendEvent(context, eventName, Properties().apply {
            putAnalyticsKey("sessionId", PrefUtils.uniqueId.toString())
            putAnalyticsKey("source", pageSource)
            putAnalyticsKey("vertical", entitySubType)
            putAnalyticsKey("contentId", entityId)
            putAnalyticsKey("catalogId", parentId)
            putAnalyticsKey("catalogTitle", catalogTitle)
            if (isPause) {
                putAnalyticsKey("durationPlayed", listeningTime)
            }
            if (CastManager.isCastConnected) {
                putAnalyticsKey("castPlatform", "chromecast")
                putAnalyticsKey("devicePlatform", CastManager.castDevice?.name.orEmpty())
            } else {
                putAnalyticsKey("castPlatform", "null")
            }
            putAnalyticsKey("contentDuration", totalDuration)
            putAnalyticsKey("elapsedTime", durationPlayed)
            putAnalyticsKey("entitySubType", entitySubType)
            putAnalyticsKey("contentTitle", contentTitle)
            putAnalyticsKey("stream_type", stremType)
            putAnalyticsKey("networkType", networkType)
            putAnalyticsKey("contentFormat", contentFormat)
            putAnalyticsKey("trigger", trigger)
            putAnalyticsKey(
                "networkType",
                NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext())
            )
            putAnalyticsKey("extend_session", DataController.extendSession)

            if (!source.isNullOrEmpty())
                putAnalyticsKey("catalogSource", source)

            if (extraData != null) {
                putAnalyticsKey("segmentId", extraData.segmentId ?: "")
                putAnalyticsKey("segmentName", extraData.segmentName ?: "")
                putAnalyticsKey("segmentPosition", extraData.segmentPosition ?: -1)
                putAnalyticsKey("searchTerm", extraData.searchTerm ?: "")
                putAnalyticsKey("tileName", extraData.title ?: "")
                putAnalyticsKey("tabName", extraData.tabName ?: "")
                putAnalyticsKey("ranking", extraData.ranking ?: -1)
                putAnalyticsKey("similarCatalog", extraData.similarCatalog ?: "")
            }

            putAnalyticsKey("play_button_position", playButtonPosition)
            if (isPlayedPreview != null) {
                putAnalyticsKey("IsPlayedPreview", "$isPlayedPreview")
            }
        })
    }
}