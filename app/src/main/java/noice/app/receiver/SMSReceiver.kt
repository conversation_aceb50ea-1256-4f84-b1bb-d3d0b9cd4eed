package noice.app.receiver
/**
 * Created by Atiq on 09-11-2020.
 */
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status
import java.util.regex.Pattern


class SMSReceiver :BroadcastReceiver() {

    private var otpListener: OTPReceiveListener? = null

    /**
     * @param otpListener
     */
    fun setOTPListener(otpListener: OTPReceiveListener) {
        this.otpListener = otpListener
    }


    /**
     * @param context
     * @param intent
     */
    override fun onReceive(context: Context, intent: Intent) {
        if (SmsRetriever.SMS_RETRIEVED_ACTION == intent.action) {
            val extras = intent.extras
            val status = extras!!.get(SmsRetriever.EXTRA_STATUS) as Status
            when (status.statusCode) {

                CommonStatusCodes.SUCCESS -> {

                    //This is the full message
                    val message = extras.get(SmsRetriever.EXTRA_SMS_MESSAGE) as String
                    /*<#> Your Noice code is: 123456
                    jon3Ho1gGYQ*/
                    Log.d("SMSReceiver - ", "message$message")
                    //Extract the OTP code and send to the listener

                    if (otpListener != null) {
                        val code = getFourDigitNumber(message)
                        otpListener?.onOTPReceived(code)
                    }
                }
                CommonStatusCodes.TIMEOUT ->
                    // Waiting for SMS timed out (5 minutes)
                    if (otpListener != null) {
                        otpListener!!.onOTPTimeOut()
                    }

                CommonStatusCodes.API_NOT_CONNECTED ->

                    if (otpListener != null) {
                        otpListener!!.onOTPReceivedError("API NOT CONNECTED")
                    }

                CommonStatusCodes.NETWORK_ERROR ->

                    if (otpListener != null) {
                        otpListener!!.onOTPReceivedError("NETWORK ERROR")
                    }

                CommonStatusCodes.ERROR ->

                    if (otpListener != null) {
                        otpListener!!.onOTPReceivedError("SOME THING WENT WRONG")
                    }
            }
        }
    }

    /**
     *
     */
    interface OTPReceiveListener {

        fun onOTPReceived(otp: String)

        fun onOTPTimeOut()

        fun onOTPReceivedError(error: String)
    }

    private fun getFourDigitNumber(data: String):String{
        val pattern = Pattern.compile("(\\d{4})")

//   \d is for a digit
//   {} is the number of digits here 4.

        val matcher = pattern.matcher(data)
        var digit = ""
        if (matcher.find()) {
            digit = matcher.group(0) ?: ""  // 4 digit number
        }
        return digit
    }
}