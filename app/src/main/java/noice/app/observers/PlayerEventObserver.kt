package noice.app.observers

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.Player
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import noice.app.model.AdEventCustom
import noice.app.player.models.PlayerEvent

class PlayerEventObserver {

    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    //All Player Events
    private val playerEvents = MutableSharedFlow<PlayerEvent>()
    //AD Event
    private val adsEvent = MutableSharedFlow<AdEventCustom>()
    //Cast Events
    private val castEvent = MutableSharedFlow<Pair<Player,Boolean>>()
    // Progress update
    private val progressEvent = MutableSharedFlow<Long>()
    // click event for preview completed
    private val previewEventClick = MutableSharedFlow<String>()


    fun invokePreviewClickEvent(source:String) {
        ioScope.launch {
            previewEventClick.emit(source)
        }
    }
    fun subscribePreviewClickEvents(lifecycleOwner: LifecycleOwner, action: suspend (String) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            previewEventClick.collectLatest(action)
        }
    }


    fun invokePlayerEvent(event: PlayerEvent) {
        ioScope.launch {
            playerEvents.emit(event)
        }
    }

    fun subscribePlayerEvents(lifecycleOwner: LifecycleOwner, list: List<String> = listOf(), action: suspend (PlayerEvent) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            playerEvents.filter {
                getFilter(it.event, list)
            }.collectLatest(action)
        }
    }

    suspend fun subscribePlayerEvents(list: List<String> = listOf(), action: suspend (PlayerEvent) -> Unit) {
        playerEvents.filter {
            getFilter(it.event, list)
        }.collectLatest(action)
    }

    //Player Music Button Event
    private val uiEvents = MutableSharedFlow<PlayerEvent>()
    private var lastEvent = Pair("", "")

    fun invokeMusicButtonEvent(event: PlayerEvent) {
        if (lastEvent.first == event.exoData?.id && lastEvent.second == event.event) {
            //This is to block duplicate immediate UI events
            return
        }
        lastEvent = Pair(event.exoData?.id.toString(), event.event)
        ioScope.launch {
            uiEvents.emit(event)
        }
    }

    fun subscribeMusicButtonEvents(lifecycleOwner: LifecycleOwner, action: suspend (PlayerEvent) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            uiEvents.collectLatest(action)
        }
    }

    suspend fun subscribeMusicButtonEvents(action: suspend (PlayerEvent) -> Unit) {
        uiEvents.collectLatest(action)
    }

    suspend fun subscribeMusicButtonEventsFor(contentId: String, action: suspend (PlayerEvent) -> Unit) {
        uiEvents.filter {
            it.exoData?.id == contentId
        }.collectLatest(action)
    }


    fun invokeProgressEventEvent(event: Long) {
        ioScope.launch {
            progressEvent.emit(event)
        }
    }

    fun subscribeProgressEvent(lifecycleOwner: LifecycleOwner, action: suspend (Long) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            progressEvent.collectLatest(action)
        }
    }


    fun invokeAdsEvent(event: AdEventCustom) {
        ioScope.launch {
            adsEvent.emit(event)
        }
    }

    fun subscribeAdsEvent(lifecycleOwner: LifecycleOwner, action: suspend (AdEventCustom) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            adsEvent.collectLatest(action)
        }
    }
    fun invokeCastEvents(player: Player,isCast:Boolean = false) {
        ioScope.launch {
            castEvent.emit(Pair(player,isCast))
        }
    }
    fun subscribeForCastButton(lifecycleOwner: LifecycleOwner, action: suspend (Pair<Player,Boolean>) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            castEvent.collectLatest(action)
        }
    }

    //Common
    private fun getFilter(event: String, list: List<String>) = if (list.isNotEmpty()) {
        event in list
    } else {
        true
    }
}