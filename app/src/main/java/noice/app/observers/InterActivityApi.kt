package noice.app.observers

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.launch

/**
 * This class can be used to get some data from one component into other such as from
 * one activity to another.
 * We need to call subscribeApis method in the activity or component from where we need
 * data and invoke function in which we need the data.
 * It will be same as API call
 */
class InterActivityApi {

    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val api = MutableSharedFlow<ApiCall<*>>()

    fun <T> invoke(call: ApiCall<T>) {
        ioScope.launch {
            api.emit(call)
        }
    }

    fun <T> subscribeApis(lifecycleOwner: LifecycleOwner, path: String, action: (ApiCall<T>) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            api.filterIsInstance<ApiCall<T>>().filter {
                it.path.startsWith(path)
            }.collectLatest {
                it.path = it.path.split("/")[1]
                action.invoke(it)
            }
        }
    }

    class ApiCall<T>(
        var path: String,
        private val listener: (T?) -> Unit
    ) {
        fun sendResult(result: T?) {
            listener.invoke(result)
        }
    }
}