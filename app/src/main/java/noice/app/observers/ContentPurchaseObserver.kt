package noice.app.observers

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import noice.app.modules.podcast.model.Content

class ContentPurchaseObserver {

    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    private val purchaseEvents = MutableSharedFlow<Any?>()

    fun invokeEvent(event: Any?) {
        ioScope.launch {
            purchaseEvents.emit(event)
        }
    }

    fun subscribe(lifecycleOwner: LifecycleOwner, action: suspend (Any?) -> Unit) {
        lifecycleOwner.lifecycle.coroutineScope.launch {
            purchaseEvents.collectLatest(action)
        }
    }
}
