package noice.app.listner

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.moengage.inapp.listeners.OnClickActionListener
import com.moengage.inapp.model.ClickData
import com.moengage.inapp.model.actions.Action
import com.moengage.inapp.model.actions.CustomAction
import com.moengage.inapp.model.actions.NavigationAction
import com.moengage.inapp.model.enums.ActionType
import noice.app.model.eventbus.EventMessage
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.MediaAction
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.IN_APP_FOLLOW_CLICKED
import noice.app.utils.PrefUtils
import noice.app.utils.ShareUtils
import noice.app.utils.Utils
import org.greenrobot.eventbus.EventBus

class InAppClickListener(val context: Context) : OnClickActionListener {
    //Callback triggered whenever user clicks on any UI widget of the In-App with navigation actionType
    override fun onClick(clickData: ClickData): Boolean {
        val action: Action = clickData.action
        if (action.actionType === ActionType.CUSTOM_ACTION) {
            val customClickValue = (action as CustomAction).keyValuePairs
            if (!customClickValue.isNullOrEmpty() && customClickValue.containsKey("actionType")) {
                if (customClickValue.containsKey("actionValue") && customClickValue.containsKey("entityType") && customClickValue.containsKey("entityId") && customClickValue.containsKey("entitySubType")) {
                    val mediaAction = if (PrefUtils.isLoggedIn) {
                        MediaAction(
                            customClickValue["actionType"].toString(),
                            customClickValue["actionValue"].toString().toDouble(),
                            customClickValue["entityId"].toString(),
                            customClickValue["entityType"].toString(),
                            PrefUtils.userDetails?.id.toString(),
                            customClickValue["entityType"].toString(),
                            null
                        )
                    } else {
                        MediaAction(
                            customClickValue["actionType"].toString(),
                            customClickValue["actionValue"].toString().toDouble(),
                            customClickValue["entityId"].toString(),
                            customClickValue["entityType"].toString(),
                            PrefUtils.guestId,
                            customClickValue["entityType"].toString(),
                            null
                        )
                    }
                    EventBus.getDefault().post(EventMessage(mediaAction, IN_APP_FOLLOW_CLICKED))
                }
            }
            return false
        } else if (action.actionType === ActionType.NAVIGATE) {
            val keyValue = (action as NavigationAction).keyValuePairs

            if (!keyValue.isNullOrEmpty() && keyValue.containsKey("entitySubType")) {
                if (keyValue.containsKey("entityType") && keyValue.containsKey("entityId")) {
                    val mediaAction = Utils.getIndexEventAction(
                        keyValue["entityType"].toString(),
                        keyValue["entitySubType"].toString()
                    )
                    EventBus.getDefault().post(OpenIndexEvent(mediaAction, keyValue["entityId"]))
                } else {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_VERTICALS_SCREEN,
                            keyValue["entitySubType"]
                        )
                    )
                }
            } else {
                val url = (clickData.action as NavigationAction).navigationUrl
                if (url.isNotEmpty()) {
                    val uri = Uri.parse(url)
                    when (uri.host) {
                        "play.google.com" -> {
                            val pn = uri.getQueryParameter("id").toString()
                            ShareUtils.redirectToPlayStore(context, pn)
                        }
                        else -> {
                            EventBus.getDefault().post(EventMessage(Intent().apply {
                                data = uri
                            }, Constants.MO_IN_APP_DEEPLINK))
                        }
                    }
                }
            }
            return false
        }

        return true
    }
}