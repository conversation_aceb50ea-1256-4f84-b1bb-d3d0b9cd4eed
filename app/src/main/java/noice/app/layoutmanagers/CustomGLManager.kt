package noice.app.layoutmanagers

import android.content.Context
import android.util.Log
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

class CustomGLManager(
    context: Context,
    spanCount: Int,
    @RecyclerView.Orientation orientation: Int,
    reverseLayout: <PERSON>olean
) : GridLayoutManager(context, spanCount, orientation, reverseLayout) {
    override fun onLayoutChildren(recycler: RecyclerView.Recycler?, state: RecyclerView.State?) {
        try {
            super.onLayoutChildren(recycler, state)
        } catch (e: IndexOutOfBoundsException) {
            Log.e("Exception", e.message.toString())
        }
    }

    override fun supportsPredictiveItemAnimations(): Boolean {
        return false
    }
}