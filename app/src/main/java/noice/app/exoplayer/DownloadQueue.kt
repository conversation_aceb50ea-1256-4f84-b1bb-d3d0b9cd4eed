package noice.app.exoplayer

import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.offline.Download
import kotlinx.coroutines.Job
import noice.app.BaseApplication
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.podcast.model.Content
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.ServerInterface
import noice.app.utils.Constants
import noice.app.utils.PrefUtils
import org.greenrobot.eventbus.EventBus

@OptIn(UnstableApi::class)
class DownloadQueue {

    companion object {
        const val STATE_IDLE = -1
    }

    private var downloadQueue = ArrayList<Content>()
    private var completedQueue = ArrayList<Content>()
    private var queueState = STATE_IDLE
    private var downloadListener : ExoPlayerDownloadTracker.DownloadProgressListener? = null
    private var contentJob : Job? = null

    fun addDownloads(downloadQueue : ArrayList<Content>,source:String) {
        downloadQueue.forEach { content ->
            if (!this.downloadQueue.contains(content) && !completedQueue.contains(content)) {
                this.downloadQueue.add(content)

                if (queueState == STATE_IDLE || queueState == Download.STATE_COMPLETED) {
                    queueState = Download.STATE_DOWNLOADING
                    getContentAndSetListener(source)
                }
            }
        }
    }

    fun getDownloadList():ArrayList<Content>{
        return downloadQueue
    }

    private fun getContentAndSetListener(source:String) {
        if (downloadQueue.isNotEmpty()) {
            if (!downloadQueue[0].id.isNullOrEmpty() && downloadQueue[0].meta != null) {
                startDownload(source)
            } else if (!downloadQueue[0].id.isNullOrEmpty()) {
                val map = HashMap<String, String>()
                contentJob = BaseApplication.doServerCall({ NetworkRequests.getEpisodeDetails(downloadQueue[0].id!!, map) },
                    object : ServerInterface<BaseModel<Content>> {

                        override fun onCustomError(e: ApiError) {
                            handleNextDownload(source)
                        }

                        override fun onError(e: Throwable) {
                            handleNextDownload(source)
                        }

                        override fun onSuccess(data: BaseModel<Content>, dataFromCache: Boolean) {
                            data.data?.let { content ->
                                downloadQueue[0] = content
                                startDownload(source)
                            }
                        }
                    })
            } else {
                handleNextDownload(source)
            }
        }
    }

    private fun startDownload(source:String) {
        if (!BaseApplication.application.getDownloadTracker().isDownloaded(downloadQueue[0].id, downloadQueue[0].url)) {
            BaseApplication.application.getDownloadTracker().download(
                null,
                downloadQueue[0],
                source
            )
            setDownloadListener(source)
        } else {
            handleNextDownload(source)
        }
    }

    private fun setDownloadListener(source: String) {
        if (!downloadQueue[0].id.isNullOrEmpty()) {
            downloadListener = object : ExoPlayerDownloadTracker.DownloadProgressListener(downloadQueue[0].id ?: "") {
                override fun onDownloadsChanged(download: Download) {
                    if (download.state == Download.STATE_COMPLETED) {
                        EventBus.getDefault().post(EventMessage(downloadQueue[0], Constants.SHOW_DOWNLOAD))

                        handleNextDownload(source)
                    }
                }

                override fun onProgressChanged(download: Download) {

                }
            }

            downloadListener?.let {
                BaseApplication.application.getDownloadTracker().addListener(it)
            }
        }
    }

    private fun handleNextDownload(source:String) {
        downloadListener?.let { listener ->
            BaseApplication.application.getDownloadTracker().removeListener(listener)
        }

        if (downloadQueue.isNotEmpty()) {
            completedQueue.add(downloadQueue[0])
            downloadQueue.removeAt(0)

            if (downloadQueue.isNotEmpty()) {
                getContentAndSetListener(source)
            } else {
                queueState = Download.STATE_COMPLETED
                PrefUtils.showRestoreDownload = false
            }
        } else {
            queueState = Download.STATE_COMPLETED
            PrefUtils.showRestoreDownload = false
        }
    }

    fun release() {
        contentJob?.cancel()
        contentJob = null
    }
}