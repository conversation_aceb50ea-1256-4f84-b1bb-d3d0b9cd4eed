package noice.app.exoplayer

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.content.res.Resources
import android.graphics.Color
import android.media.AudioManager
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.View.GONE
import android.view.View.INVISIBLE
import android.view.View.OVER_SCROLL_NEVER
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.Window
import android.view.animation.AccelerateInterpolator
import android.view.animation.AnimationSet
import android.view.animation.AnimationUtils
import android.webkit.WebView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.core.view.children
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.PlayerControlView
import androidx.media3.ui.PlayerNotificationManager
import androidx.media3.ui.PlayerView
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.google.ads.interactivemedia.v3.api.AdError
import com.google.ads.interactivemedia.v3.api.AdEvent
import com.google.ads.interactivemedia.v3.api.player.VideoProgressUpdate
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.firebase.firestore.ListenerRegistration
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.CustomTimeBar
import noice.app.R
import noice.app.data.DataController
import noice.app.data.DataController.sleepTime
import noice.app.databinding.CustomPlayerFragmentBinding
import noice.app.enums.UserAction
import noice.app.exoplayer.ExoplayerUtils.mediaDurationMs
import noice.app.exoplayer.PlayerDetailFragment.Companion.PORTRAIT
import noice.app.exoplayer.cast.CastBottomSheetFragment
import noice.app.exoplayer.cast.CastManager
import noice.app.layoutmanagers.CustomGLManager
import noice.app.layoutmanagers.CustomLLManager
import noice.app.listner.ActionHandler
import noice.app.listner.IPlayerActivity
import noice.app.listner.OnClickInterface
import noice.app.model.AdErrorCustom
import noice.app.model.AdEventCustom
import noice.app.model.ExoNotificationData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.BaseActivity
import noice.app.modules.ads.GamDialog
import noice.app.modules.ads.VideoAdsDialog
import noice.app.modules.chat.ChatDialogFragment
import noice.app.modules.chat.model.Message
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.dashboard.home.fragment.NoiceContentMenu
import noice.app.modules.dashboard.home.fragment.PlaybackSpeedDialog
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.dashboard.home.fragment.SleepTimeDialog
import noice.app.modules.dashboard.home.model.UserActionEvent
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.live.stream.LiveStreamManager
import noice.app.modules.live.stream.core.ext.isStatus
import noice.app.modules.media.adapter.MediaQueAdapter
import noice.app.modules.media.dialog.CommentDialog
import noice.app.modules.media.dialog.ReplyCommentDialog
import noice.app.modules.media.fragment.QueuePlayerFragment
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.model.Comment
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.modules.radio.NoiceRadioMenu
import noice.app.modules.radio.adapter.RadioAdapter
import noice.app.modules.radio.model.Radio
import noice.app.observers.GlobalObservers
import noice.app.player.managers.QueueManager
import noice.app.player.managers.QueueManager.isHandledByQueueManager
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.player.playrequests.PlayRequest
import noice.app.player.playrequests.QueueAlterRequest
import noice.app.player.service.MediaService
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.AnalyticsUtil
import noice.app.utils.AppOrientationListener
import noice.app.utils.CacheUtils
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.BASE_PLAYER_CLIP_PLAY
import noice.app.utils.Constants.Companion.BASE_PLAYER_PLAY_OR_PAUSE_EVENT
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.RELOAD_QUEUE
import noice.app.utils.CustomImaWebViewClient
import noice.app.utils.DateUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MediaUtil
import noice.app.utils.MediaUtil.getEpisode
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.SafeDelay
import noice.app.utils.StringUtils.unescapeJava
import noice.app.utils.Utils
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.secondsToMs
import noice.app.utils.clickWithDebounce
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import noice.app.utils.isZero
import noice.app.utils.postThreadSafe
import noice.app.utils.putAnalyticsKey
import noice.app.utils.setOrientationListener
import noice.app.utils.showAtomically
import noice.app.utils.toPixel
import noice.app.utils.visible
import noice.app.views.CustomAdView
import noice.app.views.MiniPlayerView
import noice.app.views.SnackBarCustom
import org.apache.commons.lang3.time.DurationFormatUtils
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@AndroidEntryPoint
@OptIn(UnstableApi::class)
abstract class BasePlayerActivity : BaseActivity(), IPlayerActivity,
    SleepTimeDialog.PlayerSleepClickListener {

    private val basePlayerViewModel: BasePlayerViewModel by viewModels()
    private val channelPodcastViewModel: ChannelPodcastViewModel by viewModels()
    private lateinit var bottomSheetBinding: CustomPlayerFragmentBinding
    private var connection: ServiceConnection? = null
    private lateinit var miniView: MiniPlayerView
    private var mediaService: MediaService? = null
    private var mediaContent: ExoNotificationData? = null
    var bottomSheetBehavior: BottomSheetBehavior<View>? = null
    var radioSheetBehavior: BottomSheetBehavior<View>? = null
    private var queueAdapter: MediaQueAdapter? = null
    private var binder: MediaService.MediaServiceBinder? = null
    private var serviceRunningCallback: MutableLiveData<Boolean>? = null
    private var currentPlayBackSpeed = PlaybackSpeedDialog.PLAYBACK_SPEED_1x
    private var radioAdapter: RadioAdapter? = null
    private var radioList = ArrayList<Radio>()
    private var replyCommentDialog: ReplyCommentDialog? = null
    private var adEvent: AdEventCustom? = null
    private lateinit var playerPagerAdapter: PlayerPagerAdapter
    private lateinit var pagerDelay: SafeDelay<Int>
    private var adProgress = 0
    private var community: Community? = null
    private var content: Content? = null
    private var commentDialog: CommentDialog? = null
    private var chatListener: ListenerRegistration? = null
    private lateinit var actionHandler: ActionHandler
    private var shouldUploadQueue = false
    private var webViewReplaced = false
    private var isServiceBound = false
    private lateinit var appOrientationListener: AppOrientationListener
    private lateinit var adView: CustomAdView
    private var castDialog: CastBottomSheetFragment? = null
    private var playbackSpeedDialog: PlaybackSpeedDialog? = null
    private var sleepTimeDialog: SleepTimeDialog? = null

    @Inject
    lateinit var liveStreamManager: LiveStreamManager

    private var videoAdsDialog: VideoAdsDialog? = null
    private var playerBgColor: Int = Color.BLACK
    private var previewTimeMillis = 0L
    private var currentTimeMs: Long = 0

    companion object {
        const val BASE_PATH = "basePlayerActivity"

        const val OPEN_PLAYER = "OPEN_PLAYER"
        const val OPEN_DOWNLOADS = "OPEN_DOWNLOADS"
        const val OPEN_CONTENT_PAGE = "OPEN_CONTENT_PAGE"
        const val EVENT_ID = "EVENT_ID"
        const val ENTITY_ID = "ENTITY_ID"
        const val ENTITY_TYPE = "ENTITY_TYPE"
        const val ENTITY_SUB_TYPE = "ENTITY_SUB_TYPE"
        const val OPEN_INDEX_ACTION = "OPEN_INDEX_ACTION"
        const val NOTIFICATION_ID = "NOTIFICATION_ID"
        const val PARENT_ID = "PARENT_ID"

        const val START_SERVICE = "START_SERVICE"

        /* used to handle appsflyer deeplink event bus trigger when app in background to prevent
        bottom navigation crash issue. We need to know when app is actually visible to the user. */
        var isAppInBackground = false
    }

    fun initBindings(
        bottomSheetBinding: CustomPlayerFragmentBinding,
        miniView: MiniPlayerView
    ) {
        this.bottomSheetBinding = bottomSheetBinding
        this.miniView = miniView
    }

    fun setUp() {
        adView = CustomAdView(this)

        initViews()

        miniView.setUp(bottomSheetBinding)

        setUpObservers()
        CacheUtils.initialise()

        registerViewModelEvents()

        /* displaying ad on expanded media player */
        if (PrefUtils.userDetails?.isSubscribed == false) {
            getDisplayAds()
        }
    }

    private fun initViews() {
        initButtonBar()

        actionHandler = ActionHandler(basePlayerViewModel, this)
        bottomSheetBinding.toolbarPlayer.setupCastButton()
        queueAdapter = MediaQueAdapter(
            this, object : OnClickInterface<ExoNotificationData> {
                override fun dataClicked(data: ExoNotificationData) {
                    QueueAlterRequest.Builder()
                        .pageSource(data.pageSource)
                        .exoContents(listOf(data))
                        .play(this@BasePlayerActivity)

                    queueAdapter?.notifyDataSetChanged()
                }
            }
        )

        radioAdapter = RadioAdapter(radioList, "content", "Player")

        bottomSheetBinding.playerLayoutMain.layoutParams.height =
            Resources.getSystem().displayMetrics.heightPixels

        bottomSheetBinding.playerViewPager.isSaveEnabled = false // added for fix a crash
        playerPagerAdapter = PlayerPagerAdapter(this, adView, bottomSheetBinding)
        bottomSheetBinding.playerViewPager.adapter = playerPagerAdapter
        val recyclerView = bottomSheetBinding.playerViewPager.getRecyclerView()
        recyclerView?.isNestedScrollingEnabled = false
        recyclerView?.overScrollMode = OVER_SCROLL_NEVER
        pagerDelay = SafeDelay { proceed, position ->
            if (proceed && position != null && position > 0 && position < QueueManager.queue.size) {
                val data = QueueManager.queue[position]
                val fragment = getCurrentFragment(data)
                fragment?.let {
                    it.hideViewJob?.cancel()
                }
                QueueAlterRequest.Builder()
                    .pageSource(data.pageSource)
                    .exoContents(listOf(data))
                    .swipe(this)
            }
        }

        bottomSheetBinding.playerViewPager.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            var state = ViewPager2.SCROLL_STATE_IDLE

            override fun onPageSelected(position: Int) {
                if (state > ViewPager2.SCROLL_STATE_IDLE) {
                    pagerDelay.removeCallbacks()
                    pagerDelay.postDelayed(position)
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
                this.state = state
            }
        })

        bottomSheetBehavior = BottomSheetBehavior.from(bottomSheetBinding.bottomSheetPlayer)
        bottomSheetBehavior?.isHideable = true
        bottomSheetBehavior?.skipCollapsed = true
        bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN

        bottomSheetBinding.adsLoader.setLayoutBackground(R.color.black)

        bottomSheetBinding.toolbarPlayer.setBackClick {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        }

        bottomSheetBinding.toolbarPlayer.audioStreamClick {
            if (!NetworkUtils.isNetworkConnected(this)) {
                SnackBarCustom.Builder()
                    .parentView(bottomSheetBinding.root)
                    .text(getString(R.string.this_action_requires_internet))
                    .show()
            }
            AnalyticsUtil.sendEvent("content_type_switched", Bundle().apply {
                if (CastManager.isCastConnected) {
                    putAnalyticsKey("castPlatform", "chromecast")
                }
                putAnalyticsKey("contentId", mediaContent?.id)
                putAnalyticsKey("contentTitle", mediaContent?.title)
                putAnalyticsKey("catalogId", mediaContent?.catalogId)
                putAnalyticsKey("catalogTitle", mediaContent?.catalogTitle)
                putAnalyticsKey(
                    "timestampPosition",
                    bottomSheetBinding.playerControlView.player?.contentPosition.msToSeconds()
                )
                putAnalyticsKey("durationPlayed", getMediaSessionTimeMs().msToSeconds())
                putAnalyticsKey("entitySubType", mediaContent?.entitySubType ?: "")
                putAnalyticsKey("switchTo", "audio")
                val contentFormat = if (DataController.isPlayingVideo) {
                    if (mediaContent?.orientation == PORTRAIT) {
                        "vertical video"
                    } else {
                        "video"
                    }
                } else {
                    "audio"
                }
                putAnalyticsKey("contentFormat", contentFormat)
            })
            DataController.isVideoStreamSelected = false
            bottomSheetBinding.toolbarPlayer.customStreamType(PlayerDetailFragment.STREAM_TYPE_AUDIO)
        }

        bottomSheetBinding.toolbarPlayer.videoStreamClick {
            if (!NetworkUtils.isNetworkConnected(this)) {
                SnackBarCustom.Builder()
                    .parentView(bottomSheetBinding.root)
                    .text(getString(R.string.this_action_requires_internet))
                    .show()
            }

            getVideoQualitiesFor(mediaContent?.id)

            AnalyticsUtil.sendEvent("content_type_switched", Bundle().apply {
                if (CastManager.isCastConnected) {
                    putAnalyticsKey("castPlatform", "chromecast")
                }
                putAnalyticsKey("contentId", mediaContent?.id)
                putAnalyticsKey("contentTitle", mediaContent?.title)
                putAnalyticsKey("catalogId", mediaContent?.catalogId)
                putAnalyticsKey("catalogTitle", mediaContent?.catalogTitle)
                putAnalyticsKey(
                    "timestampPosition",
                    bottomSheetBinding.playerControlView.player?.contentPosition.msToSeconds()
                )
                putAnalyticsKey("durationPlayed", getMediaSessionTimeMs().msToSeconds())
                putAnalyticsKey("entitySubType", mediaContent?.entitySubType ?: "")
                putAnalyticsKey("switchTo", "video")
                val contentFormat = if (DataController.isPlayingVideo) {
                    if (mediaContent?.orientation == PORTRAIT) {
                        "vertical video"
                    } else {
                        "video"
                    }
                } else {
                    "audio"
                }
                putAnalyticsKey("contentFormat", contentFormat)
            })
            DataController.isVideoStreamSelected = true
            bottomSheetBinding.toolbarPlayer.customStreamType(PlayerDetailFragment.STREAM_TYPE_VIDEO)
        }

        bottomSheetBinding.toolbarPlayer.setThreeDotListener {
            if (isCurrentMediaARadio()) {
                val noiceMenu = NoiceRadioMenu().apply {
                    arguments = Bundle().apply {
                        putParcelable(NoiceRadioMenu.RADIO, mediaContent)
                    }
                }
                noiceMenu.show(supportFragmentManager, "noiceMenu")
            } else {
                val bundle = Bundle()
                val data = QueueManager.getMediaData(mediaContent?.id)
                val fragment = getCurrentFragment(data)
                val community = fragment?.community
                if (fragment?.content != null) {
                    bundle.putParcelable(NoiceContentMenu.EPISODE, fragment.content)
                } else {
                    val episode = mediaContent.getEpisode(community)
                    bundle.putParcelable(NoiceContentMenu.EPISODE, episode)
                }
                bundle.putString(NoiceContentMenu.ENTITY_SUB_TYPE, mediaContent?.entitySubType)
                bundle.putString(NoiceContentMenu.PAGE_SOURCE, AnalyticsUtil.podcast_media_player)
                val menu = NoiceContentMenu()
                menu.arguments = bundle
                menu.show(supportFragmentManager, "noiceMenu")
            }
        }

        bottomSheetBinding.learnMore.setOnClickListener {
            val children = bottomSheetBinding.companionAdSlot.getSlot().children.toList()
            if (children.isNotEmpty()) {
                children[0].performClick()

                adEvent?.let { it1 ->
                    AnalyticsUtil.sendAdEvent(
                        "ad_clicked", it1, mediaContent,
                        adMediaType = "audio"
                    )
                }
            }
        }

        bottomSheetBinding.companionAdSlot.setOnClickListener {
            val children = bottomSheetBinding.companionAdSlot.getSlot().children.toList()
            if (children.isNotEmpty()) {
                children[0].performClick()

                adEvent?.let { it1 ->
                    AnalyticsUtil.sendAdEvent(
                        "ad_clicked", it1, mediaContent,
                        adMediaType = "audio"
                    )
                }
            }
        }

        bottomSheetBinding.companionAdSlot.setImageListener { isImageLoaded ->
            if (isImageLoaded) {
                bottomSheetBinding.learnMore.visibility = VISIBLE
            } else {
                bottomSheetBinding.learnMore.visibility = GONE
            }
        }

        bottomSheetBinding.swipeScrim.setOnClickListener {
            PrefUtils.isScrimViewShown = true
            bottomSheetBinding.swipeScrim.visibility = GONE
        }

        bottomSheetBinding.commentLayout.setOnClickListener {
            onCommentLayoutClicked()
        }

//        bottomSheetBinding.dislike.setOnClickListener {
//
//            if(!NetworkUtils.isNetworkConnected(this)) {
//                Utils.showSnackBarInDialog(bottomSheetBinding.bottomSheetPlayer, getString(R.string.this_action_requires_internet))
//                return@setOnClickListener
//            }
//
//            if (!PrefUtils.isLoggedIn) {
//                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-content-like-dislike")
//                handleUserNotLoggedIn(source = "podcast_media_player", loginDialogData = loginDialogData)
//                return@setOnClickListener
//            }
//
//            processLikeDislike(UserAction.DISLIKE.action)
//
//            AnalyticsUtil.sendEvent(
//                mediaContent?.entitySubType ?: "",
//
//                mediaContent?.id ?: "",
//                mediaContent?.id,
//                "content_disliked",
//                "queue",
//                (mediaContent?.timeElapsed ?: 0).toString(),
//                (mediaContent?.duration ?: 0).toString(),
//                "",
//                mediaContent?.catalogTitle ?: "",
//                mediaContent?.title ?: ""
//            )
//        }

        bottomSheetBinding.shareLayout.setOnClickListener {
            onShareLayoutClicked()
        }

        bottomSheetBinding.commentCard.setOnClickListener {
            val chatDialog = ChatDialogFragment.newInstance(mediaContent)
            chatDialog.show(supportFragmentManager, "Chat")
        }
        bottomSheetBinding.toolbarPlayer.setCastClickListener {
            AnalyticsUtil.sendEvent("cast_clicked")
            castDialog = CastBottomSheetFragment.newInstance(CastManager.isCastConnected)
            castDialog?.show(supportFragmentManager, "CastDialogFragment")
        }
    }

    private fun initButtonBar() = with(bottomSheetBinding) {
        onPlaybackSpeedClicked()
        onSleepClicked()
        llComment.clickWithDebounce {
            onCommentLayoutClicked()
        }
        llShare.clickWithDebounce {
            onShareLayoutClicked()
        }
        llQueue.clickWithDebounce {
            onQueueClicked()
        }
    }

    private fun CustomPlayerFragmentBinding.onPlaybackSpeedClicked() {
        this.llSpeed.clickWithDebounce {
            supportFragmentManager.let { manager ->
                playbackSpeedDialog = PlaybackSpeedDialog.newInstance(currentPlayBackSpeed)
                playbackSpeedDialog?.show(manager, "playback")
            }
        }
    }

    private fun CustomPlayerFragmentBinding.onSleepClicked() {
        this.llSleep.clickWithDebounce {
            supportFragmentManager.let { manager ->
                sleepTimeDialog = SleepTimeDialog.newInstance(
                    sleepTime,
                    this@BasePlayerActivity,
                    mediaContent?.entitySubType
                )
                sleepTimeDialog?.show(manager, "Sleep")
            }
        }
    }

    override fun onSleepClicked(clickedSleepTimer: Int) = with(bottomSheetBinding) {
        sleepTime = clickedSleepTimer
        playerControlView.handleSleepTimeChanged(clickedSleepTimer)
    }

    private fun onCommentLayoutClicked() {
        if (mediaContent?.isRadio == true) {
            val chatDialog = ChatDialogFragment.newInstance(mediaContent)
            chatDialog.show(supportFragmentManager, "Chat")
        } else {
            val episode = mediaContent?.getEpisode(community)
            commentDialog = if (content != null) {
                CommentDialog.newInstance(content?.id.toString(), content!!, source = "Player")
            } else {
                CommentDialog.newInstance(
                    mediaContent?.id.toString(),
                    episode,
                    source = "Player"
                )
            }

            commentDialog?.show(supportFragmentManager, "Comment")
        }
    }

    private fun onShareLayoutClicked() {
        val dialog =
            ShareDialog.newInstance(
                mediaContent,
                mediaContent?.isRadio ?: false,
                null,
                isFromMediaPlayer = true
            )
        dialog.show(supportFragmentManager, "media_player")
    }

    private fun onQueueClicked() {
        val bundle = Bundle()
        bundle.putString("title", PrefUtils.queueTitle)
        bundle.putFloat("speed", currentPlayBackSpeed)
        bundle.putString("source", "Podcast_Media_Player")
        QueuePlayerFragment.showAllowingStateLoss(supportFragmentManager, bundle)
    }

    private fun findImaWebView(parent: ViewGroup) {
        if (webViewReplaced) {
            return
        }
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            if (child is ViewGroup && child !is WebView) {
                findImaWebView(child)
            } else if (child is WebView) {
                setWebClient(child)
                break
            }
        }
    }

    private fun setWebClient(webView: WebView) {
        webViewReplaced = true
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            webView.webViewClient = CustomImaWebViewClient(webView)
        }
    }

    private fun setUpObservers() {
        appOrientationListener = setOrientationListener(false) { appOrientation ->
            if (CastManager.isCastConnected.isFalse() &&
                DataController.isPlayingVideo &&
                !DataController.isVideoInFullscreen &&
                appOrientation.isLandscape
            ) {
                PlayerEvent(PlayerEvent.ENABLE_FULL_SCREEN_MODE, mediaContent?.id).sendPlayerEvent()
            }
        }

        GlobalObservers.interActivityApi.subscribeApis(this, BASE_PATH) { call ->
            if (call.path == "mediaSessionTime") {
                call.sendResult(getMediaSessionTimeMs())
            }
        }

        GlobalObservers.playerEventObserver
            .subscribeAdsEvent(this) { adEvent ->
                findImaWebView(bottomSheetBinding.adsControlView.adViewGroup)
                if (adEvent.uiEventId == AdEventCustom.START_UI) {
                    startAd()
                } else if (adEvent.uiEventId == AdEventCustom.END_UI) {
                    hideAdLayout()
                } else if (adEvent.videoProgressUpdate != null) {
                    handleAdProgress(adEvent.videoProgressUpdate, adEvent)
                } else if (adEvent.adErrorCustom != null) {
                    handleAdErrorEvent(adEvent.adErrorCustom)
                } else if (adEvent.type != null) {
                    handleAdEvent(adEvent)
                }
            }

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(this) {
                miniView.handlePlayerUIEvents(it)
                Log.d("pause5", "${it.event}")
                if (isCurrentMediaARadio()) {
                    bottomSheetBinding.playerControlViewRadio
                } else {
                    bottomSheetBinding.playerControlView
                }.handlePlayerUIEvents(it)
            }
        GlobalObservers.playerEventObserver.subscribePreviewClickEvents(this) { source ->
            decideOpenBottomSheetDialog(source, false)
        }

        GlobalObservers.playerEventObserver
            .subscribePlayerEvents(
                this,
                listOf(
                    PlayerEvent.ON_NEXT,
                    PlayerEvent.QUEUE_UPDATE,
                    PlayerEvent.QUEUE_INITIALISED,
                    PlayerEvent.ADD_TO_MANUAL_QUEUE,
                    PlayerEvent.UPDATE_PLAYER_SPEED,
                    PlayerEvent.SLEEP_TIMER,
                    PlayerEvent.PLAY_REQUEST_EVENT,
                    PlayerEvent.NOTIFICATION_CLICKS,
                    PlayerEvent.SEEK_MEDIA,
                    PlayerEvent.VIDEO_QUALITY_CHANGED
                )
            ) {
                handlePlayerEvents(it)
            }
        GlobalObservers.gamEventObserver.subscribeAdEvents(this) { gamEvent ->
            GamDialog().showAtomically(supportFragmentManager, "GamDialog")
        }
        GlobalObservers.playerEventObserver
            .subscribeForCastButton(this) { pair ->

                if (QueueManager.getPlayingData()?.isRadio.isTrue()) {
                    bottomSheetBinding.playerControlViewRadio.player = pair.first
                    setPlayer(bottomSheetBinding.playerControlViewRadio)
                } else {
                    bottomSheetBinding.playerControlView.player = pair.first
                    setPlayer(bottomSheetBinding.playerControlView)
                }

                if (pair.second) {
                    castDialog?.dismissAllowingStateLoss()
                    Utils.showSnackBar(bottomSheetBinding.shareLayout, "Tersambung ke Chromecast")
                    AnalyticsUtil.sendEvent("cast_connected", Bundle().apply {
                        putAnalyticsKey("devicePlatform", CastManager.castDevice?.name)
                        putAnalyticsKey("brandDevice", CastManager.castDevice?.description)
                        putAnalyticsKey("status", "success")
                    })
                    bottomSheetBinding.toolbarPlayer.showAudioVideoTab(false)
                } else {
                    castDialog?.dismissAllowingStateLoss()
                    Utils.showSnackBar(
                        bottomSheetBinding.shareLayout,
                        "Sambungan berhasil terputus"
                    )
                    AnalyticsUtil.sendEvent("cast_disconnected", Bundle().apply {
                        putAnalyticsKey("devicePlatform", CastManager.castDevice?.name)
                        putAnalyticsKey("brandDevice", CastManager.castDevice?.description)
                        putAnalyticsKey("status", "success")
                    })
                    bottomSheetBinding.toolbarPlayer.showAudioVideoTab(mediaContent?.showVideo.isTrue())
                }
                mediaContent = QueueManager.getPlayingData()
                setMediaDetail()
                bottomSheetBinding.toolbarPlayer.updateCastButton(CastManager.isCastConnected)
            }

        /* Added this listener to handle player UI for purchased content. Content continues from the last duration. */
        GlobalObservers.contentPurchaseObserver
            .subscribe(this) { response ->
                if (response is Content && response.id == mediaContent?.id && response.catalogId == mediaContent?.catalogId) {
                    val data = QueueManager.getPlayingData()

                    data?.videoUrl = response.videoUrl
                    data?.songUrl = response.url ?: ""
                    data?.hasPurchased = response.hasPurchased
                    data?.isPurchaseNeeded = response.isPurchaseNeeded
                    data?.displayAdsEnabled = response.displayAdsEnabled

                    /* in order to handle the new url after purchase, we need to update it to the player
                    * for currently playing item. I reused 'mediaService?.switchTo()' method for this
                    * purpose as the functionality needed to handle this case is in it instead of rewriting
                    * everything again. */
                    QueueManager.updateQueueData(data)
                    mediaService?.switchTo(
                        if (DataController.isPlayingVideo) PlayerDetailFragment.STREAM_TYPE_VIDEO
                        else PlayerDetailFragment.STREAM_TYPE_AUDIO
                    )

                    setMediaDetail()
                    handlePreviewProgress(
                        bottomSheetBinding.playerControlView.player?.currentPosition ?: 0
                    )
                }
            }

        DataController.onAdPlayStateChange.observe(this) {
            bottomSheetBinding.playerViewPager.isUserInputEnabled = !it
        }
    }

    private fun decideOpenBottomSheetDialog(source: String, isUserClick: Boolean) {
        if (!NetworkUtils.isNetworkConnected(this)) {
            Utils.showSnackBarInDialog(
                bottomSheetBinding.bottomSheetPlayer,
                getString(R.string.this_action_requires_internet)
            )
            return
        }

        if (!PrefUtils.isLoggedIn) {
            handleUserNotLoggedIn(source = source)
            return
        }
        supportFragmentManager.let { fm ->
            ClickHandler.openPurchaseDialog(fm, content, source, isUserClick)
        }
    }

    private fun handlePlayerEvents(playerEvent: PlayerEvent) {
        when (playerEvent.event) {
            PlayerEvent.ON_NEXT -> {
                onMediaItemTransition(playerEvent.exoData)
            }

            PlayerEvent.QUEUE_UPDATE -> {
                onQueueUpdate()
            }

            PlayerEvent.QUEUE_INITIALISED -> {
                if (!liveStreamManager.isSessionActive()) {
                    setFirstQueueItemOnPlayer()
                }
            }

            PlayerEvent.ADD_TO_MANUAL_QUEUE -> {
                if (playerEvent.genericData != null && playerEvent.genericData is Boolean) {
                    handleAddToQueueResult(playerEvent.genericData)
                }
            }

            PlayerEvent.UPDATE_PLAYER_SPEED -> {
                currentPlayBackSpeed = playerEvent.genericData as Float
                bottomSheetBinding.playerControlView.onPlayBackSpeedClicked(currentPlayBackSpeed) { color, text ->
                    with(bottomSheetBinding.tvPlaybackSpeed) {
                        this.text = text
                        setTextColor(color)
                    }
                }
            }

            PlayerEvent.SLEEP_TIMER -> {
                if (playerEvent.genericData == SleepTimeDialog.SLEEP_TIME_0) {
                    if (isCurrentMediaARadio()) {
                        miniView.updateSleep(SleepTimeDialog.SLEEP_TIME_0)
                        bottomSheetBinding.playerControlViewRadio
                    } else {
                        bottomSheetBinding.playerControlView
                    }.updateSleep(SleepTimeDialog.SLEEP_TIME_0)
                }
            }

            PlayerEvent.PLAY_REQUEST_EVENT -> {
                if (playerEvent.genericData is PlayRequest) {
                    sendToMediaService(playerEvent.genericData)
                }
            }

            PlayerEvent.NOTIFICATION_CLICKS -> {
                val action = playerEvent.genericData as String
                if (action == PlayerNotificationManager.ACTION_PAUSE) {
                    mediaService?.sendPauseEvent()
                }
            }

            PlayerEvent.SEEK_MEDIA -> {
                seekMedia(playerEvent.genericData as Boolean)
            }

            PlayerEvent.VIDEO_QUALITY_CHANGED -> {
                mediaService?.changeVideoQuality()
            }
        }
    }

    private fun handleAddToQueueResult(result: Boolean) {
        if (result) {
            Toast.makeText(this, getString(R.string.added_queue), Toast.LENGTH_LONG).show()
        } else {
            NoiceAlertDialog.Builder()
                .setTitle(getString(R.string.queue_is_already_full))
                .setMessage(getString(R.string.can_only_accomodate_50_delete_some_to_add_other))
                .setPositiveButtonText(getString(R.string.tutup))
                .setNegativeButtonText(getString(R.string.okay))
                .setListener(object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean, eventId: Int) {
                        if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                            QueuePlayerFragment.showAllowingStateLoss(
                                supportFragmentManager,
                                Bundle().apply {
                                    putString("title", "")
                                    putString("source", "HomeActivity")
                                })
                        }
                    }
                }).show(supportFragmentManager)
        }
    }

    private fun registerViewModelEvents() {
        lifecycleScope.launch {
            basePlayerViewModel.eventFlow.collect {
                when (it) {
                    BasePlayerViewModel.Event.FetchRadios -> {
                        getRadioChannelContent()
                    }

                    is BasePlayerViewModel.Event.HandleNotLoggedIn -> {
                        handleUserNotLoggedIn(loginDialogData = it.loginDialogData)
                    }

                    BasePlayerViewModel.Event.RefreshMiniPlayer -> {
                        miniView.setData(mediaContent, getMediaType())
                    }

                    is BasePlayerViewModel.Event.LoaderEvent -> {
                        if (it.show) {
                            bottomSheetBinding.errorViewPlayer.showLoading()
                        } else {
                            bottomSheetBinding.errorViewPlayer.hide()
                        }
                    }

                    is BasePlayerViewModel.Event.ResetMediaData -> {
                        if (it.exoData?.uniqueId == mediaContent?.uniqueId) {
                            mediaContent = it.exoData
                            setMediaDetail()
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    private fun ViewPager2.getRecyclerView(): RecyclerView? {
        try {
            val field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            field.isAccessible = true
            return field.get(this) as RecyclerView
        } catch (e: NoSuchFieldException) {
            e.printStackTrace()
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        }
        return null
    }

    private fun startAd() {
        bottomSheetBinding.adsLoader.showLoading()
        bottomSheetBinding.adsPlayerLayout.visibility = VISIBLE
        bottomSheetBinding.toolbarPlayer.setCastButtonsVisiBility(false)

        basePlayerViewModel.isUnlockContentBannerVisible(false)

        bottomSheetBinding.toolbarPlayer.threeDotButtonVisibility(GONE)
        bottomSheetBinding.toolbarPlayer.setToolbarBackground(
            ContextCompat.getColor(
                this,
                R.color.black
            )
        )
        bottomSheetBinding.toolbarPlayer.setToolbarTitle(getString(R.string.advertisement))
        bottomSheetBinding.toolbarPlayer.showAudioVideoTab(false)
        bottomSheetBinding.adProgressBar.progress = 0

        setPlayerQueueVisibility()

        if (DataController.isPlayerOpen) {
            setStatusBarColor(ContextCompat.getColor(this, R.color.black700))
        }
    }

    private fun handleAdProgress(data: VideoProgressUpdate, adEvent: AdEventCustom?) {
        var currentTimeMs = data.currentTimeMs
        if (currentTimeMs < 0) {
            currentTimeMs = 0
        }
        var durationMs = data.durationMs
        if (durationMs < 0) {
            durationMs = 1
        }
        val progress = ((currentTimeMs / durationMs.toFloat()) * 100).toInt()
        adProgress = progress
        miniView.setProgress(progress)
        bottomSheetBinding.adProgressBar.progress = progress
        bottomSheetBinding.adPosition.text =
            DurationFormatUtils.formatDuration(currentTimeMs, "mm:ss")
        bottomSheetBinding.adDuration.text = DurationFormatUtils.formatDuration(durationMs, "mm:ss")

        if (adEvent?.ad?.contentType?.contains("video") == true) {
            videoAdsDialog?.updateProgress(
                progress,
                DurationFormatUtils.formatDuration(currentTimeMs, "mm:ss"),
                DurationFormatUtils.formatDuration(durationMs, "mm:ss")
            )
        }
        this.currentTimeMs = currentTimeMs
        handlePreviewProgress(currentTimeMs)
    }

    override fun handlePreviewProgress(currentTimeMs: Long) {
        if (mediaContent?.hasPurchased.isFalse() &&
            mediaContent?.isPurchaseNeeded.isTrue()
        ) {
            val remainingMs = previewTimeMillis - currentTimeMs
            val minutes = remainingMs / 60000
            val seconds = (remainingMs % 60000) / 1000
            // Update the TextView with the remaining time
            val exoPosition = String.format("%02d:%02d", minutes, seconds)

            //exo_progress
            if (!DataController.isAdPlaying
                && (minutes.isZero() && seconds.isZero()
                        || minutes < 0
                        || seconds < 0
                        || bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.exo_position).text.toString() == exoPosition)
            ) {
                bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview)
                    .visible(true)

                ContentPlayRequest.Builder().playWhenReady(this, false)
                /* sending pause event manually, as above pause request is not able to trigger pause event,
                * reason is below we are seeking player to 2 seconds before the preview end. */
                mediaService?.sendPauseEvent()

                bottomSheetBinding.playerControlView.findViewById<CustomTimeBar>(R.id.exo_progress)
                    .setPosition(previewTimeMillis)

                /* in order to handle a bug where when preview content (with preview url) finishes,
                * player automatically moves to the next item in the queue. so we see this content purchase
                * dialog on next playing content for buying the finished content (preview content).
                * here we have added -2000 means after preview ends we show this purchase content dialog and
                * set playing preview content to 2 seconds before it ends, it prevents content change. */
                mediaService?.seekPlayer(previewTimeMillis - 2000)
                mediaService?.handlePreviewContentCompleted(previewTimeMillis - 2000)

                mediaContent?.isPreviewCompleted = true
                if (bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview).text != "00:00 tersisa") {
                    bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview).text =
                        "00:00 tersisa"
                    decideOpenBottomSheetDialog("podcast_media_player", false)
                }
            } else {
                bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview).text =
                    exoPosition.plus(" tersisa")
                bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview)
                    .visible(true)
                mediaContent?.isPreviewCompleted = false
            }
        } else {
            bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview)
                .visible(false)
        }
    }

    private fun handleAdErrorEvent(adErrorEvent: AdErrorCustom) {
        hideAdLayout()
        val adConfig = MediaUtil.getAdConfigFor(mediaContent?.entitySubType.toString())
        AnalyticsBuilder.oldBuilder().apply {
            putAnalyticsKey(
                "source", if (bottomSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
                    "ad expanded media player"
                } else "ad minimize media player"
            )
            putAnalyticsKey("error source", adErrorEvent.errorSource)
            putAnalyticsKey("error code", adErrorEvent.errorCode)
            putAnalyticsKey("error message", adErrorEvent.message)
            putAnalyticsKey(
                "error type", if (adErrorEvent.errorType == AdError.AdErrorType.LOAD) {
                    "No ads served"
                } else "Triton audio ad"
            )
            putAnalyticsKey("api url", adConfig?.url)
            putAnalyticsKey("ad load time", adErrorEvent.adLoadTime)
            putAnalyticsKey("vast server load time", adErrorEvent.vastServerLoadTime)
        }.also {
            it.send("error encountered")
        }
    }

    private fun handleAdEvent(adEventCustom: AdEventCustom) {
        adEvent = adEventCustom
        /* We can have mix of Audio and Video ad at any position, so handled the ad started event. */
        if (adEventCustom.type == AdEvent.AdEventType.STARTED && adEventCustom.ad?.contentType?.contains(
                "video"
            ) == true
        ) {
            if (videoAdsDialog == null) {
                videoAdsDialog = VideoAdsDialog.newInstance()
                videoAdsDialog?.setCompanionSlot(bottomSheetBinding.companionAdSlot)
                videoAdsDialog?.setEventsData(mediaContent, adEvent)
                videoAdsDialog?.show(supportFragmentManager)
            }
        } else {
            videoAdsDialog?.hide()
            videoAdsDialog = null
        }

        if (adEvent?.type == AdEvent.AdEventType.CONTENT_PAUSE_REQUESTED ||
            adEvent?.type == AdEvent.AdEventType.LOADED
        ) {
            bottomSheetBinding.companionAdSlot.prepareForAd()
            miniView.getCompanionSlot().prepareForAd()

            bottomSheetBinding.learnMore.visibility = VISIBLE

            setPlayer(bottomSheetBinding.adsControlView)

            bottomSheetBinding.adProgressBar.progress = 0

            bottomSheetBinding.adPosition.text = getString(R.string.zero_minutes_hours)

            miniView.getCompanionSlot().visibility = VISIBLE
            bottomSheetBinding.adsPlayerLayout.visibility = VISIBLE
            bottomSheetBinding.toolbarPlayer.setCastButtonsVisiBility(false)

            basePlayerViewModel.isUnlockContentBannerVisible(false)

            bottomSheetBinding.toolbarPlayer.setToolbarBackground(
                ContextCompat.getColor(
                    this,
                    R.color.black
                )
            )
            bottomSheetBinding.toolbarPlayer.threeDotButtonVisibility(INVISIBLE)
            bottomSheetBinding.toolbarPlayer.setToolbarTitle(getString(R.string.advertisement))
            bottomSheetBinding.toolbarPlayer.showAudioVideoTab(false)
            miniView.handleAds(mediaContent)

            bottomSheetBinding.adsLoader.hide()

            setPlayerQueueVisibility()
        } else if (adEvent?.type == AdEvent.AdEventType.CONTENT_RESUME_REQUESTED || adEvent?.type == AdEvent.AdEventType.ALL_ADS_COMPLETED) {
            hideAdLayout()
        }
    }

    override fun onDestroy() {
        if (mediaService == null && !liveStreamManager.isSessionActive() && PrefUtils.isLoggedIn)
            MoEngageAnalytics.sendEvent(this, "app closed")

        unbindAudioService()
        super.onDestroy()
    }

    override fun onPause() {
        BaseApplication.application.appsFlyerDeepLinkIntent = null
        super.onPause()
        isAppInBackground = true
        if (!DataController.isVideoInFullscreen && !DataController.wasPlayingVideo &&
            CastManager.isCastConnected.isFalse()
        ) {
            DataController.wasPlayingVideo = DataController.isPlayingVideo
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_AUDIO,
                    Constants.STREAM_TYPE,
                    secondData = true
                )
            )
        }

        /* This case handles the user attribution event 'deeplink_resolved' when user installs the
        * app first and then clicks the appsflyer link afterwards (type param in the event was going as 'install'
        * it should be 'app open'). */
        PrefUtils.isAppInstall = false
    }

    override fun sendToMediaService(playRequest: PlayRequest?) {

        if (playRequest.isHandledByQueueManager()) {
            QueueManager.handlePlayRequest(playRequest)
        } else {
            LiveTrigger.onCancelLiveJoin()
            if (liveStreamManager.isSessionActive()) {
                stopLiveRoom()
            }
            lifecycleScope.launch {
                MediaService.start(applicationContext, playRequest)
                bindToAudioService()
            }
        }
    }

    private fun createServiceConnection() {
        connection = null
        connection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                binder = service as MediaService.MediaServiceBinder
                mediaService = binder?.service
                shouldUploadQueue = true
                serviceRunningCallback?.value = true
                serviceRunningCallback = null

                mediaService?.initAdsAndQueue(
                    WeakReference(bottomSheetBinding.adsControlView),
                    WeakReference(bottomSheetBinding.companionAdSlot.getSlot()),
                    WeakReference(miniView.getCompanionSlot().getSlot())
                )

                setPlayer(bottomSheetBinding.playerControlView)
                setPlayer(bottomSheetBinding.playerControlViewRadio)
            }

            override fun onServiceDisconnected(name: ComponentName?) {
                onServiceDisconnected()
            }
        }
    }

    private fun bindToAudioService() {
        if (connection == null) {
            createServiceConnection()
            isServiceBound = MediaService.bind(applicationContext, connection)
        }
        if (mediaService != null && serviceRunningCallback != null) {
            serviceRunningCallback?.value = true
            serviceRunningCallback = null
        }
    }

    private fun unbindAudioService() {
        if (connection != null && isServiceBound) {
            MediaService.unBind(applicationContext, connection)
            onServiceDisconnected()
        }
    }

    private fun onServiceDisconnected() {
        isServiceBound = false
        connection = null
        mediaService = null
        bottomSheetBinding.playerControlView.player = null
        bottomSheetBinding.playerControlViewRadio.player = null
        bottomSheetBinding.adsControlView.player = null
    }

    fun stopMediaService() {
        pause(PlayRequest.REASON_STOPPING_SERVICE)

        unbindAudioService()
        stopService(Intent(this, MediaService::class.java))
        handlePlayerEvents(PlayerEvent(PlayerEvent.SLEEP_TIMER, SleepTimeDialog.SLEEP_TIME_0))
        if (DataController.isAdPlaying) {
            hideAdLayout()
        }
        closePlayer()

        mediaService = null
    }

    fun checkLiveStreamProcessDeath() {
        liveStreamManager.checkForProcessDeath(
            context = this,
            callback = liveStreamCallback
        )
    }

    @Synchronized
    override fun joinLiveRoom(room: LiveRoom, extraData: noice.app.model.ExtraData?) {
        if (liveStreamManager.isStatus(LiveStreamManager.Status.REQUESTED)) {
            return
        }

        if (liveStreamManager.isSessionActive()) {
            liveStreamManager.switchStream(room)
        } else {
            startLiveStream(
                room = room,
                extraData = extraData
            )
        }
    }

    override fun stopLiveRoom() {
        liveStreamManager.stopStream()
    }

    private val liveStreamCallback = object : LiveStreamManager.Callback {
        override fun onRequested() {
            closePlayer()
            stopMediaService()
        }

        override fun onStarted(exoNotificationData: ExoNotificationData?) {
            miniView.apply {
                setData(
                    mediaContent = exoNotificationData,
                    mediaType = MiniPlayerView.MEDIA_TYPE_LIVE
                )
                show()
                handlePlayerUIEvents(PlayerEvent(PlayerEvent.PLAY))
            }
        }

        override fun onEdited(exoNotificationData: ExoNotificationData?) {
            miniView.setData(
                mediaContent = exoNotificationData,
                mediaType = MiniPlayerView.MEDIA_TYPE_LIVE
            )
        }

        override fun onEnded() {
            miniView.apply {
                if (getMiniPlayerMediaType() == MiniPlayerView.MEDIA_TYPE_LIVE) {
                    handlePlayerUIEvents(PlayerEvent(PlayerEvent.PAUSE_END_LOADER))
                }
            }
            setFirstQueueItemOnPlayer(shouldShowMiniPlayer = true)
        }
    }

    private fun startLiveStream(
        room: LiveRoom,
        extraData: noice.app.model.ExtraData?
    ) {
        liveStreamManager.Builder()
            .room(room)
            .extraData(extraData)
            .callback(liveStreamCallback)
            .startStream(this)
    }

    private fun onQueueUpdate() {

        if (QueueManager.queue.size == 1) {
            setFirstQueueItemOnPlayer()
        } else {
            queueAdapter?.updateQueue()

            setPlayerQueueVisibility()

            handleMiniPlayerVisibility()

            playerPagerAdapter.updatePager(bottomSheetBinding.playerViewPager)
        }

        BaseApplication.updateLocalQueue()

        EventBus.getDefault().post(EventMessage("", RELOAD_QUEUE))
    }

    private fun handleMiniPlayerVisibility() {
        if (QueueManager.queue.isNotEmpty()) {
            miniView.show()
        } else {
            miniView.hide()
        }
    }

    private fun onMediaItemTransition(currentData: ExoNotificationData?) {

        val previousMediaContent = mediaContent

        mediaContent = currentData

        onQueueUpdate()

        setPlayerQueueVisibility()

        if (CastManager.isCastConnected) {
            if (mediaContent?.isRadio.isTrue()) {
                setPlayer(bottomSheetBinding.playerControlViewRadio)
            } else {
                setPlayer(bottomSheetBinding.playerControlView)
            }
        }
        if (mediaContent?.videoUrl?.isEmpty() == false) {
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_VIDEO,
                    Constants.STREAM_TYPE,
                    secondData = true
                )
            )
            getVideoQualitiesFor(mediaContent?.id)
            openPlayer()
        } else {
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_AUDIO,
                    Constants.STREAM_TYPE,
                    secondData = false
                )
            )
        }
        DataController.isVideoStreamSelected = mediaContent?.playVideo ?: false

        if (previousMediaContent?.isRadio != mediaContent?.isRadio) {
            swapPlayerView(mediaContent)
        } else if (mediaContent?.isRadio == false) {
            queueAdapter?.notifyDataSetChanged()
        }

        setMediaDetail()

        bottomSheetBinding.playerControlView.setMediaContent(mediaContent)
        bottomSheetBinding.playerControlViewRadio.setMediaContent(mediaContent)

        handleMiniPlayerVisibility()
    }

    private fun getVideoQualitiesFor(contentId: String?) {
        val selectedQuality = DataController.videoQualityList.find {
            it.isDefault == true
        }
        DataController.videoQualityList.clear()

        if (contentId.isNullOrEmpty()) {
            return
        }
        basePlayerViewModel.getVideoQualitiesFor(contentId).observe(this) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            when (it?.status) {
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        val exists = it.data?.data?.find { videoQuality ->
                            videoQuality.resolution == selectedQuality?.resolution
                        } != null
                        if (exists) {
                            it.data?.data?.forEach { videoQuality ->
                                videoQuality.isDefault =
                                    videoQuality.resolution == selectedQuality?.resolution
                                DataController.videoQualityList.add(videoQuality)
                            }
                        } else {
                            DataController.videoQualityList.addAll(it.data?.data ?: ArrayList())
                        }
                    }
                }

                else -> {

                }
            }
        }
    }

    private fun swapPlayerView(data: ExoNotificationData?) {

        val layoutMargin =
            bottomSheetBinding.headerTextPlayer.layoutParams as LinearLayout.LayoutParams

        bottomSheetBinding.apply {
            if (data?.isRadio == true) {
                playerControlView.visibility = GONE
                playerControlViewRadio.visibility = VISIBLE
                playerViewPager.isUserInputEnabled = false
                bottomsheetLine.visibility = GONE
                layoutMargin.topMargin = 24F.toPixel(playerControlView.context)

                headingLayoutPlayer.background =
                    ContextCompat.getDrawable(playerControlViewRadio.context, R.color.white10)

                headerTextPlayer.text = getString(R.string.radio_channel)
                swipeScrim.visibility = GONE
                basePlayerViewModel.isLikeIconVisible(false)
                buttonBar.visibility = GONE
                likeCommentsShareBar.visibility = VISIBLE
            } else {
                if (!PrefUtils.isScrimViewShown) {
                    swipeScrim.visibility = VISIBLE
                } else {
                    swipeScrim.visibility = GONE
                }

                playerControlView.visibility = VISIBLE
                playerControlViewRadio.visibility = GONE
                playerViewPager.isUserInputEnabled = true
                bottomsheetLine.visibility = VISIBLE
                layoutMargin.topMargin = 12F.toPixel(playerControlView.context)
                headingLayoutPlayer.background = ContextCompat.getDrawable(
                    playerControlView.context,
                    R.drawable.bottom_sheet_rounded_grey_24dp
                )
                headerTextPlayer.text = getString(R.string.queue_list)

                basePlayerViewModel.isLikeIconVisible(true)
                buttonBar.visibility = VISIBLE
                likeCommentsShareBar.visibility = GONE
            }
            toolbarPlayer.setTitleGravity(Gravity.CENTER)
        }

        if (data?.isRadio == true) {
            bottomSheetBinding.commentIcon.setImageResource(R.drawable.ic_chat_white)
        } else {
            bottomSheetBinding.commentIcon.setImageResource(R.drawable.ic_comment_white)
        }

        bottomSheetBinding.rvQueuePlayer.apply {
            setHasFixedSize(true)
            if (itemDecorationCount > 0) {
                for (i in 0 until itemDecorationCount) {
                    removeItemDecorationAt(i)
                }
            }
            addItemDecoration(RecyclerViewMargin(resources.getDimensionPixelSize(R.dimen.dp8)))

            if (data?.isRadio == true) {
                if (adapter !is RadioAdapter) {
                    layoutManager =
                        CustomGLManager(this@BasePlayerActivity, 2, RecyclerView.VERTICAL, false)
                    adapter = radioAdapter
                }
            } else if (adapter !is MediaQueAdapter) {
                layoutManager =
                    CustomLLManager(this@BasePlayerActivity, RecyclerView.HORIZONTAL, false)
                adapter = queueAdapter
            }
        }
    }

    private fun getRadioChannelContent() {
        viewModelBase.getLiveRadio().observe(this) {
            if (it.status == ResponseStatus.SUCCESS) {
                radioList.clear()
                radioList.addAll(it.data?.toMutableList() as ArrayList<Radio>)
                radioAdapter?.notifyDataSetChanged()
            }
            setPlayerQueueVisibility()
        }
    }

    fun getCurrentFragment(data: ExoNotificationData?) =
        supportFragmentManager.findFragmentByTag("f${data?.uniqueId.hashCode()}") as? PlayerDetailFragment

    fun onMessageEventBase(event: EventMessage) {
        if (event.eventCode == Constants.POST_COMMENT && event.data as String == mediaContent?.id) {
            var commentCount = community?.aggregations?.comments ?: 0
            commentCount += 1
            community?.aggregations?.comments = commentCount
            setCommunityDetail()
        } else if (event.eventCode == Constants.COMMENT_DELETED && event.data as String == mediaContent?.id) {
            var commentCount = community?.aggregations?.comments ?: 0
            commentCount -= 1
            community?.aggregations?.comments = commentCount
            setCommunityDetail()
        } else if (event.eventCode == Constants.USER_ACTION_EVENT && (event.data as UserActionEvent).episode?.id == mediaContent?.id) {
            content = event.data.episode
            community = event.data.community
            setCommunityDetail()
        } else if (event.eventCode == Constants.HANDLE_NON_LOGGED_IN) {
            handleUserNotLoggedIn()
        } else if (event.eventCode == Constants.PLAYER_SLEEP) {
            DataController.sleepTime = event.data as Int
            mediaService?.handleSleepTimer(DataController.sleepTime)
            if (isCurrentMediaARadio()) {
                miniView.updateSleep(DataController.sleepTime)
                bottomSheetBinding.playerControlViewRadio
            } else {
                bottomSheetBinding.playerControlView
            }.updateSleep(DataController.sleepTime)
        } else if (event.eventCode == Constants.LIHAT_COMMENT_CLICK) {
            val comment = event.data as Comment
            replyCommentDialog = ReplyCommentDialog.newInstance(
                comment.id.toString(),
                comment,
                event.secondData as Content,
                source = event.source,
                false
            )
            replyCommentDialog?.show(supportFragmentManager, "Comment")
        } else if (event.eventCode == Constants.REPLY_FROM_COMMENT) {
            val comment = event.data as Comment
            replyCommentDialog = ReplyCommentDialog.newInstance(
                comment.id.toString(),
                comment,
                event.secondData as Content,
                source = event.source,
                true
            )
            replyCommentDialog?.show(supportFragmentManager, "Comment")
        } else if (event.eventCode == BASE_PLAYER_CLIP_PLAY) {
            val content = event.data as Content

            ContentPlayRequest.Builder()
                .contents(listOf(content))
                .pageSource("Noice Clip")
                .queueTitle(content.catalog?.title.toString())
                .addCatalogContents(true)
                .contentFetchLimit(20)
                .play(this)

            openPlayer()
        } else if (event.eventCode == BASE_PLAYER_PLAY_OR_PAUSE_EVENT) {
            if (!(event.data as Boolean)) {
                pause(PlayRequest.REASON_CLIP_REQUEST)
            }
        } else if (event.eventCode == Constants.COMMUNITY_DATA) {
            content = event.data as Content
            community = content?.meta

            setCommunityDetail()
        } else if (event.eventCode == Constants.UPDATE_QUEUE) {
            if (shouldUploadQueue) {
                mediaService?.updateFirstQueueMedia()
                BaseApplication.updateQueue()
            }
        } else if (event.eventCode == Constants.STREAM_TYPE) {
            val streamType = event.data as String
            if (streamType == PlayerDetailFragment.STREAM_TYPE_AUDIO) {
                DataController.isVideoInFullscreen = false
            }
            /*if (mediaContent?.hasAccessToVideo == false) {
                DataController.isPlayingVideo = false
                return
            }*/
            bottomSheetBinding.toolbarPlayer.setSelected(streamType)

            //event.secondData indicates that should we update media source as well or not
            if (event.secondData != false) {
                if (streamType == PlayerDetailFragment.STREAM_TYPE_VIDEO) {
                    appOrientationListener.enable()
                } else {
                    appOrientationListener.disable()
                }
                mediaService?.switchTo(streamType)
            } else {
                DataController.isPlayingVideo = streamType == PlayerDetailFragment.STREAM_TYPE_VIDEO
            }
        }
    }

    private fun hideAdLayout() {
        DataController.isAdPlaying = false
        DataController.onAdPlayStateChange.postThreadSafe(false)
        setPlayerQueueVisibility()
        adProgress = 0
        bottomSheetBinding.adsControlView.player = null
        miniView.getCompanionSlot().visibility = GONE
        bottomSheetBinding.adsPlayerLayout.visibility = GONE
        bottomSheetBinding.toolbarPlayer.setCastButtonsVisiBility(true)

        basePlayerViewModel.isUnlockContentBannerVisible(mediaContent?.hasPurchased.isFalse() || mediaContent?.isPurchaseNeeded.isTrue())

        handlePreviewProgress(currentTimeMs)
        videoAdsDialog?.hide()
        videoAdsDialog = null

        if (mediaContent?.videoUrl?.isEmpty() == false) {
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_VIDEO,
                    Constants.STREAM_TYPE,
                    secondData = false
                )
            )
        } else {
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_AUDIO,
                    Constants.STREAM_TYPE,
                    secondData = false
                )
            )
        }

        bottomSheetBinding.toolbarPlayer.setToolbarBackground(Color.TRANSPARENT)
        bottomSheetBinding.toolbarPlayer.threeDotButtonVisibility(VISIBLE)
        bottomSheetBinding.toolbarPlayer.setToolbarTitle(
            mediaContent?.entitySubType?.substring(0, 1)?.uppercase(Locale.getDefault())
                ?.plus(mediaContent?.entitySubType?.substring(1)).toString()
        )
        bottomSheetBinding.toolbarPlayer.handleMediaChange(mediaContent)
        miniView.setData(mediaContent, getMediaType())
        bottomSheetBinding.adsLoader.hide()

        bottomSheetBinding.companionAdSlot.getSlot().removeAllViews()
        miniView.getCompanionSlot().getSlot().removeAllViews()

        setStatusBarColor()
    }

    fun setFirstQueueItemOnPlayer(shouldShowMiniPlayer: Boolean = true) {
        val firstItem = QueueManager.getFirstMedia()
        if (firstItem != null) {

            setPlayerQueueVisibility()

            swapPlayerView(firstItem)

            mediaContent = firstItem

            DataController.playerContentId = mediaContent?.id.toString()

            setCommunityDetail()

            setMediaDetail()

            if (mediaContent?.isRadio == true) {
                bottomSheetBinding.playerControlViewRadio.setMediaContent(mediaContent)
            } else {
                bottomSheetBinding.playerControlView.setMediaContent(mediaContent)
            }

            if (shouldShowMiniPlayer) {
                miniView.show()
            } else {
                miniView.hide()
            }

            playerPagerAdapter.updatePager(bottomSheetBinding.playerViewPager)

            queueAdapter?.updateQueue()
        } else {
            miniView.hide()
        }
    }

    private fun getMediaType(): Int {
        return if (mediaContent?.isRadio == true) {
            MiniPlayerView.MEDIA_TYPE_RADIO
        } else {
            MiniPlayerView.MEDIA_TYPE_CONTENT
        }
    }

    @Synchronized
    private fun setMediaDetail() {
        if (isFinishing || isDestroyed)
            return

        bottomSheetBinding.toolbarPlayer.handleMediaChange(mediaContent)

        miniView.setData(mediaContent, getMediaType())

        chatListener?.remove()

        if (mediaContent?.isRadio == true) {
            realtimeUpdateListener(mediaContent?.catalogId)
            basePlayerViewModel.sendEventToUI(BasePlayerViewModel.Event.FetchRadios)
        } else {
            bottomSheetBinding.commentCard.visibility = GONE
            if ((mediaContent?.duration ?: 0) > 0) {
                bottomSheetBinding.playerControlView.setTotalDuration(
                    mediaContent?.duration.secondsToMs(),
                    DateUtils.formatSeconds(mediaContent?.duration ?: 0)
                )
            }
        }

        bottomSheetBinding.toolbarPlayer.threeDotButtonVisibility(VISIBLE)

        if (mediaContent?.entitySubType == ENTITY_TYPE_LIVE_STREAM) {
            bottomSheetBinding.toolbarPlayer.setToolbarTitle("")
        } else {
            bottomSheetBinding.toolbarPlayer.setToolbarTitle(
                mediaContent?.entitySubType?.substring(0, 1)?.uppercase(Locale.getDefault())
                    .plus(mediaContent?.entitySubType?.substring(1))
            )
        }

        basePlayerViewModel.isUnlockContentBannerVisible(mediaContent?.hasPurchased.isFalse() || mediaContent?.isPurchaseNeeded.isTrue())

        previewTimeMillis =
            if (mediaContent?.hasPurchased.isFalse() || mediaContent?.isPurchaseNeeded.isTrue()) {
                (mediaContent?.previewTime ?: 0).secondsToMs()
            } else {
                mediaContent?.mediaDurationMs ?: 0
            }
    }

    override fun setPlayerQueueVisibility(): Boolean {
        if (DataController.isAdPlaying || (QueueManager.queue.size <= 1 && !mediaContent?.isRadio.isTrue())) {
            bottomSheetBinding.rvQueuePlayer.visibility = GONE
            bottomSheetBinding.headingLayoutPlayer.visibility =
                if (isCurrentMediaARadio()) INVISIBLE else GONE
            bottomSheetBinding.playerControlView.next?.isClickable = false
            return false
        } else {
            bottomSheetBinding.rvQueuePlayer.visible(isCurrentMediaARadio())
            bottomSheetBinding.headingLayoutPlayer.visible(isCurrentMediaARadio())
            bottomSheetBinding.playerControlView.next?.isClickable = true
            return true
        }
    }

    private fun processLikeDislike(action: String, isCatalogFollowed: Boolean? = null) {

        val isSomeThing = community?.userActions?.find { it.action.equals(action, true) }
        val value = if (isSomeThing?.actionValue == 1.0) {
            0.0
        } else {
            1.0
        }
        isSomeThing?.actionValue = value

        if (action == UserAction.LIKE.action) {
            if (value == 1.0) {
                community?.aggregations?.likes = community?.aggregations?.likes?.plus(1)
                community?.userActions?.find {
                    it.action.equals(UserAction.DISLIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                        community?.aggregations?.dislikes =
                            community?.aggregations?.dislikes?.minus(
                                1
                            )
                    }
                }

                val contentLink = "${Constants.WEB_BASE_URL}content/${content?.id}"
                val catalogLink =
                    "${Constants.WEB_BASE_URL}catalog/${content?.catalogId ?: content?.catalog?.id ?: ""}"

                MoEngageAnalytics.sendEvent(this, "content liked", Bundle().apply {
                    putString("vertical", content?.entitySubType ?: "")
                    putString(
                        "catalog title",
                        content?.catalogTitle ?: content?.catalog?.title ?: ""
                    )
                    putString("content title", content?.title ?: "")
                    putString("catalog id", content?.catalogId ?: content?.catalog?.id ?: "")
                    putString("content id", content?.id ?: "")
                    putString("catalog source", content?.source ?: content?.catalog?.source ?: "")
                    putString("catalog follow", if (isCatalogFollowed == true) "yes" else "no")
                    putString("content link", contentLink)
                    putString("catalog link", catalogLink)
                })
            } else {
                community?.aggregations?.likes = community?.aggregations?.likes?.minus(1)
            }
            content?.meta = community
        } else if (action == UserAction.DISLIKE.action) {
            if (value == 1.0) {
                community?.aggregations?.dislikes = community?.aggregations?.dislikes?.plus(1)
                community?.userActions?.find {
                    it.action.equals(UserAction.LIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                        community?.aggregations?.likes = community?.aggregations?.likes?.minus(1)
                    }
                }

                val contentLink = "${Constants.WEB_BASE_URL}content/${content?.id}"
                val catalogLink =
                    "${Constants.WEB_BASE_URL}catalog/${content?.catalogId ?: content?.catalog?.id ?: ""}"

                MoEngageAnalytics.sendEvent(this, "content disliked", Bundle().apply {
                    putString("vertical", content?.entitySubType ?: "")
                    putString(
                        "catalog title",
                        content?.catalogTitle ?: content?.catalog?.title ?: ""
                    )
                    putString("content title", content?.title ?: "")
                    putString("catalog id", content?.catalogId ?: content?.catalog?.id ?: "")
                    putString("content id", content?.id ?: "")
                    putString("catalog source", content?.source ?: content?.catalog?.source ?: "")
                    putString("content link", contentLink)
                    putString("catalog link", catalogLink)
                })
            } else {
                community?.aggregations?.dislikes = community?.aggregations?.dislikes?.minus(1)
            }
            content?.meta = community
        }

        setCommunityDetail()

        performAction(action, value)
    }

    private fun performAction(action: String, value: Double) {

        val entityType = "content"
        val id = mediaContent?.id.toString()

        val entitySubtype = mediaContent?.entitySubType.toString()

        val mediaAction = if (PrefUtils.isLoggedIn) {
            MediaAction(
                action,
                value,
                id,
                entityType,
                PrefUtils.userDetails?.id.toString(),
                entitySubtype,
                null
            )
        } else {
            MediaAction(
                action,
                value,
                id,
                entityType,
                PrefUtils.guestId,
                entitySubtype,
                null
            )
        }

        AnalyticsUtil.sendEvent(
            entitySubtype,
            id,
            "",
            action,
            AnalyticsUtil.podcast_media_player
        )

        actionHandler.handleClick(mediaAction, object : OnClickInterface<Community?> {
            override fun dataClicked(data: Community?) {

                community = data

                setCommunityDetail()
            }
        })
    }

    private fun setCommunityDetail() {
        if (!isCurrentMediaARadio()) {
            if ((community?.aggregations?.likes ?: 0) < 0) {
                community?.aggregations?.likes = 0
            }
            if ((community?.aggregations?.dislikes ?: 0) < 0) {
                community?.aggregations?.dislikes = 0
            }

            setTotalComment(community?.aggregations?.comments ?: 0)

            val isLike = community?.userActions?.find {
                it.action?.equals(UserAction.LIKE.action, true) == true
            }

            val isDisLike = community?.userActions?.find {
                it.action.equals(
                    UserAction.DISLIKE.action,
                    true
                )
            }
            if (isDisLike != null) {
//                bottomSheetBinding.dislike.isSelected = isDisLike.actionValue != 0.0
            } else {
                setMediaAction(UserAction.DISLIKE.action)
//                bottomSheetBinding.dislike.isSelected = false
            }
        }
    }

    private fun setTotalComment(number: Number) {
        with(bottomSheetBinding) {
            Utils.abbrNumberFormat(number).let {
                noOfComments.text = it
                tvTotalComment.text = it
            }
        }
    }

    private fun setMediaAction(action: String) {
        if (community?.userActions == null) {
            community?.userActions = ArrayList()
        }
        MediaAction(
            action,
            0.0,
            mediaContent?.id,
            mediaContent?.entityType,
            PrefUtils.userDetails?.id,
            mediaContent?.entitySubType,
            null
        ).let {
            community?.userActions?.add(it)
        }
    }

    override fun setPlayer(playerView: PlayerControlView) {
        mediaService?.setPlayerView(playerView)
    }

    override fun setPlayerVideo(playerView: PlayerView) {
        mediaService?.setPlayerView(playerView)
    }

    override fun getMainMediaSource() = mediaService?.getMainMediaSource()

    override fun isPlayWhenReady() = mediaService?.isPlayWhenReady() ?: false

    override fun isPlaying() = mediaService?.isPlaying() ?: false

    override fun changeDataSaver() = mediaService?.changeDataSaver()

    override fun getContentPlayState(contentId: String?) =
        mediaService?.getContentPlayState(contentId)

    override fun checkServiceAndProceed(
        eventId: String,
        callBack: ServiceActionCallback?
    ) {
        if (eventId == START_SERVICE) {
            serviceRunningCallback = null
            serviceRunningCallback = MutableLiveData()
            serviceRunningCallback?.observe(this) {
                callBack?.onProceed()
            }
            sendToMediaService(null)
        }
    }

    fun openPlayer() {
        when {
            liveStreamManager.isSessionActive() -> {
                liveStreamManager.expand()
            }

            isCurrentMediaARadio() -> {
                radioSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
            }

            else -> {
                bottomSheetBehavior?.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }

    }

    private fun isCurrentMediaARadio() = mediaContent?.isRadio ?: false

    private fun realtimeUpdateListener(radioId: String?) {
        val before24Hour = System.currentTimeMillis()
            .plus(
                TimeUnit.DAYS.toMillis(-1)
            )
        chatListener = DataController.fireStoreChat.collection("Comment")
            .document(radioId ?: "Noice")
            .collection("messages")
            .whereEqualTo("parentId", radioId)
            .orderBy("postedTime")
            .startAt(before24Hour)
            .addSnapshotListener { value, error ->
                if (error == null && value != null && value.documents.isNotEmpty()) {
                    val data = value.toObjects(Message::class.java)
                    val message = data[value.documents.size - 1]
                    bottomSheetBinding.commentCard.visibility = VISIBLE
                    setTotalComment(value.documents.size)
                    ImageUtils.loadImageByUrl(
                        bottomSheetBinding.imgUser,
                        message?.getSmallUserImage(),
                        true,
                        R.drawable.ic_user_profile, originalUrl = message?.userImage
                    )

                    bottomSheetBinding.txtMessage.text = message.chatText.unescapeJava()
                    bottomSheetBinding.txtName.text = message?.name

                    bottomSheetBinding.imgUser.setOnClickListener {
                        EventBus.getDefault().post(
                            OpenIndexEvent(
                                OpenIndexEvent.OPEN_USER_PAGE,
                                message?.userId,
                                targetPageId = "Radio_Player"
                            )
                        )
                    }
                    bottomSheetBinding.txtName.setOnClickListener {
                        bottomSheetBinding.imgUser.performClick()
                    }

                    animateView()
                } else {
                    setTotalComment(0)
                    bottomSheetBinding.commentCard.visibility = GONE
                }
            }
    }

    private fun animateView() {

        val animFadeIn = AnimationUtils.loadAnimation(
            this,
            R.anim.chat_fade_in
        )
        val animSlideUp = AnimationUtils.loadAnimation(
            this,
            R.anim.chat_slide_up
        )

        val s = AnimationSet(true)
        s.interpolator = AccelerateInterpolator()

        s.addAnimation(animSlideUp)
        s.addAnimation(animFadeIn)
        bottomSheetBinding.contentLayout.startAnimation(s)
    }

    fun pause(reason: String) = mediaService?.pause(reason)

    fun closePlayer() {
        if (bottomSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        }
        if (radioSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
            radioSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        }
        if (liveStreamManager.isSessionActive()) {
            liveStreamManager.collapse()
        }
    }

    override fun onBackPressed() {
        moveTaskToBack(true)
    }

    override fun isPlayingCurrentCatalog(homeContent: HomeContent): Boolean {
        return if ((homeContent.entityType == Constants.ENTITY_TYPE_CONTENT && homeContent.id == mediaContent?.id) ||
            homeContent.entityType == Constants.ENTITY_TYPE_CATALOG && homeContent.id == mediaContent?.catalogId
        ) {
            return DataController.playWhenReady
        } else {
            false
        }
    }

    override fun seekMedia(forward: Boolean) = mediaService?.seekMedia(forward)

    override fun getMediaSessionTimeMs() = mediaService?.getMediaSessionTimeMs()

    fun setPauseReason(reason: String) = mediaService?.setPauseReason(reason)

    override fun onResume() {
        super.onResume()

        if (DataController.wasPlayingVideo && DataController.isPlayerOpen) {
            DataController.wasPlayingVideo = false
            EventBus.getDefault().post(
                EventMessage(
                    PlayerDetailFragment.STREAM_TYPE_VIDEO,
                    Constants.STREAM_TYPE,
                    secondData = true
                )
            )
        }

        if (liveStreamManager.isSessionActive()) {
            val isHeadsetOn = Utils.isHeadsetOn(this)

            if (!isHeadsetOn) {
                val am = getSystemService(AUDIO_SERVICE) as AudioManager
                am.isSpeakerphoneOn = true
            }
        }
    }

    /* Display ads are visible when it's active/enabled at media player api level. */
    private fun getDisplayAds() {
        channelPodcastViewModel.getAds(
            Constants.Ads.AdsEntityType.PAGE,
            Constants.Ads.AdsEntityValue.MEDIA_PLAYER
        ).observe(this) { adResponse ->
            if (adResponse?.status == ResponseStatus.SUCCESS) {
                if (adResponse.data?.data?.isActive == true) {
                    playerPagerAdapter.updateAdResponse(adResponse.data.data)
                    lifecycleScope.launch {
                        basePlayerViewModel.sendDisplayAdResponseEvent(
                            Pair(
                                adResponse.data.data,
                                adView
                            )
                        )
                    }
                }
            }
        }
    }

    override fun setPlayerBackground(color: Int) {
        playerBgColor = color
        bottomSheetBinding.root.setBackgroundColor(color)
        setStatusBarColor(color)
    }

    override fun setStatusBarColor(color: Int?) {
        if (DataController.isPlayerOpen) {
            val blendedColor = ColorUtils.blendARGB(color ?: playerBgColor, Color.BLACK, 0.6f)
            window?.statusBarColor = blendedColor
        } else {
            val blendedColor = ColorUtils.blendARGB(
                ContextCompat.getColor(this, R.color.black700),
                ContextCompat.getColor(this, R.color.black700),
                0.6f
            )
            window?.statusBarColor = blendedColor
        }
    }

    override fun isPreViewContentPlayed(): Boolean {
        return (mediaContent?.isPreviewCompleted.isTrue() &&
                bottomSheetBinding.playerControlView.findViewById<TextView>(R.id.txtPreview).text == "00:00 tersisa" &&
                mediaContent?.hasPurchased.isFalse() &&
                mediaContent?.isPurchaseNeeded.isTrue())
    }

    override fun seekPlayer(secondsToMs: Long) {
        mediaService?.seekPlayer(secondsToMs)
    }

    override fun window(): Window {
        return this.window
    }

    override fun activity() = this

    fun getContent(): Content? = content
}