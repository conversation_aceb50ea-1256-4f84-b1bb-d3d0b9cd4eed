package noice.app.exoplayer

import android.app.Dialog
import android.content.Context
import android.media.AudioManager
import android.os.Bundle
import android.view.WindowManager
import androidx.mediarouter.app.MediaRouteChooserDialogFragment


class CustomChooserDialogFragment : MediaRouteChooserDialogFragment() {

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        dialog.window?.decorView?.systemUiVisibility = requireActivity().window.decorView.systemUiVisibility
        dialog.setOnShowListener {
            dialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
            val wm = requireContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
            wm.updateViewLayout(dialog.window?.decorView, dialog.window?.attributes)
        }
        dialog.volumeControlStream = AudioManager.STREAM_MUSIC
        return dialog
    }
}