package noice.app.exoplayer

import android.Manifest
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageButton
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.TimeBar
import com.google.ads.interactivemedia.v3.api.AdEvent
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.moengage.core.internal.utils.showToast
import dagger.hilt.android.AndroidEntryPoint import kotlinx.coroutines.* import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.CustomPlayerFragmentBinding
import noice.app.databinding.FragmentPlayerDetailBinding
import noice.app.enums.BadgeType
import noice.app.enums.UserAction
import noice.app.exoplayer.cast.CastManager
import noice.app.listner.ActionHandler
import noice.app.listner.OnClickInterface
import noice.app.model.ExoNotificationData
import noice.app.model.LoginDialogData
import noice.app.model.ads.AdsResponse
import noice.app.model.eventbus.EventMessage
import noice.app.model.user.User
import noice.app.modules.dashboard.home.fragment.NoiceContentMenu
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.modules.media.model.Share
import noice.app.modules.podcast.model.Content
import noice.app.observers.GlobalObservers
import noice.app.player.managers.QueueManager
import noice.app.player.models.PlayerEvent
import noice.app.player.models.PlayerEvent.Companion.PAUSE_END_LOADER
import noice.app.rest.ResponseStatus
import noice.app.ui.icon.Icons
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.ExperimentUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PermissionUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import noice.app.utils.getView
import noice.app.utils.hideWithAnimation
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import noice.app.utils.putAnalyticsKey
import noice.app.utils.showWithAnimation
import noice.app.utils.visible
import noice.app.views.CustomAdView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.net.URL

@AndroidEntryPoint
class PlayerDetailFragment : Fragment() {

    companion object {
        const val STREAM_TYPE_AUDIO = "STREAM_TYPE_AUDIO"
        const val STREAM_TYPE_VIDEO = "STREAM_TYPE_VIDEO"
        private const val PLAYER_DATA = "PLAYER_DATA"
        private const val TOOLBAR_DELAY = 300L
        const val PORTRAIT = "portrait"

        fun newInstance(
            exoNotificationData: ExoNotificationData?,
            bottomSheetBinding: CustomPlayerFragmentBinding
        ) = PlayerDetailFragment().apply {
            arguments = Bundle().apply {
                putParcelable(PLAYER_DATA, exoNotificationData)
            }
            this.bottomSheetBinding = bottomSheetBinding

        }
    }

    private val basePlayerViewModel: BasePlayerViewModel by activityViewModels()
    private var bottomSheetBinding: CustomPlayerFragmentBinding? = null
    private lateinit var binding: FragmentPlayerDetailBinding
    private lateinit var ctx: Context
    private var mediaContent: ExoNotificationData? = null
    private var share: Share? = null
    private var isFollowedMediaPlayerContent = 0.0
    private var catalogCommunity: Community? = null
    private var isSamePage = false
    private var userDetails: User? = null
    private lateinit var actionHandler: ActionHandler
    var community: Community? = null
    var content: Content? = null
    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null
    private var isFreshErrorCallback = true
    private var streamType = ""

    private var adView: CustomAdView? = null
    private var adsResponse: AdsResponse? = null
    private var isVideoPortrait: Boolean? = null
    private var streamingType = ""
    var hideViewJob: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mediaContent = arguments?.parcelable(PLAYER_DATA)
        streamType = if (mediaContent?.isDownloaded == true) {
            "downloaded"
        } else {
            "streamed"
        }
        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPlayerDetailBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewLifecycleOwner.lifecycleScope.launch {
            mediaContent?.videoUrl?.let {
                isVideoPortrait = if (mediaContent?.orientation != null) {
                    mediaContent?.orientation == PORTRAIT
                } else {
                    false
                }
            }
        }
        bottomSheetBinding?.playerControlView?.let {
//            if (isVideoPortrait == true) {
//                it.prev?.visibility = VISIBLE
//                it.next?.visibility = VISIBLE
//                it.forward?.visibility = GONE
//                it.reverse?.visibility = GONE
//            } else {
//                it.forward?.visibility = VISIBLE
//                it.reverse?.visibility = VISIBLE
//                it.prev?.visibility = GONE
//                it.next?.visibility = GONE
//            }
        }

        initViews()

        lifecycleScope.launch {
            /* handled for the first item on player, as response is received after it is added to player.
             * it's also called on next content and sets the ad. */
            launch {
                basePlayerViewModel.adResponseFlow.collectLatest {
                    if (it != null) {
                        adsResponse = it.first
                        adView = it.second
                        setAd()
                    }
                }
            }

            collectLikeIconVisibility()
            collectIsContentLiked()
            collectIsUnlockContentBannerVisible()
        }

        GlobalObservers.playerEventObserver
            .subscribePlayerEvents(
                viewLifecycleOwner, listOf(
                    PlayerEvent.RADIO_META_DATA,
                    PlayerEvent.PLAYER_ERROR_NO_INTERNET,
                    PlayerEvent.ENABLE_FULL_SCREEN_MODE,
                    PlayerEvent.ON_NEXT
                )
            ) {
                handlePlayerEvent(it)
            }
        GlobalObservers.playerEventObserver
            .subscribeForCastButton(this) { pair ->
                binding.castView.visible(pair.second.isTrue())
                if (pair.second.isTrue()) {
                    binding.equalizer.animateBars()
                } else {
                    binding.equalizer.stopBars()
                }
                /*if (DataController.isVideoStreamSelected.isTrue()){
                    binding.videoPlayerView.player = pair.first
                    ctx.getPlayerActivity()?.setPlayerVideo(binding.videoPlayerView)
                }*/
            }

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(this) {
                if (it.event == PlayerEvent.END_LOADER
                    && DataController.isPlayingVideo
                    && DataController.isPlaying
                ) {
                    if (isVideoPortrait == true
                        && streamingType == STREAM_TYPE_VIDEO
                        && bottomSheetBinding?.toolbarPlayer?.isVisible == true
                        && !DataController.isAdPlaying
                    ) {
                        hideViewJob?.cancel()
                        hideViewJob = lifecycleScope.launch {
                            delay(3000)
                            hideView()
                        }
                        return@subscribeMusicButtonEvents
                    }
                }
                if ((it.event == PlayerEvent.END_LOADER && DataController.isPlayingVideo && binding.errorLayout.visibility == VISIBLE) && Utils.isNetworkConnected.isTrue()) {
                    binding.errorLayout.visibility = GONE
                    binding.squareCardView.visibility = GONE
//                    binding.videoPlayerView.visibility = VISIBLE
                    videoPlayerVisible(true)
                }

                if (it.exoData?.showVideo == true && DataController.isPlayingVideo) {
                    binding.videoPlayerView.keepScreenOn = it.event != PlayerEvent.PAUSE_END_LOADER
                } else {
                    binding.videoPlayerView.keepScreenOn = false
                }

                if (it.exoData?.showVideo == true && DataController.isPlayingVideo && it.exoData.isPremium == true) {
                    ctx.getPlayerActivity()?.window()?.setFlags(
                        WindowManager.LayoutParams.FLAG_SECURE,
                        WindowManager.LayoutParams.FLAG_SECURE
                    )
                } else {
                    ctx.getPlayerActivity()?.window()
                        ?.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
                }
                if (it.event == PAUSE_END_LOADER
                    && DataController.isPlayingVideo
                ) {
                    hideViewJob?.cancel()
                }
            }

        /* Added this listener to handle the Display Ads visibility */
        GlobalObservers.contentPurchaseObserver
            .subscribe(this) { response ->
                if (response is Content && response.id == mediaContent?.id && response.catalogId == mediaContent?.catalogId) {
                    val data = QueueManager.getPlayingData()
                    data?.videoUrl = response.videoUrl
                    data?.songUrl = response.url ?: ""
                    data?.hasPurchased = response.hasPurchased
                    data?.isPurchaseNeeded = response.isPurchaseNeeded
                    data?.displayAdsEnabled = response.displayAdsEnabled

                    mediaContent = data

                    binding.frameAdView.visibility =
                        if (data?.displayAdsEnabled == true && isVideoPortrait == false)
                            VISIBLE
                        else GONE
                }
            }

        setMediaDetail()

        /* This code subscribes to ad events and hides the view after a 2.5-second delay
            when all ads are completed, but only if the video is in portrait
            orientation and the streaming type is video.
         */
        GlobalObservers.playerEventObserver
            .subscribeAdsEvent(this) { adEvent ->
                if (adEvent.type == AdEvent.AdEventType.ALL_ADS_COMPLETED) {
                    hideViewJob?.cancel()
                    hideViewJob = lifecycleScope.launch {
                        if (isVideoPortrait == true && streamingType == STREAM_TYPE_VIDEO) {
                            hideView()
                        }
                    }
                }
            }
    }

    private fun CoroutineScope.collectLikeIconVisibility() {
        launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                basePlayerViewModel.isLikeVisibleState.collect { isVisible ->
                    binding.likeIcon.visible(isVisible)
                }
            }
        }
    }

    private fun CoroutineScope.collectIsContentLiked() {
        launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                basePlayerViewModel.isContentLiked.collect { isLiked ->
                    binding.likeIcon.isSelected = isLiked
                    community?.userActions?.forEach {
                        if (it.action.equals(UserAction.LIKE.action, true)) {
                            it.actionValue = if (isLiked) 1.0 else 0.0
                        }
                    }
                }
            }
        }
    }

    private fun CoroutineScope.collectIsUnlockContentBannerVisible() {
        launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                basePlayerViewModel.isUnlockContentBannerVisible.collect { isVisible ->
                    binding.unlockContentBanner.visible(isVisible)
                }
            }
        }
    }

    private fun handlePlayerEvent(playerEvent: PlayerEvent) {
        if (playerEvent.event == PlayerEvent.ON_NEXT) {
            hideViewJob?.cancel()
        }
        if (playerEvent.event == PlayerEvent.RADIO_META_DATA) {
            val title = playerEvent.genericData.toString()
            if (title.isNotEmpty()) {
                binding.txtCurrent.visibility = VISIBLE
                binding.txtCurrent.text =
                    getString(R.string.currently_playing_colon).plus(" ").plus(title)
            } else {
                binding.txtCurrent.visibility = GONE
            }
        } else if (playerEvent.event == PlayerEvent.PLAYER_ERROR_NO_INTERNET) {
            if (!DataController.isPlayingVideo) {
                return
            }

            val bufferedDuration = binding.videoPlayerView.player?.totalBufferedDuration ?: 0
            val isSomethingInBuffer = bufferedDuration > 1000

            if (ctx.getPlayerActivity()?.isPlaying() != true && !isSomethingInBuffer) {
                binding.errorLayout.visibility = VISIBLE
                videoPlayerVisible(false)
                binding.squareCardView.visibility = GONE

                if (isFreshErrorCallback) {
                    isFreshErrorCallback = false

                    AnalyticsUtil.sendEvent("error_encountered", Bundle().apply {
                        putAnalyticsKey("errorType", "no connection")
                        putAnalyticsKey("errorName", "player")
                        putAnalyticsKey("errorMessage", playerEvent.event)
                        putAnalyticsKey("source", "expanded media player")
                        putAnalyticsKey("contentFormat", "video")
                        putAnalyticsKey("contentId", content?.id)
                        putAnalyticsKey("contentTitle", content?.title)
                        putAnalyticsKey("catalogId", content?.catalogId ?: content?.catalog?.id)
                        putAnalyticsKey(
                            "catalogTitle",
                            content?.catalogTitle ?: content?.catalog?.title
                        )
                    })
                }
            } else {
                if (QueueManager.getPlayingData()?.playVideo.isTrue() && Utils.isNetworkConnected.isFalse()) {
                    binding.errorLayout.visibility = VISIBLE
                    videoPlayerVisible(false)
                    binding.squareCardView.visibility = GONE
                } else {
                    binding.errorLayout.visibility = GONE
                    videoPlayerVisible(true)
                    binding.squareCardView.visibility = GONE
                }

                isFreshErrorCallback = true
            }
        } else if (playerEvent.event == PlayerEvent.ENABLE_FULL_SCREEN_MODE
            && isVideoPortrait == false
        ) {
            if (playerEvent.genericData == mediaContent?.id) {
                toggleFullScreen()
            }
        }
    }

    @OptIn(UnstableApi::class)
    private fun initViews() {
//        val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheetBinding?.root as View)
//        bottomSheetBehavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
//            override fun onStateChanged(bottomSheet: View, newState: Int) {
//                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
//                    /* This code delays hiding the player controls for portrait video
//                        content when the bottom sheet is expanded,
//                        but only if no ad is currently playing */
//                    if (DataController.isAdPlaying) return
//                    view?.postDelayed({
//                        if (isVideoPortrait && streamingType == STREAM_TYPE_VIDEO) {
//                            hideView()
//                        }
//                    }, 2500)
//                } else if (newState == BottomSheetBehavior.STATE_HIDDEN) {
//                    if (bottomSheetBinding?.toolbarPlayer?.isVisible == false) showView()
//                }
//            }
//
//            override fun onSlide(bottomSheet: View, slideOffset: Float) {
//                // Handle slide if needed
//            }
//        })
        actionHandler = ActionHandler(basePlayerViewModel, this)
        binding.castView.visible(CastManager.isCastConnected.isTrue())
        if (CastManager.isCastConnected.isTrue()) {
            binding.equalizer.animateBars()
        } else {
            binding.equalizer.stopBars()
        }

        binding.videoPlayerView.getView<ImageButton>(R.id.fullscreen)?.setOnClickListener {
            toggleFullScreen()
        }

        binding.txtChannelPlayer.setOnClickListener {

            when (mediaContent?.entitySubType) {
                Constants.ENTITY_TYPE_RADIO -> {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_RADIO_PAGE,
                            mediaContent?.catalogId,
                            mediaContent?.entitySubType + "_Media_Player"
                        )
                    )
                }

                ENTITY_TYPE_LIVE_STREAM -> {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_USER_PAGE_WITH_USER_NAME,
                            userDetails?.userName,
                            targetPageId = mediaContent?.entitySubType + "_Media_Player"
                        )
                    )
                }

                Constants.ENTITY_TYPE_AUDIO_BOOK -> {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_AUDIO_BOOK_PAGE,
                            mediaContent?.catalogId,
                            mediaContent?.entitySubType + "_Media_Player"
                        )
                    )
                }

                Constants.ENTITY_TYPE_AUDIO_SERIES -> {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_AUDIO_SERIES_PAGE,
                            mediaContent?.catalogId,
                            mediaContent?.entitySubType + "_Media_Player"
                        )
                    )
                }

                else -> {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_CATALOG_PAGE,
                            mediaContent?.catalogId,
                            mediaContent?.entitySubType + "_Media_Player"
                        )
                    )
                }
            }
        }

        binding.subscribeView.setOnClickListener {

            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBarInDialog(
                    binding.root,
                    getString(R.string.this_action_requires_internet)
                )
                return@setOnClickListener
            }

            if (mediaContent?.entitySubType == ENTITY_TYPE_LIVE_STREAM) {
                if (!PrefUtils.isLoggedIn) {
                    val loginDialogData =
                        ExperimentUtils.getLoginDialogData("login-on-follow-artist")
                    basePlayerViewModel.sendEventToUI(
                        BasePlayerViewModel.Event.HandleNotLoggedIn(
                            loginDialogData
                        )
                    )
                    return@setOnClickListener
                }
                processUserFollow()
            } else {
                if (!PrefUtils.isLoggedIn) {
                    val loginDialogData =
                        ExperimentUtils.getLoginDialogData("login-on-subscribe-catalog")
                    basePlayerViewModel.sendEventToUI(
                        BasePlayerViewModel.Event.HandleNotLoggedIn(
                            loginDialogData
                        )
                    )
                    return@setOnClickListener
                }
                processFollow()
            }
        }

        binding.root.setOnClickListener {
            hideViewJob?.cancel()
            
            if (isVideoPortrait == true && streamingType == STREAM_TYPE_VIDEO) {
                bottomSheetBinding?.let {
                    if (it.controllerLayout.isVisible) {
                        hideView()
                    } else {
                        showView()
                    }
                }
            }
        }
        bottomSheetBinding?.playerControlView?.exoProgress?.addListener(object :
            TimeBar.OnScrubListener {
            override fun onScrubStart(timeBar: TimeBar, position: Long) {
                // User started scrubbing
                hideViewJob?.cancel()
            }

            override fun onScrubMove(timeBar: TimeBar, position: Long) {
                // User is moving the scrubber
            }

            override fun onScrubStop(timeBar: TimeBar, position: Long, canceled: Boolean) {
                // User stopped scrubbing
            }
        })

        binding.unlockContentBanner.setOnClickListener {
            if (!NetworkUtils.isNetworkConnected(ctx)) {
                showActionRequiresInternetDialog()
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                handleUserNotLoggedIn(source = "podcast_media_player")
                return@setOnClickListener
            }
            ClickHandler.openPurchaseDialog(
                requireActivity().supportFragmentManager,
                content,
                "podcast_media_player"
            )
        }
    }

    private fun initLike(isLiked: Boolean, isRadio: Boolean) = with(binding.likeIcon) {
        if (isRadio) {
            basePlayerViewModel.isLikeIconVisible(false)
            return@with
        }

        basePlayerViewModel.isContentLiked(isLiked)

        setOnClickListener {
            onLikeIconClicked()
        }
    }

    private fun onLikeIconClicked() {
        if (mediaContent?.hasPurchased.isFalse() || mediaContent?.isPurchaseNeeded.isTrue()) return
        if (!NetworkUtils.isNetworkConnected(ctx)) {
            showActionRequiresInternetDialog()
            return
        }

        if (!PrefUtils.isLoggedIn) {
            handleUserNotLoggedIn(
                "podcast_media_player",
                ExperimentUtils.getLoginDialogData("login-on-content-like-dislike")
            )
            return
        }

        val isCatalogFollowed = content?.catalogMeta?.userActions?.find { action ->
            action.action == UserAction.FOLLOW.action
        }?.actionValue == NoiceContentMenu.CATALOG_FOLLOWED

        val mediaAction =
            community?.userActions?.find { it.action.equals(UserAction.LIKE.action, true) }
        val newLikeValue = if (mediaAction?.actionValue == 1.0) {
            0.0
        } else {
            1.0
        }

        mediaAction?.actionValue = newLikeValue

        if (newLikeValue == 1.0) {
            community?.aggregations?.likes = community?.aggregations?.likes?.plus(1)
            community?.userActions?.find {
                it.action.equals(UserAction.DISLIKE.action, true)
            }?.let {
                if (it.actionValue == 1.0) {
                    it.actionValue = 0.0
                    community?.aggregations?.dislikes =
                        community?.aggregations?.dislikes?.minus(
                            1
                        )
                }
            }

            sendMoEngageLikeEvent(isCatalogFollowed)
            basePlayerViewModel.isContentLiked(true)
        } else {
            community?.aggregations?.likes = community?.aggregations?.likes?.minus(1)
            basePlayerViewModel.isContentLiked(false)
        }
        content?.meta = community

        performAction(UserAction.LIKE.action, newLikeValue, "content")

        AnalyticsUtil.sendEvent(
            mediaContent?.entitySubType ?: "",
            mediaContent?.id ?: "",
            mediaContent?.id,
            "content_liked",
            "queue",
            (mediaContent?.timeElapsed ?: 0).toString(),
            (mediaContent?.duration ?: 0).toString(),
            "",
            mediaContent?.catalogTitle ?: "",
            mediaContent?.title ?: "",
            isCatalogFollowed = if (isCatalogFollowed) "yes" else "no"
        )
    }

    private fun showActionRequiresInternetDialog() {
        bottomSheetBinding?.let {
            Utils.showSnackBarInDialog(
                it.bottomSheetPlayer,
                getString(R.string.this_action_requires_internet)
            )
        }
    }

    private fun handleUserNotLoggedIn(
        source: String? = null,
        loginDialogData: LoginDialogData? = null
    ) {
        (ctx as BasePlayerActivity).handleUserNotLoggedIn(
            source = source,
            loginDialogData = loginDialogData
        )
    }

    private fun sendMoEngageLikeEvent(isCatalogFollowed: Boolean) {
        val contentLink = "${Constants.WEB_BASE_URL}content/${content?.id}"
        val catalogLink =
            "${Constants.WEB_BASE_URL}catalog/${content?.catalogId ?: content?.catalog?.id ?: ""}"

        MoEngageAnalytics.sendEvent(ctx, "content liked", Bundle().apply {
            putString("vertical", content?.entitySubType ?: "")
            putString("catalog title", content?.catalogTitle ?: content?.catalog?.title ?: "")
            putString("content title", content?.title ?: "")
            putString("catalog id", content?.catalogId ?: content?.catalog?.id ?: "")
            putString("content id", content?.id ?: "")
            putString("catalog source", content?.source ?: content?.catalog?.source ?: "")
            putString("catalog follow", if (isCatalogFollowed) "yes" else "no")
            putString("content link", contentLink)
            putString("catalog link", catalogLink)
        })
    }

    /* This function is responsible for showing various UI
       elements of the player interface with animations */
    private fun hideView() {
        if (streamingType == STREAM_TYPE_AUDIO || isVideoPortrait == false) return

        bottomSheetBinding?.let {
            // hide the portrait overlay controls and overlays
            if (isVideoPortrait == true) {
                it.videoPlayerOverlay.hideWithAnimation()
                it.controllerOverlay.hideWithAnimation()
            }
            it.toolbarPlayer.animate()
                .alpha(0f)
                .setDuration(TOOLBAR_DELAY)
                .withEndAction {
                    it.toolbarPlayer.visibility = View.INVISIBLE
                    it.toolbarPlayer.alpha = 1f
                }
                .start()

            it.controllerLayout.hideWithAnimation()

            if (mediaContent?.isRadio.isTrue()) it.likeCommentsShareBar.hideWithAnimation()
            else it.buttonBar.hideWithAnimation()

            binding.titlePlayer.hideWithAnimation()

            binding.channelInfoLayout.hideWithAnimation()

            if (binding.frameAdView.isVisible) {
                binding.frameAdView.hideWithAnimation()
            }
            binding.subscribeCardView.hideWithAnimation()

            if (ctx.getPlayerActivity()?.setPlayerQueueVisibility() == false) {
                return
            }
            bottomSheetBinding?.headingLayoutPlayer?.hideWithAnimation()
            bottomSheetBinding?.rvQueuePlayer?.hideWithAnimation()
            binding.likeIcon.hideWithAnimation()
        }
    }

    /* This function is responsible for showing various UI
       elements of the player interface with animations */
    private fun showView(animate: Boolean = true) {
        bottomSheetBinding?.let {
            it.toolbarPlayer.visibility = VISIBLE
            if (animate) {
                it.toolbarPlayer.alpha = 0f
                it.toolbarPlayer.animate()
                    .alpha(1f)
                    .setDuration(TOOLBAR_DELAY)
                    .start()
            }

            if (isVideoPortrait == true) {
                it.videoPlayerOverlay.showWithAnimation(animate = animate)
                it.controllerOverlay.showWithAnimation(animate = animate)
            } else {
                adsResponse?.let {
                    binding.frameAdView.visibility = VISIBLE
                }
            }
            // default show views
            it.controllerLayout.showWithAnimation(animate = animate)

            if (mediaContent?.isRadio.isTrue()) it.likeCommentsShareBar.showWithAnimation(animate = animate)
            else it.buttonBar.showWithAnimation(animate = animate)

            binding.titlePlayer.showWithAnimation(animate = animate)

            binding.channelInfoLayout.showWithAnimation(animate = animate)

            if (binding.frameAdView.visibility == View.INVISIBLE) {
                binding.frameAdView.showWithAnimation(animate = animate)
            }
            binding.subscribeCardView.showWithAnimation(animate = animate)

            if (ctx.getPlayerActivity()
                    ?.setPlayerQueueVisibility() == true && isCurrentMediaARadio()
            ) {
                bottomSheetBinding?.headingLayoutPlayer?.showWithAnimation(animate = animate)
                bottomSheetBinding?.rvQueuePlayer?.showWithAnimation(animate = animate)
            }
            binding.likeIcon.showWithAnimation(animate = animate)
        }
    }

    private fun toggleFullScreen() {
        if (DataController.isVideoInFullscreen) {
            DataController.isVideoInFullscreen = false
        } else {

            val networkType = NetworkUtils.getNetworkType(ctx)
            val elapsedTime = binding.videoPlayerView.player?.contentPosition.msToSeconds()
            val contentFormat = if (DataController.isPlayingVideo) {
                "video"
            } else {
                "audio"
            }
            val durationPlayed = ctx.getPlayerActivity()?.getMediaSessionTimeMs().msToSeconds()
            AnalyticsUtil.sendEvent("change_orientation", Bundle().apply {
                putAnalyticsKey("orientationTo", "landscape")
                putAnalyticsKey("contentId", mediaContent?.id)
                putAnalyticsKey("contentTitle", mediaContent?.title)
                putAnalyticsKey("contentDuration", mediaContent?.duration)
                putAnalyticsKey("entitySubType", mediaContent?.entitySubType)
                putAnalyticsKey("networkType", networkType)
                putAnalyticsKey("elapsedTime", elapsedTime)
                putAnalyticsKey("catalogId", mediaContent?.catalogId)
                putAnalyticsKey("catalogTitle", mediaContent?.catalogTitle)
                putAnalyticsKey("catalogSource", mediaContent?.source)
                putAnalyticsKey("streamType", streamType)
                putAnalyticsKey("durationPlayed", durationPlayed)
                putAnalyticsKey("contentFormat", contentFormat)
            })

            DataController.isVideoInFullscreen = true
            binding.videoPlayerView.player = null
            FullscreenPlayerActivity.start(ctx, mediaContent)
        }
    }

    private fun setMediaDetail() {
        share = null

        if (mediaContent?.isRadio == false) {
            getCommunityDetail()
            binding.titlePlayer.isSelected = true
        }

        if (mediaContent?.entitySubType == ENTITY_TYPE_LIVE_STREAM) {
            binding.txtChannelPlayer.text = mediaContent?.catalogTitle ?: ""
            if (mediaContent?.userId == PrefUtils.userDetails?.id) {
                binding.subscribeCardView.visibility = GONE
            } else {
                binding.subscribeCardView.visibility = VISIBLE
            }
            binding.squareCardView.visibility = GONE
        } else {
            // change the follow button into subscribe button
            binding.subscribeCardView.visibility = VISIBLE
            if (!mediaContent?.catalogTitle.isNullOrEmpty()) {
                if (mediaContent?.catalogTitle?.length!! > 25) {
                    binding.txtChannelPlayer.text =
                        mediaContent?.catalogTitle?.substring(0, 25).plus("...")
                } else {
                    binding.txtChannelPlayer.text = mediaContent?.catalogTitle ?: ""
                }
            }
            binding.titlePlayer.text = mediaContent?.title.toString()
            binding.squareCardView.visibility = VISIBLE
        }
        binding.errorLayout.visibility = GONE
        videoPlayerVisible(false)

        handleCoverImage()

        if (mediaContent?.entitySubType != ENTITY_TYPE_LIVE_STREAM) {
            getCatalogCommunityDetail()
        }

        if (DataController.isPlayingVideo) {
            handleStreamType(STREAM_TYPE_VIDEO)
        } else {
            handleStreamType(STREAM_TYPE_AUDIO)
        }
    }

    private fun handleCoverImage() {
        val imageUrl = mediaContent?.image
        val originalUrl = mediaContent?.image

        if (Utils.isBookStyle(mediaContent?.entitySubType)) {
            binding.coverImage.visibility = GONE
            ImageUtils.showBlurImage(
                imageUrl,
                binding.coverBackgroundImage,
                placeHolder = R.drawable.ic_thumb_square
            )
            ImageUtils.loadImageByUrl(
                binding.coverImageAudioBook,
                imageUrl,
                placeHolder = R.drawable.ic_audiobook_default,
                originalUrl = originalUrl
            )
            binding.audioBookLayout.visibility = VISIBLE
        } else {
            binding.audioBookLayout.visibility = GONE
            ImageUtils.loadImageByUrl(
                binding.coverImage,
                imageUrl,
                placeHolder = R.drawable.ic_thumb_square,
                originalUrl = originalUrl
            )
            binding.coverImage.visibility = VISIBLE
        }

        ctx.getPlayerActivity()?.setPlayerBackground(ContextCompat.getColor(ctx, R.color.black700))
        ImageUtils.getBitmapFromUrl(ctx, imageUrl, R.drawable.ic_thumb_default, originalUrl)
            .observe(viewLifecycleOwner) { bitmap ->
                ImageUtils.getDominantColor(
                    viewLifecycleOwner,
                    bitmap,
                    ContextCompat.getColor(ctx, R.color.black700)
                ) { color ->
                    // You now have the dominant color as an integer
                    ctx.getPlayerActivity()?.setPlayerBackground(color)
                }
            }
    }

    private fun processUserFollow() {

        val value = if (isFollowedMediaPlayerContent > 0) {
            0.0
        } else {
            1.0
        }

        isFollowedMediaPlayerContent = value

        updateFollowUI()

        performAction(UserAction.FOLLOW.action, value, "user")
    }

    private fun processFollow() {

        val value = if (isFollowedMediaPlayerContent > 0) {
            0.0
        } else {
            1.0
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (value == 1.0) {
                /* notification permission for Android 13 */
                if (permissionUtils?.isAllPermissionGranted() == false) {
                    permissionUtils?.requestPermissions()
                    return
                }
            }
        }

        isFollowedMediaPlayerContent = value

        updateFollowUI()

        val eventName = if (value == 0.0) {
            "catalog_unfollowed"
        } else {
            showToast(ctx, "Berhasil ditambahkan ke Koleksi")
            "catalog_followed"
        }

        val eventNameAmplitude = if (value == 0.0) "catalog unfollowed" else "catalog followed"
        MoEngageAnalytics.sendEvent(ctx, eventNameAmplitude, Bundle().apply {
            putString("vertical", mediaContent?.entitySubType ?: "")
            putString("catalog title", mediaContent?.catalogTitle ?: "")
            putString("catalog id", mediaContent?.catalogId ?: "")
            putStringArrayList(
                "genre",
                ArrayList(mediaContent?.genres?.map { it.name ?: "" } ?: ArrayList()))
            putString("catalog source", mediaContent?.source ?: "")
            putString("image url", mediaContent?.catalogImage ?: "")
            putString("content id", mediaContent?.id ?: "")
        })

        AnalyticsUtil.sendEventForCatalogFollow(
            mediaContent?.entitySubType ?: "",
            eventName,
            mediaContent?.catalogId,
            mediaContent?.catalogTitle ?: "",
            "media_player"
        )

        if (isCurrentMediaARadio()) {
            MoEngageAnalytics.sendEvent(ctx, "radio favorited", Bundle().apply {
                putString("radio title", content?.catalogTitle ?: content?.catalog?.title ?: "")
                putString("radio id", content?.catalogId ?: content?.catalog?.id ?: "")
            })
        }

        performAction(UserAction.FOLLOW.action, value, "catalog")
    }

    private fun performAction(action: String, value: Double, entityType: String) {

        val id = when (entityType) {
            "catalog" -> {
                mediaContent?.catalogId
            }

            "user" -> {
                mediaContent?.userId
            }

            else -> {
                mediaContent?.id
            }
        }

        val entitySubtype = when (entityType) {
            "catalog" -> {
                mediaContent?.entitySubType ?: ""
            }

            "user" -> {
                "user"
            }

            else -> {
                mediaContent?.entitySubType ?: ""
            }
        }

        val mediaAction = if (PrefUtils.isLoggedIn) {
            MediaAction(
                action,
                value,
                id,
                entityType,
                PrefUtils.userDetails?.id.toString(),
                entitySubtype,
                null
            )
        } else {
            MediaAction(
                action,
                value,
                id,
                entityType,
                PrefUtils.guestId,
                entitySubtype,
                null
            )
        }

        AnalyticsUtil.sendEvent(
            entitySubtype,
            id,
            "",
            action,
            AnalyticsUtil.podcast_media_player
        )

        actionHandler.handleClick(mediaAction, object : OnClickInterface<Community?> {
            override fun dataClicked(data: Community?) {

                when (entityType) {
                    "catalog" -> {
                        catalogCommunity = data
                        isSamePage = true
                        EventBus.getDefault()
                            .post(EventMessage(mediaAction, Constants.USER_ACTIVITY_FOLLOW))
                    }

                    "user" -> {
                        userDetails?.meta = data
                        isSamePage = true
                        EventBus.getDefault()
                            .post(EventMessage(mediaAction, Constants.USER_ACTIVITY_FOLLOW))
                    }

                    else -> {
                        community = data
                    }
                }
                val isLike = action != UserAction.FOLLOW.action
                setCommunityDetail(isLike)
            }
        })
    }

    private fun updateFollowUI() {

        if (isFollowedMediaPlayerContent == 1.0) {
//            binding.subscribeImageView.isSelected = true
            binding.subscribeImageView.setImageResource(R.drawable.ic_subscribed)
            return
            when (mediaContent?.entitySubType) {
                Constants.ENTITY_TYPE_AUDIO_BOOK -> {
                    binding.followBtnPlayer.isSelected = true
                    binding.followBtnPlayer.text = getString(R.string.saved)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.neutral_grey
                        )
                    )
                }

                ENTITY_TYPE_LIVE_STREAM -> {
                    binding.followBtnPlayer.isSelected = true
                    binding.followBtnPlayer.text = getString(R.string.subscribed)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.follow_text_color
                        )
                    )
                }

                Constants.ENTITY_TYPE_RADIO -> {
                    binding.followBtnPlayer.isSelected = true
                    binding.followBtnPlayer.text = getString(R.string.favorited)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.neutral_grey
                        )
                    )
                }

                else -> {
                    binding.followBtnPlayer.isSelected = true
                    binding.followBtnPlayer.text = getString(R.string.subscribed)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.neutral_grey
                        )
                    )
                }
            }
        } else {
//            binding.subscribeImageView.isSelected = false
            binding.subscribeImageView.setImageResource(R.drawable.ic_subscribe)
            return

            when (mediaContent?.entitySubType) {
                Constants.ENTITY_TYPE_AUDIO_BOOK -> {
                    binding.followBtnPlayer.isSelected = false
                    binding.followBtnPlayer.text = getString(R.string.save)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.dull_white
                        )
                    )
                }

                ENTITY_TYPE_LIVE_STREAM -> {
                    binding.followBtnPlayer.isSelected = false
                    binding.followBtnPlayer.text = getString(R.string.subscribe)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.follow_text_color
                        )
                    )
                }

                Constants.ENTITY_TYPE_RADIO -> {
                    binding.followBtnPlayer.isSelected = false
                    binding.followBtnPlayer.text = getString(R.string.favorite)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.dull_white
                        )
                    )
                }

                else -> {
                    binding.followBtnPlayer.isSelected = false
                    binding.followBtnPlayer.text = getString(R.string.subscribe)
                    binding.followBtnPlayer.setTextColor(
                        ContextCompat.getColor(
                            ctx,
                            R.color.dull_white
                        )
                    )
                }
            }
        }
    }

    private fun setCommunityDetail(isLikeContent: Boolean = false) {

        if (isCurrentMediaARadio()) {
            catalogCommunity?.userActions?.find {
                it.action.equals(UserAction.FOLLOW.action, true)
            }.let { isFollow ->
                isFollowedMediaPlayerContent = isFollow?.actionValue ?: 0.0
                updateFollowUI()
            }
            binding.followCount.visibility = GONE
        } else {
            if (!isLikeContent) {
                if (mediaContent?.entitySubType == ENTITY_TYPE_LIVE_STREAM) {
                    userDetails?.meta?.userActions?.find {
                        it.action.equals(UserAction.FOLLOW.action, true)
                    }.let { isFollow ->
                        isFollowedMediaPlayerContent = isFollow?.actionValue ?: 0.0
                        updateFollowUI()
                    }
                    binding.followCount.visibility = GONE
                } else {
                    // temporarily hide follow count
                    binding.followCount.visibility = GONE
                    binding.followCount.text =
                        Utils.abbrNumberFormat(catalogCommunity?.aggregations?.followers ?: 0)

                    catalogCommunity?.userActions?.find {
                        it.action.equals(UserAction.FOLLOW.action, true)
                    }.let { isFollow ->
                        isFollowedMediaPlayerContent = isFollow?.actionValue ?: 0.0
                        updateFollowUI()
                    }
                }

                binding.txtCurrent.visibility = GONE
            }
            //setRssContent()
        }

        when (content?.badge) {
            BadgeType.EARLY_ACCESS.badge -> {
                binding.vipBadge.visibility = VISIBLE
                binding.vipBadge.setImageResource(Icons.EarlyAccess)
            }

            BadgeType.PREMIUM.badge -> {
                binding.vipBadge.visibility = VISIBLE
                binding.vipBadge.setImageResource(Icons.Premium)
            }

            BadgeType.VIP.badge -> {
                binding.vipBadge.visibility = VISIBLE
                binding.vipBadge.setImageResource(Icons.Vip)
            }

            else -> {
                binding.vipBadge.visibility = GONE
            }
        }
    }

    private fun updateSomeQueueData() {
        var hasDataUpdated = false

        if (mediaContent?.isVideoPremium != content?.isVideoPremium) {
            hasDataUpdated = true
            mediaContent?.isVideoPremium = content?.isVideoPremium
        }
        if (mediaContent?.hasVideoAccess != content?.hasVideoAccess) {
            hasDataUpdated = true
            mediaContent?.hasVideoAccess = content?.hasVideoAccess
        }
        if (mediaContent?.showVideo != content?.showVideo) {
            hasDataUpdated = true
            mediaContent?.showVideo = content?.showVideo
        }
        if (mediaContent?.videoUrl != content?.videoUrl) {
            hasDataUpdated = true
            mediaContent?.videoUrl = content?.videoUrl
        }
        if (mediaContent?.isPremium != content?.isPremium) {
            hasDataUpdated = true
            mediaContent?.isPremium = content?.isPremium
        }
        if (mediaContent?.hasPurchased != content?.hasPurchased) {
            hasDataUpdated = true
            mediaContent?.hasPurchased = content?.hasPurchased
        }

        if (hasDataUpdated) {
            QueueManager.updateQueueData(mediaContent)

            basePlayerViewModel.sendEventToUI(BasePlayerViewModel.Event.ResetMediaData(mediaContent))
        }
    }

    private fun getCommunityDetail() {
        enablePlayerActions(false)
        if (!mediaContent?.id.isNullOrEmpty() || !mediaContent?.entityType.isNullOrEmpty()) {

            basePlayerViewModel.getEpisodeDetails(mediaContent?.id ?: "")
                .observe(viewLifecycleOwner) {

                    if (it?.status == ResponseStatus.LOADING && it.data?.data == null) {
                        return@observe
                    }

                    if ((it?.status == ResponseStatus.SUCCESS || it?.status == ResponseStatus.LOADING) && it.data?.data != null) {
                        with(it.data.data) {
                            community = this?.meta
                            content = this

                            val isLike = this?.meta?.userActions?.find { userAction ->
                                userAction.action?.equals(UserAction.LIKE.action, true) == true
                            }

                            binding.tvUnlockContentBanner.text = content?.price?.let { coin ->
                                getString(R.string.unlock_content_with_coins,
                                    coin
                                )
                            }

                            initLike(isLike?.actionValue == 1.0, mediaContent?.isRadio ?: false)
                        }

                        updateSomeQueueData()

                        EventBus.getDefault().post(EventMessage(content, Constants.COMMUNITY_DATA))

                        if (content?.imageMeta?.size500.isNullOrEmpty()) {
                            content?.imageMeta?.size500 = content?.catalog?.imageMeta?.size300
                            mediaContent?.image = content?.catalog?.imageMeta?.size300
                        }

                        if (mediaContent?.entitySubType == ENTITY_TYPE_LIVE_STREAM) {
                            mediaContent?.duration = content?.duration
                            if (!content?.users.isNullOrEmpty()) {
                                if (mediaContent?.userId != content?.users?.get(0)?.id) {
                                    mediaContent?.userId = content?.users?.get(0)?.id
                                    binding.txtChannelPlayer.text =
                                        content?.users?.get(0)?.userName ?: ""
                                    mediaContent?.catalogTitle =
                                        content?.users?.get(0)?.userName ?: ""
                                }
                                getUserDetail()
                                basePlayerViewModel.sendEventToUI(BasePlayerViewModel.Event.RefreshMiniPlayer)
                            }
                        } else {
                            setCommunityDetail()
                            enablePlayerActions(true)
                        }
                    }
                }
        }
    }

    private fun getUserDetail() {
        val map = HashMap<String, Any>()
        map["includeEntities"] = "[\"userActions\",\"user\",\"live\"]"
        enablePlayerActions(false)
        if (!mediaContent?.userId.isNullOrEmpty()) {

            basePlayerViewModel.getUserDetailsByUserId(
                mediaContent?.userId.toString(),
                map,
                mediaContent?.userId.toString()
            ).observe(viewLifecycleOwner) {
                if (it?.status == ResponseStatus.SUCCESS) {
                    userDetails = it.data?.data
                    mediaContent?.userId = userDetails?.id
                    setCommunityDetail()
                    enablePlayerActions(true)
                    if (PrefUtils.isLoggedIn && userDetails?.id == PrefUtils.userDetails?.id) {
                        // change from follow button to subscription card
                        binding.subscribeCardView.visibility = GONE
                    } else {
                        binding.subscribeCardView.visibility = VISIBLE
                    }
                }
            }
        }
    }

    private fun enablePlayerActions(enable: Boolean) {
        binding.subscribeCardView.isEnabled = enable
    }

    private fun getCatalogCommunityDetail() {
        enablePlayerActions(false)
        if (!mediaContent?.catalogId.isNullOrEmpty()) {
            val map = HashMap<String, String>()
            map["userId"] = PrefUtils.userDetails?.id.toString()
            map["entityId"] = mediaContent?.catalogId.toString()
            map["entityType"] = "catalog"

            basePlayerViewModel.getCommunityDetail(map).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS) {
                    catalogCommunity = it.data
                    setCommunityDetail()
                    enablePlayerActions(true)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        if (event.eventCode == Constants.USER_ACTIVITY_FOLLOW) {
            if (isSamePage) {
                isSamePage = false
            } else {
                val data = event.data as MediaAction
                val followAction = catalogCommunity?.userActions?.find { mediaAction ->
                    mediaAction.action.equals(UserAction.FOLLOW.action, true)
                }

                if (followAction != null) {
                    followAction.actionValue = data.actionValue
                    val index = catalogCommunity?.userActions?.indexOf(followAction)
                    catalogCommunity?.userActions?.set(index ?: 0, followAction)
                } else {
                    catalogCommunity?.userActions?.add(data)
                }
                isSamePage = false
                setCommunityDetail()
            }
        } else if (event.eventCode == Constants.STREAM_TYPE) {
            val streamType = event.data as String
            handleStreamType(streamType = streamType)
        }
    }

    @OptIn(UnstableApi::class)
    private fun handleStreamType(streamType: String) {
        streamingType = streamType
        Log.d("isvideopotrait", "isvideopotrait: $isVideoPortrait")
        binding.videoPlayerView.player = null
        bottomSheetBinding?.videoPlayerView?.player = null
        if (streamType == STREAM_TYPE_VIDEO) {
            if (Utils.isNetworkConnected) {
                binding.errorLayout.visibility = GONE

                if (isVideoPortrait == true) {
                    bottomSheetBinding?.videoPlayerOverlay?.visibility = VISIBLE
                    bottomSheetBinding?.controllerOverlay?.visibility = VISIBLE
//                    if (bottomSheetBinding?.videoPlayerView?.player == null) {
//                        bottomSheetBinding?.videoPlayerView?.let {
//                            ctx.getPlayerActivity()?.setPlayerVideo(it)
//                        }
//                    }
                    showView(animate = false)
                    binding.frameAdView.visibility = GONE
                    ctx.getPlayerActivity()
                        ?.setPlayerBackground(ContextCompat.getColor(ctx, R.color.black700))
                    bottomSheetBinding?.headingLayoutPlayer?.setBackgroundResource(R.drawable.bottom_sheet_rounded_black_24dp)
                    bottomSheetBinding?.rvQueuePlayer?.setBackgroundResource(R.color.black700)
                } else {
                    hideViewJob?.cancel()
                    
                    bottomSheetBinding?.videoPlayerOverlay?.visibility = GONE
                    bottomSheetBinding?.controllerOverlay?.visibility = GONE
                    binding.videoPlayerView.showController()
                    showView(animate = false)
//                    if (binding.videoPlayerView.player == null) {
//                        ctx.getPlayerActivity()?.setPlayerVideo(binding.videoPlayerView)
//                    }
                    bottomSheetBinding?.headingLayoutPlayer?.setBackgroundResource(R.drawable.bottom_sheet_rounded_grey_24dp)
                    bottomSheetBinding?.rvQueuePlayer?.setBackgroundResource(R.color.white10)
                }
                binding.squareCardView.visibility = GONE
                videoPlayerVisible(true)
            } else {
                hideViewJob?.cancel()
                
                if (bottomSheetBinding?.controllerLayout?.isVisible == false) showView()
                binding.errorLayout.visibility = VISIBLE
                binding.videoPlayerView.visibility = GONE
                bottomSheetBinding?.videoPlayerView?.visibility = GONE
                binding.squareCardView.visibility = GONE
            }
//            binding.videoPlayerView.player?.addListener(object : Player.Listener {
//                override fun onVideoSizeChanged(videoSize: VideoSize) {
//                    super.onVideoSizeChanged(videoSize)
//                    if (streamType == STREAM_TYPE_AUDIO) return
//                    if (videoSize.width > videoSize.height) {
//                        // Landscape video
//                        basePlayerViewModel.setOrientationPortrait(false)
//                        bottomSheetBinding?.videoPlayerView?.visibility = GONE
//                        binding.videoPlayerView.visibility = VISIBLE
//
//                        Log.d("testing video landscape vide size", "landscape")
//                        // Ensure the player is set for landscape
////                        if (binding.videoPlayerView.player == null) {
//                            ctx.getPlayerActivity()?.setPlayerVideo(binding.videoPlayerView)
////                        }
//                    } else {
//                        // Portrait video
//                        basePlayerViewModel.setOrientationPortrait(true)
//                        binding.videoPlayerView.visibility = GONE
//                        bottomSheetBinding?.videoPlayerView?.visibility = VISIBLE
//
//                        // Ensure the player is set for portrait
////                        if (bottomSheetBinding?.videoPlayerView?.player == null) {
//                            bottomSheetBinding?.videoPlayerView?.let { ctx.getPlayerActivity()?.setPlayerVideo(it) }
////                        }
//
//                        Log.d("testing video potrait vide size", "potrait")
//                    }
//                }
//            })

            //if ads on video player disabled
            if (PrefUtils.appCDNConfig?.bannerAdOnVideoPlayer == false) {
                binding.frameAdView.visibility = GONE
            }
        } else {
            hideViewJob?.cancel()
            
            bottomSheetBinding?.videoPlayerOverlay?.visibility = GONE
            bottomSheetBinding?.controllerOverlay?.visibility = GONE
            bottomSheetBinding?.headingLayoutPlayer?.setBackgroundResource(R.drawable.bottom_sheet_rounded_grey_24dp)
            bottomSheetBinding?.rvQueuePlayer?.setBackgroundResource(R.color.white10)
            if (bottomSheetBinding?.toolbarPlayer?.isVisible == false) showView()
            handleCoverImage()
            binding.squareCardView.visibility = VISIBLE
            binding.videoPlayerView.visibility = GONE
            binding.videoPlayerView.player = null
            binding.videoPlayerView.keepScreenOn = false
            binding.errorLayout.visibility = GONE
            binding.frameAdView.visibility = VISIBLE
            bottomSheetBinding?.videoPlayerView?.visibility = GONE
        }
    }

    private fun isCurrentMediaARadio() = mediaContent?.isRadio == true

    override fun onResume() {
        super.onResume()
        if (DataController.isPlayingVideo) {
            if (isVideoPortrait == true) {
                if (bottomSheetBinding?.videoPlayerView?.player == null) {
                    bottomSheetBinding?.videoPlayerView?.let {
                        ctx.getPlayerActivity()?.setPlayerVideo(it)
                    }
                }
            } else {
                if (binding.videoPlayerView.player == null) {
                    ctx.getPlayerActivity()?.setPlayerVideo(binding.videoPlayerView)
                }
            }
            binding.videoPlayerView.keepScreenOn = true
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }

    private fun setAd() {
        if (adView?.parent != null) {
            (adView?.parent as ViewGroup).removeView(adView)
        }

        if (mediaContent?.displayAdsEnabled == false) {
            return
        }

        adsResponse?.let {
            adView?.setData(
                size = adsResponse?.config?.adSize,
                adUnitId = adsResponse?.config?.adId,
                viewGroup = binding.frameAdView,
                genres = getGenresAsString(),
                vertical = mediaContent?.entitySubType ?: "",
                isPlayedPreview = mediaContent?.isPurchaseNeeded,
                page = "player_page"
            )
            if (isVideoPortrait == false) {
                binding.frameAdView.visibility = VISIBLE
                binding.frameAdView.addView(adView)
            } else {
                binding.frameAdView.visibility = GONE
            }

        }
    }

    private fun getGenresAsString(): String {
        val genresString = mediaContent?.genres?.joinToString(",") { genre ->
            genre.name ?: ""
        }
        return genresString?.trim() ?: ""
    }

    /**
     * Checks the orientation of a video based on its m3u8 manifest.
     * This function is suspended and runs on the IO dispatcher to avoid blocking the main thread.
     *
     * @param videoUrl The URL of the m3u8 manifest file.
     * @return Boolean indicating whether the video is in portrait orientation (true) or not (false).
     */
    private suspend fun checkVideoOrientation(videoUrl: String): Boolean {
        return withContext(Dispatchers.IO) {
            var isPortrait = false

            // Only process m3u8 files
            if (videoUrl.endsWith(".m3u8")) {
                try {
                    // Fetch the manifest content
                    val manifest = URL(videoUrl).readText()
                    // Regular expression to find the resolution in the manifest
                    val resolutionRegex = Regex("RESOLUTION=(\\d+)x(\\d+)")
                    val match = resolutionRegex.find(manifest)

                    if (match != null) {
                        // Extract width and height from the matched resolution
                        val width = match.groupValues[1].toInt()
                        val height = match.groupValues[2].toInt()
                        isPortrait = width < height
                    }
                } catch (e: Exception) {
                    Firebase.crashlytics.log("error checking video orientation - $videoUrl")
                    e.printStackTrace()
                }
            }
            // Return the result

            val orientation = isPortrait || mediaContent?.orientation == PORTRAIT
            <EMAIL> = orientation
            orientation
        }
    }

    /* This function controls the visibility of
        video player views based on the video orientation
        and desired visibility state */
    private fun videoPlayerVisible(isVisible: Boolean) {
        if (isVideoPortrait == null) return
        /* For portrait videos:
         * - The main videoPlayerView is shown when we want to hide the video (!isVisible)
         * - The bottomSheet videoPlayerView is shown when we want to show the video (isVisible) */
        if (isVideoPortrait == true) {
            if (bottomSheetBinding?.videoPlayerView?.player == null) {
                bottomSheetBinding?.videoPlayerView?.let {
                    ctx.getPlayerActivity()?.setPlayerVideo(it)
                }
            }
            binding.videoPlayerView.visibility = if (!isVisible) VISIBLE else GONE
            bottomSheetBinding?.videoPlayerView?.visibility = if (isVisible) VISIBLE else GONE
        } else {
            /* For landscape videos:
             * - The main videoPlayerView is shown when we want to show the video (isVisible)
             * - The bottomSheet videoPlayerView is shown when we want to hide the video (!isVisible) */
            binding.videoPlayerView.visibility = if (isVisible) VISIBLE else GONE
            bottomSheetBinding?.videoPlayerView?.visibility = if (!isVisible) VISIBLE else GONE
            if (binding.videoPlayerView.player == null) {
                ctx.getPlayerActivity()?.setPlayerVideo(binding.videoPlayerView)
            }
        }
    }
}