package noice.app.exoplayer

import androidx.annotation.OptIn
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.*
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.offline.Download
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.model.Content
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.ServerInterface
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants.Companion.EPISODE_VIEW_CLICK
import noice.app.utils.Constants.Companion.EPISODE_VIEW_DOWNLOAD
import noice.app.utils.Constants.Companion.EPISODE_VIEW_PLAY_CLICK
import noice.app.utils.Constants.Companion.INITIALIZE_DOWNLOAD_LISTENER_IF_NOT
import noice.app.utils.Constants.Companion.REMOVE_DOWNLOAD
import noice.app.utils.PrefUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@OptIn(UnstableApi::class)
class EpisodeViewDownloadHelper(
    lifecycleOwner: LifecycleOwner?,
    private var contentList: ArrayList<Content>? = null,
    private val recyclerView : RecyclerView,
    private val fragmentManager: FragmentManager? = null,
    private val pageSource : String = "",
    private val extraData: ExtraData? = null
) : LifecycleObserver, OnClickInterface<Pair<Content, String>> {

    init {
        if(lifecycleOwner == null) {
            subscribe()
        } else {
            lifecycleOwner.lifecycle.addObserver(this)
        }
        (recyclerView.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
    }

    private var downloadListener : ExoPlayerDownloadTracker.DownloadProgressListener? = null
    private var noOfDownloads = 0
    private var completedDownloads = 0
    private var downloadObserver = MutableLiveData<Boolean>()
    private var episodeViewListener : OnClickInterface<Pair<Content, String>>? = null

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    fun onCreate() {
        subscribe()
    }

    private fun subscribe() {
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    fun queueDownloads(source:String) {
        CoroutineScope(Dispatchers.IO).launch {
            val toDo = getList().filter { content ->
                !BaseApplication.application.getDownloadTracker().isDownloaded(content.id, content.url)
            }

            if (!toDo.isNullOrEmpty()) {
                noOfDownloads = toDo.size

                CoroutineScope(Dispatchers.Main).launch {
                    setDownloadListener()

                    toDo.forEach { content ->
                        BaseApplication.application.getDownloadTracker().download(null, content,source)
                    }
                }
            }
        }
    }

    private fun getList(): ArrayList<Content> {
        return contentList
            ?: if (recyclerView.adapter is LoadMoreAdapter<*>) {
                ArrayList((recyclerView.adapter as LoadMoreAdapter<*>).dataSet.filterIsInstance<Content>())
            } else {
                ArrayList()
            }
    }

    private fun setDownloadListener() {
        if (downloadListener != null)
            return

        downloadListener = object : ExoPlayerDownloadTracker.DownloadProgressListener(
            ExoPlayerDownloadTracker.OBSERVE_ALL) {

            override fun onDownloadsChanged(download: Download) {
                if (download.state == Download.STATE_COMPLETED) {
                    completedDownloads++
                    if (completedDownloads == noOfDownloads) {
                        downloadObserver.value = true
                    }
                }

                notifyDownloadProgress(download)
            }

            override fun onProgressChanged(download: Download) {
                notifyDownloadProgress(download)
            }
        }
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().addListener(it)
        }
    }

    fun observeDownloadComplete() = downloadObserver

    private fun notifyDownloadProgress(download: Download) {
        CoroutineScope(Dispatchers.Main).launch {
            getList().filter { content ->
                content.id == download.request.id
            }.forEach { content ->
                val index = getList().indexOf(content)

                if (index != -1) {
                    content.downloadState = download.state
                    content.downloadProgress = download.percentDownloaded.toInt()
                    recyclerView.adapter?.notifyItemChanged(index)
                }
            }
        }
    }

    private fun releaseDownloadListener() {
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().removeListener(it)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDownloadEvent(event : EventMessage) {
        if (event.eventCode == EPISODE_VIEW_DOWNLOAD) {
            val index = getList().indexOf(event.data as Content)
            if (index != -1) {
                getList()[index].downloadState = Download.STATE_QUEUED
                recyclerView.adapter?.notifyItemChanged(index)
                setDownloadListener()
            }
        } else if (event.eventCode == INITIALIZE_DOWNLOAD_LISTENER_IF_NOT) {
            if (getList().contains(Content(event.data as String))) {
                setDownloadListener()
            }
        }
    }

    private fun sendRemoveEvent(content:Content) {
        val mediaAction = MediaAction(
            "download",
            0.0,
            content.id,
            "content",
            PrefUtils.userDetails?.id.toString(),
            content.catalogType?:content.catalog?.type?:"",
            null
        )
        BaseApplication.doServerCall({ NetworkRequests.performAction(mediaAction) },
            object : ServerInterface<BaseModel<Community>> {
                override fun onCustomError(e: ApiError) {}
                override fun onError(e: Throwable) {}
                override fun onSuccess(data: BaseModel<Community>, dataFromCache: Boolean) {}
            })
        AnalyticsUtil.sendEvent(
            content.catalogType?:content.catalog?.type?:"",
            content.id?:"",
            content.id,
            "content_removed",
            "",
            (content.meta?.timeElapsed?:0).toString(),
            (content.duration?:0).toString(),
            "",
            content.catalogTitle?:"",
            content.title?:""
        )
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        releaseDownloadListener()
        if (EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this)
    }

    override fun dataClicked(data: Pair<Content, String>, eventId: Int) {
        val content = data.first
        if (eventId == EPISODE_VIEW_DOWNLOAD) {
            BaseApplication.application.getDownloadTracker().download(null, content, data.second)
            EventBus.getDefault().post(EventMessage(content, eventId))
        } else if (eventId == REMOVE_DOWNLOAD) {
            if (fragmentManager != null) {
                NoiceAlertDialog.Builder()
                    .setTitle(recyclerView.context.getString(R.string.delete_download))
                    .setMessage(recyclerView.context.getString(R.string.delete_dowload_msg))
                    .setListener(object : OnClickInterface<Boolean> {
                        override fun dataClicked(data: Boolean, btnEventId: Int) {
                            if (btnEventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                                content.id?.let { id ->
                                    ExoplayerUtils.removeDownload(id)
                                }
                                sendRemoveEvent(content)
                                EventBus.getDefault().post(EventMessage(content, eventId))
                            }
                        }
                    }).show(fragmentManager)
            }
        } else if (eventId == EPISODE_VIEW_CLICK) {
            AnalyticsUtil.sendEventForContent(content.entitySubType,"content_opened",content.catalog?.id?:"",content.catalog?.title?:"","",content.title?:"")
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CONTENT_PAGE, content.id, pageSource, extraData = extraData))
        } else if (eventId == EPISODE_VIEW_PLAY_CLICK) {
            episodeViewListener?.dataClicked(Pair(content, ""), eventId)
        }
    }

    fun setEpisodeViewListener(episodeViewListener : OnClickInterface<Pair<Content, String>>) {
        this.episodeViewListener = episodeViewListener
    }
}