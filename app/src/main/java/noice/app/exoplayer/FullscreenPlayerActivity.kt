package noice.app.exoplayer

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.annotation.OptIn
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.PlayerControlView
import noice.app.CustomTimeBar
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.ActivityFullscreenPlayerBinding
import noice.app.exoplayer.ExoplayerUtils.mediaDurationMs
import noice.app.model.ExoNotificationData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.BaseActivity
import noice.app.observers.GlobalObservers
import noice.app.observers.InterActivityApi
import noice.app.player.models.PlayerEvent
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.DateUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.secondsToMs
import noice.app.utils.getView
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import noice.app.utils.isZero
import noice.app.utils.putAnalyticsKey
import noice.app.utils.visible
import org.greenrobot.eventbus.EventBus

@Suppress("DEPRECATION")
@OptIn(UnstableApi::class)
class FullscreenPlayerActivity : BaseActivity() {

    companion object {
        private const val EXO_DATA = "EXO_DATA"

        fun start(ctx: Context, data: ExoNotificationData?) {
            Intent(ctx, FullscreenPlayerActivity::class.java).apply {
                putExtra(EXO_DATA, data)
                ctx.startActivity(this)
            }
        }
    }

    private lateinit var binding: ActivityFullscreenPlayerBinding
    private var exoData: ExoNotificationData? = null
    private var initialTimeMillis = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFullscreenPlayerBinding.inflate(layoutInflater)
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        this.window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        setContentView(binding.root)

        //fix: when video content ends next content ad is skipped (returns error). If the requested
        //ad has a ttag for app_state:foreground/background. App state was incorrect (background).
        //The next ad is fetched before ~10 seconds of the content.
        BasePlayerActivity.isAppInBackground = false

        getDataFromIntent()

        if (DataController.isPlayingVideo && exoData?.isPremium == true) {
            window?.setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE)
        } else {
            window?.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        }
        initialTimeMillis = if (exoData?.hasPurchased.isFalse() || exoData?.isPurchaseNeeded.isTrue()){
            (exoData?.previewTime?:0).secondsToMs()
        } else {
            exoData?.mediaDurationMs?:0
        }

        setPlayer()

        setObservers()

        initViews()

        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                exit()
            }
        })
    }

    private fun getDataFromIntent() {
        exoData = intent.extras?.parcelable(EXO_DATA)
    }

    private fun setPlayer() {
        EventBus.getDefault().post(binding.playerView)

        if ((exoData?.duration?:0) > 0){
            setTotalDuration(exoData?.duration.secondsToMs(),DateUtils.formatSeconds(exoData?.duration?:0))
        }

    }

    private fun setObservers() {

        GlobalObservers.playerEventObserver
            .subscribePlayerEvents(this, listOf(PlayerEvent.ON_MEDIA_CHANGED, PlayerEvent.PLAYER_ERROR_NO_INTERNET)) { playerEvent ->
            if (playerEvent.event == PlayerEvent.ON_MEDIA_CHANGED) {
                exit()
            } else if (playerEvent.event == PlayerEvent.PLAYER_ERROR_NO_INTERNET) {
                val bufferedDuration = binding.playerView.player?.totalBufferedDuration ?: 0
                val isSomethingInBuffer = bufferedDuration > 1000

                if (binding.playerView.player?.isPlaying != true && !isSomethingInBuffer) {
                    exit()
                }
            }
        }
        GlobalObservers.playerEventObserver
            .subscribeProgressEvent(this) { currentPosition ->
                handlePreviewProgress(currentPosition)
            }

            GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(this) {
                binding.playerView.keepScreenOn = if (it.exoData?.showVideo == true && DataController.isPlayingVideo) {
                    it.event != PlayerEvent.PAUSE_END_LOADER
                } else {
                    false
                }
            }
    }

    private fun initViews() {

        binding.playerView.apply {

            if (player?.playWhenReady == true) {
                keepScreenOn = true
            }

            player?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    if (playbackState == Player.STATE_ENDED) {
                        exit()
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    super.onPlayerError(error)

                    exit()
                }
            })

            setControllerVisibilityListener(PlayerControlView.VisibilityListener { visibility ->
                when (visibility) {
                    View.VISIBLE -> {
                        setUnlockBannerMargin(resources.getDimensionPixelSize(R.dimen.dp60))
                    }

                    else -> {
                        setUnlockBannerMargin(resources.getDimensionPixelSize(R.dimen.dp16))
                    }
                }
            })

            getView<TextView>(R.id.contentTitle)?.text = exoData?.title
            getView<TextView>(R.id.catalogTitle)?.text = exoData?.catalogTitle

            getView<View>(R.id.exo_rew)?.setOnClickListener {
                PlayerEvent(PlayerEvent.SEEK_MEDIA, false).sendPlayerEvent()
            }

            getView<View>(R.id.exo_ffwd)?.setOnClickListener {
                PlayerEvent(PlayerEvent.SEEK_MEDIA, true).sendPlayerEvent()
            }

            val streamType = if (exoData?.isDownloaded == true) {
                "downloaded"
            } else {
                "streamed"
            }

            getView<View>(R.id.screenOut)?.setOnClickListener {
                getSessionTime(streamType)
            }

            getView<View>(R.id.backBtn)?.setOnClickListener {
                exit()
            }
            getView<ImageButton>(R.id.custom_play_pause)?.apply {
                setPlayPauseIcon(!(player?.isPlaying ?: false))
                setOnClickListener {
                    setPlayPauseIcon(player?.isPlaying)
                    if (isPreViewContentPlayed().isTrue()){
                        GlobalObservers.playerEventObserver.invokePreviewClickEvent("podcast_media_player")
                        exit()
                    } else {
                        binding.playerView.player?.playWhenReady = binding.playerView.player?.playWhenReady.isFalse()
                    }
                }
            }
        }
        binding.textBukaContent.setOnClickListener {
            GlobalObservers.playerEventObserver.invokePreviewClickEvent("podcast_media_player")
            exit()
        }
        binding.purchaseLayout.visible(exoData?.hasPurchased.isFalse() || exoData?.isPurchaseNeeded.isTrue())
        if (exoData?.isPremium == true  && exoData?.hasPurchased.isFalse()) {
            if (exoData?.isSubscriptionAvailable == true){
                binding.txtPreTitle.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.premium?.title.orEmpty()
                ImageUtils.loadImageByUrl(
                    binding.imgPrevBanner,
                    PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.premium?.banner,false,
                    R.drawable.sub_selected_bg,originalUrl = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.premium?.banner
                )
                binding.textBukaContent.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.premium?.button.orEmpty()
            } else {
                if (!exoData?.earlyAccessFinishDate.isNullOrEmpty()){
                    binding.txtPreTitle.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.earlyAccess?.title.orEmpty()
                    ImageUtils.loadImageByUrl(
                        binding.imgPrevBanner,
                        PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.earlyAccess?.banner,false,
                        R.drawable.sub_selected_bg,originalUrl = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.earlyAccess?.banner
                    )
                    binding.textBukaContent.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.earlyAccess?.button.orEmpty()
                } else {
                    binding.txtPreTitle.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.vip?.title.orEmpty()
                    ImageUtils.loadImageByUrl(
                        binding.imgPrevBanner,
                        PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.vip?.banner,false,
                        R.drawable.sub_selected_bg,originalUrl = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.vip?.banner
                    )
                    binding.textBukaContent.text = PrefUtils.appCDNConfig?.subscriptionPreviewConfig?.vip?.button.orEmpty()
                }
            }
        }
        if (exoData?.hasPurchased.isFalse() || exoData?.isPurchaseNeeded.isTrue()){
            handlePreviewProgress(binding.playerView.player?.currentPosition?:0)
        }

    }

    private fun getSessionTime(streamType: String) {

        val path = BasePlayerActivity.BASE_PATH + "/mediaSessionTime"
        GlobalObservers.interActivityApi.invoke(InterActivityApi.ApiCall<Long?>(path) { durationPlayed ->

            val networkType = NetworkUtils.getNetworkType(this@FullscreenPlayerActivity)
            val elapsedTime = binding.playerView.player?.contentPosition.msToSeconds()
            val contentFormat = if (DataController.isPlayingVideo) {
                "video"
            } else {
                "audio"
            }
            val durationPlayedSec = durationPlayed.msToSeconds()

            AnalyticsUtil.sendEvent("change_orientation", Bundle().apply {
                putAnalyticsKey("orientationTo", "portrait")
                putAnalyticsKey("contentId", exoData?.id)
                putAnalyticsKey("contentTitle", exoData?.title)
                putAnalyticsKey("contentDuration", exoData?.duration)
                putAnalyticsKey("entitySubType", exoData?.entitySubType)
                putAnalyticsKey("networkType", networkType)
                putAnalyticsKey("elapsedTime", elapsedTime)
                putAnalyticsKey("catalogId", exoData?.catalogId)
                putAnalyticsKey("catalogTitle", exoData?.catalogTitle)
                putAnalyticsKey("catalogSource", exoData?.source)
                putAnalyticsKey("streamType", streamType)
                putAnalyticsKey("durationPlayed", durationPlayedSec)
                putAnalyticsKey("contentFormat", contentFormat)
            })

            exit()
        })
    }

    override fun onPause() {
        binding.playerView.player = null
        DataController.wasPlayingVideo = DataController.isPlayingVideo
        EventBus.getDefault().post(EventMessage(PlayerDetailFragment.STREAM_TYPE_AUDIO, Constants.STREAM_TYPE, secondData = true))
        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        MoEngageAnalytics.setOrResetInAppContext(null)

        if (DataController.wasPlayingVideo) {
            DataController.wasPlayingVideo = false
            EventBus.getDefault().post(EventMessage(PlayerDetailFragment.STREAM_TYPE_VIDEO, Constants.STREAM_TYPE, secondData = true))
            setPlayer()
        }
    }

    private fun exit() {
        binding.playerView.player = null
        DataController.isVideoInFullscreen = false
        finish()
    }
    private fun handlePreviewProgress(currentTimeMs:Long) {
        if (exoData?.hasPurchased.isFalse() || exoData?.isPurchaseNeeded.isTrue()) {
            val remainingMs = initialTimeMillis - currentTimeMs
            val minutes = remainingMs / 60000
            val seconds = (remainingMs % 60000) / 1000
            // Update the TextView with the remaining time
            val exoPosition = String.format("%02d:%02d", minutes, seconds)
            Log.d("handlePreviewProgress- ", "$remainingMs - $currentTimeMs")
            //exo_progress
            if ((minutes.isZero() && seconds.isZero()) ||
                minutes < 0 || seconds < 0 || (binding.playerView.findViewById<TextView>(R.id.exo_position).text.toString() == exoPosition)){
                binding.txtPreview.visible(true)
                binding.playerView.findViewById<CustomTimeBar>(R.id.exo_progress).setPosition(initialTimeMillis)
                //mediaService?.seekPlayer(initialTimeMillis)
                binding.playerView.player?.seekTo(initialTimeMillis)
                binding.playerView.player?.pause()
                exoData?.isPreviewCompleted = true
                if (binding.txtPreview.text != "00:00 tersisa") {
                    binding.txtPreview.text = "00:00 tersisa"
                    exit()
                }
            } else {
                binding.txtPreview.visible(true)
                exoData?.isPreviewCompleted = false
                binding.txtPreview.text = exoPosition.plus(" tersisa")
            }
        } else {
            binding.txtPreview.visible(false)
            exoData?.isPreviewCompleted = false
        }
    }
    private fun setTotalDuration(duration:Long, totalDuration:String){
        binding.playerView.findViewById<TextView>(R.id.txtDuration)?.text = totalDuration
        binding.playerView.findViewById<CustomTimeBar>(R.id.exo_progress)?.setTotalDuration(duration)
    }
    private fun isPreViewContentPlayed(): Boolean {
        return (exoData?.isPreviewCompleted.isTrue() && binding.txtPreview.text == "00:00 tersisa" )
    }

    private fun setUnlockBannerMargin(verticalPadding: Int) {
        with(binding.purchaseLayout) {
            val layoutParams = layoutParams
            if (layoutParams is ViewGroup.MarginLayoutParams) {
                layoutParams.setMargins(
                    layoutParams.leftMargin,
                    verticalPadding,
                    layoutParams.rightMargin,
                    verticalPadding
                )
            }
            this.layoutParams = layoutParams
        }
    }

    private fun ImageButton.setPlayPauseIcon(isPlaying: Boolean?) {
        if (isPlaying == true) {
            setImageResource(R.drawable.ic_play_triangle_filled_white)
        } else {
            setImageResource(R.drawable.ic_pause_triangle_filled_white)
        }
    }
}