package noice.app.exoplayer

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableList
import com.google.android.exoplayer2.util.Log
import noice.app.model.ExoNotificationData

abstract class QueueChangeObserver : ObservableList.OnListChangedCallback<ObservableArrayList<ExoNotificationData>>() {

    override fun onChanged(sender: ObservableArrayList<ExoNotificationData>?) {
        Log.d("onChanged", "")
    }

    override fun onItemRangeChanged(
        sender: ObservableArrayList<ExoNotificationData>?,
        positionStart: Int,
        itemCount: Int
    ) {
        //Log.d("ObservableArrayList onItemRangeChanged", "positionStart : $positionStart, itemCount : $itemCount")
        checkForRange(positionStart, itemCount, false)
    }

    override fun onItemRangeInserted(
        sender: ObservableArrayList<ExoNotificationData>?,
        positionStart: Int,
        itemCount: Int
    ) {
        //Log.d("ObservableArrayList onItemRangeInserted", "positionStart : $positionStart, itemCount : $itemCount")
        checkForRange(positionStart, itemCount, false)
    }

    override fun onItemRangeMoved(
        sender: ObservableArrayList<ExoNotificationData>?,
        fromPosition: Int,
        toPosition: Int,
        itemCount: Int
    ) {
        Log.d("onItemRangeMoved", "fromPosition : $fromPosition, toPosition : $toPosition,  itemCount : $itemCount")
    }

    override fun onItemRangeRemoved(
        sender: ObservableArrayList<ExoNotificationData>?,
        positionStart: Int,
        itemCount: Int
    ) {
        //Log.d("ObservableArrayList onItemRangeRemoved", "positionStart : $positionStart, itemCount : $itemCount")
        checkForRange(positionStart, itemCount, true)
    }

    private fun checkForRange(positionStart: Int, itemCount: Int, isRemove : Boolean) {
        if (positionStart == 0) {
            if (itemCount == 1) {
                onQueueChanged(0, isRemove)
            } else if (itemCount > 1) {
                onQueueChanged(0, isRemove)
                onQueueChanged(1, isRemove)
            }
        } else if (positionStart == 1) {
            onQueueChanged(1, isRemove)
        }
    }

    abstract fun onQueueChanged(position: Int, isRemove : Boolean)
}