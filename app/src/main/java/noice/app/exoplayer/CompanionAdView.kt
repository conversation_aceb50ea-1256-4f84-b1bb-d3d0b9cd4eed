package noice.app.exoplayer

import android.content.Context
import android.util.AttributeSet
import android.widget.FrameLayout
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import noice.app.R
import noice.app.listner.CommonListener

class CompanionAdView : FrameLayout {

    private lateinit var ctx : Context
    private var bgShape = 1
    private var imageListener : CommonListener<Boolean>? = null
    private lateinit var noAdImageView : ImageView
    private lateinit var slot : FrameLayout
    private var isCompanionBannerVisible = false

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context, attrs)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context, attrs)
    }

    private fun init(context : Context, attrs: AttributeSet?) {
        ctx = context
        inflate(ctx, R.layout.companion_ad_view, this)

        val a = ctx.theme.obtainStyledAttributes(attrs, R.styleable.CompanionAdView, 0, 0)

        bgShape = a.getInt(R.styleable.CompanionAdView_bgShape,1)
        noAdImageView = findViewById(R.id.noAdImage)

        if (bgShape == 2) {
            noAdImageView.setImageResource(R.drawable.ic_no_ad_image_sq)
        } else {
            noAdImageView.setImageResource(R.drawable.ic_no_ad_image)
        }

        slot = findViewById(R.id.slot)
        slot.setOnHierarchyChangeListener(object : OnHierarchyChangeListener{
            override fun onChildViewAdded(parent: View?, child: View?) {
                imageListener?.onResult(true)
                noAdImageView.visibility = View.GONE
                isCompanionBannerVisible = true
            }

            override fun onChildViewRemoved(parent: View?, child: View?) {
                if (slot.childCount > 0) {
                    imageListener?.onResult(true)
                    noAdImageView.visibility = View.GONE
                    isCompanionBannerVisible = true
                } else {
                    imageListener?.onResult(false)
                    noAdImageView.visibility = View.VISIBLE
                    isCompanionBannerVisible = false
                }
            }
        })
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return super.onInterceptTouchEvent(ev)
    }

    fun getSlot() = slot

    fun prepareForAd() {
        noAdImageView.visibility = View.VISIBLE
        slot.removeAllViews()
    }

    fun setImageListener(listener : CommonListener<Boolean>) {
        this.imageListener = listener
    }

    fun isCompanionBannerVisible() = isCompanionBannerVisible
}