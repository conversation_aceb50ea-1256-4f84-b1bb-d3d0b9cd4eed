package noice.app.exoplayer

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.support.v4.media.MediaDescriptionCompat
import android.support.v4.media.session.MediaSessionCompat
import androidx.annotation.OptIn
import androidx.core.app.NotificationCompat
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.database.DatabaseProvider
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.HttpDataSource
import androidx.media3.datasource.TransferListener
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.NoOpCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.datasource.cronet.CronetUtil
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.audio.AudioRendererEventListener
import androidx.media3.exoplayer.audio.MediaCodecAudioRenderer
import androidx.media3.exoplayer.mediacodec.MediaCodecSelector
import androidx.media3.exoplayer.metadata.MetadataOutput
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadService
import androidx.media3.exoplayer.text.TextOutput
import androidx.media3.exoplayer.video.VideoRendererEventListener
import androidx.media3.session.MediaSession
import androidx.media3.ui.PlayerNotificationManager
import com.google.android.exoplayer2.ext.cronet.CronetDataSource
import com.google.common.util.concurrent.Futures
import com.google.common.util.concurrent.ListenableFuture
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.model.ExoNotificationData
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.podcast.model.Content
import noice.app.room.Downloads
import noice.app.utils.CacheUtils
import noice.app.utils.ClassVariantProvider
import noice.app.utils.MediaUtil
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.secondsToMs
import org.chromium.net.CronetEngine
import java.io.File
import java.net.CookieHandler
import java.net.CookieManager
import java.net.CookiePolicy
import java.util.concurrent.Executors
import kotlin.collections.HashMap

@OptIn(UnstableApi::class)
object ExoplayerUtils {

    const val DOWNLOAD_NOTIFICATION_CHANNEL_ID = "download_channel"
    const val DOWNLOAD_CONTENT_DIRECTORY = "downloads"
    const val CACHE_CONTENT_DIRECTORY = "content"
    private const val STOP_REASON_STOPPED_BY_USER = 999
    private lateinit var downloadNotificationHelper: ExoNotificationHelper
    private lateinit var databaseProvider: DatabaseProvider
    private var externalFilesDir: File? = null
    private lateinit var downloadCache: Cache
    private val requestMap = HashMap<String, Int>()
    private var randomNumber = 0
    private lateinit var cachedDSFContent : CacheDataSource.Factory
    private lateinit var cachedDSFDownload : CacheDataSource.Factory
    private lateinit var httpDataSourceFactory : HttpDataSource.Factory
    private lateinit var httpDSFWithBandwidthMeter : HttpDataSource.Factory
    private lateinit var dataSourceFactory : DataSource.Factory
    private var cronetEngine : CronetEngine? = null

    @Synchronized
    fun getDownloadNotificationHelper(
        ctx: Context
    ): ExoNotificationHelper {
        if (!::downloadNotificationHelper.isInitialized) {
            downloadNotificationHelper =
                ExoNotificationHelper(ctx, DOWNLOAD_NOTIFICATION_CHANNEL_ID)
        }
        return downloadNotificationHelper
    }

    @Synchronized
    fun getDatabaseProvider(): DatabaseProvider {
        if (!::databaseProvider.isInitialized) {
            databaseProvider = StandaloneDatabaseProvider(BaseApplication.getBaseAppContext())
        }
        return databaseProvider
    }

    @Synchronized
    fun getExternalFilesDir(): File? {
        if (externalFilesDir == null) {
            externalFilesDir = BaseApplication.getBaseAppContext().getExternalFilesDir(null)
            if (externalFilesDir == null) {
                externalFilesDir = BaseApplication.getBaseAppContext().filesDir
            }
        }
        return externalFilesDir
    }

    @Synchronized
    fun getDataSourceFactory(): DataSource.Factory {
        if (!::dataSourceFactory.isInitialized) {
            val bandwidthMeter = getBandWidthMeter()
            dataSourceFactory = DefaultDataSource.Factory(
                BaseApplication.getBaseAppContext(),
                getHttpDataSourceFactory(bandwidthMeter)
            ).setTransferListener(bandwidthMeter)
        }
        return dataSourceFactory
    }

    fun getBandWidthMeter() = NoiceBandwidthMeter.getSingletonInstance()

    fun getHttpDataSourceFactory(
        bandwidthMeter : TransferListener? = null
    ): HttpDataSource.Factory {

        if (bandwidthMeter == null && !::httpDataSourceFactory.isInitialized) {
            httpDataSourceFactory = getHttpDataSourceFactoryImpl(null)
        } else if (bandwidthMeter != null && !::httpDSFWithBandwidthMeter.isInitialized) {
            httpDSFWithBandwidthMeter = getHttpDataSourceFactoryImpl(bandwidthMeter)
        }

        return if (bandwidthMeter == null) {
            httpDataSourceFactory
        } else {
            httpDSFWithBandwidthMeter
        }
    }

    fun getCachedDataSourceFactory(
        data : ExoNotificationData
    ): CacheDataSource.Factory {
        if (data.isDownloaded == true && !::cachedDSFDownload.isInitialized) {
            cachedDSFDownload = getCachedDataSourceFactoryImpl(data)
        } else if (data.isDownloaded == false && !::cachedDSFContent.isInitialized) {
            cachedDSFContent = getCachedDataSourceFactoryImpl(data)
        }

        return if (data.isDownloaded == true) {
            cachedDSFDownload
        } else {
            cachedDSFContent
        }
    }

    private fun getCachedDataSourceFactoryImpl(data : ExoNotificationData): CacheDataSource.Factory {
        return CacheDataSource.Factory().apply {
            if (data.isDownloaded == true) {
                setCacheWriteDataSinkFactory(null)
                setCache(getDownloadCache())
                setUpstreamDataSourceFactory(null)
            } else {
                if (!CacheUtils.isCachingEnabled) {
                    setCacheWriteDataSinkFactory(null)
                }
                setCache(CacheUtils.contentCache)
                setUpstreamDataSourceFactory(getDataSourceFactory())
                setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            }
        }
    }

    private val playerUserAgent: String by lazy {
        val ctx = BaseApplication.getBaseAppContext()
        Util.getUserAgent(ctx, ctx.getString(R.string.app_name).uppercase())
    }

    private fun getHttpDataSourceFactoryImpl(bandwidthMeter: TransferListener?): HttpDataSource.Factory {
        val timeOut = 2 * 60 * 1000
        val engine = getCronetEngine()
        return if (engine != null) {
            androidx.media3.datasource.cronet.CronetDataSource.Factory(engine, Executors.newSingleThreadExecutor())
                .setTransferListener(bandwidthMeter)
                .setResetTimeoutOnRedirects(true)
                .setConnectionTimeoutMs(timeOut)
                .setReadTimeoutMs(timeOut)
                .setUserAgent(playerUserAgent)
        } else {
            val cookieManager = CookieManager()
            cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ORIGINAL_SERVER)
            CookieHandler.setDefault(cookieManager)
            DefaultHttpDataSource.Factory()
                .setTransferListener(bandwidthMeter)
                .setAllowCrossProtocolRedirects(true)
                .setConnectTimeoutMs(timeOut)
                .setReadTimeoutMs(timeOut)
                .setUserAgent(playerUserAgent)
        }
    }

    private fun getCronetEngine(): CronetEngine? {
        if (cronetEngine == null) {
            cronetEngine = CronetUtil.buildCronetEngine(BaseApplication.getBaseAppContext(), null, true)
        }
        return cronetEngine
    }

    @Synchronized
    fun getDownloadCache(): Cache {
        if (!::downloadCache.isInitialized) {
            val downloadContentDirectory = File(getExternalFilesDir(), DOWNLOAD_CONTENT_DIRECTORY)
            downloadCache = SimpleCache(
                downloadContentDirectory, NoOpCacheEvictor(), getDatabaseProvider()
            )
        }
        return downloadCache
    }

    fun stopDownload(contentId: String, stopReason: Int = STOP_REASON_STOPPED_BY_USER) {
        DownloadService.sendSetStopReason(
            BaseApplication.getBaseAppContext(),
            ExoplayerDownloadService::class.java,
            contentId,
            stopReason,
            true
        )
    }

    fun pauseDownload() {
        DownloadService.sendPauseDownloads(
            BaseApplication.getBaseAppContext(),
            ExoplayerDownloadService::class.java,
            true
        )
    }

    fun removeDownload(contentId: String) {
        CoroutineScope(Dispatchers.IO).launch {
            PrefUtils.userDetails?.id?.let { id ->
                BaseApplication.application.getAppDb().downloadsDao().apply {
                    deleteDownload(Downloads(id, contentId, Content(contentId)))
                    if (shouldRemoveFromManager(contentId) == 0) {
                        DownloadService.sendRemoveDownload(
                            BaseApplication.getBaseAppContext(),
                            ExoplayerDownloadService::class.java,
                            contentId,
                            false
                        )
                    }
                }
            }
        }
    }

    fun getPendingIntent(
        eventId: String,
        isBroadcast: Boolean,
        contentId: String?,
        notificationId: Int = -1
    ) : PendingIntent {

        val requestCode = when {
            requestMap.containsKey(contentId) -> {
                requestMap[contentId] ?: 0
            }
            contentId != null -> {
                requestMap[contentId] = randomNumber++
                requestMap[contentId] ?: 0
            }
            else -> {
                0
            }
        }

        return if (isBroadcast) {
            PendingIntent.getBroadcast(
                BaseApplication.getBaseAppContext(),
                requestCode,
                Intent(
                    BaseApplication.getBaseAppContext(),
                    NotificationBroadcast::class.java
                ).apply {
                    action = eventId
                    if (contentId != null) {
                        putExtra(BasePlayerActivity.ENTITY_ID, contentId)
                    }
                    if (notificationId != -1) {
                        putExtra(BasePlayerActivity.NOTIFICATION_ID, notificationId)
                    }
                },
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )
        } else {
            PendingIntent.getActivity(
                BaseApplication.getBaseAppContext(),
                requestCode,
                Intent(BaseApplication.getBaseAppContext(), ClassVariantProvider.of(HomeActivity::class.java)).apply {
                    action  = eventId
                    if (contentId != null) {
                        putExtra(BasePlayerActivity.ENTITY_ID, contentId)
                    }
                    if (notificationId != -1) {
                        putExtra(BasePlayerActivity.NOTIFICATION_ID, notificationId)
                    }
                },
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )
        }
    }

    fun stopAllDownloads() {
        BaseApplication.application.getDownloadTracker().getDownloadObjects(Download.STATE_QUEUED).forEach {
            stopDownload(it.request.id)
        }
        BaseApplication.application.getDownloadTracker().getDownloadObjects(Download.STATE_DOWNLOADING).forEach {
            stopDownload(it.request.id)
        }
    }

    fun getAudioRenderer() = { handler: Handler?,
                               _: VideoRendererEventListener?,
                               audioListener: AudioRendererEventListener?,
                               _: TextOutput?,
                               _: MetadataOutput? ->

        arrayOf(
            MediaCodecAudioRenderer(
                BaseApplication.getBaseAppContext(),
                MediaCodecSelector.DEFAULT,
                handler,
                audioListener
            )
        )
    }

    //MediaSession
    private const val MEDIA_SESSION_TAG = "noice_audio"


    fun getMediaSession(context: Context, player: ExoPlayer): MediaSession {
        return MediaSession.Builder(context, player)
            .setCallback(object : MediaSession.Callback {
                override fun onAddMediaItems(
                    session: MediaSession,
                    controller: MediaSession.ControllerInfo,
                    mediaItems: MutableList<MediaItem>
                ): ListenableFuture<List<MediaItem>> {
                    // Handle adding media items (optional customization)
                    return Futures.immediateFuture(mediaItems)
                }
//
//                override fun onPlaybackStateChanged(
//                    session: MediaSession,
//                    oldState: Player.State,
//                    newState: Player.State
//                ) {
//                    // Handle playback state changes if needed
//                }
//
//                override fun onSetMediaItems(
//                    session: MediaSession,
//                    controller: MediaSession.ControllerInfo,
//                    mediaItems: MutableList<MediaItem>,
//                    positionMs: Long,
//                    playbackStartPositionMs: Long
//                ): ListenableFuture<Void> {
//                    // Example: Handle setting new media items
//                    return super.onSetMediaItems(session, controller, mediaItems, positionMs, playbackStartPositionMs)
//                }
            })
            .build()
    }

    fun deleteAllDownloads(context: Context) {
        DownloadService.sendRemoveAllDownloads(
            context,
            ExoplayerDownloadService::class.java,
            false
        )
    }

    //PlayerNotification
    private const val CHANNEL_ID = "EXOPLAYER_CHANNEL_ID"
    private const val NOTIFICATION_ID = 8673

    fun getPlayerNotification(
        descriptionAdapter : PlayerNotificationManager.MediaDescriptionAdapter
    ): PlayerNotificationManager {

        return PlayerNotificationManager.Builder(
            BaseApplication.getBaseAppContext(),
            NOTIFICATION_ID,
            CHANNEL_ID
        ).setMediaDescriptionAdapter(descriptionAdapter)
            .setChannelNameResourceId(R.string.media_notification_channel_name)
            .setChannelDescriptionResourceId(R.string.media_notification_channel_desc)
            .build().apply {
                setUseChronometer(false)
                setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                setSmallIcon(R.drawable.ic_noice_home_logo)
            }
    }

    fun getDownloadData(download: Download?) : DownloadNotificationData? {
        if (download?.request?.data != null) {
            val strData = Util.fromUtf8Bytes(download.request.data)
            if (strData.isNotEmpty()) {
                return Utils.getObjectFromJson(strData)
            }
        }
        return null
    }

    fun isContentDownloaded(data: ExoNotificationData?) =
        BaseApplication.application.getDownloadTracker().isDownloaded(data?.id, data?.songUrl)

    val ExoNotificationData?.mediaDurationMs: Long
        get() {
            this ?: return 1
            var dur = exoplayerContentDuration
            if (dur <= 0) {
                dur = duration.secondsToMs()
            }
            return dur
        }
 }