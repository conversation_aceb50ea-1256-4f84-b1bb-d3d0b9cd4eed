package noice.app.exoplayer.cast

import android.util.Log
import androidx.mediarouter.media.MediaControlIntent
import androidx.mediarouter.media.MediaRouter
import com.google.android.gms.cast.CastDevice
import noice.app.BaseApplication
import noice.app.data.DataController
import noice.app.utils.isFalse
import noice.app.utils.isTrue


class PlayerMediaRouterCallBack(val callback: CastConnectionCallback) : MediaRouter.Callback() {
    override fun onRouteAdded(router: MediaRouter, route: MediaRouter.RouteInfo) {
        super.onRouteAdded(router, route)

        callback.onRouteAdded(router.routes)
    }

    override fun onRouteRemoved(router: MediaRouter, route: MediaRouter.RouteInfo) {
        super.onRouteRemoved(router, route)
        callback.onDeviceRemoved(route)
    }

    override fun onRouteSelected(router: <PERSON>Router, route: MediaRouter.RouteInfo, reason: Int) {
        Log.d("TAG", "onRouteSelected: route=$route")
        val mSelectedDevice = CastDevice.getFromBundle(route.extras)
        Log.d("TAG", "device version "+mSelectedDevice?.deviceVersion.orEmpty())
        Log.d("TAG", "device version "+mSelectedDevice?.modelName.orEmpty())
        if (route.supportsControlCategory(
                MediaControlIntent.CATEGORY_REMOTE_PLAYBACK
            )
        ) {
            // remote playback device
            //updateRemotePlayer(route);
            callback.deviceSelected(route)
        } else {
            // secondary output device
            // updatePresentation(route);
        }
    }

    override fun onRouteUnselected(router: MediaRouter, route: MediaRouter.RouteInfo, reason: Int) {
        Log.d("TAG", "onRouteUnselected: route=$route")
        if (route.supportsControlCategory(
                MediaControlIntent.CATEGORY_REMOTE_PLAYBACK
            )
        ) {
            callback.deviceUnSelected(route)
            // remote playback device
            //updateRemotePlayer(route);
        } else {
            // secondary output device
            // updatePresentation(route);
        }
    }

    override fun onRoutePresentationDisplayChanged(
        router: MediaRouter,
        route: MediaRouter.RouteInfo
    ) {
        Log.d("TAG", "onRoutePresentationDisplayChanged: route=$route")
        if (route.supportsControlCategory(
                MediaControlIntent.CATEGORY_REMOTE_PLAYBACK
            )
        ) {
            // remote playback device
            // updateRemotePlayer(route);
        } else {
            // secondary output device
            // updatePresentation(route);
        }
    }
}