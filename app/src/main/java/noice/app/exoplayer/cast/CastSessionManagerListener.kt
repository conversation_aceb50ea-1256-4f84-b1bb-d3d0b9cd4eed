package noice.app.exoplayer.cast

import android.util.Log
import android.widget.Toast
import com.google.android.gms.cast.framework.CastSession
import com.google.android.gms.cast.framework.SessionManagerListener
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.data.DataController
import noice.app.utils.isTrue
import java.io.IOException

class CastSessionManagerListener(
    private val castMessageReceiver: CastMessageReceiver,
    private val castSessionCallback: CastSessionCallback
) : SessionManagerListener<CastSession> {
    override fun onSessionEnded(session: CastSession, p1: Int) {
        showToast("session ended")
        CastManager.isCastConnected = false
        session.removeMessageReceivedCallbacks(castMessageReceiver.namespace)
    }

    override fun onSessionEnding(session: CastSession) {

    }

    override fun onSessionResumeFailed(p0: CastSession, p1: Int) {
        showToast("session resume failed")
    }

    override fun onSessionResumed(p0: CastSession, p1: Boolean) {
        CastManager.isCastConnected = true
        castSessionCallback.onSessionResumed()
        showToast("session resumed")
    }

    override fun onSessionResuming(p0: CastSession, p1: String) {
        showToast("session resuming")
    }

    override fun onSessionStartFailed(p0: CastSession, p1: Int) {
        showToast("session start failed")
    }

    override fun onSessionStarted(session: CastSession, p1: String) {
        CastManager.isCastConnected = true
        try {
            session.setMessageReceivedCallbacks(
                castMessageReceiver.namespace,
                castMessageReceiver)
        } catch (e: IOException) {
            Log.e("CastSessionManagerListener", "Exception while creating channel", e)
        }
        showToast("session started")
    }

    override fun onSessionStarting(p0: CastSession) {

    }

    override fun onSessionSuspended(p0: CastSession, p1: Int) {
        showToast("session suspended")
        CastManager.isCastConnected = false
    }
    private fun showToast(message:String) {
        if (BuildConfig.DEBUG.isTrue()){
            Toast.makeText(BaseApplication.getBaseAppContext(),message,Toast.LENGTH_SHORT).show()
        }
    }
}