/*
 * Copyright 2022 Google LLC. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package noice.app.exoplayer.cast

import com.google.android.gms.cast.framework.OptionsProvider
import android.content.Context
import android.net.Uri
import android.text.format.DateUtils
import com.google.android.gms.cast.CastMediaControlIntent
import com.google.android.gms.cast.CastMediaControlIntent.DEFAULT_MEDIA_RECEIVER_APPLICATION_ID
import com.google.android.gms.cast.LaunchOptions
import com.google.android.gms.cast.MediaMetadata
import com.google.android.gms.cast.framework.CastOptions
import com.google.android.gms.cast.framework.media.NotificationOptions
import com.google.android.gms.cast.framework.media.MediaIntentReceiver
import com.google.android.gms.cast.framework.media.CastMediaOptions
import com.google.android.gms.cast.framework.SessionProvider
import com.google.android.gms.cast.framework.media.ImagePicker
import com.google.android.gms.cast.framework.media.ImageHints
import com.google.android.gms.common.images.WebImage
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.exoplayer.expandedcontrols.ExpandedControlsActivity
import noice.app.modules.dashboard.activity.HomeActivity

/**
 * Implements [OptionsProvider] to provide [CastOptions].
 */
class CastOptionsProvider : OptionsProvider {
    override fun getCastOptions(context: Context): CastOptions {
        /*val notificationOptions = NotificationOptions.Builder()
            .setActions(
                listOf(
                    MediaIntentReceiver.ACTION_REWIND,
                    MediaIntentReceiver.ACTION_TOGGLE_PLAYBACK,
                    MediaIntentReceiver.ACTION_FORWARD,
                    MediaIntentReceiver.ACTION_SKIP_NEXT,
                    MediaIntentReceiver.ACTION_STOP_CASTING
                ), intArrayOf(1, 4)
            )
            .setTargetActivityClassName(HomeActivity::class.java.name)
            .build()*/


        // Example showing 4 buttons: "rewind", "play/pause", "forward" and "stop casting".
        val buttonActions= listOf(
            MediaIntentReceiver.ACTION_REWIND,
            MediaIntentReceiver.ACTION_TOGGLE_PLAYBACK,
            MediaIntentReceiver.ACTION_FORWARD,
            MediaIntentReceiver.ACTION_SKIP_NEXT,
            MediaIntentReceiver.ACTION_STOP_CASTING
        )

// Showing "play/pause" and "stop casting" in the compat view of the notification.
        val compatButtonActionsIndices = intArrayOf(1, 4)

// Builds a notification with the above actions. Each tap on the "rewind" and "forward" buttons skips 10 seconds.
// Tapping on the notification opens an Activity with class HomeActivity.
        val notificationOptions = NotificationOptions.Builder()
            .setActions(buttonActions, compatButtonActionsIndices)
            .setSkipStepMs(10 * DateUtils.SECOND_IN_MILLIS)
            .setTargetActivityClassName(HomeActivity::class.java.name)
            .build()

        val mediaOptions = CastMediaOptions.Builder()
            .setNotificationOptions(null)
            .setImagePicker(ImagePickerImpl())
            //.setExpandedControllerActivityClassName(ExpandedControlsActivity::class.java.name)
            .setMediaIntentReceiverClassName(HomeActivity::class.java.name)
            .build()
        /** Following lines enable Cast Connect  */
        val launchOptions = LaunchOptions.Builder()
            .setAndroidReceiverCompatible(true)
            .setRelaunchIfRunning(false)
            .build()
        return CastOptions.Builder()
            .setLaunchOptions(launchOptions)
            //.setReceiverApplicationId("C0868879")
            .setReceiverApplicationId(BuildConfig.CAST_RECEIVER_KEY)
            .setResumeSavedSession(true)
            .setRemoteToLocalEnabled(true)
            .setStopReceiverApplicationWhenEndingSession(true)
            .setCastMediaOptions(mediaOptions)
            .setEnableReconnectionService(true)
            .build()
    }

    override fun getAdditionalSessionProviders(appContext: Context): List<SessionProvider>? {
        return null
    }
    private class ImagePickerImpl : ImagePicker() {
        override fun onPickImage(mediaMetadata: MediaMetadata?, hints: ImageHints): WebImage? {
            val type = hints.type
            if (!mediaMetadata!!.hasImages()) {
                return null
            }
            val images = mediaMetadata.images
            return if (images.size == 1) {
                images[0]
            } else {
                if (type == IMAGE_TYPE_MEDIA_ROUTE_CONTROLLER_DIALOG_BACKGROUND) {
                    images[0]
                } else {
                    images[1]
                }
            }
        }
    }
}