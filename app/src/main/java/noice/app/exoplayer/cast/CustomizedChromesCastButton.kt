package noice.app.exoplayer.cast

import android.content.Context
import android.util.AttributeSet
import androidx.mediarouter.app.MediaRouteButton

class CustomizedChromesCastButton : MediaRouteButton {
    private var enable = true
    private lateinit var onClick: () -> Unit

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(
        context, attrs
    )

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )
    fun setClick(onClick: () -> Unit){
        this.onClick = onClick
    }

    fun setCastEnable(enable: Boolean) {
        this.enable = enable
    }

    override fun performClick(): <PERSON><PERSON>an {
        return if (enable) {
            onClick.invoke()
            true
        } else {
            false
        }
    }
}