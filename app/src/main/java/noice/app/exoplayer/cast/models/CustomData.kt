package noice.app.exoplayer.cast.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CustomData(
    val MEDIA_UNIQUE_ID: String?,
    val adTagUrl: String?,
    val isVideo: Boolean?,
    val mediaItem: MediaItem?,
    val contentId: String?,
    val catalogId: String?,
    val catalogTitle: String?,
    val contentTitle: String?,
    val contentURL: String?,
    val playbackType: String?,
    val vertical: String?,
    val streamType: String?,
    val catalogSource: String?,
    val contentDuration: Long?,
    val source: String?,
    val elapsedTime: Long?
):Parcelable