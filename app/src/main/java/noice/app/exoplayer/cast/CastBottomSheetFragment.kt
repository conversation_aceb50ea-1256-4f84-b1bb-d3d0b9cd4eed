package noice.app.exoplayer.cast

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.FragmentManager
import androidx.mediarouter.media.MediaRouter
import androidx.mediarouter.media.MediaRouter.RouteInfo
import androidx.mediarouter.media.MediaRouter.UNSELECT_REASON_DISCONNECTED
import com.google.android.gms.cast.CastDevice
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentBottomSheetCastBinding
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Utils
import noice.app.utils.isFalse
import noice.app.utils.isZero
import noice.app.utils.putAnalyticsKey
import noice.app.utils.visible

class CastBottomSheetFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentBottomSheetCastBinding
    private var mMediaRouter : MediaRouter? = null
    private lateinit var ctx : Context
    private lateinit var timer:CountDownTimer
    private var castAdapter: CastDeviceAdapter? = null

    companion object {
        private const val IS_CAST = "IS_CAST"
        fun newInstance(
            isCast: Boolean
        ) = CastBottomSheetFragment().apply {
            arguments = Bundle().apply {
                putBoolean(IS_CAST, isCast)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?): View {
        binding = FragmentBottomSheetCastBinding.inflate(inflater, container, false)
        if (CastManager.isCastConnected){
            binding.txtTitle.text = ctx.getString(R.string.disconnect_cast_title)
            binding.learnMore.visible(true)
            binding.learnMore.text = ctx.getString(R.string.disconnect_device)
            binding.txtDevice.visible(true)
            binding.rvList.visible(false)
            binding.pb.visible(false)
            binding.txtDevice.text = mMediaRouter?.selectedRoute?.name
        } else {
            binding.pb.visible(true)
            binding.rvList.visible(true)
        }
        binding.emptyView.visible(false)

        binding.learnMore.setOnClickListener {
            if (CastManager.isCastConnected){
                mMediaRouter?.unselect(UNSELECT_REASON_DISCONNECTED)
            }
        }
        binding.btnEmpty.setOnClickListener {
            dismissAllowingStateLoss()
        }
         timer = object: CountDownTimer(10000, 1000){
            override fun onTick(p0: Long) {

            }
            override fun onFinish() {
                cancel()
                if (CastManager.deviceList.isEmpty() && castAdapter?.itemCount.isZero()) {
                    binding.emptyView.visible(true)
                    binding.rvList.visible(false)
                    binding.pb.visible(false)
                } else {
                    binding.emptyView.visible(false)
                }
            }
        }
        return binding.root
    }
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            if (bottomSheet != null) {
                val coordinatorLayout = bottomSheet.parent as CoordinatorLayout
                val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
                bottomSheetBehavior.isHideable = false
                bottomSheetBehavior.peekHeight = Utils.getDeviceHeight(ctx)
                coordinatorLayout.parent.requestLayout()
            }
        }
        return dialog
    }
    override fun show(manager: FragmentManager, tag: String?) {
        manager.beginTransaction().add(this, tag)
            .commitAllowingStateLoss()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
        mMediaRouter = MediaRouter.getInstance(ctx)

        val routes = mMediaRouter?.routes


        if (CastManager.deviceList.isEmpty()){
            if (routes != null) {
                for (routeInfo in routes) {
                    val device = CastDevice.getFromBundle(routeInfo.extras)
                    if (device != null && CastManager.deviceList.contains(routeInfo).isFalse()) {
                        CastManager.deviceList.add(routeInfo)
                    }
                }
            }
        }

        CastManager.subscribeCastEvents(this){
            when(it.event){
                CastManager.CastAction.UPDATED -> {
                    if (CastManager.isCastConnected.isFalse()) {
                        castAdapter?.notifyDataSetChanged()
                        if (CastManager.deviceList.isEmpty() && castAdapter?.itemCount.isZero()){
                            binding.emptyView.visible(true)
                            binding.rvList.visible(false)
                        }
                    }
                }
                CastManager.CastAction.SELECTED -> {
                    //dismissAllowingStateLoss()
                }
                CastManager.CastAction.UNSELECTED -> {
                    dismissAllowingStateLoss()
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        castAdapter = CastDeviceAdapter(CastManager.deviceList, onItemClick = { routeInfo ->
            binding.pb.visible(true)
            binding.txtTitle.text = ctx.getText(R.string.connecting_cast)
            mMediaRouter?.selectRoute(routeInfo)
            binding.rvList.visible(false)
            binding.learnMore.visible(true)
        })
        binding.rvList.adapter = castAdapter
        binding.pb.visible(CastManager.deviceList.isEmpty())
        binding.emptyView.visible(false)
        if (CastManager.deviceList.isEmpty() && castAdapter?.itemCount.isZero()){
            timer.start()
        } else {
            AnalyticsUtil.sendEvent("cast_device_list",Bundle().apply { putAnalyticsKey("total_device_cast",CastManager.deviceList.size) })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        timer.cancel()
    }
}