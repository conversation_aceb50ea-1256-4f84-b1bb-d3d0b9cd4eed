package noice.app.exoplayer.cast

import android.os.Bundle
import android.util.Log
import com.google.android.gms.cast.Cast
import com.google.android.gms.cast.CastDevice
import com.google.gson.Gson
import noice.app.exoplayer.cast.models.CastInfo
import noice.app.utils.AnalyticsUtil
import noice.app.utils.putAnalyticsKey

class CastMessageReceiver() : Cast.MessageReceivedCallback {
    val namespace: String
        get() = "urn:x-cast:noice.app.ad-receiver"

    override fun onMessageReceived(castDevice: CastDevice, namespace: String, message: String) {
        Log.d("CastMessageReceiver", "onMessageReceived: $message")
        val castInfo =  Gson().fromJson(message, CastInfo::class.java)
        if (!castInfo.customMediaInfo?.breakClips.isNullOrEmpty()) {

            when(castInfo?.event?.type){
                "BREAK_CLIP_LOADING" ->{
                    sendAdEvent("ad_requested",castInfo)
                }
                "BREAK_CLIP_STARTED" ->{
                    sendAdEvent("ad_started",castInfo)
                }
                "BREAK_CLIP_ENDED" ->{
                    sendAdEvent("ad_completed",castInfo)
                }
            }
        }
    }

    @Synchronized
    fun getBreakData(castInfo: CastInfo) = castInfo.customMediaInfo?.breakClips?.find {
        it.id == castInfo.event?.breakClipId
    }

    private fun sendAdEvent(
        eventName: String,
        castInfo:CastInfo,
    ) {
        Log.d("CastMessageEvent", "eventInfo: ${castInfo.event?.type.orEmpty()}")
        val bundle = Bundle()
        bundle.putAnalyticsKey("adID", castInfo.event?.breakClipId.orEmpty())
        bundle.putAnalyticsKey("adDuration", -1)
        bundle.putAnalyticsKey("adURL", castInfo.customMediaInfo?.customData?.adTagUrl ?: "")
        bundle.putAnalyticsKey("vertical", castInfo.customMediaInfo?.customData?.vertical.toString())
        bundle.putAnalyticsKey("contentId", castInfo.customMediaInfo?.contentId.toString())
        bundle.putAnalyticsKey("contentTitle", castInfo.customMediaInfo?.metadata?.title ?: "")
        bundle.putAnalyticsKey("contentDuration", castInfo.customMediaInfo?.customData?.contentDuration ?: 0L)
        bundle.putAnalyticsKey("catalogId", castInfo.customMediaInfo?.customData?.catalogId ?: "")
        bundle.putAnalyticsKey("catalogTitle", castInfo.customMediaInfo?.customData?.catalogTitle ?: "")
        bundle.putAnalyticsKey("catalogSource", castInfo.customMediaInfo?.customData?.catalogSource ?: "")
        bundle.putAnalyticsKey(
            "adType", getBreakData(castInfo)?.id.orEmpty()
        )
        bundle.putAnalyticsKey("adStartTime", castInfo.event?.currentMediaTime)
        bundle.putAnalyticsKey("noOfAds", castInfo.customMediaInfo?.breakClips?.size.toString())
        bundle.putAnalyticsKey("adPosition", (castInfo.event?.index?:0).toString())

        AnalyticsUtil.firebaseAnalytics.logEvent(eventName, bundle)
    }

}