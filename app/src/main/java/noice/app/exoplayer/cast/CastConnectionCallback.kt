package noice.app.exoplayer.cast

import androidx.mediarouter.media.MediaRouter
import androidx.mediarouter.media.MediaRouter.RouteInfo
import com.google.android.gms.cast.CastDevice

interface CastConnectionCallback {
    fun onRouteAdded(routeInformation:List<RouteInfo>)
    fun deviceSelected(routeInfo:RouteInfo)
    fun deviceUnSelected(routeInfo:RouteInfo)
    fun onDeviceRemoved(route: RouteInfo)
}