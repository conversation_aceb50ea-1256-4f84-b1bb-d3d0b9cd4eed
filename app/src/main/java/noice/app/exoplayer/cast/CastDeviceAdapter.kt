package noice.app.exoplayer.cast

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.mediarouter.media.MediaRouter
import androidx.recyclerview.widget.RecyclerView
import noice.app.databinding.CastDeviceLayoutBinding
import noice.app.modules.podcast.model.Channel

class CastDeviceAdapter (var datalist:ArrayList<MediaRouter.RouteInfo>, val onItemClick: (MediaRouter.RouteInfo) -> Unit,):RecyclerView.Adapter<CastDeviceAdapter.UserHolder>() {
    class UserHolder(val binding: CastDeviceLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(info: MediaRouter.RouteInfo) {
            binding.txtDevice.text = info.name
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserHolder {
        val binding = CastDeviceLayoutBinding
            .inflate(LayoutInflater.from(parent.context), parent, false)
        return UserHolder(binding)
    }



    override fun onBindViewHolder(holder: UserHolder, position: Int) {
        holder.bind(datalist[position])
        holder.binding.root.rootView.setOnClickListener { onItemClick(datalist[position]) }
    }

    override fun getItemCount(): Int {
        return datalist.size
    }

}