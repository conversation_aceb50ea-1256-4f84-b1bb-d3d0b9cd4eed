package noice.app.exoplayer.cast

import android.net.Uri
import androidx.annotation.OptIn
import androidx.media3.cast.MediaItemConverter
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaItem.DrmConfiguration
import androidx.media3.common.MediaMetadata
import androidx.media3.common.MimeTypes
import androidx.media3.common.util.Assertions
import androidx.media3.common.util.UnstableApi
import com.google.android.gms.cast.MediaInfo
import com.google.android.gms.cast.MediaQueueItem
import com.google.android.gms.common.images.WebImage
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import noice.app.utils.MediaUtil
import org.json.JSONException
import org.json.JSONObject
import java.util.UUID

@OptIn(UnstableApi::class)
class CastMediaItemConverter : MediaItemConverter {
    override fun toMediaItem(mediaQueueItem: MediaQueueItem): MediaItem {
        val mediaInfo = mediaQueueItem.media
        Assertions.checkNotNull(mediaInfo)
        val metadataBuilder = MediaMetadata.Builder()
        val metadata = mediaInfo!!.metadata
        if (metadata != null) {
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_TITLE)) {
                metadataBuilder.setTitle(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_TITLE))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_SUBTITLE)) {
                metadataBuilder.setSubtitle(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_SUBTITLE))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_ARTIST)) {
                metadataBuilder.setArtist(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_ARTIST))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_ARTIST)) {
                metadataBuilder.setAlbumArtist(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_ARTIST))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_TITLE)) {
                metadataBuilder.setArtist(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_TITLE))
            }
            if (metadata.images.isNotEmpty()) {
                metadataBuilder.setArtworkUri(metadata.images[0].url)
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_COMPOSER)) {
                metadataBuilder.setComposer(metadata.getString(com.google.android.gms.cast.MediaMetadata.KEY_COMPOSER))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_DISC_NUMBER)) {
                metadataBuilder.setDiscNumber(metadata.getInt(com.google.android.gms.cast.MediaMetadata.KEY_DISC_NUMBER))
            }
            if (metadata.containsKey(com.google.android.gms.cast.MediaMetadata.KEY_TRACK_NUMBER)) {
                metadataBuilder.setTrackNumber(metadata.getInt(com.google.android.gms.cast.MediaMetadata.KEY_TRACK_NUMBER))
            }
        }
        // `mediaQueueItem` came from `toMediaQueueItem()` so the custom JSON data must be set.
        return getMediaItem(
            Assertions.checkNotNull(mediaInfo.customData), metadataBuilder.build()
        )
    }

    override fun toMediaQueueItem(mediaItem: MediaItem): MediaQueueItem {
        Assertions.checkNotNull(mediaItem.localConfiguration)
        requireNotNull(mediaItem.localConfiguration!!.mimeType) { "The item must specify its mimeType" }
        val metadata = com.google.android.gms.cast.MediaMetadata(
            if (MimeTypes.isAudio(mediaItem.localConfiguration!!.mimeType)) com.google.android.gms.cast.MediaMetadata.MEDIA_TYPE_MUSIC_TRACK else com.google.android.gms.cast.MediaMetadata.MEDIA_TYPE_MOVIE
        )
        if (mediaItem.mediaMetadata.title != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_TITLE,
                mediaItem.mediaMetadata.title.toString()
            )
        }
        if (mediaItem.mediaMetadata.subtitle != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_SUBTITLE,
                mediaItem.mediaMetadata.subtitle.toString()
            )
        }
        if (mediaItem.mediaMetadata.artist != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_ARTIST,
                mediaItem.mediaMetadata.artist.toString()
            )
        }
        if (mediaItem.mediaMetadata.albumArtist != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_ARTIST,
                mediaItem.mediaMetadata.albumArtist.toString()
            )
        }
        if (mediaItem.mediaMetadata.albumTitle != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_ALBUM_TITLE,
                mediaItem.mediaMetadata.albumTitle.toString()
            )
        }
        if (mediaItem.mediaMetadata.artworkUri != null) {
            metadata.addImage(WebImage(mediaItem.mediaMetadata.artworkUri!!))
        }
        if (mediaItem.mediaMetadata.composer != null) {
            metadata.putString(
                com.google.android.gms.cast.MediaMetadata.KEY_COMPOSER,
                mediaItem.mediaMetadata.composer.toString()
            )
        }
        if (mediaItem.mediaMetadata.discNumber != null) {
            metadata.putInt(
                com.google.android.gms.cast.MediaMetadata.KEY_DISC_NUMBER,
                mediaItem.mediaMetadata.discNumber!!
            )
        }
        if (mediaItem.mediaMetadata.trackNumber != null) {
            metadata.putInt(
                com.google.android.gms.cast.MediaMetadata.KEY_TRACK_NUMBER,
                mediaItem.mediaMetadata.trackNumber!!
            )
        }
        val contentUrl = mediaItem.localConfiguration!!.uri.toString()
        val contentId =
            if (mediaItem.mediaId == MediaItem.DEFAULT_MEDIA_ID) contentUrl else mediaItem.mediaId
        val mediaInfo = MediaInfo.Builder(contentId)
            .setStreamType(MediaInfo.STREAM_TYPE_BUFFERED)
            .setContentType(mediaItem.localConfiguration!!.mimeType)
            .setContentUrl(contentUrl)
            .setMetadata(metadata)
            .setCustomData(getCustomData(mediaItem))
            .build()
        return MediaQueueItem.Builder(mediaInfo).build()
    }

    companion object {
        private const val KEY_MEDIA_ITEM = "mediaItem"
        private const val KEY_PLAYER_CONFIG = "exoPlayerConfig"
        private const val KEY_MEDIA_ID = "mediaId"
        private const val KEY_URI = "uri"
        private const val KEY_TITLE = "title"
        private const val KEY_MIME_TYPE = "mimeType"
        private const val KEY_DRM_CONFIGURATION = "drmConfiguration"
        private const val KEY_UUID = "uuid"
        private const val KEY_LICENSE_URI = "licenseUri"
        private const val KEY_REQUEST_HEADERS = "requestHeaders"

        // Deserialization.
        private fun getMediaItem(
            customData: JSONObject, mediaMetadata: MediaMetadata
        ): MediaItem {
            return try {
                val mediaItemJson  = if (customData.has(KEY_MEDIA_ITEM)) {
                    customData.getJSONObject(KEY_MEDIA_ITEM)
                } else {
                    JSONObject().put("mediaId",customData.get("contentId"))
                        .put("uri",customData.get("contentUrl"))
                        .put("mimeType",MediaUtil.guessMime(customData.get("contentUrl").toString()))
                }
                val builder = MediaItem.Builder()
                    .setUri(Uri.parse(mediaItemJson.getString(KEY_URI)))
                    .setMediaId(mediaItemJson.getString(KEY_MEDIA_ID))
                    .setMediaMetadata(mediaMetadata)
                if (mediaItemJson.has(KEY_MIME_TYPE)) {
                    builder.setMimeType(mediaItemJson.getString(KEY_MIME_TYPE))
                }
                if (mediaItemJson.has(KEY_DRM_CONFIGURATION)) {
                    populateDrmConfiguration(
                        mediaItemJson.getJSONObject(KEY_DRM_CONFIGURATION),
                        builder
                    )
                }
                builder.build()
            } catch (e: JSONException) {
                Firebase.crashlytics.log("cast crash -$customData")
                throw RuntimeException(e)
            }
        }

        @Throws(JSONException::class)
        private fun populateDrmConfiguration(json: JSONObject, mediaItem: MediaItem.Builder) {
            val drmConfiguration = DrmConfiguration.Builder(
                UUID.fromString(
                    json.getString(
                        KEY_UUID
                    )
                )
            )
                .setLicenseUri(json.getString(KEY_LICENSE_URI))
            val requestHeadersJson = json.getJSONObject(KEY_REQUEST_HEADERS)
            val requestHeaders = HashMap<String, String>()
            val iterator = requestHeadersJson.keys()
            while (iterator.hasNext()) {
                val key = iterator.next()
                requestHeaders[key] = requestHeadersJson.getString(key)
            }
            drmConfiguration.setLicenseRequestHeaders(requestHeaders)
            mediaItem.setDrmConfiguration(drmConfiguration.build())
        }

        // Serialization.
        private fun getCustomData(mediaItem: MediaItem): JSONObject {
            val json = JSONObject()
            val keys = mediaItem.mediaMetadata.extras!!.keySet()
            for (key in keys) {
                try {
                    // json.put(key, bundle.get(key)); see edit below
                    json.put(key, JSONObject.wrap(mediaItem.mediaMetadata.extras!![key]))
                } catch (e: JSONException) {
                    //Handle exception here
                }
            }
            try {
                json.put(KEY_MEDIA_ITEM, getMediaItemJson(mediaItem))
                val playerConfigJson = getPlayerConfigJson(mediaItem)
                if (playerConfigJson != null) {
                    json.put(KEY_PLAYER_CONFIG, playerConfigJson)
                }
            } catch (e: JSONException) {
                throw RuntimeException(e)
            }
            return json
        }

        @Throws(JSONException::class)
        private fun getMediaItemJson(mediaItem: MediaItem): JSONObject {
            Assertions.checkNotNull(mediaItem.localConfiguration)
            val json = JSONObject()
            json.put(KEY_MEDIA_ID, mediaItem.mediaId)
            json.put(KEY_TITLE, mediaItem.mediaMetadata.title)
            json.put(KEY_URI, mediaItem.localConfiguration!!.uri.toString())
            json.put(KEY_MIME_TYPE, mediaItem.localConfiguration!!.mimeType)
            if (mediaItem.localConfiguration!!.drmConfiguration != null) {
                json.put(
                    KEY_DRM_CONFIGURATION,
                    getDrmConfigurationJson(mediaItem.localConfiguration!!.drmConfiguration)
                )
            }
            return json
        }

        @Throws(JSONException::class)
        private fun getDrmConfigurationJson(drmConfiguration: DrmConfiguration?): JSONObject {
            val json = JSONObject()
            json.put(KEY_UUID, drmConfiguration!!.scheme)
            json.put(KEY_LICENSE_URI, drmConfiguration.licenseUri)
            json.put(
                KEY_REQUEST_HEADERS, JSONObject(
                    drmConfiguration.licenseRequestHeaders as Map<*, *>?
                )
            )
            return json
        }

        @Throws(JSONException::class)
        private fun getPlayerConfigJson(mediaItem: MediaItem): JSONObject? {
            if (mediaItem.localConfiguration == null
                || mediaItem.localConfiguration!!.drmConfiguration == null
            ) {
                return null
            }
            val drmConfiguration = mediaItem.localConfiguration!!.drmConfiguration
            val drmScheme: String
            drmScheme = if (C.WIDEVINE_UUID == drmConfiguration!!.scheme) {
                "widevine"
            } else if (C.PLAYREADY_UUID == drmConfiguration.scheme) {
                "playready"
            } else {
                return null
            }
            val playerConfigJson = JSONObject()
            playerConfigJson.put("withCredentials", false)
            playerConfigJson.put("protectionSystem", drmScheme)
            if (drmConfiguration.licenseUri != null) {
                playerConfigJson.put("licenseUrl", drmConfiguration.licenseUri)
            }
            if (!drmConfiguration.licenseRequestHeaders.isEmpty()) {
                playerConfigJson.put("headers", JSONObject(drmConfiguration.licenseRequestHeaders as Map<*, *>?))
            }
            return playerConfigJson
        }
    }
}