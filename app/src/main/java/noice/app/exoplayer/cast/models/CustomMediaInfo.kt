package noice.app.exoplayer.cast.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CustomMediaInfo(
    val breakClips: ArrayList<BreakClip>?,
    val breaks: ArrayList<Break>?,
    val contentId: String?,
    val contentType: String?,
    val contentUrl: String?,
    val customData: CustomData?,
    val duration: Double?,
    val hlsSegmentFormat: String?,
    val hlsVideoSegmentFormat: String?,
    val mediaCategory: String?,
    val metadata: Metadata?,
    val streamType: String?,
    val vmapAdsRequest: VmapAdsRequest?
):Parcelable