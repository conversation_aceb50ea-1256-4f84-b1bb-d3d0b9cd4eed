package noice.app.exoplayer.cast

import android.annotation.SuppressLint
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.mediarouter.media.MediaControlIntent
import androidx.mediarouter.media.MediaRouteSelector
import androidx.mediarouter.media.MediaRouter
import com.google.android.gms.cast.CastDevice
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import java.util.concurrent.Executor
import java.util.concurrent.Executors

object CastManager {
    private lateinit var mMediaRouterCallback : PlayerMediaRouterCallBack
    private lateinit var mSelector : MediaRouteSelector
    var deviceList: ArrayList<MediaRouter.RouteInfo> = ArrayList()
    var castDevice: MediaRouter.RouteInfo? = null
    var isCastConnected : Boolean = false
    private val castEvent = MutableSharedFlow<CastEvent>()
    private val ioScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    enum class CastAction {
        UPDATED,
        SELECTED,
        UNSELECTED
    }

    data class CastEvent(val event: CastAction,)

    init {
       setUpCast()
    }

    fun subscribeCastEvents(lifecycleOwner: LifecycleOwner, action: suspend (CastEvent) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            castEvent.collectLatest(action)
        }
    }

    fun invokeCastDeviceEvent(event: CastEvent) {
        ioScope.launch {
            castEvent.emit(event)
        }
    }

    private fun setUpCast() {
        mSelector = MediaRouteSelector.Builder() // These are the framework-supported intents
            .addControlCategory(MediaControlIntent.CATEGORY_REMOTE_PLAYBACK)
            .build()
        mMediaRouterCallback = PlayerMediaRouterCallBack(object : CastConnectionCallback{
            @SuppressLint("NotifyDataSetChanged")
            override fun onRouteAdded(routeInformation: List<MediaRouter.RouteInfo>) {


                for (routeInfo in routeInformation) {
                    val device = CastDevice.getFromBundle(routeInfo.extras)
                    if (device != null) {
                        deviceList.add(routeInfo)
                    }
                }
                invokeCastDeviceEvent(CastEvent(CastAction.UPDATED))
                Log.d("TAG", "onRouteAdded: name=$deviceList")
            }

            override fun deviceSelected(routeInfo: MediaRouter.RouteInfo) {
                castDevice = routeInfo
                invokeCastDeviceEvent(CastEvent(CastAction.SELECTED))
            }

            override fun deviceUnSelected(routeInfo: MediaRouter.RouteInfo) {
                invokeCastDeviceEvent(CastEvent(CastAction.UNSELECTED))
            }

            override fun onDeviceRemoved(route: MediaRouter.RouteInfo) {
                if (deviceList.contains(route).isTrue()){
                    deviceList.remove(route)
                }
                invokeCastDeviceEvent(CastEvent(CastAction.UPDATED))
            }
        })
    }


    fun registerForDeviceCallback(){
        MediaRouter.getInstance(BaseApplication.getBaseAppContext()).addCallback(mSelector, mMediaRouterCallback,MediaRouter.CALLBACK_FLAG_PERFORM_ACTIVE_SCAN)
    }

    fun unRegisterDeviceCallback(){
        MediaRouter.getInstance(BaseApplication.getBaseAppContext()).removeCallback(mMediaRouterCallback)
    }

}