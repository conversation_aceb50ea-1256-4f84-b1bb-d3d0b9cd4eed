package noice.app.exoplayer

import androidx.recyclerview.widget.DiffUtil
import noice.app.model.ExoNotificationData
import noice.app.utils.Utils.checkIndex

class PlayerPagerDiff(
    private val oldList : List<ExoNotificationData>,
    private val newList : List<ExoNotificationData>
) : DiffUtil.Callback() {

    override fun getOldListSize() = oldList.size

    override fun getNewListSize() = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return if (newList.checkIndex(newItemPosition) && oldList.checkIndex(oldItemPosition)) {
            newList[newItemPosition].id == oldList[oldItemPosition].id && newList[newItemPosition].uniqueId == oldList[oldItemPosition].uniqueId
        } else {
            false
        }
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return if (newList.checkIndex(newItemPosition) && oldList.checkIndex(oldItemPosition)) {
            newList[newItemPosition].id == oldList[oldItemPosition].id && newList[newItemPosition].uniqueId == oldList[oldItemPosition].uniqueId
        } else {
            false
        }
    }
}