package noice.app.exoplayer

import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.DiffUtil
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import noice.app.databinding.CustomPlayerFragmentBinding
import noice.app.model.ExoNotificationData
import noice.app.model.ads.AdsResponse
import noice.app.player.managers.QueueManager
import noice.app.views.CustomAdView

class PlayerPagerAdapter(
    private val activity: AppCompatActivity,
    private val adView: CustomAdView? = null,
    private val bottomSheetBinding: CustomPlayerFragmentBinding
) : FragmentStateAdapter(activity) {

    private val queue = ArrayList<ExoNotificationData>()
    private var adsResponse: AdsResponse? = null

    override fun getItemCount(): Int {
        return queue.size
    }

    override fun getItemId(position: Int): Long {
        return queue[position].uniqueId.hashCode().toLong()
    }

    override fun containsItem(itemId: Long): Boolean {
        return queue.any { it.uniqueId.hashCode().toLong() == itemId }
    }

    override fun createFragment(position: Int) =
        PlayerDetailFragment.newInstance(queue.getOrNull(position), bottomSheetBinding)

    fun updatePager(viewPager: ViewPager2) {
        val diff = PlayerPagerDiff(queue, QueueManager.queue)
        val result = DiffUtil.calculateDiff(diff)

        queue.clear()
        queue.addAll(QueueManager.queue)

        result.dispatchUpdatesTo(this)

        viewPager.post {
            viewPager.setCurrentItem(0, false)
        }
    }

    fun updateAdResponse(adsResponse: AdsResponse?) {
        this.adsResponse = adsResponse
    }
}