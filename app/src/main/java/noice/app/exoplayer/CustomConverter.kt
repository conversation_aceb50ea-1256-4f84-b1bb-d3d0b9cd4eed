package noice.app.exoplayer

import androidx.media3.cast.MediaItemConverter
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import com.google.android.gms.cast.MediaQueueItem

@UnstableApi
class CustomConverter : MediaItemConverter {
    override fun toMediaQueueItem(mediaItem: MediaItem): MediaQueueItem {
        // The MediaQueueItem you build is expected to be in the tag.
        return mediaItem.localConfiguration?.tag as MediaQueueItem
    }

    override fun toMediaItem(item: MediaQueueItem): MediaItem {
        // This should give the same as when you build your media item to be passed to ExoPlayer.
        return MediaItem.Builder()
            .setUri(item.media?.contentUrl)
            .setTag(item)
            .build()
    }
}