package noice.app.exoplayer

import android.content.Context
import android.content.DialogInterface
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.annotation.OptIn
import androidx.fragment.app.FragmentManager
import com.appsflyer.AppsFlyerLib
import androidx.media3.common.Format
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaItem.DrmConfiguration
import androidx.media3.common.DrmInitData
import androidx.media3.common.TrackSelectionParameters
import androidx.media3.common.util.Log
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.HttpDataSource
import androidx.media3.exoplayer.drm.DrmSession.DrmSessionException
import androidx.media3.exoplayer.drm.DrmSessionEventListener
import androidx.media3.exoplayer.drm.OfflineLicenseHelper
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadHelper
import androidx.media3.exoplayer.offline.DownloadHelper.LiveContentUnsupportedException
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadRequest
import androidx.media3.exoplayer.offline.DownloadService
import androidx.media3.exoplayer.scheduler.Requirements
import com.google.gson.Gson
import kotlinx.coroutines.*
import noice.app.BaseApplication
import noice.app.R
import noice.app.exoplayer.TrackSelectionDialog.Companion.willHaveContent
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.model.Content
import noice.app.rest.NetworkRequests
import noice.app.room.Downloads
import noice.app.utils.*
import java.io.IOException
import java.util.*
import java.util.concurrent.CopyOnWriteArraySet


/**
 * Tracks media that has been downloaded.
 */
@OptIn(UnstableApi::class)
class ExoPlayerDownloadTracker
    (
    context: Context,
    downloadManager: DownloadManager
) {
    /**
     * Listens for changes in the tracked downloads.
     */
    interface Listener {
        /**
         * Called when the tracked downloads changed.
         */
        fun onDownloadsChanged(download: Download)
    }

    interface ProgressListener {
        /**
         * Called when the tracked downloads changed.
         */
        fun onProgressChanged(download: Download)
    }

    private val context = context.applicationContext
    private val listeners: CopyOnWriteArraySet<DownloadProgressListener> = CopyOnWriteArraySet()
    private val pendingDownloads = ArrayList<Content>()
    private val downloads: LinkedHashMap<Uri, Download> = LinkedHashMap()
    private val downloadIndex = downloadManager.downloadIndex
    private var startDownloadDialogHelper: StartDownloadDialogHelper? = null
    private var downloadCounter = 0
    private val serviceHandler = Handler(Looper.getMainLooper())

    fun addListener(listener: DownloadProgressListener) {
        listeners.add(listener)
    }

    fun removeListener(listener: DownloadProgressListener) {
        listeners.remove(listener)
    }

    fun isDownloaded(id: String?, url: String?): Boolean {
        if (!url.isNullOrEmpty() && downloads.containsKey(Uri.parse(url))) {
            val download = downloads[Uri.parse(url)]
            if (id == download?.request?.id && download?.state == Download.STATE_COMPLETED) {
                return true
            }
        }
        return false
    }

    fun getStateFor(content: Content?): Int {
        if (content != null && pendingDownloads.contains(content)) {
            return Download.STATE_QUEUED
        } else if (!content?.url.isNullOrEmpty() && downloads.containsKey(Uri.parse(content?.url))) {
            val download = downloads[Uri.parse(content?.url)]
            return if(download?.state == Download.STATE_COMPLETED) {
                if (isDownloaded(content?.id, content?.url)) {
                    Download.STATE_COMPLETED
                } else {
                    -1
                }
            } else {
                download?.state ?: -1
            }
        }
        return -1
    }

    fun getDownloadObjects(state : Int) : List<Download> {
        return downloads.filter {
            it.value.state == state
        }.map {
            it.value
        }
    }

    fun getDownloads(state : Int = -1): List<Content> {
        return runBlocking(Dispatchers.IO) {
            if (state != -1) {
                downloads.filter {
                    it.value.state == state
                }
            } else {
                downloads
            }.map {
                it.value.request.id
            }.let {
                return@runBlocking BaseApplication.application.getAppDb().downloadsDao().getDownloadedContents(it)
            }
        }
    }

    fun getDownloadRequest(uri: Uri): DownloadRequest? {
        val download = downloads[uri]
        return if (download != null && download.state != Download.STATE_FAILED) download.request else null
    }

    fun download(fragmentManager: FragmentManager? = null, content : Content, source : String) {

        if (!pendingDownloads.contains(content)) {
            pendingDownloads.add(content)
        }

        if (startDownloadDialogHelper == null) {
            CoroutineScope(Dispatchers.Main).launch {

                if (pendingDownloads.isNotEmpty()) {
                    pendingDownloads[0]
                } else {
                    content
                }.let {
                    val mediaItem = MediaItem.Builder().setUri(it.url).setMediaId(it.id.toString()).build()

                    startDownloadDialogHelper = StartDownloadDialogHelper(
                        fragmentManager,
                        DownloadHelper.forMediaItem(
                            context,
                            mediaItem,
                            ExoplayerUtils.getAudioRenderer(),
                            ExoplayerUtils.getHttpDataSourceFactory()
                        ),
                        mediaItem,
                        it,
                        source
                    )
                }
            }
        }
    }

    private fun loadDownloads() {
        try {
            downloadIndex.getDownloads().use { loadedDownloads ->
                while (loadedDownloads.moveToNext()) {
                    val download = loadedDownloads.download
                    downloads[download.request.uri] = download
                }
            }
        } catch (e: IOException) {
            Log.w(TAG, "Failed to query downloads", e)
        }
    }

    fun onProgressChanged(downloads: List<Download>) {
        CoroutineScope(Dispatchers.Main).launch {
            downloads.find {
                it.state == Download.STATE_DOWNLOADING
            }?.let { currentDownload ->
                listeners.filter { listener ->
                    listener.id == currentDownload.request.id
                }.forEach { listener ->
                    listener.notifyProgress(currentDownload)
                }

                listeners.filter {
                    it.id == OBSERVE_ALL
                }.forEach { listener ->
                    listener.notifyProgress(currentDownload)
                }
            }
        }
    }

    private inner class DownloadManagerListener : DownloadManager.Listener {
        override fun onDownloadChanged(
            downloadManager: DownloadManager,
            download: Download,
            finalException: Exception?
        ) {

            when (download.state) {
                Download.STATE_COMPLETED -> {
                    CoroutineScope(Dispatchers.IO).launch {
                        val content = BaseApplication.application.getAppDb().downloadsDao()
                            .getDownloadedContent(download.request.id)
                        if (content != null) {
                            AnalyticsUtil.sendEvent(
                                (content.catalogType) ?: (content.catalog?.type) ?: "",
                                content.id ?: "",
                                content.id,
                                "content_downloaded",
                                "",
                                (content.meta?.timeElapsed ?: 0).toString(),
                                (content.duration ?: 0).toString(),
                                "",
                                content.catalogTitle ?: "",
                                content.title ?: ""
                            )


                            val eventValue: MutableMap<String, Any> = HashMap()
                            eventValue["af_content_type"] =
                                content.catalogType ?: content.catalog?.type ?: ""
                            eventValue["af_content_id"] = content.id ?: ""
                            eventValue["af_content"] = content.title ?: ""
                            eventValue["media_parent"] = content.catalogTitle ?: ""
                            eventValue["media_duration"] = content.duration ?: 0L
                            AppsFlyerLib.getInstance().logEvent(
                                BaseApplication.getBaseAppContext(),
                                "media_downloaded",
                                eventValue
                            )

                            val mediaAction = MediaAction(
                                "download",
                                1.0,
                                content.id,
                                "content",
                                PrefUtils.userDetails?.id.toString(),
                                content.catalogType ?: content.catalog?.type ?: "",
                                null
                            )
                            BaseApplication.doServerCall({
                                NetworkRequests.performAction(mediaAction)
                            }, null)

                            val downloadData = ExoplayerUtils.getDownloadData(download)
                            downloadCounter = 0

                            val contentLink = "${Constants.WEB_BASE_URL}content/${content.id}"
                            val catalogLink = "${Constants.WEB_BASE_URL}catalog/${content.catalogId ?: content.catalog?.id ?: ""}"

                            MoEngageAnalytics.sendEvent(context, "content downloaded", Bundle().apply {
                                putString(
                                    "vertical",
                                    content.catalogType ?: content.catalog?.type ?: ""
                                )
                                putString(
                                    "catalog title",
                                    content.catalogTitle ?: content.catalog?.title ?: ""
                                )
                                putString("content title", content.title ?: "")
                                putString(
                                    "catalog id",
                                    content.catalogId ?: content.catalog?.id ?: ""
                                )
                                putString("content id", content.id ?: "")
                                putString("content duration", content.duration.toString())
                                putString("source", downloadData?.source ?: "")
                                putString("network type", NetworkUtils.getNetworkType(context))
                                putString("download quality", PrefUtils.downLoadQuality)
                                putString("content link", contentLink)
                                putString("catalog link", catalogLink)
                            })
                        }
                    }
                }
                Download.STATE_DOWNLOADING -> {
                    CoroutineScope(Dispatchers.IO).launch {
                        val content = BaseApplication.application.getAppDb().downloadsDao().getDownloadedContent(download.request.id)
                        val downloadData = ExoplayerUtils.getDownloadData(download)
                        val entitySubType = content?.catalogType?:content?.catalog?.type?:content?.entitySubType?:""
                        downloadCounter +=1
                        if (downloadCounter == 1) {
                            MoEngageAnalytics.sendEvent(context,"content download started", Bundle().apply {
                                putString("vertical", content?.catalogType ?: content?.catalog?.type ?: "")
                                putString("catalog title", content?.catalogTitle ?: content?.catalog?.title ?: "")
                                putString("content title", content?.title ?: "")
                                putString("catalog id", content?.catalogId ?: content?.catalog?.id ?: "")
                                putString("content id", content?.id ?: "")
                                putString("content duration", content?.duration.toString())
                                putString("source", downloadData?.source ?: "")
                                putString("network type", NetworkUtils.getNetworkType(context))
                                putString("download quality", PrefUtils.downLoadQuality)
                            })
                        }
                    }
                }
                Download.STATE_FAILED, Download.STATE_REMOVING, Download.STATE_STOPPED -> {
                    downloadCounter = 0
                }
                else -> {

                }
            }

            downloads[download.request.uri] = download
            listeners.filter { listener ->
                if (listener.id == OBSERVE_ALL) {
                    true
                } else {
                    download.request.id == listener.id
                }
            }.forEach { listener ->
                listener.onDownloadsChanged(download)
            }
        }

        override fun onDownloadRemoved(
            downloadManager: DownloadManager, download: Download
        ) {
            downloads.remove(download.request.uri)
            listeners.filter { listener ->
                if (listener.id == OBSERVE_ALL) {
                    true
                } else {
                    download.request.id == listener.id
                }
            }.forEach { listener ->
                listener.onDownloadsChanged(download)
            }
        }

        override fun onRequirementsStateChanged(
            downloadManager: DownloadManager,
            requirements: Requirements,
            notMetRequirements: Int
        ) {
            if ((notMetRequirements - notMetRequirements.xor(Requirements.DEVICE_STORAGE_NOT_LOW)) == Requirements.DEVICE_STORAGE_NOT_LOW) {
                Toast.makeText(BaseApplication.getBaseAppContext(), "Download stopped!!, Storage low", Toast.LENGTH_LONG).show()
                StorageUtils.getMemoryStats {
                    if (it != null) {
                        AnalyticsUtil.sendEvent("download failed", Bundle().apply {
                            putString("reason", "storage full")
                            putString("storage free", it.first.toString())
                            putString("storage total", it.second.toString())
                        })
                    }
                }
            }
        }
    }

    abstract class DownloadProgressListener(val id : String) : Listener, ProgressListener {
        fun notifyProgress(download: Download) {
            onProgressChanged(download)
        }
    }

    @OptIn(UnstableApi::class)
    private inner class StartDownloadDialogHelper
        (
        private val fragmentManager: FragmentManager?,
        private val downloadHelper: androidx.media3.exoplayer.offline.DownloadHelper,
        private val mediaItem: MediaItem,
        private val content: Content,
        private val source: String
    ) : DownloadHelper.Callback, DialogInterface.OnDismissListener, TrackSelectionDialog.TrackSelectionListener {

        private var trackSelectionDialog: TrackSelectionDialog? = null
        private var widevineOfflineLicenseFetchTask: Job? = null
        private var keySetId: ByteArray? = null

        fun release() {
            downloadHelper.release()
            trackSelectionDialog?.dismiss()
            widevineOfflineLicenseFetchTask?.cancel()
            startDownloadDialogHelper = null
            pendingDownloads.remove(content)
            if (pendingDownloads.isNotEmpty()) {
                download(null, pendingDownloads[0], source)
            }
        }

        // DownloadHelper.Callback implementation.
        @OptIn(UnstableApi::class)
        override fun onPrepared(helper: DownloadHelper) {

            val format = getFirstFormatWithDrmInitData(helper)
            if (format == null) {
                onDownloadPrepared(helper, content)
                return
            }
            if (format.drmInitData !== null && !hasSchemaData(format.drmInitData!!)) {
                Toast.makeText(context, R.string.download_start_error_offline_license, Toast.LENGTH_LONG).show()
                Log.e(TAG, "Downloading content where DRM scheme data is not located in the manifest is not supported")
                return
            }
            widevineOfflineLicenseFetchTask = getWidevineOfflineLicenseFetchTask(
                format = format,
                drmConfiguration = mediaItem.localConfiguration?.drmConfiguration,
                httpDataSourceFactory = ExoplayerUtils.getHttpDataSourceFactory(),
                dialogHelper = this,
                downloadHelper = helper
            )
        }

        override fun onPrepareError(helper: DownloadHelper, e: IOException) {
            val isLiveContent = e is LiveContentUnsupportedException
            val toastStringId =
                if (isLiveContent) R.string.download_live_unsupported else R.string.download_start_error
            val logMessage =
                if (isLiveContent) "Downloading live content unsupported" else "Failed to start download"
            Toast.makeText(context, toastStringId, Toast.LENGTH_LONG).show()
            Log.e(TAG, logMessage, e)

            release()
        }

        // DialogInterface.OnDismissListener implementation.
        override fun onDismiss(dialogInterface: DialogInterface) {
            trackSelectionDialog = null
            downloadHelper.release()
        }
        // Internal methods.

        /**
         * Returns the first [Format] with a non-null [Format.drmInitData] found in the
         * content's tracks, or null if none is found.
         */
        private fun getFirstFormatWithDrmInitData(helper: DownloadHelper): Format? {
            for (periodIndex in 0 until helper.periodCount) {
                val mappedTrackInfo = helper.getMappedTrackInfo(periodIndex)
                for (rendererIndex in 0 until mappedTrackInfo.rendererCount) {
                    val trackGroups = mappedTrackInfo.getTrackGroups(rendererIndex)
                    for (trackGroupIndex in 0 until trackGroups.length) {
                        val trackGroup = trackGroups[trackGroupIndex]
                        for (formatIndex in 0 until trackGroup.length) {
                            val format = trackGroup.getFormat(formatIndex)
                            if (format.drmInitData != null) {
                                return format
                            }
                        }
                    }
                }
            }
            return null
        }

        fun onOfflineLicenseFetched(helper: DownloadHelper, keySetId: ByteArray) {
            this.keySetId = keySetId
            onDownloadPrepared(helper, content)
        }

        fun onOfflineLicenseFetchedError(e: DrmSessionException) {
            Toast.makeText(context, R.string.download_start_error_offline_license, Toast.LENGTH_LONG).show()
            Log.e(TAG, "Failed to fetch offline DRM license", e)
        }

        private fun onDownloadPrepared(helper: DownloadHelper, content: Content) {
            if (helper.periodCount == 0) {
                Log.d(TAG, "No periods found. Downloading entire stream.")
                startDownload(content)
                downloadHelper.release()
                return
            }

            val tracks = downloadHelper.getTracks(0)
            if (!willHaveContent(tracks)) {
                Log.d(TAG, "No dialog content. Downloading entire stream.")
                startDownload(content)
                downloadHelper.release()
                return
            }

            val trackSelectorParameters = DownloadHelper.getDefaultTrackSelectorParameters(context)

            if (fragmentManager != null) {
                trackSelectionDialog = TrackSelectionDialog.createForTracksAndParameters( /* titleId= */
                    R.string.exo_download_description,
                    tracks,
                    trackSelectorParameters,
                    allowAdaptiveSelections = false,
                    allowMultipleOverrides = false,
                    trackSelectionListener = this,
                    onDismissListener = this
                )
                trackSelectionDialog?.show(fragmentManager, null)
            } else {
                DownloadSelectionHelper(tracks.groups, false, trackSelectorParameters, this)
            }
        }

        /**
         * Returns whether any the [DrmInitData.SchemeData] contained in `drmInitData` has
         * non-null [DrmInitData.SchemeData.data].
         */
        private fun hasSchemaData(drmInitData: DrmInitData): Boolean {
            for (i in 0 until drmInitData.schemeDataCount) {
                if (drmInitData[i].hasData()) {
                    return true
                }
            }
            return false
        }

        private fun startDownload(content: Content, downloadRequest: DownloadRequest = buildDownloadRequest(
            content,
            source
        )) {
            serviceHandler.post {
                DownloadService.sendAddDownload(
                    context,
                    ExoplayerDownloadService::class.java,
                    downloadRequest,
                    true
                )
            }

            CoroutineScope(Dispatchers.IO).launch {
                PrefUtils.userDetails?.id?.let { userId ->
                    BaseApplication.application.getAppDb().downloadsDao().apply {
                        if (content.catalog != null) {
                            content.catalogType = content.catalog?.type
                            content.catalogTitle = content.catalog?.title
                            content.catalogId = content.catalog?.id
                            content.source = content.catalog?.source
                        }
                        content.downloadTime = System.currentTimeMillis()
                        addDownload(Downloads(userId, content.id ?: "", content))
                    }
                }
            }

            release()
        }

        private fun buildDownloadRequest(content: Content, source: String): DownloadRequest {

            val catalogTitle = if (!content.catalogTitle.isNullOrEmpty()) {
                content.catalogTitle
            } else {
                content.catalog?.title
            }

            val strData = Gson().toJson(DownloadNotificationData(
                content.id,
                catalogTitle,
                content.title,
                source
            ))

            return downloadHelper.getDownloadRequest(
                    content.id ?: "",
                    Util.getUtf8Bytes(strData)
                ).copyWithKeySetId(keySetId)
        }

        init {
            downloadHelper.prepare(this)
        }

        override fun onTracksSelected(trackSelectionParameters: TrackSelectionParameters?) {
            for (periodIndex in 0 until downloadHelper.periodCount) {
                if (trackSelectionParameters != null) {
                    downloadHelper.clearTrackSelections(periodIndex)
                    downloadHelper.addTrackSelection(periodIndex, trackSelectionParameters)
                }
            }
            val downloadRequest = buildDownloadRequest(content, source)
            if (downloadRequest.streamKeys.isEmpty()) {
                // All tracks were deselected in the dialog. Don't start the download.
                return
            }
            startDownload(content, downloadRequest)
        }
    }

    /**
     * Downloads a Widevine offline license in a background thread.
     */
    private fun getWidevineOfflineLicenseFetchTask(
        format: Format,
        drmConfiguration: DrmConfiguration?,
        httpDataSourceFactory: HttpDataSource.Factory,
        dialogHelper: StartDownloadDialogHelper,
        downloadHelper: DownloadHelper
    ): Job {
        return CoroutineScope(Dispatchers.IO).launch {
            downloadLicense(
                format,
                drmConfiguration,
                httpDataSourceFactory,
                dialogHelper,
                downloadHelper
            )
        }
    }

    private fun downloadLicense(
        format: Format,
        drmConfiguration: DrmConfiguration?,
        httpDataSourceFactory: HttpDataSource.Factory,
        dialogHelper: StartDownloadDialogHelper,
        downloadHelper: DownloadHelper
    ) {
        val offlineLicenseHelper = OfflineLicenseHelper.newWidevineInstance(
            drmConfiguration?.licenseUri.toString(),
            drmConfiguration?.forceDefaultLicenseUri ?: false,
            httpDataSourceFactory,
            drmConfiguration?.licenseRequestHeaders,
            DrmSessionEventListener.EventDispatcher()
        )
        try {
            val keySetId = offlineLicenseHelper.downloadLicense(format)
            dialogHelper.onOfflineLicenseFetched(downloadHelper, keySetId)
        } catch (e: DrmSessionException) {
            dialogHelper.onOfflineLicenseFetchedError(e)
            e.printStackTrace()
        } finally {
            offlineLicenseHelper.release()
        }
    }

    companion object {
        private const val TAG = "DownloadTracker"
        const val OBSERVE_ALL = "OBSERVE_ALL"
    }

    init {
        downloadManager.addListener(DownloadManagerListener())
        loadDownloads()
    }
}