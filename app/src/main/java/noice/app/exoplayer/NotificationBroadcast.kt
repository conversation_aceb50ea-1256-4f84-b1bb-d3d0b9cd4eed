package noice.app.exoplayer

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.NOTIFICATION_SERVICE
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import com.google.android.exoplayer2.ui.PlayerNotificationManager
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.player.models.PlayerEvent
import noice.app.utils.ClassVariantProvider

class NotificationBroadcast : BroadcastReceiver() {

    companion object {
        const val PAUSE_DOWNLOAD = "noice.app.PAUSE_DOWNLOAD"
        const val PLAY_DOWNLOAD = "noice.app.PLAY_DOWNLOAD"
        const val RETRY_DOWNLOAD = "noice.app.RETRY_DOWNLOAD"
        const val CANCEL_DOWNLOAD = "noice.app.CANCEL_DOWNLOAD"

        val INTENT_FILTER = IntentFilter().apply {
            addAction(CANCEL_DOWNLOAD)
            addAction(PLAY_DOWNLOAD)
            addAction(RETRY_DOWNLOAD)
            addAction(PAUSE_DOWNLOAD)
            addAction(PlayerNotificationManager.ACTION_PAUSE)
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == PlayerNotificationManager.ACTION_PAUSE) {
            PlayerEvent(PlayerEvent.NOTIFICATION_CLICKS, intent.action).sendPlayerEvent()
            return
        }

        context?.let {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                context.sendBroadcast(Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS))
            }

            val notificationId = intent?.getIntExtra(BasePlayerActivity.NOTIFICATION_ID, -1) ?: -1
            if (notificationId != -1) {
                val notificationManager = context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.cancel(notificationId)
            }

            val contentId = intent?.getStringExtra(BasePlayerActivity.ENTITY_ID) ?: ""
            intent?.setClass(context, ClassVariantProvider.of(HomeActivity::class.java))
            intent?.putExtra(BasePlayerActivity.ENTITY_ID, contentId)
            intent?.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        }
    }
}