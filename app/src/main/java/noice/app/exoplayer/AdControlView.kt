package noice.app.exoplayer

import android.content.Context
import android.util.AttributeSet
import androidx.media3.common.AdViewProvider
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.PlayerControlView

@UnstableApi
class AdControlView : PlayerControlView, AdViewProvider {

    private var frameLayout = FrameLayout(context)

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(
        context, attrs
    )

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    override fun getAdViewGroup(): ViewGroup {
        return frameLayout
    }
}