package noice.app.exoplayer

import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.media3.common.PlaybackParameters
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.PlayerControlView
import androidx.media3.ui.TimeBar
import noice.app.BuildConfig
import noice.app.CustomTimeBar
import noice.app.R
import noice.app.data.DataController.sleepTime
import noice.app.exoplayer.cast.CastManager
import noice.app.model.ExoNotificationData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.home.fragment.SleepTimeDialog
import noice.app.modules.dashboard.home.fragment.SleepTimeDialog.Companion.SLEEP_TIME_0
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.secondsToMs
import noice.app.utils.Utils.unwrap
import noice.app.utils.getView
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import noice.app.utils.putAnalyticsKey
import org.greenrobot.eventbus.EventBus

@OptIn(UnstableApi::class)
class PlayerControllerView : PlayerControlView,
    SleepTimeDialog.PlayerSleepClickListener {

    private lateinit var ctx : Context
    private var sleep : ImageView? = null
    var play : ImageButton? = null
    private var btnPlayPause : ImageButton? = null
    var pause : ImageButton? = null
    var forward : ImageButton? = null
    private var txtDuration : TextView? = null
    var reverse : ImageButton? = null
    private var loader : ProgressBar? = null
    var exoProgress : CustomTimeBar? = null
    private var sleepTimeDialog: SleepTimeDialog? = null
    private var mediaContent: ExoNotificationData? = null

    var next : ImageButton? = null
    var prev : ImageButton? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(
        context, attrs
    ) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    ) {
        init(context)
    }

    private fun init(context: Context) {
        ctx = context

        sleep = getView(R.id.sleep)
        play = getView(R.id.exo_play)
        pause = getView(R.id.exo_pause)
        loader = getView(R.id.loader)
        forward = getView(R.id.exo_ffwd)
        reverse = getView(R.id.exo_rew)
        exoProgress = getView(R.id.exo_progress)
        txtDuration = getView(R.id.txtDuration)
        next = getView(R.id.exo_next)
        prev = getView(R.id.custom_prev)

//        reverse?.visibility = View.GONE
        btnPlayPause = getView(R.id.exo_play_pause)

        if (!isInEditMode) {
            val radioUpdateVersion = PrefUtils.appConfig?.app_update_config?.android?.liveRadioUpdateVersionCode ?: 0

            getView<View>(R.id.preClickPlayPause)?.setOnClickListener {
//                btnPlayPause?.performClick()
                if (mediaContent?.isRadio == true && BuildConfig.VERSION_CODE < radioUpdateVersion){
                    EventBus.getDefault().post(EventMessage(null,Constants.FORCE_UPDATE))
                } else {
                    if (ctx.getPlayerActivity()?.isPreViewContentPlayed().isTrue()){
                        GlobalObservers.playerEventObserver.invokePreviewClickEvent("podcast_media_player")
                    } else {
                        <EMAIL> {
                            /* now in media3, inside the PlayerControlView, the play/pause button is a single button,
                             so we need to check the content description to determine if it's play or pause
                             and then perform the appropriate action */
                            if (player?.isPlaying == false) {
//                                play?.visibility = GONE
//                                pause?.visibility = VISIBLE
                                ContentPlayRequest.Builder()
                                    .playWhenReady(ctx, true)
                            } else {
//                                pause?.visibility = GONE
//                                play?.visibility = VISIBLE
                                ContentPlayRequest.Builder()
                                    .playWhenReady(ctx, false)
                            }
                        }
                    }
                }
            }


            exoProgress?.addListener(object : TimeBar.OnScrubListener {
                override fun onScrubStart(timeBar: TimeBar, position: Long) {
                    // Scrubbing started
                    if (mediaContent?.hasPurchased.isFalse() && position > mediaContent?.previewTime.secondsToMs()){
                        exoProgress?.setPosition(mediaContent?.previewTime.secondsToMs())
                        ctx.getPlayerActivity()?.seekPlayer(mediaContent?.previewTime.secondsToMs())
                    }
                }

                override fun onScrubMove(timeBar: TimeBar, position: Long) {
                    // Scrubbing in progress, position is the current scrub position
                    if (mediaContent?.hasPurchased.isFalse() && position > mediaContent?.previewTime.secondsToMs()){
                        exoProgress?.setPosition(mediaContent?.previewTime.secondsToMs())
                    }
                }

                override fun onScrubStop(timeBar: TimeBar, position: Long, canceled: Boolean) {
                    // Scrubbing stopped, canceled indicates if the scrubbing was canceled
                    if (mediaContent?.hasPurchased.isFalse() && position > mediaContent?.previewTime.secondsToMs()){
                        exoProgress?.setPosition(mediaContent?.previewTime.secondsToMs())
                        ctx.getPlayerActivity()?.seekPlayer(mediaContent?.previewTime.secondsToMs())
                    }
                }
            })

        }

        forward?.setOnClickListener {
            ctx.getPlayerActivity()?.seekMedia(true)
        }

        reverse?.setOnClickListener {
            ctx.getPlayerActivity()?.seekMedia(false)
        }

        sleep?.setOnClickListener {
            ctx.fetchActivity()?.supportFragmentManager?.let { manager ->
                sleepTimeDialog = SleepTimeDialog.newInstance(sleepTime, this, mediaContent?.entitySubType)
                sleepTimeDialog?.show(manager, "Sleep")
            }
        }

        prev?.isEnabled = false  // Disable the click function
        prev?.isClickable = false
        prev?.alpha = 0.4f
        prev?.setOnClickListener {
            player?.seekToPrevious()
        }
    }

    fun setTotalDuration(duration:Long,totalDuration:String){
        txtDuration?.text = totalDuration
        exoProgress?.setTotalDuration(duration)
    }

    fun updateSleep(clickedSleepTimer: Int) {
        sleepTime = clickedSleepTimer
        if (sleepTime == SLEEP_TIME_0) {
            sleep?.setImageResource(R.drawable.ic_sleep)
        } else {
            sleep?.setImageResource(R.drawable.ic_sleep_timer_activated)
        }
    }

    override fun onSleepClicked(sleepTime: Int) {
        updateSleep(sleepTime)
        handleSleepTimeChanged(sleepTime)
    }

    fun handleSleepTimeChanged(sleepTime: Int) {
        val bundle = Bundle()
        bundle.putInt("timer",sleepTime)
        if (CastManager.isCastConnected){
            bundle.putAnalyticsKey("castPlatform","chromecast")
        }
        AnalyticsUtil.firebaseAnalytics.logEvent("set_sleep_timer",bundle)

        EventBus.getDefault().post(EventMessage(sleepTime, Constants.PLAYER_SLEEP))

        if (sleepTime != SLEEP_TIME_0) {
            when {
                sleepTime == SleepTimeDialog.END_OF_TRACK -> {
                    Utils.showSnackBar(ctx.unwrap(), ctx.getString(R.string.sleep_time_end_of_track))
                }
                sleepTime >= 60 -> {
                    if (sleepTime >= 120) {
                        Utils.showSnackBar(ctx.unwrap(), ctx.getString(R.string.sleep_time_msg_hour, 2))
                    } else {
                        Utils.showSnackBar(
                            ctx.unwrap(),
                            ctx.getString(R.string.sleep_time_msg_hour, 1)
                        )
                    }
                }
                else -> {
                    Utils.showSnackBar(ctx.unwrap(), ctx.getString(R.string.sleep_time_msg_min, sleepTime))
                }
            }
        }
    }

    fun handlePlayerUIEvents(playerEvent: PlayerEvent) {
        if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER) {
            loader?.visibility = View.VISIBLE
        } else if (playerEvent.event == PlayerEvent.END_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
            loader?.visibility = View.GONE
        }
    }

    fun onPlayBackSpeedClicked(
        speed: Float,
        onPlaybackSpeedChanged: (color: Int, text: String) -> Unit
    ) {
        val playbackSpeedColor: Int = ContextCompat.getColor(ctx, R.color.white)
        val text =
            if (speed == 1.0f || speed == 2.0f) speed.toInt().toString()
            else speed.toString().replace(".", ",")

        val bundle = Bundle()
        bundle.putFloat("speed", speed)
        AnalyticsUtil.firebaseAnalytics.logEvent("speed_adjusted", bundle)

        onPlaybackSpeedChanged(playbackSpeedColor, ctx.getString(R.string.playback_speed, text))
        player?.playbackParameters = PlaybackParameters(speed)
    }

    fun setMediaContent(mediaContent: ExoNotificationData?) {
        this.mediaContent = mediaContent
    }

    companion object {
        const val PLAY = "Play"
    }
}