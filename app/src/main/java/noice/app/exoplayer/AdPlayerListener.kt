package noice.app.exoplayer

import com.google.ads.interactivemedia.v3.api.player.AdMediaInfo
import com.google.ads.interactivemedia.v3.api.player.VideoAdPlayer
import com.google.ads.interactivemedia.v3.api.player.VideoProgressUpdate

interface AdPlayerListener : VideoAdPlayer.VideoAdPlayerCallback {
    override fun onContentComplete() {

    }

    override fun onAdProgress(p0: AdMediaInfo, p1: VideoProgressUpdate) {

    }

    override fun onBuffering(p0: AdMediaInfo) {
        
    }

    override fun onEnded(p0: AdMediaInfo) {
        
    }

    override fun onError(p0: AdMediaInfo) {
        
    }

    override fun onLoaded(p0: AdMediaInfo) {
        
    }

    override fun onPause(p0: AdMediaInfo) {
        
    }

    override fun onPlay(p0: AdMediaInfo) {
        
    }

    override fun onResume(p0: AdMediaInfo) {
        
    }

    override fun onVolumeChanged(p0: AdMediaInfo, p1: Int) {
        
    }
}