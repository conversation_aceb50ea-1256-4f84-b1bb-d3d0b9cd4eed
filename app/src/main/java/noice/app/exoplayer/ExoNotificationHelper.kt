package noice.app.exoplayer

import android.app.Notification
import android.app.PendingIntent
import android.content.Context
import androidx.annotation.DrawableRes
import androidx.annotation.OptIn
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.media3.common.C
import androidx.media3.exoplayer.offline.Download
import noice.app.R
import noice.app.modules.indexpages.model.OpenIndexEvent


@OptIn(androidx.media3.common.util.UnstableApi::class)
class ExoNotificationHelper(context: Context, channelId: String) {

    private val notificationBuilder = NotificationCompat.Builder(context.applicationContext, channelId)

    fun buildProgressNotification(
        context: Context,
        @DrawableRes smallIcon: Int?,
        data: DownloadNotificationData?,
        downloads: List<Download>
    ): Notification {

        var totalPercentage = 0f
        var downloadTaskCount = 0
        var allDownloadPercentagesUnknown = true
        var haveDownloadedBytes = false
        var haveDownloadTasks = false
        for (i in downloads.indices) {
            val download = downloads[i]
            if (download.state == Download.STATE_REMOVING) {
                continue
            }
            if (download.state != Download.STATE_RESTARTING
                && download.state != Download.STATE_DOWNLOADING
            ) {
                continue
            }
            haveDownloadTasks = true
            val downloadPercentage = download.percentDownloaded
            if (downloadPercentage != C.PERCENTAGE_UNSET.toFloat()) {
                allDownloadPercentagesUnknown = false
                totalPercentage += downloadPercentage
            }
            haveDownloadedBytes = haveDownloadedBytes or (download.bytesDownloaded > 0)
            downloadTaskCount++
        }
        var progress = 0
        var indeterminate = true
        if (haveDownloadTasks) {
            progress = (totalPercentage / downloadTaskCount).toInt()
            indeterminate = allDownloadPercentagesUnknown && haveDownloadedBytes
        }

        //val pauseIntent = ExoplayerUtils.getPendingIntent(NotificationBroadcast.PAUSE_DOWNLOAD, true, data?.contentId)
        val cancelIntent = ExoplayerUtils.getPendingIntent(NotificationBroadcast.CANCEL_DOWNLOAD, true, data?.contentId)

        val actions = arrayListOf(
            //DownloadAction(context.getString(R.string.pause), pauseIntent),
            DownloadAction(context.getString(R.string.cancel_ind).uppercase(), cancelIntent)
        )

        return buildNotification(
            context,
            smallIcon,
            ExoplayerUtils.getPendingIntent(OpenIndexEvent.OPEN_CONTENT_PAGE, false, data?.contentId),
            data?.catalogTitle ?: context.getString(R.string.downloading),
            data?.contentTitle,
            100,
            progress,
            indeterminate,  /* ongoing= */
            ongoing = true,
            showWhen = false,
            actions
        )
    }

    fun buildDownloadCompletedNotification(
        context: Context,
        @DrawableRes smallIcon: Int?,
        data: DownloadNotificationData?,
        notificationId: Int
    ): Notification {
        val playIntent = ExoplayerUtils.getPendingIntent(NotificationBroadcast.PLAY_DOWNLOAD, true, data?.contentId, notificationId)

        val actions = arrayListOf(
            DownloadAction(context.getString(R.string.play), playIntent)
        )
        return buildEndStateNotification(context, smallIcon, ExoplayerUtils.getPendingIntent(BasePlayerActivity.OPEN_DOWNLOADS, false, data?.contentId, notificationId),
            data?.catalogTitle,
            data?.contentTitle, actions)
    }

    fun buildDownloadFailedNotification(
        context: Context,
        @DrawableRes smallIcon: Int,
        data: DownloadNotificationData?,
        notificationId: Int
    ): Notification {
        val retryIntent = ExoplayerUtils.getPendingIntent(NotificationBroadcast.RETRY_DOWNLOAD, true, data?.contentId, notificationId)

        val actions = arrayListOf(
            DownloadAction(context.getString(R.string.retry_ind), retryIntent)
        )
        return buildEndStateNotification(context, smallIcon, ExoplayerUtils.getPendingIntent(OpenIndexEvent.OPEN_CONTENT_PAGE, false, data?.contentId, notificationId),
            data?.catalogTitle,
            data?.contentTitle, actions)
    }

    private fun buildEndStateNotification(
        context: Context,
        @DrawableRes smallIcon: Int?,
        contentIntent: PendingIntent?,
        message: String?,
        title: String?,
        actions: ArrayList<DownloadAction>
    ): Notification {
        return buildNotification(
            context,
            smallIcon,
            contentIntent,
            message,
            title,
            0,  /* maxProgress= */
            0,/* currentProgress= */
            indeterminateProgress = false,
            ongoing = false,
            showWhen = true,
            actions = actions
        )
    }

    private fun buildNotification(
        context: Context,
        @DrawableRes smallIcon: Int?,
        contentIntent: PendingIntent?,
        title: String?,
        message: String?,
        maxProgress: Int,
        currentProgress: Int,
        indeterminateProgress: Boolean,
        ongoing: Boolean,
        showWhen: Boolean,
        actions: ArrayList<DownloadAction>
    ): Notification {

        notificationBuilder.clearActions()
        actions.forEach {
            notificationBuilder.addAction(R.drawable.ic_download, it.name, it.pendingIntent)
        }

        if (smallIcon != null) {
            notificationBuilder.setSmallIcon(smallIcon)
        } else {
            notificationBuilder.setSmallIcon(R.drawable.ic_noice_logo_notification)
        }
        notificationBuilder.color = ContextCompat.getColor(context, R.color.dull_yellow)
        notificationBuilder.setContentTitle(title)
        notificationBuilder.setContentIntent(contentIntent)
        notificationBuilder.setStyle(
            if (message == null) null else NotificationCompat.BigTextStyle().bigText(message)
        )
        notificationBuilder.setProgress(maxProgress, currentProgress, indeterminateProgress)
        notificationBuilder.setOngoing(ongoing)
        notificationBuilder.setShowWhen(showWhen)
        notificationBuilder.setAutoCancel(true)

        notificationBuilder.build().apply {
            flags = flags or Notification.FLAG_AUTO_CANCEL
            return this
        }
    }
}