package noice.app.exoplayer

import androidx.media3.common.C
import androidx.media3.common.ParserException
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSourceException
import androidx.media3.datasource.HttpDataSource
import androidx.media3.datasource.HttpDataSource.CleartextNotPermittedException
import androidx.media3.exoplayer.source.UnrecognizedInputFormatException
import androidx.media3.exoplayer.upstream.DefaultLoadErrorHandlingPolicy
import androidx.media3.exoplayer.upstream.LoadErrorHandlingPolicy.LoadErrorInfo
import androidx.media3.exoplayer.upstream.Loader.UnexpectedLoaderException
import noice.app.BaseApplication
import noice.app.player.models.PlayerEvent
import noice.app.utils.NetworkUtils
import java.io.FileNotFoundException
import java.net.UnknownHostException

@UnstableApi
class CustomPolicy : DefaultLoadErrorHandlingPolicy() {

    override fun getRetryDelayMsFor(loadErrorInfo: LoadErrorInfo): Long {
        return if (!NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())) {
            PlayerEvent(PlayerEvent.PLAYER_ERROR_NO_INTERNET).sendPlayerEvent()
            3000
        } else if (!shouldNotRetry(loadErrorInfo)) {
            3000
        } else {
            C.TIME_UNSET
        }
    }

    override fun getMinimumLoadableRetryCount(dataType: Int): Int {
        return 200
    }

    private fun shouldNotRetry(loadErrorInfo: LoadErrorInfo) =
        loadErrorInfo.exception is FileNotFoundException ||
        loadErrorInfo.exception.cause is FileNotFoundException ||
        (loadErrorInfo.exception is HttpDataSource.InvalidResponseCodeException &&
                (loadErrorInfo.exception as? HttpDataSource.InvalidResponseCodeException)?.responseCode == 404) ||
        loadErrorInfo.exception.cause is UnknownHostException ||
        loadErrorInfo.exception is ParserException ||
        loadErrorInfo.exception is CleartextNotPermittedException ||
        loadErrorInfo.exception is UnexpectedLoaderException ||
        loadErrorInfo.exception is UnrecognizedInputFormatException ||
        DataSourceException.isCausedByPositionOutOfRange(loadErrorInfo.exception)
}
