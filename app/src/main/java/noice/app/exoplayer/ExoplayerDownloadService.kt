package noice.app.exoplayer

import android.app.Notification
import android.content.Context
import android.content.Intent
import androidx.annotation.OptIn
import androidx.media3.common.util.NotificationUtil
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadService
import androidx.media3.exoplayer.scheduler.PlatformScheduler
import com.google.gson.Gson
import noice.app.BaseApplication
import noice.app.R
import noice.app.exoplayer.ExoplayerUtils.DOWNLOAD_NOTIFICATION_CHANNEL_ID

/** A service for downloading media.  */
@UnstableApi
class ExoplayerDownloadService : DownloadService(
    FOREGROUND_NOTIFICATION_ID,
    DEFAULT_FOREGROUND_NOTIFICATION_UPDATE_INTERVAL,
    DOWNLOAD_NOTIFICATION_CHANNEL_ID,
    R.string.exo_download_notification_channel_name,
    0
) {
    override fun getDownloadManager(): DownloadManager {
        // This will only happen once, because getDownloadManager is guaranteed to be called only once
        // in the life cycle of the process.
        val downloadManager: DownloadManager = (application as BaseApplication).getDownloadManager()
        val downloadNotificationHelper = ExoplayerUtils.getDownloadNotificationHelper( /* context= */this)
        downloadManager.addListener(
            TerminalStateNotificationHelper(
                this, downloadNotificationHelper, FOREGROUND_NOTIFICATION_ID + 1
            )
        )
        return downloadManager
    }

    override fun getScheduler(): PlatformScheduler {
        return PlatformScheduler(this, JOB_ID)
    }

    override fun getForegroundNotification(
        downloads: MutableList<Download>,
        notMetRequirements: Int
    ): Notification {
        BaseApplication.application.getDownloadTracker().onProgressChanged(downloads)

        val data = getDownloadData(downloads)

        return ExoplayerUtils.getDownloadNotificationHelper( /* context= */this)
            .buildProgressNotification(
                this,
                null,
                data,
                downloads
            )
    }

    private fun getDownloadData(downloads: List<Download>) : DownloadNotificationData? {
        val download = downloads.find {
            it.state == Download.STATE_DOWNLOADING
        }
        return if (download?.request?.data != null) {
            val strData = Util.fromUtf8Bytes(download.request.data)
            Gson().fromJson(strData, DownloadNotificationData::class.java)
        } else {
            null
        }
    }

    /**
     * Creates and displays notifications for downloads when they complete or fail.
     *
     *
     * This helper will outlive the lifespan of a single instance of [ExoplayerDownloadService].
     * It is static to avoid leaking the first [ExoplayerDownloadService] instance.
     */
    private inner class TerminalStateNotificationHelper(
        context: Context,
        private val notificationHelper: ExoNotificationHelper, firstNotificationId: Int
    ) : DownloadManager.Listener {
        private val context = context.applicationContext
        private var nextNotificationId: Int = firstNotificationId
        override fun onDownloadChanged(
            downloadManager: DownloadManager, download: Download, finalException: Exception?
        ) {
            val data = ExoplayerUtils.getDownloadData(download)

            val notificationId = nextNotificationId++

            val notification = when (download.state) {
                Download.STATE_COMPLETED -> {
                    notificationHelper.buildDownloadCompletedNotification(
                        this@ExoplayerDownloadService,
                        null,
                        data,
                        notificationId
                    )
                }
                Download.STATE_FAILED -> {
                    notificationHelper.buildDownloadFailedNotification(
                        this@ExoplayerDownloadService,
                        R.drawable.ic_download_failed,
                        data,
                        notificationId
                    )
                }
                else -> {
                    return
                }
            }
            NotificationUtil.setNotification(context, notificationId, notification)
        }
    }

    override fun onTaskRemoved(rootIntent: Intent) {
        ExoplayerUtils.stopAllDownloads()
        stopSelf()
    }

    companion object {
        private const val JOB_ID = 1
        private const val FOREGROUND_NOTIFICATION_ID = 1
    }
}