package noice.app.exoplayer

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import noice.app.model.ExoNotificationData
import noice.app.model.LoginDialogData
import noice.app.model.ads.AdsResponse
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.repository.ChannelPodcastApiRepository
import noice.app.modules.profile.repository.ProfileRepository
import noice.app.views.CustomAdView
import javax.inject.Inject

@HiltViewModel
class BasePlayerViewModel @Inject constructor(
    private val dashBoardRepository: DashboardApiRepository,
    private val profileRepository: ProfileRepository,
    private val channelRepository: ChannelPodcastApiRepository
) : ViewModel() {

    private val catalogRepository = ChannelPodcastApiRepository()

    private var _adResponseSharedFlow =
        MutableSharedFlow<Pair<AdsResponse?, CustomAdView?>?>(replay = 1)
    val adResponseFlow = _adResponseSharedFlow

    fun getEpisodeDetails(episodeId: String) = catalogRepository.getEpisodeDetails(episodeId)

    fun getVideoQualitiesFor(contentId: String) = channelRepository.getVideoQualitiesFor(contentId)

    fun getUserDetailsByUserId(
        userId: String,
        map: HashMap<String, Any> = HashMap(),
        cachingConstant: String?
    ) = profileRepository.getUserDetailsByUserId(userId, map, cachingConstant)

    fun getCommunityDetail(map: HashMap<String, String>) =
        dashBoardRepository.getCommunityDetail(map)

    //UI Events
    private val eventChannel = Channel<Event>(Channel.BUFFERED)
    val eventFlow = eventChannel.receiveAsFlow()

    private val _isLikeVisibleState = MutableStateFlow(false)
    val isLikeVisibleState = _isLikeVisibleState.asStateFlow()

    private val _isContentLiked = MutableStateFlow(false)
    val isContentLiked = _isContentLiked.asStateFlow()

    private val _isUnlockContentBannerVisible = MutableStateFlow(false)
    val isUnlockContentBannerVisible = _isUnlockContentBannerVisible.asStateFlow()

    sealed class Event {

        object FetchRadios : Event()
        object RefreshMiniPlayer : Event()
        data class ResetMediaData(val exoData: ExoNotificationData?) : Event()
        class HandleNotLoggedIn(val loginDialogData: LoginDialogData? = null) : Event()
        class LoaderEvent(val show: Boolean) : Event()
    }

    fun sendEventToUI(event: Event) {
        CoroutineScope(Dispatchers.Main).launch {
            eventChannel.send(event)
        }
    }

    fun performAction(mediaAction: MediaAction) = dashBoardRepository.performAction(mediaAction)

    fun sendDisplayAdResponseEvent(adsResponseAndAdView: Pair<AdsResponse?, CustomAdView?>?) {
        CoroutineScope(Dispatchers.Main).launch {
            _adResponseSharedFlow.emit(adsResponseAndAdView)
        }
    }

    fun isLikeIconVisible(isVisible: Boolean) = _isLikeVisibleState.update { isVisible }
    fun isContentLiked(isLiked: Boolean) = _isContentLiked.update { isLiked }
    fun isUnlockContentBannerVisible(isVisible: Boolean) =
        _isUnlockContentBannerVisible.update { isVisible }
}