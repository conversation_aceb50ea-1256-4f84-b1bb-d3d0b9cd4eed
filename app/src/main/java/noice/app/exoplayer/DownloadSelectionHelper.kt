package noice.app.exoplayer

import androidx.annotation.OptIn
import androidx.media3.common.C
import androidx.media3.common.Format
import androidx.media3.common.Tracks
import androidx.media3.common.TrackGroup
import androidx.media3.common.TrackSelectionOverride
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.ui.TrackSelectionView
import com.google.common.collect.ImmutableList
import noice.app.enums.DownloadQuality
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.checkIndex

@OptIn(UnstableApi::class)
class DownloadSelectionHelper(
    trackGroups: ImmutableList<Tracks.Group>,
    private val allowMultipleOverrides: Boolean,
    private val trackSelectorParameters: DefaultTrackSelector.Parameters,
    private val listener: TrackSelectionDialog.TrackSelectionListener
) {
    private val overrides = mutableMapOf<TrackGroup, TrackSelectionOverride>()
    private val trackInfos = arrayListOf<TrackInfo>()

    init {
        overrides.clear()
        TrackSelectionView.filterOverrides(trackSelectorParameters.overrides, trackGroups, allowMultipleOverrides).also { overridesMap ->
            overrides.putAll(overridesMap)
        }

        trackGroups.forEach { trackGroup ->
            if (trackGroup.type == C.TRACK_TYPE_AUDIO) {
                for (trackIndex in 0 until trackGroup.length) {
                    if (trackGroup.getTrackFormat(trackIndex).bitrate > 0) {
                        trackInfos.add(TrackInfo(trackGroup, trackIndex))
                    }
                }
            }
        }

        trackInfos.sortBy { info ->
            info.bitrate
        }

        selectTrackBasedOnUserSettings()
    }

    private fun selectTrackBasedOnUserSettings() {
        when (PrefUtils.downLoadQuality) {
            DownloadQuality.LOW.value -> {
                if (trackInfos.isNotEmpty()) {
                    onTrackSelected(trackInfos[0])
                }
            }
            DownloadQuality.MEDIUM.value -> {
                if (trackInfos.checkIndex(1)) {
                    onTrackSelected(trackInfos[1])
                } else if (trackInfos.isNotEmpty()) {
                    onTrackSelected(trackInfos[0])
                }
            }
            DownloadQuality.HIGH.value -> {
                if (trackInfos.checkIndex(2)) {
                    onTrackSelected(trackInfos[2])
                } else if (trackInfos.checkIndex(1)) {
                    onTrackSelected(trackInfos[1])
                } else if (trackInfos.isNotEmpty()) {
                    onTrackSelected(trackInfos[0])
                }
            }
        }
    }

    private fun onTrackSelected(trackInfo: TrackInfo) {
        val mediaTrackGroup = trackInfo.trackGroup.mediaTrackGroup
        val trackIndex = trackInfo.trackIndex

        if (!allowMultipleOverrides && overrides.isNotEmpty()) {
            // Removed other overrides if we don't allow multiple overrides.
            overrides.clear()
        }
        overrides[mediaTrackGroup] =
            TrackSelectionOverride(mediaTrackGroup, ImmutableList.of(trackIndex))

        val builder =
            trackSelectorParameters.buildUpon()
        for (i in TrackSelectionDialog.SUPPORTED_TRACK_TYPES.indices) {
            val trackType =
                TrackSelectionDialog.SUPPORTED_TRACK_TYPES[i]
            builder.setTrackTypeDisabled(
                trackType,
                false
            )
            builder.clearOverridesOfType(trackType)
            val overrides = getOverrides(trackType)
            overrides.values.forEach { override ->
                builder.addOverride(override)
            }
        }

        listener.onTracksSelected(builder.build())
    }

    private fun getOverrides(trackType: Int): Map<TrackGroup, TrackSelectionOverride> {
        return if (trackType == C.TRACK_TYPE_AUDIO) {
            overrides
        } else {
            emptyMap()
        }
    }

    private class TrackInfo(val trackGroup: Tracks.Group, val trackIndex: Int) {
        val format: Format
            get() = trackGroup.getTrackFormat(trackIndex)

        val bitrate: Int
            get() = format.bitrate
    }
}