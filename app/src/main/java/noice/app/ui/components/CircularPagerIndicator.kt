package noice.app.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import noice.app.ui.theme.Dimens
import noice.app.ui.theme.Neutral100

@Composable
fun CircularPagerIndicator(
    modifier: Modifier = Modifier,
    currentPage: Int,
    pageCount: Int,
    spacedBy: Dp = Dimens.dp4,
    indicatorSize: Dp = Dimens.dp8,
    activeColor: Color = Neutral100,
    inActiveColor: Color = Neutral100.copy(alpha = 0.3f),
    animationDuration: Int = 500
) {
    Box(
        modifier = modifier
    ) {
        LazyRow(
            modifier = Modifier
                .wrapContentHeight()
                .wrapContentWidth()
                .align(Alignment.Center),
            horizontalArrangement = Arrangement.spacedBy(spacedBy)
        ) {
            items(pageCount) { index ->
                CircularPagerIndicatorItem(
                    isActive = index == currentPage,
                    indicatorSize = indicatorSize,
                    activeColor = activeColor,
                    inActiveColor = inActiveColor,
                    animationDuration = animationDuration
                )
            }
        }
    }
}


@Composable
private fun CircularPagerIndicatorItem(
    modifier: Modifier = Modifier,
    isActive: Boolean,
    indicatorSize: Dp,
    activeColor: Color,
    inActiveColor: Color,
    animationDuration: Int
) {
    val animatedColor by animateColorAsState(
        targetValue = if (isActive) activeColor else inActiveColor,
        animationSpec = tween(durationMillis = animationDuration)

    )

    Box(
        modifier = modifier
            .size(indicatorSize)
            .clip(CircleShape)
            .background(color = animatedColor)
    )
}


@Preview
@Composable
private fun PreviewCircularIndicator() {
    CircularPagerIndicator(
        currentPage = 3,
        pageCount = 5
    )
}