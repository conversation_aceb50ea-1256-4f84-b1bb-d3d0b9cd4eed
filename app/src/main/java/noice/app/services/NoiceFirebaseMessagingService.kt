package noice.app.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.core.app.NotificationCompat
import com.appsflyer.AppsFlyerLib
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.moengage.firebase.MoEFireBaseHelper
import com.moengage.pushbase.MoEPushHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import noice.app.BaseApplication
import noice.app.R
import noice.app.data.DataController
import noice.app.exoplayer.BasePlayerActivity
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.models.OnBoardingComplete
import noice.app.rest.apiinterfaces.UserApiInterface
import noice.app.utils.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class NoiceFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "NOICE_NOTIFICATIONS"
        private const val NOTIFICATION_CHANNEL_DESC = "NOICE_BUSINESS_NOTIFICATIONS"
    }

    private var apiJob: Job? = null
    private var tokenJob: Job? = null
    private var notificationId = 0

    @Inject
    lateinit var userApiInterface: UserApiInterface

    override fun onNewToken(p0: String) {
        super.onNewToken(p0)
        // Sending new token to AppsFlyer
        AppsFlyerLib.getInstance().updateServerUninstallToken(applicationContext, p0)
        MoEFireBaseHelper.getInstance().passPushToken(applicationContext, p0)

        PrefUtils.fcmToken = p0

        if (PrefUtils.isLoggedIn) {
            apiJob = BaseApplication.doServerCall({
                userApiInterface.updateOnBoardingFlag(
                    PrefUtils.token,
                    OnBoardingComplete(fcmToken = p0)
                )
            }, null)
        }

        tokenJob = BaseApplication.doServerCall({
            val map = HashMap<String, RequestBody>()
            map["fcmToken"] = p0.toRequestBody("multipart/form-data".toMediaTypeOrNull())
            userApiInterface.updateUserDetails(PrefUtils.token, null, map)
        }, null)

        BaseApplication.firebaseAnalytics.setUserProperty("fcmToken", p0)
    }

    override fun onMessageReceived(p0: RemoteMessage) {

        if (p0.data.containsKey("af-uinstall-tracking")) {
            return
        }

        super.onMessageReceived(p0)

        if (MoEPushHelper.getInstance().isFromMoEngagePlatform(p0.data)) {
            MoEFireBaseHelper.getInstance().passPushPayload(applicationContext, p0.data)
            return
        } else {
            val notification = p0.notification

            if (!p0.data["notificationId"].isNullOrEmpty()) {
                EventBus.getDefault().post(EventMessage(null, Constants.NOTIFICATION_RECEIVED))

                val notificationTrigger = if (p0.data["isManual"] == "true") {
                    "manual"
                } else {
                    "auto"
                }

                AnalyticsUtil.sendEvent("notification_received", Bundle().apply {
                    putString("notificationTrigger", notificationTrigger)
                    putString("notificationType", p0.data["type"])
                    putString("notificationId", p0.data["notificationId"])
                    putString("entityType", p0.data["entityType"])
                    putString("entityId", p0.data["entityId"])
                    putString("entityTitle", notification?.title)
                })
            }

            if (p0.data["entityType"] == Constants.ENTITY_TYPE_LIVE_STREAM &&
                DataController.isRoomActive &&
                DataController.currentRoomId == p0.data["entityId"]
            ) {
                return
            }

            val notificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            val pendingIntent = PendingIntent.getActivity(
                applicationContext,
                notificationId,
                getIntentForRedirection(p0.data),
                PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val notificationChannel = NotificationChannel(
                    NOTIFICATION_CHANNEL_ID,
                    NOTIFICATION_CHANNEL_DESC,
                    NotificationManager.IMPORTANCE_HIGH
                )
                notificationManager.createNotificationChannel(notificationChannel)
            }

            val futureTarget = GlideApp.with(this)
                .asBitmap()
                .load(notification?.imageUrl)
                .submit()

            val bitmap = try {
                futureTarget.get()
            } catch (e: Exception) {
                null
            }

            val builder = NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_noice_logo_notification)
                .setContentTitle(notification?.title)
                .setContentText(notification?.body)
                .setAutoCancel(true)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setContentIntent(pendingIntent).apply {
                    if (bitmap != null) {
                        setLargeIcon(bitmap)
                    }
                }


            notificationManager.notify(notificationId++, builder.build())

            if (notificationId > 99999) {
                notificationId = 0
            }
        }
    }

    private fun getIntentForRedirection(data: MutableMap<String, String>): Intent {
        return if (data.contains("entityType") && data.contains("entityId")) {
            Intent(this, ClassVariantProvider.of(HomeActivity::class.java)).apply {
                action = BasePlayerActivity.OPEN_INDEX_ACTION
                putExtra(
                    BasePlayerActivity.EVENT_ID, Utils.getIndexEventAction(
                        data["entityType"] ?: "",
                        data["entitySubType"] ?: ""
                    )
                )
                putExtra(BasePlayerActivity.ENTITY_ID, data["entityId"])
                putExtra(BasePlayerActivity.ENTITY_TYPE, data["entityType"])
                putExtra(BasePlayerActivity.NOTIFICATION_ID, data["notificationId"])

                if (!data["parentId"].isNullOrEmpty()) {
                    putExtra(BasePlayerActivity.PARENT_ID, data["parentId"])
                }

                putExtra(Constants.SOURCE, "Push Notification")
            }
        } else {
            Intent(this, ClassVariantProvider.of(HomeActivity::class.java)).apply {
                putExtra(Constants.SOURCE, "Push Notification")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        apiJob?.cancel()
        tokenJob?.cancel()
        apiJob = null
        tokenJob = null
    }
}