package noice.app.services.observer

/**
 * data class for detail information of the network trace,
 * obtained through the network interceptors
 */
data class InterceptorStatTrace(
    val url: String,
    val type: OkhttpSizeInterceptorType,
    val requestSize: Long = 0,
    val responseSize: Long = 0
)

/**
 * data class for precise information of all network traces,
 * used by [NetworkDataObserver]
 */
data class InterceptorStatSummary(
    var requestSize: Long = 0,
    var responseSize: Long = 0
)