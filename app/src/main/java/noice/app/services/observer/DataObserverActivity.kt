package noice.app.services.observer

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import noice.app.R
import noice.app.databinding.ActivityDataObserverBinding
import noice.app.utils.bindings.BaseAdapter
/**
 * this activity component can be used to show packet's listings
 */
class DataObserverActivity : AppCompatActivity() {
    /**
     * Binding object between activity and xml file, it contains all objects
     * of UI components used by this activity
     */
    private val viewModel: DataObserverViewModel by viewModels()
    /**
     * {@inheritDoc}
     *
     *
     * This will calls on every new initialization of this activity,
     * It can be used for any initializations or on start executions
     *
     * [savedInstanceState] to get data on activity state changed
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        DataBindingUtil.setContentView<ActivityDataObserverBinding>(
            this,
            R.layout.activity_data_observer
        ).apply {
            lifecycleOwner = this@DataObserverActivity
            <EMAIL> {
                viewModel = this
                onCreate()
            }
            /**
             * this will setup recycler view on the first load
             */
            recyclerView.apply {
                //sets recyclerview's adapter
                adapter = BaseAdapter<InterceptorStatTrace>(R.layout.item_data_observer)
            }
        }
    }
}