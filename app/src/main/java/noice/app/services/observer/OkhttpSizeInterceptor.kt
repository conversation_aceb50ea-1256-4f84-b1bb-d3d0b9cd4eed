package noice.app.services.observer

import okhttp3.Interceptor
import okhttp3.Response

/**
 * possible available types to consume this interceptor
 */
enum class OkhttpSizeInterceptorType {
    /**
     * this type represents, interceptor is actually used to monitor image specific
     * information (can be used with [GlideApp] or other image libraries like [<PERSON>])
     */
    MEDIA_IMAGE,

    /**
     * this type represents, interceptor is actually used to monitor api specific
     * information (can be used with [Retrofit] or other okhttp based libraries like [<PERSON>ley])
     */
    REST,

    /**
     *
     */
    CUSTOM
}

/**
 * okhttp specific interceptor to calculate the overall size of the request and response,
 * this interceptor later injects the information to the [NetworkDataObserver], to estimate
 * the final results
 *
 * @param type can be used to identified for what purpose the interceptor is actually used.
 */
class OkhttpSizeInterceptor(private val type: OkhttpSizeInterceptorType) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val requestSize = request.body?.contentLength() ?: 0
        val response: Response

        try {
            response = chain.proceed(request)
        } catch (e: Exception) {
            //http failure
            throw e
        }
        val responseSize = response.body?.contentLength() ?: 0
        NetworkDataObserver.insertHttpTrace(
            type,
            InterceptorStatTrace(request.url.toString(), type, requestSize, responseSize)
        )
        return response
    }
}