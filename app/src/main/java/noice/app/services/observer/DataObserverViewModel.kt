package noice.app.services.observer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
/**
 * view model for data observer activity
 */
class DataObserverViewModel : ViewModel() {

    /**
     * observable list of network traces received from [NetworkDataObserver]
     */
    private val _networkTraces =
        MutableLiveData<ArrayList<InterceptorStatTrace>>().apply {
            value = NetworkDataObserver.httpTraces
        }
    val networkTraces: LiveData<ArrayList<InterceptorStatTrace>>
        get() = _networkTraces

    /**
     * the simplest way to manage initialize view model's components
     * once the activity is loaded
     */
    fun onCreate() {
        NetworkDataObserver.listener = { _networkTraces.value = it }
    }

    /**
     * This method will be called when this ViewModel is no longer used and will be destroyed.
     */
    override fun onCleared() {
        super.onCleared()
        NetworkDataObserver.listener = null
    }
}