package noice.app.services.observer

import android.content.Context
import android.net.TrafficStats
import noice.app.utils.Utils.bytesToReadable
import java.lang.reflect.Array
import kotlin.collections.ArrayList

/**
 * this class uses OS's [TrafficStats] API to monitor data passed through the app
 */
class TrafficStat(
    context: Context
) {
    /**
     * process id inside which the app is running, it is equivalent to the uid of the applicationInfo object
     */
    private var processId = context.packageManager.getApplicationInfo(context.packageName, 0).uid

    /**
     * flag which checks whether the device supports traffic monitoring via [TrafficStats] API or not
     */
    private var isTrafficStatSupported: Boolean = false

    /**
     * last received bytes on internet (download)
     */
    private var rxBytes: Long = 0

    /**
     * last sent bytes on internet (upload)
     */
    private var txBytes: Long = 0

    /**
     * initial downloaded bytes by the app (from the time of device boot)
     */
    private var initialRxBytes: Long = 0

    /**
     * initial uploaded bytes by the app (from the time of device boot)
     */
    private var initialTxBytes: Long = 0

    /**
     * total downloaded bytes by the app (from the time of device boot)
     */
    private var totalRxBytes: Long = 0

    /**
     * total uploaded bytes by the app (from the time of device boot)
     */
    private var totalTxBytes: Long = 0


    init {
        val bytesUpDown = fetchBytesFromTraficApi(true)
        if (bytesUpDown.isNotEmpty()) {
            initialRxBytes = bytesUpDown[0]
            initialTxBytes = bytesUpDown[1]
        }
    }

    /**
     * this method performs calculations and estimates the download/upload bytes from [TrafficStats] API
     */
    fun update() {
        val bytesUpDown = fetchBytesFromTraficApi()
        if (bytesUpDown.isEmpty()) return

        val currentRxBytes = bytesUpDown[0]
        val currentTxBytes = bytesUpDown[1]

        //calculating bytes and caching them
        rxBytes = currentRxBytes - totalRxBytes
        txBytes = currentTxBytes - totalTxBytes

        totalRxBytes = currentRxBytes
        totalTxBytes = currentTxBytes
    }

    private fun fetchBytesFromTraficApi(initial: Boolean = false): ArrayList<Long> {
        //extracting the total download bytes
        val currentRxBytes = TrafficStats.getUidRxBytes(processId)

        //extracting the total upload bytes
        val currentTxBytes = TrafficStats.getUidTxBytes(processId)

        //checking whether device supports the API or not
        if (currentRxBytes == TrafficStats.UNSUPPORTED.toLong()) {
            print("TrafficStats is not supported in this device")
            return arrayListOf()
        }

        isTrafficStatSupported = true

        if (initial) return arrayListOf(currentRxBytes, currentTxBytes)
        return arrayListOf(currentRxBytes - initialRxBytes, currentTxBytes - initialTxBytes)
    }

    /**
     * this will return list of results, it calculates from the update function
     * @return list of results in human readable form
     */
    fun toStringList(): ArrayList<String> {
        if (!isTrafficStatSupported) return arrayListOf("TrafficStats is not supported in this device")
        return arrayListOf(
            "Last 5 secs: ${bytesToReadable(txBytes)} Up ↑  | ${
                bytesToReadable(rxBytes)
            } Down ↓",
            "Total: ${bytesToReadable(totalTxBytes)} Up ↑ | : ${
                bytesToReadable(
                    totalRxBytes
                )
            } Down ↓"
        )
    }

    /**
     * this will combine the information returned by [toStringList] method
     */
    override fun toString(): String = toStringList().toString()

}