package noice.app.services.observer

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import noice.app.R
import noice.app.utils.Utils.bytesToReadable
import java.util.*

/**
 * A singleton object to control the network consumption should be availability
 * It generally covers all primary information which are needed to observe data consumtion by the app
 */
object NetworkDataObserver {

    private lateinit var context: Context

    /**
     * listener to observe data transmission from other parts of the app
     */
    var listener: ((ArrayList<InterceptorStatTrace>) -> Unit)? = null


    /**
     * channel for the notification, please refer [createNotificationChannel] for more information
     */
    private const val CHANNEL_ID = "DataConsumption"

    /**
     * id of the notification which will be used to create/update/delete notification in tray
     */
    private const val NOTIFICATION_ID = 100

    /**
     * time interval which will be needed to update the network logs on notification
     * this will be configurable and can be control using [withInterval] method
     */
    private var interval: Long = 1000

    /**
     * System API: to control notification in front of user
     */
    private var notificationManager: NotificationManager? = null

    /**
     * this will manage the RX/TX bytes calculation.
     * Behind the scene, it uses [android.net.TrafficStats] API which is available after ANDROID-API 8
     */
    private var trafficStat: TrafficStat? = null

    /**
     * to schedule interval to update notification, the interval is control by [interval] property
     */
    private val timer by lazy { Timer() }

    /**
     * this will hold the precise information of overall traces,
     * received from various sources
     */
    private val okHttpSummaryHolder by lazy { mutableMapOf<OkhttpSizeInterceptorType, InterceptorStatSummary>() }

    /**
     * this will hold the detail information of each network traces,
     * received from various sources
     */
    private val okHttpStatTraceHolder by lazy { mutableMapOf<OkhttpSizeInterceptorType, ArrayList<InterceptorStatTrace>>() }

    private var customStatMessage: String? = null

    /**
     * task to perform once the [timer] reaches to its end position
     */
    private val timerTask by lazy {
        object : TimerTask() {
            override fun run() {
                trafficStat?.apply {
                    update()
                    val messages = httpTraceToStringList
                    messages.addAll(toStringList())
                    updateNotification(messages)
                }
            }
        }
    }

    /**
     * computed property to decorate http traces before
     * sending it to the external world
     */
    val httpTraces: ArrayList<InterceptorStatTrace>
        get() {
            val mutableList = arrayListOf<InterceptorStatTrace>()
            okHttpStatTraceHolder.values.forEach { mutableList.addAll(it) }
            return mutableList
        }

    /**
     * computed property to decorate http trace's messages before
     * sending it to the external world
     */
    val httpTraceToStringList: ArrayList<String>
        get() {
            val messages = arrayListOf<String>()

            okHttpSummaryHolder[OkhttpSizeInterceptorType.REST]?.let {
                messages.add(
                    "APIs: ${
                        bytesToReadable(it.responseSize)
                    } Down ↓"
                )
            }
            okHttpSummaryHolder[OkhttpSizeInterceptorType.MEDIA_IMAGE]?.let {
                messages.add(
                    "Images: ${
                        bytesToReadable(it.responseSize)
                    } Down ↓"
                )
            }

            okHttpSummaryHolder[OkhttpSizeInterceptorType.CUSTOM]?.let {
                messages.add(
                    String.format(
                        customStatMessage ?: "Player: %s Down ↓",
                        bytesToReadable(it.responseSize)
                    )
                )
            }
            return messages
        }

    /**
     * initializer for [context]
     * @return the instance of [NetworkDataObserver]
     */
    fun withContext(context: Context): NetworkDataObserver {
        NetworkDataObserver.context = context
        notificationManager = context.getSystemService(NotificationManager::class.java)
        return this
    }

    /**
     * initializer for [context]
     * @return the instance of [NetworkDataObserver]
     */
    fun withCustomMessage(message: String): NetworkDataObserver {
        customStatMessage = message
        return this
    }

    /**
     * initializer for [interval]
     * this can be used externally to control the duration of the interval to update the results
     *
     * @param [interval] to define the duration
     * @return the instance of [NetworkDataObserver]
     */
    private fun withInterval(interval: Long): NetworkDataObserver {
        NetworkDataObserver.interval = interval
        notificationManager = context.getSystemService(NotificationManager::class.java)
        return this
    }

    /**
     * this will start monitoring the network traffic,
     * it will performs observes transfer of bytes, calculate them and updates the notification
     */
    fun start() {
        if (!this::context.isInitialized) throw Exception("call withContext method before starting the lookup")
        trafficStat = TrafficStat(context)
        timer.scheduleAtFixedRate(timerTask, 0, interval)
    }

    /**
     * to stop monitoring the network traffic,
     * it will cancel all the schedule tasks and removed notification from the system tray
     */
    fun stop() {
        notificationManager?.cancel(NOTIFICATION_ID)
        timerTask.cancel()
        timer.cancel()
    }

    /**
     * this method can be used to insert http traces within the observer holders,
     * the source of the traffic can be anything (like Http Client, TrafficStat, etc)
     */
    fun insertHttpTrace(
        okhttpSizeInterceptorType: OkhttpSizeInterceptorType,
        interceptorStatDetail: InterceptorStatTrace
    ) {
        val interceptorStatSummary =
            okHttpSummaryHolder[okhttpSizeInterceptorType] ?: InterceptorStatSummary()

        val (url, type, requestSize, responseSize) = interceptorStatDetail

        Log.d(NetworkDataObserver::class.java.name, "$type , $url")

        interceptorStatSummary.requestSize += requestSize
        interceptorStatSummary.responseSize += responseSize

        okHttpSummaryHolder[okhttpSizeInterceptorType] = interceptorStatSummary

        val okHttpStatTraceList = okHttpStatTraceHolder[okhttpSizeInterceptorType] ?: arrayListOf()
        okHttpStatTraceList.add(interceptorStatDetail)

        okHttpStatTraceHolder[okhttpSizeInterceptorType] = okHttpStatTraceList
    }

    fun insertCustomStats(
        okhttpSizeInterceptorType: OkhttpSizeInterceptorType,
        statSummary: InterceptorStatSummary
    ) {
        val interceptorStatSummary =
            okHttpSummaryHolder[okhttpSizeInterceptorType] ?: InterceptorStatSummary()

        val (requestSize, responseSize) = statSummary

        interceptorStatSummary.requestSize += requestSize
        interceptorStatSummary.responseSize += responseSize

        okHttpSummaryHolder[okhttpSizeInterceptorType] = interceptorStatSummary
    }

    /**
     * this method will specifically updates the notification and it's information
     * within the system tray
     *
     * @param [messages] list of messages which needs to be shown within the notification
     */
    private fun updateNotification(
        messages: ArrayList<String>
    ) {
        createNotificationChannel()

        val notificationIntent = Intent(context, DataObserverActivity::class.java)
        notificationIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)

        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            context,
            0, notificationIntent, PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentIntent(pendingIntent)
            .setLocalOnly(true)
            .setSmallIcon(R.drawable.ic_noice_home_logo)
            .setColor(ContextCompat.getColor(context, R.color.chucker_color_primary))
            .setContentTitle("Network Utilization")
            .setAutoCancel(true)

        val style = NotificationCompat.InboxStyle()
        messages.let {
            (it.size - 1 downTo 0).forEach { i ->
                val message = messages[i]
                if (i == 0) {
                    notification.setContentText(message)
                }
                style.addLine(message)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                notification.setSubText(it.size.toString())
            } else {
                notification.setNumber(it.size)
            }
            notification.setStyle(style)
        }
        notificationManager?.notify(NOTIFICATION_ID, notification.build())
    }


    /**
     * creates a low-priority channel for notification which will be used with network observer notification
     * @see [updateNotification] for more information
     */
    private fun createNotificationChannel() = notificationManager?.apply {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createNotificationChannel(
                NotificationChannel(
                    CHANNEL_ID, "Foreground Data Channel",
                    NotificationManager.IMPORTANCE_LOW
                )
            )
        }
    }
}
