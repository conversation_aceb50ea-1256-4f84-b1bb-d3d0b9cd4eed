package noice.app.views.homesegmentviews

import android.content.Context
import android.graphics.drawable.Animatable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat
import noice.app.R
import noice.app.data.DataController
import noice.app.enums.BadgeType
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.podcast.model.Content
import noice.app.ui.icon.Icons
import noice.app.utils.ClickHandler
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.SOURCE_EXCLUSIVE
import noice.app.utils.Constants.Companion.SOURCE_ORIGINAL
import noice.app.utils.Constants.Companion.SOURCE_SPECIAL
import noice.app.utils.ImageUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.setTitleWithVideoSpan
import noice.app.utils.visible

class RecentlyPlayedSegment : FrameLayout {

    private lateinit var ctx: Context
    lateinit var viewHolder: ViewHolder
    private var entityType:String? = ""
    private var pageSource:String?=null
    private var prevoiusScreen:String?=null
    private var extraData: ExtraData? = null

    constructor(context: Context,entityType:String?="",pageSource:String?="",prevoiusScreen:String) : super(context) {
        this.entityType = entityType
        this.pageSource = pageSource
        this.prevoiusScreen = prevoiusScreen
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    private fun init(context: Context) {
        ctx = context
        inflate(context, R.layout.recently_played_segment, this)
        viewHolder = ViewHolder(this)
    }

    fun setData(content : HomeContent) {

        if (content.imageMeta?.size300.isNullOrEmpty()) {
            content.imageMeta?.size300 = content.catalog?.imageMeta?.size300
        }

        if (Utils.isBookStyle(content.category)) {
            handleCoverImage(true, content.imageMeta?.size300?:"", ImageUtils.getOriginalImage(content))
        } else {
            handleCoverImage(false, content.imageMeta?.size300 ?: "", content.imageMeta?.original)
        }

        viewHolder.content = content
        viewHolder.title.setTitleWithVideoSpan(
            content.title,
            content.showVideo,
            R.drawable.ic_video_filled
        )
        viewHolder.subTitle.text = content.catalog?.title?:""
        viewHolder.title.setOnClickListener {
            ClickHandler.redirect(content.id.toString(), content.entityType.toString(), content.category.toString(), "Home_$prevoiusScreen", extraData)
        }

        if (content.duration != null) {
            if(content.meta?.timeElapsed != null && content.meta?.timeElapsed != 0L) {
                viewHolder.progressBar.progress =   Utils.remainingPercent(content.meta?.timeElapsed, content.duration)
            } else {
                viewHolder.progressBar.progress = 0
            }
        } else {
            viewHolder.progressBar.progress = 0
        }

        if (DataController.playerContentId == content.id && ctx.getPlayerActivity()?.isPlayWhenReady() == true){
            (viewHolder.equalizer.drawable as Animatable).start()
            viewHolder.playButtonLayout.visibility = View.GONE
            viewHolder.loader.visibility = View.GONE
            viewHolder.relativeEqualizer.visibility = View.VISIBLE
        } else {
            (viewHolder.equalizer.drawable as Animatable).stop()
            viewHolder.playButtonLayout.visibility = View.VISIBLE
            viewHolder.loader.visibility = View.GONE
            viewHolder.relativeEqualizer.visibility = View.GONE
        }

        when (content.badge) {
            BadgeType.EARLY_ACCESS.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.EarlyAccess)
            }
            BadgeType.PREMIUM.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.Premium)
            }
            BadgeType.VIP.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.Vip)
            }
            else -> {

            }
        }

        toggleLoader(content.showLoader)
    }

    fun setData(content : ExoNotificationData) {
        viewHolder.equalizer.visibility = View.GONE

        if (Utils.isBookStyle(content.entitySubType)){
            handleCoverImage(true, content.image ?: "",content.image)
        } else {
            handleCoverImage(false, content.image ?: "",content.image)
        }

        viewHolder.title.setTitleWithVideoSpan(
            content.title,
            content.showVideo,
            R.drawable.ic_video_filled
        )
        viewHolder.subTitle.text = content.catalogTitle

        if (content.duration!=null){
            if(content.timeElapsed != null && content.timeElapsed != 0L) {
                viewHolder.progressBar.progress =   Utils.remainingPercent(content.timeElapsed, content.duration)
            } else {
                viewHolder.progressBar.progress = 0
            }
        } else {
            viewHolder.progressBar.progress = 0
        }
        viewHolder.playButtonLayout.visibility = View.GONE
    }

    fun setData(content: Content) {

        if (content.imageMeta?.size300.isNullOrEmpty()) {
            content.imageMeta?.size300 = content.catalog?.imageMeta?.size300
        }

        if (Utils.isBookStyle(content.catalog?.type)) {
            handleCoverImage(true, content.imageMeta?.size300 ?: "", content.imageMeta?.size500)
        } else {
            handleCoverImage(false, content.imageMeta?.size300 ?: "", content.imageMeta?.size500)
        }

        viewHolder.title.setTitleWithVideoSpan(
            content.title,
            content.showVideo,
            R.drawable.ic_video_filled
        )
        viewHolder.title.setOnClickListener {
            ClickHandler.openScreenFromHomeInnerAdapter(content,entityType,pageSource,prevoiusScreen, extraData = extraData)
        }
        viewHolder.subTitle.text = if(!content.catalogTitle.isNullOrEmpty()) {
            content.catalogTitle
        } else if(!content.catalog?.title.isNullOrEmpty()) {
            content.catalog?.title
        } else {
            ""
        }

        if (content.duration!=null){
            if(content.meta?.timeElapsed != null && content.meta?.timeElapsed != 0L) {
                viewHolder.progressBar.progress = Utils.remainingPercent(content.meta?.timeElapsed, content.duration)
            } else {
                viewHolder.progressBar.progress = 0
            }
        } else {
            viewHolder.progressBar.progress = 0
        }

        if (DataController.playerContentId == content.id && ctx.getPlayerActivity()?.isPlayWhenReady() == true){
            (viewHolder.equalizer.drawable as Animatable).start()
            //viewHolder.equalizer.animateBars()
            viewHolder.playButtonLayout.visibility = View.GONE
            viewHolder.loader.visibility = View.GONE
            viewHolder.relativeEqualizer.visibility = View.VISIBLE
        }else{
            (viewHolder.equalizer.drawable as Animatable).stop()
            //viewHolder.equalizer.stopBars()
            viewHolder.playButtonLayout.visibility = View.VISIBLE
            viewHolder.loader.visibility = View.GONE
            viewHolder.relativeEqualizer.visibility = View.GONE
        }

        toggleLoader(content.showLoader)

        viewHolder.itemView.setOnClickListener {
            ClickHandler.openScreenFromHomeInnerAdapter(content,entityType,pageSource,prevoiusScreen, extraData = extraData)
        }

        when (content.badge) {
            BadgeType.EARLY_ACCESS.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.EarlyAccess)
            }
            BadgeType.PREMIUM.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.Premium)
            }
            BadgeType.VIP.badge -> {
                viewHolder.origExc.visible(true)
                viewHolder.origExc.setImageResource(Icons.Vip)
            }
            else -> {

            }
        }
    }

    private fun handleCoverImage(bookStyle : Boolean, imageUrl : String, originalUrl:String?) {
        if (bookStyle) {
            viewHolder.coverImage.visibility = View.GONE
            ImageUtils.showBlurImage(
                imageUrl,
                viewHolder.coverBackgroundImage,
                placeHolder = R.drawable.ic_thumb_square
            )
            ImageUtils.loadImageByUrl(viewHolder.coverImageAudioBook, imageUrl, placeHolder = R.drawable.ic_audiobook_default, originalUrl = originalUrl)
            viewHolder.audioBookLayout.visibility = View.VISIBLE
        } else {
            viewHolder.audioBookLayout.visibility = View.GONE
            ImageUtils.loadImageByUrl(viewHolder.coverImage, imageUrl, placeHolder = R.drawable.ic_thumb_square, originalUrl = originalUrl)
            viewHolder.coverImage.visibility = View.VISIBLE
        }
    }

    fun setWidth() {
        layoutParams = LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT)
        viewHolder.recentlyPlayedView.layoutParams = LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    private fun toggleLoader(showLoader : Boolean) {
        if (showLoader) {
            viewHolder.relativeEqualizer.visibility = View.GONE
            viewHolder.playButtonLayout.visibility = View.GONE
            viewHolder.loader.visibility = View.VISIBLE
        } else {
            viewHolder.loader.visibility = View.GONE
            if (viewHolder.relativeEqualizer.visibility == View.VISIBLE) {
                viewHolder.playButtonLayout.visibility = View.GONE
            } else {
                viewHolder.playButtonLayout.visibility = View.VISIBLE
                viewHolder.relativeEqualizer.visibility = View.GONE
            }
        }
    }

    /** A method for setting some extra information to handle on a screen when item is tapped. */
    fun setExtraData(extraData: ExtraData?) {
        this.extraData = extraData
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var content : HomeContent?=null
        val coverImage : ImageView = view.findViewById(R.id.coverImage)
        val playButton : ImageView = view.findViewById(R.id.playButton)
        val playButtonLayout : View = view.findViewById(R.id.playButtonLayout)
        val progressBar : ProgressBar = view.findViewById(R.id.progressBar)
        val title : TextView = view.findViewById(R.id.title)
        val subTitle : TextView = view.findViewById(R.id.subTitle)
        val recentlyPlayedView : LinearLayout = view.findViewById(R.id.rvQueue)
        val equalizer : ImageView = view.findViewById(R.id.equalizer)
        val loader : ProgressBar = view.findViewById(R.id.loader)
        val audioBookLayout : FrameLayout = view.findViewById(R.id.audioBookLayout)
        val coverBackgroundImage : ImageView = view.findViewById(R.id.coverBackgroundImage)
        val coverImageAudioBook : ImageView = view.findViewById(R.id.coverImageAudioBook)
        val origExc : ImageView = view.findViewById(R.id.origExc)
        val relativeEqualizer : FrameLayout = view.findViewById(R.id.relative_equalizer)

        init {
            val animatedVectorDrawableCompat =
                AnimatedVectorDrawableCompat.create(view.context, R.drawable.equalizer)

            animatedVectorDrawableCompat?.registerAnimationCallback(object :Animatable2Compat.AnimationCallback() {
                override fun onAnimationEnd(drawable: Drawable?) {
                    equalizer.post { animatedVectorDrawableCompat.start() }
                }
            })
            equalizer.setImageDrawable(animatedVectorDrawableCompat)
        }
    }
}