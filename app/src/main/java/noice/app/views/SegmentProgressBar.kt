package noice.app.views

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import noice.app.R

class SegmentProgressBar : View {

    private var progressWidth = 0F
    private var maxProgress = 0F

    private var cornerRadius = DEFAULT_CORNERS.toFloat()

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressRectF = RectF()
    private val path = Path()

    private var primaryProgressPercent = 0F
    private var secondaryProgressPercent = 0F
    private var thirdProgressPercent = 0F
    private var fourthProgressPercent = 0F


    private var bgProgressBar = 0

    private var primaryColor = 0
    private var secondaryColor = 0
    private var thirdColor = 0
    private var fourthColor = 0

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        val t = context.obtainStyledAttributes(attrs, R.styleable.SegmentProgressBar, 0, 0)

        bgProgressBar =
            t.getColor(R.styleable.SegmentProgressBar_backgroundColor, PROGRESS_BAR_BG_COLOR)
        primaryColor =
            t.getColor(R.styleable.SegmentProgressBar_primaryColor, DEFAULT_PRIMARY_COLOR)
        secondaryColor =
            t.getColor(R.styleable.SegmentProgressBar_secondaryColor, DEFAULT_SECONDARY_COLOR)
        thirdColor =
            t.getColor(R.styleable.SegmentProgressBar_thirdColor, DEFAULT_THIRD_COLOR)
        fourthColor =
            t.getColor(R.styleable.SegmentProgressBar_fourthColor, DEFAULT_FOURTH_COLOR)


        primaryProgressPercent =
            t.getFloat(R.styleable.SegmentProgressBar_primaryPercent, 0.0F)
        secondaryProgressPercent =
            t.getFloat(R.styleable.SegmentProgressBar_secondaryPercent, 0.0F)
        thirdProgressPercent =
            t.getFloat(R.styleable.SegmentProgressBar_thirdPercent, 0.0F)
        fourthProgressPercent =
            t.getFloat(R.styleable.SegmentProgressBar_fourthPercent, 0.0F)


        cornerRadius =
            t.getDimensionPixelSize(R.styleable.SegmentProgressBar_cornerRadius, DEFAULT_CORNERS)
                .toFloat()

        maxProgress =
            t.getFloat(R.styleable.SegmentProgressBar_maxProgress, DEFAULT_MAX_PROGRESS)

        t.recycle()
    }


    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        setMaxWidth()
    }

    private fun setMaxWidth() {
        progressWidth = width.toFloat()
        invalidate()
    }

    fun setMax(max: Float) {
        maxProgress = max
    }

    fun setPrimaryProgress(value: Float) {
        primaryProgressPercent = value
        invalidate()
    }

    fun setSecondaryProgress(value: Float) {
        secondaryProgressPercent = value
        invalidate()
    }

    fun setThirdProgress(value: Float) {
        thirdProgressPercent = value
        invalidate()
    }

    fun setFourthProgress(value: Float) {
        fourthProgressPercent = value
        invalidate()
    }

    fun setCornerRadius(cornerRadius: Float) {
        this.cornerRadius = cornerRadius
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        //background
        paint.color = PROGRESS_BAR_BG_COLOR
        drawRoundRect(
            canvas,
            0F,
            0F,
            progressWidth,
            height.toFloat(),
            cornerRadius,
            cornerRadius,
            cornerRadius,
            cornerRadius,
            paint
        )

        //First
        paint.color = primaryColor
        val right = (progressWidth / maxProgress) * primaryProgressPercent
        drawRoundRect(
            canvas,
            0F,
            0F,
            right,
            height.toFloat(),
            cornerRadius,
            O,
            cornerRadius,
            O,
            paint
        )

        //Second
        paint.color = secondaryColor
        val offset = (progressWidth / maxProgress) * secondaryProgressPercent
        drawRoundRect(
            canvas,
            right,
            O,
            right + offset,
            height.toFloat(),
            O,
            O,
            O,
            O,
            paint
        )

        //Third
        paint.color = thirdColor
        val thirdPercent = (progressWidth / maxProgress) * thirdProgressPercent
        drawRoundRect(
            canvas,
            right + offset,
            O,
            right + offset + thirdPercent,
            height.toFloat(),
            O,
            O,
            O,
            O,
            paint
        )

        //Fourth
        paint.color = fourthColor
        val fourthPercent = (progressWidth / maxProgress) * fourthProgressPercent
        val rightOffset = right + offset + thirdPercent + fourthPercent
        val fourthRadius = if (rightOffset >= progressWidth) {
            cornerRadius
        } else {
            0F
        }
        drawRoundRect(
            canvas,
            right + offset + thirdPercent,
            O,
            rightOffset,
            height.toFloat(),
            O,
            fourthRadius,
            O,
            fourthRadius,
            paint
        )
    }


    private fun drawRoundRect(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        topLeftRadius: Float,
        topRightRadius: Float,
        bottomLeftRadius: Float,
        bottomRightRadius: Float,
        paint: Paint
    ) {

        val radiusArr = floatArrayOf(
            topLeftRadius, topLeftRadius,
            topRightRadius, topRightRadius,
            bottomRightRadius, bottomRightRadius,
            bottomLeftRadius, bottomLeftRadius
        )

        path.reset()
        progressRectF.set(left, top, right, bottom)
        path.addRoundRect(progressRectF, radiusArr, Path.Direction.CW)
        path.close()
        canvas.drawPath(path, paint)

    }

    companion object {

        const val O = 0F
        const val DEFAULT_MAX_PROGRESS = 100.0F

        const val DEFAULT_CORNERS = 6

        private val PROGRESS_BAR_BG_COLOR = Color.parseColor("#ffffff")
        private val DEFAULT_PRIMARY_COLOR = Color.parseColor("#4285F4")
        private val DEFAULT_SECONDARY_COLOR = Color.parseColor("#FAD810")
        private val DEFAULT_THIRD_COLOR = Color.parseColor("#1DB551")
        private val DEFAULT_FOURTH_COLOR = Color.parseColor("#707070")
    }
}