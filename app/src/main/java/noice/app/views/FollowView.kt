package noice.app.views

import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.enums.UserAction
import noice.app.model.eventbus.EventMessage
import noice.app.modules.creator.dialog.BadgeInfoDialog
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.follow.FollowUser
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.ImageUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.unwrap
import noice.app.utils.isTrue
import noice.app.utils.visible
import org.greenrobot.eventbus.EventBus

class FollowView : FrameLayout {

    private lateinit var ctx: Context
    lateinit var viewHolder: ViewHolder
    private var clickTime: Long = 0

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    private fun init(context: Context) {
        ctx = context
        inflate(context, R.layout.follow_user_layout, this)
        layoutParams = LayoutParams(MATCH_PARENT, WRAP_CONTENT)
        viewHolder = ViewHolder(this)
    }

    fun setData(data: FollowUser) {

        ImageUtils.loadImageByUrl(
            viewHolder.imgUser,
            data.user?.smallImage,
            placeHolder = R.drawable.ic_user_profile, circleCrop = true, originalUrl = data.user?.originalImage
        )

        viewHolder.txtName.text = data.user?.displayName
        viewHolder.itemView.setOnClickListener {
            EventBus.getDefault().post(
                OpenIndexEvent(
                    OpenIndexEvent.OPEN_USER_PAGE,
                    data.user?.id
                )
            )
        }

        viewHolder.txtUserName.text = "@".plus(data.user?.userName)

        if (data.user?.isVerified == true){
            viewHolder.verifiedBadge.visibility = View.VISIBLE
            viewHolder.txtUserName.setTextColor(ContextCompat.getColor(ctx, R.color.light_blue))
        } else {
            viewHolder.verifiedBadge.visibility = View.GONE
            viewHolder.txtUserName.setTextColor(ContextCompat.getColor(ctx, R.color.white))
        }

        if (data.user?.creatorProfile?.isActive.isTrue()){
            if (data.user?.creatorProfile?.nPlusBadge.isTrue()){
                viewHolder.noicemakerBadge.setImageResource(R.drawable.ic_n_plus_badge)
            } else {
                viewHolder.noicemakerBadge.setImageResource(R.drawable.ic_noicemaker_badge)
            }
        }
        viewHolder.noicemakerBadge.visibility = if (data.user?.creatorProfile?.isActive.isTrue()) VISIBLE else GONE

        viewHolder.imgVip.visible(data.user?.isSubscribed.isTrue())

        viewHolder.noicemakerBadge.setOnClickListener {
            val badgeInfoDialog = BadgeInfoDialog.newInstance()
            badgeInfoDialog.show((ctx.unwrap() as HomeActivity).supportFragmentManager, "BadgeInfoDialog")
        }

        val follow = data.user?.meta?.userActions?.find { it.action == UserAction.FOLLOW.action }

        if (follow!=null){
            if (follow.actionValue == 1.0){
                viewHolder.followBtn.isSelected = true
            viewHolder.followBtn.text = ctx.getString(R.string.following)
            viewHolder.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_following,
                0,
                0,
                0
            )
            }else{
                viewHolder.followBtn.isSelected = false
                viewHolder.followBtn.text = ctx.getString(R.string.follow)
                viewHolder.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_follower_plus,
                    0,
                    0,
                    0
                )
            }
        }else{
            viewHolder.followBtn.isSelected = false
            viewHolder.followBtn.text = ctx.getString(R.string.follow)
            viewHolder.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_follower_plus,
                0,
                0,
                0
            )
        }

        if (data.user?.id == PrefUtils.userDetails?.id){
            viewHolder.followBtn.visibility = View.GONE
        }else{
            viewHolder.followBtn.visibility = View.VISIBLE
        }

        viewHolder.followBtn.setOnClickListener {

            if (SystemClock.elapsedRealtime() - clickTime < 1000){
                return@setOnClickListener
            }

            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (ctx as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            clickTime = SystemClock.elapsedRealtime()

            val actionValue: Double
            if (follow==null){
                actionValue = 1.0
                val mediaAction = if (PrefUtils.isLoggedIn) {
                    MediaAction(
                        UserAction.FOLLOW.action,
                        actionValue, data.user?.id,
                        "user",
                        PrefUtils.userDetails?.id.toString(),
                        "user",
                        null
                    )
                } else {
                    MediaAction(
                        UserAction.FOLLOW.action,
                        actionValue, data.user?.id,
                        "user",
                        PrefUtils.guestId,
                        "user",
                        null
                    )
                }
                val actionList = ArrayList<MediaAction>()
                actionList.add(mediaAction)
                if (data.user?.meta == null){
                    data.user?.meta = Community()
                }
                data.user?.meta?.userActions = actionList
            }else{
                actionValue = if (follow.actionValue == 0.0){
                    1.0
                }else{
                    0.0
                }
                follow.actionValue = actionValue
                val index = data.user.meta?.userActions?.indexOf(follow)?:0
                data.user.meta?.userActions?.set(index,follow)
            }

            if (actionValue == 1.0){
                EventBus.getDefault().post(EventMessage(data,Constants.FOLLOW_USER))
                viewHolder.followBtn.isSelected = true
                viewHolder.followBtn.text = ctx.getString(R.string.following)
                viewHolder.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_following,
                    0,
                    0,
                    0
                )

            }else{
                viewHolder.followBtn.isSelected = false
                viewHolder.followBtn.text = ctx.getString(R.string.follow)
                viewHolder.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_follower_plus,
                    0,
                    0,
                    0
                )
                EventBus.getDefault().post(EventMessage(data,Constants.UNFOLLOW_USER))
            }
            val eventName = if (actionValue == 0.0){
                "user_unfollowed"
            }else{
                "user_followed"
            }
            AnalyticsUtil.sendEventForUserFollow("user",  eventName, data.user?.id, data.user?.userName?:"",data.user?.displayName?:"" ,"follower_page")

        }
    }

    class ViewHolder(view : View) : RecyclerView.ViewHolder(view) {
        val imgUser : ImageView = view.findViewById(R.id.imgUser)
        val noicemakerBadge : ImageView = view.findViewById(R.id.noicemakerBadge)
        val imgVip : ImageView = view.findViewById(R.id.imgVip)
        val verifiedBadge : ImageView = view.findViewById(R.id.verifiedBadge)
        val txtUserName : TextView = view.findViewById(R.id.txtUserName)
        val txtName : TextView = view.findViewById(R.id.txtName)
        val followBtn : TextView = view.findViewById(R.id.followBtn)
        init {
            imgUser.clipToOutline = true
        }
    }
}