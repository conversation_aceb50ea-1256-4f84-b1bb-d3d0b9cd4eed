package noice.app.modules.onboarding.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.models.*
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import noice.app.modules.profile.repository.ProfileRepository
import javax.inject.Inject

@HiltViewModel
class NewOnBoardingViewModel @Inject constructor(
    private val onBoardingRepository: OnBoardingApiRepository,
    private val repository: ProfileRepository,
    private val dashBoardRepository: DashboardApiRepository
) : ViewModel() {

    private val mutableClickedAction = MutableLiveData<Int>()
    val clickedAction: LiveData<Int> get() = mutableClickedAction

    fun setClickedAction(action : Int) {
        mutableClickedAction.value = action
    }

    private val mutableSuccessData = MutableLiveData<OnBoardingSuccessData>()
    val successData: LiveData<OnBoardingSuccessData> get() = mutableSuccessData
    private var onBoardingReason = -1

    fun setSuccessData() {
        mutableSuccessData.value = OnBoardingSuccessData(onBoardingReason)
    }

    fun setGenreData(genreList : List<Genre>) {
        mutableSuccessData.value = OnBoardingSuccessData(onBoardingReason, genreList = genreList)
    }

    fun getGenre(orderBy : String) = onBoardingRepository.getGenre(orderBy = orderBy)

    fun updateUserPreference(request: UserPreferenceRequest) = repository.updateUserPreference(request)

    fun performAction(mediaAction: MediaAction) = dashBoardRepository.performAction(mediaAction)

    fun updateOnBoardingFlag(request : OnBoardingComplete) = onBoardingRepository.updateOnBoardingFlag(request)
}