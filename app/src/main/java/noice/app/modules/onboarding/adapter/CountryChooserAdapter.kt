package noice.app.modules.onboarding.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.Filter
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.databinding.library.baseAdapters.BR
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.databinding.CountryChooserViewBinding
import noice.app.listner.CommonListener
import noice.app.modules.onboarding.models.Country

class CountryChooserAdapter(
    private val countries: List<Country>,
    private val commonListener: CommonListener<Country>
) : RecyclerView.Adapter<CountryChooserAdapter.CountryViewHolder>() {

    private val filteredCountries = arrayListOf<Country>()
    private val countryFilter = CountryFilter()
    var searchResultListener: CommonListener<Boolean>? = null

    init {
        filteredCountries.clear()
        filteredCountries.addAll(countries)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountryViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = CountryChooserViewBinding.inflate(layoutInflater)
        binding.root.layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, parent.context.resources.getDimensionPixelSize(R.dimen.dp48))
        return CountryViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return filteredCountries.size
    }

    override fun onBindViewHolder(holder: CountryViewHolder, position: Int) {
        val item = filteredCountries[position]
        holder.itemView.setOnClickListener {
            commonListener.onResult(item)
        }
        holder.bind(item)
    }

    fun filter(keyWord: String) {
        countryFilter.filter(keyWord)
    }

    inner class CountryFilter : Filter() {

        override fun performFiltering(prefix: CharSequence): FilterResults {
            val results = FilterResults()
            if (prefix.isEmpty()) {
                results.values = countries
                results.count = countries.size
            } else {
                val prefixString = prefix.toString().lowercase()
                val newValues = arrayListOf<Country>()
                for (i in countries.indices) {
                    val valueText = countries[i].name.toString().lowercase()

                    if (valueText.startsWith(prefixString)) {
                        newValues.add(countries[i])
                    } else {
                        val words = valueText.split(" ".toRegex()).dropLastWhile { it.isEmpty() }
                            .toTypedArray()
                        for (word in words) {
                            if (word.startsWith(prefixString)) {
                                newValues.add(countries[i])
                                break
                            }
                        }
                    }
                }
                results.values = newValues
                results.count = newValues.size
            }
            return results
        }

        @SuppressLint("NotifyDataSetChanged")
        override fun publishResults(constraint: CharSequence, results: FilterResults) {
            filteredCountries.clear()
            filteredCountries.addAll((results.values as List<*>).filterIsInstance<Country>())
            notifyDataSetChanged()
            if (results.count > 0) {
                searchResultListener?.onResult(true)
            } else {
                searchResultListener?.onResult(false)
            }
        }
    }

    open inner class CountryViewHolder(private val binding: CountryChooserViewBinding) : RecyclerView.ViewHolder(binding.root) {
        open fun bind(item: Country) {
            binding.setVariable(BR.country, item)
            binding.countryLayout.background = if (item.default == true) {
                ContextCompat.getDrawable(binding.root.context, R.drawable.background_black700_radius_4dp)
            } else {
                null
            }
            binding.executePendingBindings()
        }
    }
}