package noice.app.modules.onboarding.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import noice.app.modules.onboarding.fragments.IntroFragment

class IntroViewPagerAdapter(manager: FragmentManager, private val fragmentCount: Int) :
    FragmentPagerAdapter(manager, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {

    override fun getCount(): Int {
        return fragmentCount
    }

    override fun getItem(position: Int): Fragment {
        return IntroFragment.newInstance(position)
    }
}