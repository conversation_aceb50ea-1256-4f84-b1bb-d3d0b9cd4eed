package noice.app.modules.onboarding.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentMyGenreBinding
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.activity.EditGenreActivity
import noice.app.modules.onboarding.adapter.MyGenreAdapter
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.profile.fragment.userprofile.UserProfileViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.Constants
import noice.app.utils.PrefUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class MyGenreFragment : Fragment() {

    private lateinit var ctx : Context
    private lateinit var binding : FragmentMyGenreBinding
    private lateinit var adapter : MyGenreAdapter
    private var genreList = ArrayList<Genre>()
    private val viewModel : UserProfileViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMyGenreBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {
        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setBackToHomeVisibility(GONE)
        binding.errorView.setLayoutBackground(R.color.black)
        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()

            getData()
        }

        adapter = MyGenreAdapter(genreList, MyGenreAdapter.TILE_VIEW)
        binding.genreRecyclerView.adapter = adapter
        binding.genreRecyclerView.addItemDecoration(RecyclerViewMargin(resources.getDimensionPixelSize(R.dimen.dp16)))

        binding.toolbar.setSkipButtonClick {
            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (ctx as HomeActivity).handleUserNotLoggedIn()
                return@setSkipButtonClick
            }

            EditGenreActivity.start(ctx, genreList)
        }

        binding.editGenre.setOnClickListener {
            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (ctx as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            EditGenreActivity.start(ctx, genreList)
        }

        getData()
    }

    private fun getData() {
        if (genreList.isNullOrEmpty())
            binding.errorView.showLoading()

        getGenre()
    }

    private fun getGenre() {
        viewModel.profileUseCase.getUserPreferences("genre", "my_genre_${PrefUtils.userDetails?.id}").observe(viewLifecycleOwner) {

            when(it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleGenreResponse(it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleGenreResponse(it)
                    } else {
                        binding.emptyView.visibility = VISIBLE
                        binding.genreRecyclerView.visibility = GONE
                    }
                }
                else -> {
                    if (genreList.isEmpty()) {
                        binding.errorView.showError(it?.message, type = "onboarding", errorName = it?.message)
                        binding.emptyView.visibility = GONE
                    } else {
                        binding.errorView.hide()
                    }
                }
            }
        }
    }

    private fun handleGenreResponse(
        resource: Resource<BaseModel<List<Genre>>>
    ) {
        genreList.clear()
        genreList.addAll(resource.data?.data ?: ArrayList())
        adapter.notifyDataSetChanged()
        binding.genreRecyclerView.visibility = VISIBLE
        binding.emptyView.visibility = GONE
        binding.errorView.hide()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event : EventMessage) {
        if (event.eventCode == Constants.GENRE_UPDATED && event.data is List<*>) {
            genreList.clear()
            genreList.addAll(event.data.filterIsInstance<Genre>())
            adapter.notifyDataSetChanged()

            Utils.showSnackBar(binding, getString(R.string.successfully_changed_the_genre))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }
}