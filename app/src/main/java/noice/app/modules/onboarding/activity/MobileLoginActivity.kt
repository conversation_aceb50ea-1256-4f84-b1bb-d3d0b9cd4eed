package noice.app.modules.onboarding.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.ActivityMobileLoginBinding
import noice.app.modules.onboarding.fragments.OtpFragmentDirections
import noice.app.modules.onboarding.helper.ReCaptchaHelper
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.onboarding.viewmodel.MobileLoginViewModel
import noice.app.utils.Utils.parcelable

@AndroidEntryPoint
class MobileLoginActivity : AppCompatActivity() {

    companion object {
        const val LOGIN_CHANGE_EVENT = "LOGIN_CHANGE_EVENT"

        fun start(ctx: Context, loginChangeEvent: LoginChangeEvent? = null) {
            Intent(ctx, MobileLoginActivity::class.java).apply {
                putExtra(LOGIN_CHANGE_EVENT, loginChangeEvent)
                ctx.startActivity(this)
            }
        }
    }

    private lateinit var binding: ActivityMobileLoginBinding
    private val viewModel: MobileLoginViewModel by viewModels()
    private var navController: NavController? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMobileLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        viewModel.reCaptchaHelper = ReCaptchaHelper(this)

        getDataFromIntent()

        initViews()
    }

    private fun getDataFromIntent() {
        viewModel.loginChangeEvent = intent.parcelable(LOGIN_CHANGE_EVENT)
    }

    private fun initViews() {
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.navHostMobileLogin) as NavHostFragment
        navController = navHostFragment.navController

        navController?.addOnDestinationChangedListener { _, destination, _ ->
            if (destination.label == getString(R.string.country_chooser)) {
                binding.toolbar.visibility = View.GONE
            } else {
                binding.toolbar.visibility = View.VISIBLE
            }
        }

        binding.toolbar.setBackClick {
            onBackPressedDispatcher.onBackPressed()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                when (navController?.currentDestination?.label) {
                    getString(R.string.otp_fragment) -> {
                        navController?.navigate(OtpFragmentDirections.navigateToMobileNumber())
                    }
                    getString(R.string.mobile_number_fragment) -> {
                        finish()
                    }
                    else -> {
                        navController?.navigateUp()
                    }
                }
            }
        })
    }
}