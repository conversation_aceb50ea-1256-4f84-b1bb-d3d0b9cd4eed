package noice.app.modules.onboarding.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.exoplayer.ExoplayerUtils
import noice.app.modules.chat.report.ReportCommentRequest
import noice.app.modules.onboarding.repository.DeleteAccountRepository
import java.io.File
import javax.inject.Inject

@HiltViewModel
class DeleteAccountViewModel @Inject constructor(
    private val repository: DeleteAccountRepository
) : ViewModel() {

    private val mutableClickedAction = MutableLiveData<Int>()
    val clickedAction: LiveData<Int> get() = mutableClickedAction

    fun setClickedAction(action : Int) {
        mutableClickedAction.value = action
    }

    fun deleteAccount(request: ReportCommentRequest) = repository.deleteAccount(request)

    fun clearDownload(ctx : Context) {
        viewModelScope.launch(Dispatchers.IO) {
            val downloadSizeDir = File(
                ExoplayerUtils.getExternalFilesDir(),
                ExoplayerUtils.DOWNLOAD_CONTENT_DIRECTORY
            )

            val result = downloadSizeDir.deleteRecursively()

            if (result) {
                ExoplayerUtils.deleteAllDownloads(ctx)
            }

            BaseApplication.application.getAppDb().downloadsDao().clearDownloadsFromDb()
        }
    }
}