package noice.app.modules.onboarding.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.modules.onboarding.models.Genre
import noice.app.views.GenreTileView

class GenreAdapter(val data: ArrayList<Genre>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var content: GenreTileView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        content = GenreTileView(parent.context)
        return content.viewHolder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        content.viewHolder = holder as GenreTileView.ViewHolder
        content.setData(data[position])
    }

    override fun getItemCount(): Int {
        return data.size
    }
}