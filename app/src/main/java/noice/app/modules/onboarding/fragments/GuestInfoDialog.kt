package noice.app.modules.onboarding.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import noice.app.R
import noice.app.databinding.GuestInfoDialogBinding

class GuestInfoDialog : BottomSheetDialogFragment() {

    companion object {
        fun newInstance() = GuestInfoDialog()
    }

    private lateinit var ctx : Context
    private lateinit var binding : GuestInfoDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = GuestInfoDialogBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews(){

        binding.textOk.setOnClickListener {
            dismiss()
        }

        binding.textTutup.setOnClickListener {
            dismiss()
        }
    }

    fun show(manager: FragmentManager) : GuestInfoDialog {
        return GuestInfoDialog().apply {
            val ft = manager.beginTransaction()
            ft.add(this, GuestInfoDialog::class.java.simpleName)
            ft.commitAllowingStateLoss()
        }
    }
}