package noice.app.modules.onboarding.fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import noice.app.BaseApplication
import noice.app.R
import noice.app.databinding.FragmentFeedbackBinding
import noice.app.model.eventbus.EventMessage
import noice.app.modules.onboarding.activity.LoginActivity
import noice.app.utils.Constants
import noice.app.utils.PrefUtils
import org.greenrobot.eventbus.EventBus

class FeedbackFragment : Fragment() {

    private lateinit var binding : FragmentFeedbackBinding
    private lateinit var ctx : Context

    companion object {
        fun newInstance() = FeedbackFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentFeedbackBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    override fun onResume() {
        super.onResume()

        logout()
    }

    private fun initViews() {
        setMailToSpan()

        binding.continueBtn.setOnClickListener {
            val i = Intent(ctx, LoginActivity::class.java)
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(i)
        }
    }

    private fun setMailToSpan() {
        val wordToSpan: Spannable =
            SpannableString(ctx.getString(R.string.need_help_emailto_noice))

        wordToSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(ctx, R.color.dull_yellow)),
            24,
            40,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        binding.mailForHelp.text = wordToSpan
    }

    private fun logout() {
        EventBus.getDefault().post(EventMessage(null, Constants.UPDATE_QUEUE))

        Handler(Looper.getMainLooper()).postDelayed({
            PrefUtils.logout(baseContext = ctx, redirect = true)
        }, 600)
    }
}