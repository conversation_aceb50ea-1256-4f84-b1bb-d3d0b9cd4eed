package noice.app.modules.onboarding.activity

import android.animation.Animator
import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Base64
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.airbnb.lottie.LottieCompositionFactory
import com.appsflyer.AppsFlyerLib
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.moengage.core.enableAdIdTracking
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.ActivitySplashBinding
import noice.app.exoplayer.BasePlayerActivity
import noice.app.exoplayer.BasePlayerActivity.Companion.PARENT_ID
import noice.app.model.appconfig.AppConfig
import noice.app.model.appconfig.BottomNavConfigsAos
import noice.app.model.appconfig.FirebaseVariant
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.fragments.GuestLoginDialog.Companion.GUEST_LOGIN
import noice.app.modules.onboarding.models.GuestTokenRequest
import noice.app.modules.onboarding.models.LoginResponse
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.ResponseStatus
import noice.app.rest.ServerInterface
import noice.app.update.InAppUpdateManager
import noice.app.update.InAppUpdateStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.CacheUtils
import noice.app.utils.ClassVariantProvider
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.CONFIG_BOTTOM_NAV_CONFIGS
import noice.app.utils.Constants.Companion.DATA_VERSION_OLD
import noice.app.utils.ExperimentUtils
import noice.app.utils.ExperimentUtils.FIREBASE_VARIANT_QUICK_PICKS_AOS
import noice.app.utils.ImageUtils
import noice.app.utils.InstallReferrerUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PrefUtils
import noice.app.utils.ShareUtils
import noice.app.utils.Utils
import noice.app.utils.automotive.AutomotiveUtils
import noice.app.utils.edgeToEdge
import noice.app.utils.isFalse
import noice.app.utils.isTrue
import noice.app.utils.visible
import noice.app.workmanager.PlayIntegrityWorker
import org.json.JSONObject
import org.json.JSONTokener
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit

@AndroidEntryPoint
class SplashActivity : AppCompatActivity(), InAppUpdateManager.InAppUpdateListener {

    companion object {
        private const val WAITING_MILLIS = 1500.toLong()
        const val REQ_CODE_VERSION_UPDATE = 530
        private const val CHANNEL_ID = "APP_UPDATE"
        private const val UPDATE_NOTIFICATION_ID = 8862
    }

    private val viewModel: OnBoardingViewModel by viewModels()

    private var inAppUpdateManager: InAppUpdateManager? = null
    private var isForceUpdate = false
    private var forcedLogout = false
    private var changeIcon = false

    private var forceUpdateLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                inAppUpdateManager?.updateApp()
            } else if (result.resultCode == Activity.RESULT_CANCELED) {
                finish()
            }
        }

    private lateinit var binding: ActivitySplashBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Don't show splash screen when receive deeplink while in warm state (app in background)
        if (!isTaskRoot && intent.data != null) {
            getDataFromIntent()
            startNextScreen(
                Intent(this, HomeActivity::class.java).setFlags(
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                )
            )
        }
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.canProceedToHomeScreen.collectLatest { proceedToHome ->
                    if (proceedToHome) handleRedirection()
                }
            }
        }
        binding = ActivitySplashBinding.inflate(layoutInflater)
        edgeToEdge()
        setContentView(binding.root)

        if (PrefUtils.adsIdentifier.isEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val idInfo: AdvertisingIdClient.Info =
                        AdvertisingIdClient.getAdvertisingIdInfo(applicationContext)
                    val advertisingId: String = idInfo.id ?: ""

                    if (!idInfo.isLimitAdTrackingEnabled) {
                        PrefUtils.adsIdentifier = advertisingId
                        enableAdIdTracking(BaseApplication.getBaseAppContext())
                    } else { // a fallback in case if user has disabled ads identifier
                        PrefUtils.adsIdentifier = Settings.Secure.getString(
                            BaseApplication.application.contentResolver,
                            Settings.Secure.ANDROID_ID
                        )
                        AppsFlyerLib.getInstance().setCollectAndroidID(true)
                    }

                    checkForToken()
                } catch (e: IOException) {
                    handleDeviceIdException()
                    e.printStackTrace()
                } catch (e: IllegalStateException) {
                    handleDeviceIdException()
                    e.printStackTrace()
                } catch (e: GooglePlayServicesNotAvailableException) {
                    handleDeviceIdException()
                    e.printStackTrace()
                } catch (e: GooglePlayServicesRepairableException) {
                    handleDeviceIdException()
                    e.printStackTrace()
                }
            }
        } else checkForToken()

        InstallReferrerUtils.checkInstallReferrer(this)

        getDataFromIntent()

        if (PrefUtils.isLoggedIn) {
            getUserPreferences()
        }
        animateSplashIcon()
    }

    private fun getDataFromIntent() {
        if (intent.hasExtra("entityId") && intent.hasExtra("entityType")) {
            intent.putExtra(
                BasePlayerActivity.EVENT_ID, Utils.getIndexEventAction(
                    intent.getStringExtra("entityType") ?: "",
                    intent.getStringExtra("entitySubType") ?: ""
                )
            )

            intent.putExtra(BasePlayerActivity.ENTITY_ID, intent.getStringExtra("entityId"))
            intent.putExtra(BasePlayerActivity.ENTITY_TYPE, intent.getStringExtra("entityType"))
            intent.putExtra(
                BasePlayerActivity.ENTITY_SUB_TYPE,
                intent.getStringExtra("entitySubType")
            )

            intent.action = BasePlayerActivity.OPEN_INDEX_ACTION

            if (intent.hasExtra("parentId")) {
                intent.putExtra(PARENT_ID, intent.getStringExtra("parentId"))
            }

            DataController.redirectIntent = intent

            val notificationTrigger = if (intent.getStringExtra("isManual") == "true") {
                "manual"
            } else {
                "auto"
            }
            MoEngageAnalytics.sendEvent(this, "app opened", "source", "push notification")

            AnalyticsUtil.sendEvent("notification_clicked", Bundle().apply {
                putString("Source", "device push")
                putString("notificationTrigger", notificationTrigger)
                putString("notificationType", intent.getStringExtra("type"))
                putString("notificationId", intent.getStringExtra("notificationId"))
                putString("entityType", intent.getStringExtra("entityType"))
                putString("entityId", intent.getStringExtra("entityId"))
                putString("entityTitle", intent.getStringExtra("title"))
            })
        } else if (intent.data != null) {
            MoEngageAnalytics.sendEvent(this, "app opened", "source", "external link")
            DataController.redirectIntent = intent
        } else {
            MoEngageAnalytics.sendEvent(this, "app opened", "source", "direct")
        }

        intent.putExtra(Constants.SOURCE, "Push Notification")
    }

    override fun onStart() {
        super.onStart()

        if (!isForceUpdate) {
            fetchRemoteConfig()
        }
    }


    private fun fetchRemoteConfig() {

        val remoteConfig = Firebase.remoteConfig
        remoteConfig.setDefaultsAsync(R.xml.remote_config)
        remoteConfig.setConfigSettingsAsync(
            FirebaseRemoteConfigSettings.Builder()
                .setFetchTimeoutInSeconds(10)
                .setMinimumFetchIntervalInSeconds(TimeUnit.MINUTES.toSeconds(5))
                .build()
        )

        var minVersionCode = -1
        var maxVersionCode = -1
        var currentVersionCode = -1

        remoteConfig.getString("app_config").let { configStr ->
            if (configStr.isNotEmpty()) {
                val config = Gson().fromJson(configStr, AppConfig::class.java)

                forcedLogout = config.app_update_config?.android?.forcedLogout ?: false
                minVersionCode = config.app_update_config?.android?.minForceUpdateVersionCode ?: 0
                maxVersionCode = config.app_update_config?.android?.maxForceUpdateVersionCode ?: 0
                currentVersionCode =
                    config.app_update_config?.android?.current_app_version_code ?: 0

                val stateAlias1 = packageManager.getComponentEnabledSetting(
                    ComponentName(
                        this@SplashActivity,
                        SplashActivityAlias1::class.java
                    )
                )
                val stateAlias2 = packageManager.getComponentEnabledSetting(
                    ComponentName(
                        this@SplashActivity,
                        SplashActivityAlias2::class.java
                    )
                )
                val stateSplash = packageManager.getComponentEnabledSetting(
                    ComponentName(
                        this@SplashActivity,
                        SplashActivity::class.java
                    )
                )
                val stateMatch =
                    if (config.app_update_config?.android?.app_icon_id == 1 && stateSplash == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                        true
                    } else if (config.app_update_config?.android?.app_icon_id == 2 && stateAlias1 == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                        true
                    } else if (config.app_update_config?.android?.app_icon_id == 3 && stateAlias2 == PackageManager.COMPONENT_ENABLED_STATE_ENABLED) {
                        true
                    } else false
                val freshAndAlias1 =
                    PrefUtils.appConfig?.app_update_config?.android?.app_icon_id == null && stateAlias1 == PackageManager.COMPONENT_ENABLED_STATE_ENABLED
                if (freshAndAlias1 || PrefUtils.appConfig?.app_update_config?.android?.app_icon_id != null ||
                    config?.app_update_config?.android?.app_icon_id != 1
                ) {
                    changeIcon =
                        (PrefUtils.appConfig?.app_update_config?.android?.app_icon_id != config?.app_update_config?.android?.app_icon_id)
                                || !stateMatch
                }

                PrefUtils.appConfig = config
            }
        }

        remoteConfig.getString(CONFIG_BOTTOM_NAV_CONFIGS).let { bottomNavConfigs ->
            if (bottomNavConfigs.isNotEmpty()) {
                val configs = Gson().fromJson(bottomNavConfigs, BottomNavConfigsAos::class.java)
                PrefUtils.bottomNavConfigsAos = configs
            }
        }

        /* quick-picks experiment */
        remoteConfig.getString(FIREBASE_VARIANT_QUICK_PICKS_AOS).let { experiment ->
            if (experiment.isNotEmpty()) {
                val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                PrefUtils.firebaseVariantQuickPicks = variant
            }
        }

        /* similar catalog experiment */
        remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_SIMILAR_CATALOG_AOS)
            .let { experiment ->
                if (experiment.isNotEmpty()) {
                    val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                    PrefUtils.firebaseVariantSimilarCatalog = variant
                }
            }

        /* genre based top podcast experiment */
        remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST_AOS)
            .let { experiment ->
                if (experiment.isNotEmpty()) {
                    val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                    PrefUtils.firebaseVariantGenreBasedTopPodcast = variant
                }
            }

        if (minVersionCode != -1 && maxVersionCode != -1) {
            isForceUpdate = BuildConfig.VERSION_CODE in minVersionCode until maxVersionCode
        }

        if (BuildConfig.VERSION_CODE < currentVersionCode && Utils.isNetworkConnected.isTrue()) {
            checkForUpdate()
        } else {
            viewModel.setCheckUpdate(true)
        }
    }

    private fun <T> fireIntent(clazz: Class<T>, waitingMillis: Long = WAITING_MILLIS) {
        val i = Intent(this, clazz)
        i.putExtra("source", "Splash Activity")
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.R || waitingMillis == 0L) {
            startNextScreen(i)
        } else {
            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed(object : Runnable {
                override fun run() {
                    startNextScreen(i)
                    handler.removeCallbacks(this)
                }
            }, waitingMillis)
        }
    }

    private fun startNextScreen(i: Intent) {
        startActivity(i)
        finish()
    }

    private fun getHomeData() {
        CacheUtils.exists(listOf(Constants.BUAT_KAMU, Constants.HOME_BANNER)) { map ->
            if (map?.containsValue(0) == false) {
                fireIntent(ClassVariantProvider.of(HomeActivity::class.java), 0)
            } else {
                viewModel.getHomeData().observe(this) {
                    if (it) {
                        fireIntent(ClassVariantProvider.of(HomeActivity::class.java), 0)
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQ_CODE_VERSION_UPDATE) {
            if (resultCode == RESULT_CANCELED) {
                if (isForceUpdate) {
                    // If the update is cancelled by the user,
                    // you can request to start the update again.
                    inAppUpdateManager?.checkForUpdate()
                } else {
                    viewModel.setCheckUpdate(true)
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun checkForToken() {
        if (PrefUtils.token.isNullOrEmpty()) {
            val jwt = Utils.getJwtToken()
            BaseApplication.doServerCall({
                NetworkRequests.getGuestToken(
                    GuestTokenRequest(
                        jwt,
                        "dasd",
                        PrefUtils.fcmToken,
                        Utils.getDeviceId(),
                        Utils.getDeviceIdOld(),
                        Utils.getAppInstalledTime(this@SplashActivity)
                    )
                )
            }, object : ServerInterface<BaseModel<LoginResponse>> {
                override fun onCustomError(e: ApiError) {
                    viewModel.setCheckToken(true)
                }

                override fun onError(e: Throwable) {
                    viewModel.setCheckToken(true)
                }

                override fun onSuccess(data: BaseModel<LoginResponse>, dataFromCache: Boolean) {
                    PrefUtils.token = data.data?.accessToken.toString()
                    PrefUtils.isNewAcquiredUser = data.data?.data?.isOnboardingComplete == false

                    val getId: String = PrefUtils.token.toString().split(".")[1]
                    val byteArrayData: ByteArray = Base64.decode(getId, Base64.DEFAULT)
                    val text = String(byteArrayData, StandardCharsets.UTF_8)

                    val json = JSONTokener(text).nextValue() as JSONObject
                    if (json.has("id"))
                        PrefUtils.guestId = json.getString("id")

                    launchIntegrityFlow()

                    AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.guestId)

                    BaseApplication.firebaseAnalytics.setUserProperty(
                        "guest_user_id",
                        PrefUtils.guestId ?: ""
                    )
                    BaseApplication.firebaseAnalytics.setUserProperty(
                        "device_id",
                        Utils.getDeviceId()
                    )
                    BaseApplication.firebaseAnalytics.setUserProperty("appUserType", "guest")

                    viewModel.setCheckToken(true)
                }
            })
        } else {
            viewModel.setCheckToken(true)
            launchIntegrityFlow()
        }
    }

    private fun launchIntegrityFlow() {
        PlayIntegrityWorker.beginWork()
    }

    private fun handleRedirection() {
        /* proceeding with the flow */
        PrefUtils.dataVersion =
            DATA_VERSION_OLD // after app update, preventing user to keep using older data-versions

        AutomotiveUtils.optMandatoryLogin()

        if (PrefUtils.loginType == GUEST_LOGIN) {
            getHomeData()
        } else if (PrefUtils.isLoggedIn) {
            if (PrefUtils.onBoardingStep == 1) {
                fireIntent(CompleteProfileActivity::class.java)
            } else {
                getHomeData()
            }
        } else {
            PrefUtils.dataVersion = DATA_VERSION_OLD

            // these tracking are from legacy code when user click login as guest
            val eventValue: MutableMap<String, Any> = HashMap()
            AppsFlyerLib.getInstance()
                .logEvent(BaseApplication.getBaseAppContext(), "enter_as_guest", eventValue)
            MoEngageAnalytics.sendEvent(this, "enter as guest", "", null)
            BaseApplication.firebaseAnalytics.setUserId(PrefUtils.fcmToken)
            AnalyticsUtil.sendEvent("enter_as_guest", null)

            PrefUtils.loginType = GUEST_LOGIN
            getHomeData()
        }
    }

    private fun checkForUpdate() {
        if (inAppUpdateManager == null) {
            inAppUpdateManager = InAppUpdateManager.Builder(this)
                .listener(this)
                .isForceUpdate(isForceUpdate)
                .requestCode(REQ_CODE_VERSION_UPDATE)
                .build()

            inAppUpdateManager?.let { manager ->
                lifecycle.addObserver(manager)
                manager.snackBarMessage(getString(R.string.update_downloaded))
                manager.snackBarAction(getString(R.string.restart_caps))
            }
        }
        inAppUpdateManager?.checkForUpdate()
    }

    override fun onInAppUpdateError(code: Int, error: Throwable?) {
        viewModel.setCheckUpdate(true)
    }

    override fun onInAppUpdateStatus(status: InAppUpdateStatus?) {

        if (status?.isDownloaded == true) {
            val rootView: View = window.decorView.findViewById(android.R.id.content)

            val snackBar = Snackbar.make(
                rootView,
                "An update has just been downloaded.",
                Snackbar.LENGTH_INDEFINITE
            )
            snackBar.setActionTextColor(ContextCompat.getColor(this, R.color.white))
            snackBar.setAction("RESTART") {
                // Triggers the completion of the update of the app for the flexible flow.
                if (isForceUpdate && forcedLogout) {
                    PrefUtils.logout(fullClear = true, redirect = false, baseContext = this)
                }
                inAppUpdateManager?.completeUpdate()
            }

            snackBar.show()
        } else if (status?.isUpdateAvailable == true && isForceUpdate) {
            ForceUpdateActivity.startForResult(this, forceUpdateLauncher)
        } else {
            if (status?.isUpdateAvailable == true) {
                val currentCode = status.getUpdateInfo()?.availableVersionCode() ?: 0
                if (currentCode > PrefUtils.notifiedVersionCode) {
                    notifyUserForUpdate(currentCode)
                }
            }
            viewModel.setCheckUpdate(true)
        }
    }

    private fun notifyUserForUpdate(currentCode: Int) {

        createNotificationChannel()

        val notificationIntent = ShareUtils.getPlayStoreIntent(packageName)

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_noice_logo_dark)
            .setContentTitle(getString(R.string.app_update_content_title))
            .setContentText(getString(R.string.app_update_content_text))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .build()

        with(NotificationManagerCompat.from(this)) {
            // notificationId is a unique int for each notification that you must define
            notify(UPDATE_NOTIFICATION_ID, notification)
        }

        PrefUtils.notifiedVersionCode = currentCode
    }

    private fun createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val channel = NotificationChannel(CHANNEL_ID, CHANNEL_ID, importance)
            // Register the channel with the system
            val notificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun getUserPreferences() {
        viewModel.getUserPreference().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.userPreference = it.data
            }
        }
    }

    private fun handleAppIcon(config: AppConfig?) {
        val manager = packageManager
        if (config?.app_update_config?.android?.app_icon_id == 2) {
            manager.setComponentEnabledSetting(
                ComponentName(this@SplashActivity, SplashActivityAlias1::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP
            )
            manager.setComponentEnabledSetting(
                ComponentName(this@SplashActivity, SplashActivity::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP
            )
        } else {
            manager.setComponentEnabledSetting(
                ComponentName(this@SplashActivity, SplashActivity::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP
            )
            manager.setComponentEnabledSetting(
                ComponentName(this@SplashActivity, SplashActivityAlias1::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP
            )
        }
    }

    private fun handleDeviceIdException() {
        PrefUtils.adsIdentifier = Settings.Secure.getString(
            BaseApplication.application.contentResolver,
            Settings.Secure.ANDROID_ID
        )
        checkForToken()
    }

    private fun animateSplashIcon() {
        if (!PrefUtils.appCDNConfig?.splash_config?.imageUrl.isNullOrEmpty() || PrefUtils.appCDNConfig?.splash_config?.animationUrl.isNullOrEmpty()
            || PrefUtils.appCDNConfig?.splash_config?.isAnimated.isFalse()
        ) {
            loadSplashImageFromCDN()
        } else {
            binding.lottieView.visible(true)
            binding.imgSplash.visible(false)
            LottieCompositionFactory.fromUrl(
                this,
                PrefUtils.appCDNConfig?.splash_config?.animationUrl
            )
                .addListener { composition ->
                    binding.lottieView.setComposition(composition)
                    binding.lottieView.playAnimation()
                }
                .addFailureListener {
                    loadSplashImageFromCDN()
                }
        }

        binding.lottieView.addAnimatorListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animator: Animator) {

            }

            override fun onAnimationEnd(animator: Animator) {
                binding.lottieView.clearAnimation()
                binding.lottieView.visible(false)
                viewModel.setAnimationComplete(true)
            }

            override fun onAnimationCancel(animator: Animator) {
                viewModel.setAnimationComplete(true)
            }

            override fun onAnimationRepeat(animator: Animator) {

            }
        })
    }

    private fun loadSplashImageFromCDN() {
        lifecycleScope.launch {
            delay(1500)
            viewModel.setAnimationComplete(true)
        }
        binding.lottieView.visible(false)
        binding.imgSplash.visible(true)
        PrefUtils.appCDNConfig?.splash_config?.imageUrl?.let {
            ImageUtils.loadImageByUrl(
                binding.imgSplash,
                PrefUtils.appCDNConfig?.splash_config?.imageUrl,
                false,
                placeHolder = R.drawable.transparent,
                fallback = R.drawable.ic_noice_splash,
                originalUrl = it
            )
        } ?: run {
            binding.imgSplash.setImageDrawable(
                AppCompatResources.getDrawable(
                    this@SplashActivity,
                    R.drawable.ic_noice_splash
                )
            )
        }
    }

}