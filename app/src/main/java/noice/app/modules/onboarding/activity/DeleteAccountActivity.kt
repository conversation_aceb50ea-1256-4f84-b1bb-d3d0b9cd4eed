package noice.app.modules.onboarding.activity

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint
import noice.app.databinding.ActivityDeleteAccountBinding
import noice.app.modules.onboarding.adapter.DeleteAccountPagerAdapter
import noice.app.modules.onboarding.viewmodel.DeleteAccountViewModel

@AndroidEntryPoint
class DeleteAccountActivity : AppCompatActivity() {

    companion object {
        const val BACK = 1
        const val CONTINUE = 2
    }

    private lateinit var binding : ActivityDeleteAccountBinding
    private lateinit var adapter : DeleteAccountPagerAdapter
    private val viewModel : DeleteAccountViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeleteAccountBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initViews()
    }

    private fun initViews() {
        adapter = DeleteAccountPagerAdapter(this)
        binding.pager.adapter = adapter
        binding.pager.isUserInputEnabled = false

        viewModel.clickedAction.observe(this) { action ->
            manageActions(action)
        }
    }

    override fun onBackPressed() {
        val currentItem = binding.pager.currentItem
        if (currentItem == 0) {
            super.onBackPressed()
        } else {
            binding.pager.currentItem = currentItem - 1
        }
    }

    private fun manageActions(action : Int) {
        when (action) {
            BACK -> {
                onBackPressed()
            }
            CONTINUE -> {
                binding.pager.currentItem = binding.pager.currentItem + 1
            }
        }
    }
}