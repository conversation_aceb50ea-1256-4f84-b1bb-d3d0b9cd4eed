package noice.app.modules.onboarding.fragments

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import noice.app.R
import noice.app.databinding.FragmentDeleteAccountReasonBinding
import noice.app.listner.OnClickInterface
import noice.app.modules.chat.report.ReportCommentRequest
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.onboarding.activity.DeleteAccountActivity
import noice.app.modules.onboarding.viewmodel.DeleteAccountViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils

class DeleteAccountReasonFragment : Fragment() {

    private lateinit var binding : FragmentDeleteAccountReasonBinding
    private lateinit var ctx : Context
    private val viewModel : DeleteAccountViewModel by activityViewModels()
    private var confirmationDialog : NoiceAlertDialog? = null

    companion object {
        fun newInstance() = DeleteAccountReasonFragment()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentDeleteAccountReasonBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {
        showVisibility()

        binding.toolbar.setBackClick {
            requireActivity().onBackPressed()
        }

        binding.textAccountDeleteReason.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable) {}
            override fun beforeTextChanged(
                s: CharSequence, start: Int,
                count: Int, after: Int
            ) {
                binding.inputReasonInfo.text = getString(R.string.required_characters)
            }

            override fun onTextChanged(
                s: CharSequence, start: Int,
                before: Int, count: Int
            ) {
                binding.inputReasonInfo.text = getString(R.string.current_characters, s.length)
                binding.inputReasonInfo.setCompoundDrawablesWithIntrinsicBounds(0, 0,0,0)
            }
        })

        binding.continueBtn.setOnClickListener {
            if (!NetworkUtils.isNetworkConnected(ctx)) {
                hideVisibility()
                return@setOnClickListener
            } else {
                showVisibility()
            }

            if (binding.textAccountDeleteReason.text.toString().isEmpty() || binding.textAccountDeleteReason.text?.length!! < 20) {
                binding.inputReasonInfo.text = getString(R.string.your_explanation_is_too_short)
                binding.inputReasonInfo.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_warning_fill, 0,0,0)
                return@setOnClickListener
            }

            confirmationDialog = NoiceAlertDialog.Builder()
                .setTitle(getString(R.string.are_you_sure))
                .setMessage(getString(R.string.after_processing_your_acc_will_be_completely_lost))
                .setNegativeButtonText(getString(R.string.yakin))
                .setPositiveButtonText(getString(R.string.cancel_indo))
                .setListener(object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean, eventId: Int) {
                        if (eventId == NoiceAlertDialog.NEGATIVE_BTN_CLICK) {
                            binding.errorView.showLoading()
                            sendDeleteAccountRequest()
                        }
                    }
                }).show(childFragmentManager)
        }

        binding.retryBtn.setOnClickListener {
            binding.continueBtn.performClick()
        }
    }

    private fun sendDeleteAccountRequest() {
        val request = ReportCommentRequest(
            PrefUtils.userDetails?.id.toString(),
            "user",
            binding.textAccountDeleteReason.text.toString(),
            "delete_account"
        )

        viewModel.deleteAccount(request).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {
                viewModel.clearDownload(ctx)
                viewModel.setClickedAction(DeleteAccountActivity.CONTINUE)
            } else {
                Utils.showSnackBar(ctx, ctx.getString(R.string.failed_to_delete_acc_due_to_system_problem))
            }
            binding.errorView.hide()
        }
    }

    private fun showVisibility() {
        binding.toolbar.visibility = VISIBLE
        binding.reasonLayout.visibility = VISIBLE
        binding.customErrorView.visibility = GONE
    }

    private fun hideVisibility() {
        binding.toolbar.visibility = GONE
        binding.reasonLayout.visibility = GONE
        binding.customErrorView.visibility = VISIBLE
    }
}