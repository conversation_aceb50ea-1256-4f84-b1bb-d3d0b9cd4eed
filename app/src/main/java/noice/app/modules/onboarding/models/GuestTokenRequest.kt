package noice.app.modules.onboarding.models

import com.google.gson.annotations.SerializedName

data class GuestTokenRequest(
    @SerializedName("registrationToken")
    val registrationToken: String,
    @SerializedName("password")
    val password: String = "asdasd",
    @SerializedName("fcmToken")
    val fcmToken: String ?= null,
    @SerializedName("deviceId")
    val deviceId: String ?= null,

    val oldDeviceId: String? = null,
    val installedTimestamp: String? = null
)