package noice.app.modules.onboarding.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import noice.app.BaseApplication
import noice.app.R
import noice.app.databinding.FragmentOnBoardingSuccessBinding
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.OnBoardingSuccessData
import noice.app.modules.onboarding.viewmodel.NewOnBoardingViewModel
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class OnBoardingSuccessFragment : Fragment() {

    companion object {

        fun newInstance() = OnBoardingSuccessFragment()
    }

    private lateinit var binding : FragmentOnBoardingSuccessBinding
    private val viewModel : NewOnBoardingViewModel by activityViewModels()
    private var genreList = ArrayList<Genre>()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentOnBoardingSuccessBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }

    private fun initViews() {
        viewModel.successData.observe(viewLifecycleOwner) { data ->
            if (data != null && data.genreList.isNotEmpty()) {
                getData(data)

                setData()
            }
        }
    }

    private fun getData(data : OnBoardingSuccessData) {
        genreList = ArrayList(data.genreList)
    }

    private fun setData() {
        binding.title.text = getString(R.string.find_enter_success_title)
        binding.subTitle.text = getString(R.string.find_enter_success_subtitle)

        setGenrePills()

        sendEvent()

        BaseApplication.firebaseAnalytics.setUserProperty("motivation_selected", "genre")
    }

    private fun setGenrePills() {
        if (genreList.isEmpty()) return

        val moreCount = "+${genreList.size - 3} Lainnya"
        val genreMore = Genre(id = "")
        genreMore.name = moreCount

        /* doing false to remove selected tick */
        genreList[0].isSelected = false
        genreList[1].isSelected = false
        genreList[2].isSelected = false

        binding.genre1.setData(genreList[0])
        binding.genre2.setData(genreList[1])
        binding.genre3.setData(genreList[2])
        binding.genreMore.setData(genreMore)

        binding.genreMore.visibility = if (genreList.size > 3) View.VISIBLE else View.GONE
    }

    fun sendEvent() {
        AnalyticsBuilder.newBuilder().apply {
            putAnalyticsKey("source", "Entertainment selection success page")
        }.also {
            it.send("onboarding page opened")
        }
    }
}