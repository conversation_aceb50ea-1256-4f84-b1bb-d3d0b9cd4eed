package noice.app.modules.onboarding.fragments

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import noice.app.R
import noice.app.databinding.FragmentGuestLoginDialogBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.profile.PolicyTermsActivity
import noice.app.utils.*
import noice.app.views.SnackBarCustom

class GuestLoginDialog : BottomSheetDialogFragment() {

    companion object {
        const val GUEST_LOGIN = "GUEST_LOGIN"
        fun show(fragmentManager: FragmentManager) = GuestLoginDialog().apply {
            val ft = fragmentManager.beginTransaction()
            ft.add(this, GuestLoginDialog::class.simpleName)
            ft.commitAllowingStateLoss()
        }
    }

    private lateinit var binding: FragmentGuestLoginDialogBinding
    private lateinit var ctx: Context

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            if (bottomSheet != null) {
                val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
                bottomSheetBehavior.peekHeight = bottomSheet.height
            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentGuestLoginDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {

        setTncSpan()

        binding.textTutup.setOnClickListener {
            dismissAllowingStateLoss()
        }

        binding.enterAsGuest.setOnClickListener {
            if(!binding.acceptTncCheckBox.isChecked){
                SnackBarCustom.Builder()
                    .parentView(binding.root.rootView)
                    .text(getString(R.string.agree_tnc))
                    .show()
                return@setOnClickListener
            }

            AnalyticsUtil.sendEvent("enter_as_guest", null)

            PrefUtils.loginType = GUEST_LOGIN

            val i = Intent(ctx, ClassVariantProvider.of(HomeActivity::class.java))
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(i)
        }
    }

    private fun setTncSpan() {
        val tnc = getString(R.string.accept_tnc)
        val span = SpannableString(tnc)

        val clickSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                widget.cancelPendingInputEvents()
                PolicyTermsActivity.start(ctx, PolicyTermsActivity.TYPE_TERMS_AND_CONDITION,"Onboarding_Profile_Completion")
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)

                ds.isUnderlineText = false
                ds.color = ContextCompat.getColor(ctx, R.color.white)
            }
        }

        span.setSpan(clickSpan, 19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(StyleSpan(Typeface.BOLD),19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.acceptTncCheckBox.highlightColor = ContextCompat.getColor(ctx, R.color.dull_white30)
        binding.acceptTncCheckBox.movementMethod = LinkMovementMethod()
        binding.acceptTncCheckBox.text = span
    }
}