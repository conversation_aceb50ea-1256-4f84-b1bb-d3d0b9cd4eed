package noice.app.modules.onboarding.adapter

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import noice.app.modules.onboarding.fragments.*

class OnBoardingPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {

    private var list = listOf(
        IntroductionFragment.newInstance(),
        ChooseGenreFragment.newInstance(),
        OnBoardingSuccessFragment.newInstance()
    )

    override fun getItemCount(): Int {
        return list.size
    }

    override fun createFragment(position: Int): Fragment {
        return list[position]
    }

    override fun getItemId(position: Int): Long {
        return list[position].hashCode().toLong()
    }

    override fun containsItem(itemId: Long): Boolean = list.find { it.hashCode().toLong() == itemId } != null
}