package noice.app.modules.onboarding.activity

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import dagger.hilt.android.AndroidEntryPoint
import noice.app.databinding.FragmentCountryChooserBinding
import noice.app.listner.CommonListener
import noice.app.listner.OnOkay
import noice.app.modules.onboarding.adapter.CountryChooserAdapter
import noice.app.modules.onboarding.viewmodel.MobileLoginViewModel
import noice.app.utils.TypingDelay

@AndroidEntryPoint
class CountryChooserFragment : Fragment() {

    private lateinit var binding: FragmentCountryChooserBinding
    private lateinit var adapter: CountryChooserAdapter
    private val viewModel: MobileLoginViewModel by activityViewModels()
    private val typingDelay = TypingDelay(500)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCountryChooserBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {
        adapter = CountryChooserAdapter(viewModel.countries) { selectedCountry ->
            viewModel.countries.filter { country ->
                country.default == true
            }.forEach { country ->
                country.default = false
            }
            viewModel.countries.find { country ->
                country.id == selectedCountry.id
            }?.let { country ->
                country.default = true
                viewModel.selectedCountry.value = country
            }

            findNavController().navigateUp()
        }
        adapter.searchResultListener = CommonListener { haveResults ->
            if (haveResults) {
                binding.searchEmpty.visibility = View.GONE
            } else {
                binding.searchEmpty.visibility = View.VISIBLE
            }
        }
        binding.countryRecyclerView.adapter = adapter
        binding.countryRecyclerView.post {
            val index = viewModel.countries.indexOf(viewModel.selectedCountry.value)
            if (index != -1) {
                (binding.countryRecyclerView.layoutManager as LinearLayoutManager).scrollToPositionWithOffset(index, 20)
            }
        }

        typingDelay.setCallback(object : OnOkay<Boolean> {
            override fun okay(isOkay: Boolean) {
                adapter.filter(binding.countryEditText.text.toString())
            }
        })

        binding.backBtn.setOnClickListener {
            activity?.onBackPressedDispatcher?.onBackPressed()
        }

        binding.clearText.setOnClickListener {
            binding.countryEditText.setText("")
        }

        binding.countryEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                typingDelay.removeCallbacks()
            }

            override fun afterTextChanged(s: Editable?) {
                typingDelay.postDelayed()
                val text = s.toString()
                if (text.isEmpty()) {
                    binding.clearText.visibility = View.GONE
                } else {
                    binding.clearText.visibility = View.VISIBLE
                }
            }
        })
    }
}