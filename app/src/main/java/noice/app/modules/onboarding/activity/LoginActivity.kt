package noice.app.modules.onboarding.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.view.PKeys
import com.appsflyer.AppsFlyerLib
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.GraphRequest
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.crashlytics.ktx.setCustomKeys
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.databinding.ActivityLoginBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.fragments.GuestLoginDialog
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.modules.profile.viewmodel.ProfileViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClassVariantProvider
import noice.app.utils.Constants
import noice.app.utils.FacebookAnalytics
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils

@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    companion object {
        const val LOGIN_TYPE_FACEBOOK = "facebook"
        const val LOGIN_TYPE_GOOGLE = "google"
        const val SOURCE = "source"
    }

    private val viewModel: OnBoardingViewModel by viewModels()
    private val profileViewModel : ProfileViewModel by viewModels()

    private lateinit var mGoogleSignInClient: GoogleSignInClient
    private var facebookCallbackManager: CallbackManager? = null
    private lateinit var binding: ActivityLoginBinding
    private var userEmail = ""
    private var source = ""
    private lateinit var gmailResultLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Utils.checkPlayServices(this)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getDataFromIntent()

        initGoogleLogin()

        initFacebookLogin()

        initViews()

        AnalyticsUtil.sendEvent("login_page_opened", null)
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Onboarding_Welcome_Page")
            putString("previousScreen", "start")
        })

        MoEngageAnalytics.sendEvent(this, "login page viewed", "source", "Splash Activity")
    }

    private fun getDataFromIntent() {
        if (intent.hasExtra(SOURCE))
            source = intent.getStringExtra(SOURCE) ?: ""
    }

    private fun initGoogleLogin() {

        gmailResultLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                try {
                    val account = task.getResult(ApiException::class.java)
                    binding.errorView.showLoading()
                    account.idToken?.let { accessToken ->
                        userEmail = account.email ?: ""
                        login(accessToken, LOGIN_TYPE_GOOGLE)
                    }
                } catch (e: ApiException) {
                    handleLoginException(LOGIN_TYPE_GOOGLE, "onActivityResult : " + e.message)
                    AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                        putString("loginType", LOGIN_TYPE_GOOGLE)
                        putString("email", userEmail)
                        putString("source", "Google")
                        putString("exception", e.message)
                        putString("networkType", NetworkUtils.getNetworkType(this@LoginActivity))
                    })
                }
            }
        }

        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(PKeys.google_client_id.toString())
            .requestEmail()
            .build()

        mGoogleSignInClient = GoogleSignIn.getClient(this, gso)

        binding.googleLogin.setOnClickListener {
            val signInIntent = mGoogleSignInClient.signInIntent
            gmailResultLauncher.launch(signInIntent)
        }
    }

    private fun initFacebookLogin() {

        binding.facebookLoginOriginal.setPermissions("email", "public_profile")
        facebookCallbackManager = CallbackManager.Factory.create()

        facebookCallbackManager?.let { callbackManager ->
            binding.facebookLoginOriginal.registerCallback(
                callbackManager,
                object : FacebookCallback<LoginResult> {
                    override fun onSuccess(result: LoginResult) {
                        binding.errorView.showLoading()

                        result?.let {
                            getEmailFromFacebook(it)
                        }

                        result?.accessToken?.let { accessToken ->
                            login(accessToken.token, LOGIN_TYPE_FACEBOOK)
                        }
                    }

                    override fun onCancel() {
                        handleLoginException(LOGIN_TYPE_FACEBOOK, null, false)
                    }

                    override fun onError(error: FacebookException) {
                        error.printStackTrace()
                        handleLoginException(
                            LOGIN_TYPE_FACEBOOK,
                            "FacebookCallback onError : " + error.message
                        )
                        AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                            putString("loginType", LOGIN_TYPE_GOOGLE)
                            putString("email", userEmail)
                            putString("source", "Facebook")
                            putString("exception", error.message)
                            putString(
                                "networkType",
                                NetworkUtils.getNetworkType(this@LoginActivity)
                            )
                        })
                    }
                })
        }

        binding.facebookLogin.setOnClickListener {
            if(!AccessToken.getCurrentAccessToken()?.token.isNullOrEmpty()) {
                login(AccessToken.getCurrentAccessToken()?.token.toString(), LOGIN_TYPE_FACEBOOK)
            } else {
                binding.facebookLoginOriginal.performClick()
            }
        }
    }

    private fun initViews() {

        binding.loginWithPhone.setOnClickListener {
            MobileLoginActivity.start(this)
        }

        binding.continueAsGuest.setOnClickListener {
            BaseApplication.firebaseAnalytics.setUserId(PrefUtils.fcmToken)

            GuestLoginDialog.show(supportFragmentManager)

            val eventValue: MutableMap<String, Any> = HashMap()
            AppsFlyerLib.getInstance().logEvent(BaseApplication.getBaseAppContext(), "enter_as_guest", eventValue)
            MoEngageAnalytics.sendEvent(this, "enter as guest", "", null)
        }
    }

    private fun login(accessToken: String, type: String) {
        if (PrefUtils.appsFlyerId.isNullOrEmpty()) {
            PrefUtils.appsFlyerId = AppsFlyerLib.getInstance().getAppsFlyerUID(BaseApplication.application)
        }

        viewModel.login(LoginRequest(accessToken, type, PrefUtils.appsFlyerId), userEmail).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.createSession(it.data, type)

                viewModel.fetchAndReplaceQueue()

                PrefUtils.dataVersion = Constants.DATA_VERSION_OLD

                if (it.data.newUser == true) {
                    getUserDetails(type, true)
                    MoEngageAnalytics.sendEvent(
                        this@LoginActivity,
                        "user registered",
                        "login type",
                        type
                    )
                } else {
                    getUserDetails(type)
                    AnalyticsUtil.sendEvent("login", Bundle().apply {
                        putString("loginType", type)
                        putString("email", userEmail)
                    })
                    MoEngageAnalytics.sendEvent(this@LoginActivity, "logged in", "login type", type)

                    val eventValue: MutableMap<String, Any> = HashMap()
                    eventValue["af_login_method"] = type
                    AppsFlyerLib.getInstance()
                        .logEvent(BaseApplication.getBaseAppContext(), "af_login", eventValue)
                }
                PrefUtils.isLoggedIn = true
                PrefUtils.isLoggedInAsGuest = false
                PrefUtils.loginType = type
            } else {
                binding.errorView.hide()
                val showMessage = !it?.message.isNullOrEmpty()
                handleLoginException(type, "ApiErrorLogin : " + it?.message, showMessage)
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", type)
                    putString("email", userEmail)
                    putString("source", "Login")
                    putString("exception", it.message?:"")
                    putString("networkType", NetworkUtils.getNetworkType(this@LoginActivity))
                })
            }
        }
    }

    private fun getUserDetails(type: String,isNewUser:Boolean=false) {
        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "userActions"

        viewModel.getUserDetails(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {

                PrefUtils.userDetails = it.data.data

                AnalyticsUtil.setLoginProperties()

                if (isNewUser) {
                    AnalyticsUtil.sendEvent("user_registered", Bundle().apply {
                        putString("loginType", type)
                        putString("email", userEmail)
                        putString("profileUserId", it.data.data?.id)
                    })

                    MoEngageAnalytics.sendEvent(
                        this@LoginActivity,
                        "user registered",
                        "login type",
                        type
                    )
                }
                AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.userDetails?.id)
                AppsFlyerLib.getInstance().setCustomerIdAndLogSession(PrefUtils.userDetails?.id,this)

                if (it.data.data?.isOnboardingComplete == true) {
                    PrefUtils.onBoardingStep = 0
                    val i = Intent(this, ClassVariantProvider.of(HomeActivity::class.java))
                    i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(i)
                } else {
                    PrefUtils.onBoardingStep = 1
                    CompleteProfileActivity.start(
                        this,
                        source = "Onboarding_Welcome_Page"
                    )
                }

                Firebase.crashlytics.setCustomKeys {
                    key("userName",PrefUtils.userDetails?.userName.orEmpty())
                    key("displayName",PrefUtils.userDetails?.displayName.orEmpty())
                    key("userId",PrefUtils.userDetails?.id.orEmpty())
                }
                Firebase.crashlytics.setUserId(PrefUtils.userDetails?.id.orEmpty())
                MoEngageAnalytics.setUniqueId(this, PrefUtils.userDetails?.id ?: "")
                MoEngageAnalytics.setAttributes(this, isLoggedIn = true)
                MoEngageAnalytics.sendEvent(this, "logged in", "login type", type)

                getSelectedGenre()
                getUserPreferences()
            } else if (it?.status != ResponseStatus.LOADING) {
                handleLoginException(type, "ApiErrorUserDetails : " + it?.message)
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", type)
                    putString("email", userEmail)
                    putString("source", "UserDetail")
                    putString("exception", it?.message?:"")
                    putString("networkType", NetworkUtils.getNetworkType(this@LoginActivity))
                })
            }

            if (it?.status != ResponseStatus.LOADING) {
                binding.errorView.hide()
            }
        }
    }


    private fun getSelectedGenre() {
        profileViewModel.getUserPreference("genre", null).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                PrefUtils.selectedGenre = ArrayList(it.data?.data)
                MoEngageAnalytics.setAttributes(this, isLoggedIn = true)
            }
        }
    }

    private fun getUserPreferences() {
        viewModel.getUserPreference().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.userPreference = it.data
            }
        }
    }

    private fun getEmailFromFacebook(loginResult: LoginResult) {
        val request = GraphRequest.newMeRequest(
            loginResult.accessToken
        ) { `object`, _ ->
            userEmail = try {
                `object`?.getString("email") ?: ""
            } catch (e: Exception) {
                ""
            }
        }
        val parameters = Bundle()
        parameters.putString("fields", "email")
        request.parameters = parameters
        request.executeAsync()
    }

    private fun handleLoginException(type: String, message: String?, showMessage : Boolean = true) {

        if(BuildConfig.DEBUG && message != null) {
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
        if(type == LOGIN_TYPE_FACEBOOK) {
            if (showMessage)
                Utils.showSnackBar(this, getString(R.string.facebook_error_msg))
            LoginManager.getInstance().logOut()
        } else if(type == LOGIN_TYPE_GOOGLE) {
            if (showMessage)
                Utils.showSnackBar(this@LoginActivity, getString(R.string.google_error_msg))
            if(::mGoogleSignInClient.isInitialized) {
                mGoogleSignInClient.signOut()
            }
        }
    }
}