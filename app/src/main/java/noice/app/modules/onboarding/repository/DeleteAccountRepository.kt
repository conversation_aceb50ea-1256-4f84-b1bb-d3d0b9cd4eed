package noice.app.modules.onboarding.repository

import androidx.lifecycle.MutableLiveData
import noice.app.BaseApplication
import noice.app.model.generics.BaseModel
import noice.app.modules.chat.report.ReportCommentData
import noice.app.modules.chat.report.ReportCommentRequest
import noice.app.rest.*
import noice.app.rest.apiinterfaces.CommunityApiInterface
import noice.app.utils.PrefUtils
import javax.inject.Inject

class DeleteAccountRepository @Inject constructor(
    private var communityApiService: CommunityApiInterface
) {

    fun deleteAccount(request: ReportCommentRequest): MutableLiveData<Resource<ReportCommentData>> {
        val mutableData = MutableLiveData<Resource<ReportCommentData>>()

        BaseApplication.doServerCall(
            { communityApiService.reportComment(PrefUtils.token, request) },
            object : ServerInterface<BaseModel<ReportCommentData>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<ReportCommentData>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }
}
