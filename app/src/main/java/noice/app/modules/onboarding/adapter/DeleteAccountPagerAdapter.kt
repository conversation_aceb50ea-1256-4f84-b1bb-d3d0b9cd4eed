package noice.app.modules.onboarding.adapter

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import noice.app.modules.onboarding.fragments.DeleteAccountReasonFragment
import noice.app.modules.onboarding.fragments.FeedbackFragment
import noice.app.modules.onboarding.fragments.LostBenefitFragment

class DeleteAccountPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {

    private val fragments = mutableListOf(
        LostBenefitFragment.newInstance(),
        DeleteAccountReasonFragment.newInstance(),
        FeedbackFragment.newInstance()
    )

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun createFragment(position: Int): Fragment {
        return fragments[position]
    }
}