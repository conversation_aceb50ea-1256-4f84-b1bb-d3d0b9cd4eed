package noice.app.modules.onboarding.fragments

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import noice.app.R
import noice.app.databinding.FragmentOtpBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.activity.CompleteProfileActivity
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.models.MobileLoginRequest
import noice.app.modules.onboarding.viewmodel.MobileLoginViewModel
import noice.app.modules.profile.viewmodel.ProfileViewModel
import noice.app.receiver.SMSReceiver
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.views.SnackBarCustom
import noice.app.views.otp.OnOtpCompletionListener
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.TimeUnit

@AndroidEntryPoint
class OtpFragment : Fragment(), OnOtpCompletionListener, SMSReceiver.OTPReceiveListener {

    companion object {
        private const val LOGIN_TYPE_OTP = "otp"
    }

    private val viewModel: MobileLoginViewModel by activityViewModels()
    private val profileViewModel : ProfileViewModel by viewModels()

    private lateinit var binding: FragmentOtpBinding
    private lateinit var ctx: Context
    private var code: String = ""
    private val args: OtpFragmentArgs by navArgs()
    private var otpId: String = ""
    private var countDown: CountDownTimer? = null
    private var smsReceiver: SMSReceiver? = null
    private var isLoginApiFired = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentOtpBinding.inflate(inflater, container, false)

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Onboarding_OTP_Input")
            putString("previousScreen", "Onboarding_PhoneNumber_Input")
        })

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.otpView.setOtpListener()
        binding.otpView.setOtpCompletionListener(this)

        // mobile = "7011535276"
        binding.txtMobile.text = viewModel.formattedMobileNumber

        startSMSListener(true)

        binding.proceed.setOnClickListener {
            code = binding.otpView.getOtpValue()
            if (code.isEmpty() || code.length < 4) {
                SnackBarCustom.Builder()
                    .parentView(binding.root)
                    .text(getString(R.string.otp_does_not_match))
                    .show()
                binding.otpView.error = getString(R.string.enter_your_otp_code_first)
                return@setOnClickListener
            }

            Utils.hideKeyboard(ctx)
            MoEngageAnalytics.sendEvent(ctx, "otp verified", "", null)

            login()
        }

        binding.txtResend.setOnClickListener {
            binding.otpView.error = null
            binding.otpView.setOtp("")
            startSMSListener(false)
            MoEngageAnalytics.sendEvent(ctx, "otp requested", "", null)
        }

        binding.txtEdit.setOnClickListener {
            binding.otpView.error = null
            navigateSafely(OtpFragmentDirections.navigateToMobileNumber())
        }
    }

    private fun navigateSafely(direction: NavDirections) {
        val controller = findNavController()
        if (controller.currentDestination?.id == R.id.navigationOtpFragment) {
            controller.navigate(direction)
        }
    }

    private fun sendOtpForLogin(captchaToken: String?) {
        if (view == null)
            return

        val request = MobileLoginRequest(listOf("sms"), viewModel.dialingCode.drop(1), viewModel.mobileNumber, captchaToken)
        binding.errorView.showLoading()
        viewModel.mobileLoginRequest("otp", request).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            binding.errorView.hide()
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {
                viewModel.updateLoginCounter()
                binding.otpView.error = null
                otpId = it.data.data?.id.toString()
                Utils.showSnackBar(ctx, ctx.getString(R.string.msg_otp_request_success))
                showCountDown()

                AnalyticsBuilder.oldBuilder().send("otp requested")
                MoEngageAnalytics.sendEvent(ctx, "otp requested", "", null)
            } else {
                binding.otpView.error = it?.message.toString()

                binding.txtResendText.visibility = View.VISIBLE
                binding.txtResend.visibility = View.VISIBLE

                Utils.showSnackBar(ctx, it?.message.toString())
            }
        }
    }

    private fun showCountDown() {
        binding.txtCountDown.visibility = View.VISIBLE
        binding.txtResend.visibility = View.GONE
        countDown = object : CountDownTimer(60000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val time = String.format("%02d:%02d",
                    TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished),
                    TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) -
                            TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)))

                binding.txtCountDown.text = time
            }

            override fun onFinish() {
                binding.txtCountDown.visibility = View.GONE
                binding.txtResend.visibility = View.VISIBLE
            }
        }.start()
    }

    override fun onOtpCompleted(otp: String?) {
        code = otp ?: ""
    }

    private fun login() {
        if (isLoginApiFired) {
            return
        }

        isLoginApiFired = true

        if (isDetached || isRemoving || !isAdded)
            return

        binding.errorView.showLoading()

        viewModel.login(
            LoginRequest(type = LOGIN_TYPE_OTP, otp = code, otpId = otpId, appsFlyerId = PrefUtils.appsFlyerId)
        ).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.createSession(it.data, LOGIN_TYPE_OTP)

                viewModel.fetchAndReplaceQueue()

                getSelectedGenre()

                PrefUtils.dataVersion = Constants.DATA_VERSION_OLD

                if (it.data.newUser == true) {
                    getUserDetails(true)
                    MoEngageAnalytics.sendEvent(
                        ctx,
                        "user registered",
                        "login type",
                        "phone"
                    )
                } else {
                    getUserDetails()
                    AnalyticsUtil.sendEvent("login", Bundle().apply {
                        putString("loginType", "phone")
                        putString("phoneNumber", viewModel.mobileNumber)
                    })
                }

                PrefUtils.isLoggedIn = true
                PrefUtils.isLoggedInAsGuest = false
                PrefUtils.loginType = LOGIN_TYPE_OTP

                AnalyticsUtil.sendEvent("otp_verified")
            } else {
                isLoginApiFired = false
                binding.errorView.hide()
                binding.otpView.error = getString(R.string.enter_your_otp_code_first)

                SnackBarCustom.Builder()
                    .parentView(binding.root)
                    .text(getString(R.string.failed_try_again))
                    .show()

                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", "phone")
                    putString("loginStep", "otp")
                    putString("phoneNumber", viewModel.mobileNumber)
                    putString("networkType", NetworkUtils.getNetworkType(ctx))
                    putString("exception", it.message)
                })
            }
        }
    }

    private fun getUserDetails(isNewUser:Boolean = false) {

        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "userActions|artist"

        viewModel.getUserDetails(map).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {

                PrefUtils.userDetails = it.data.data
                MoEngageAnalytics.setUniqueId(ctx, PrefUtils.userDetails?.id ?: "")
                MoEngageAnalytics.setAttributes(ctx, isLoggedIn = true)
                MoEngageAnalytics.sendEvent(ctx, "logged in", "login type", "phone")

                if (isNewUser) {
                    AnalyticsUtil.sendEvent("user_registered", Bundle().apply {
                        putString("loginType", LOGIN_TYPE_OTP)
                        putString("phoneNumber", viewModel.formattedMobileNumber)
                        putString("profileUserId", it.data.data?.id)
                    })
                }

                AnalyticsUtil.setLoginProperties()

                when (it.data.data?.isOnboardingComplete) {
                    true -> {
                        PrefUtils.onBoardingStep = 0
                        if (viewModel.loginChangeEvent != null) {
                            EventBus.getDefault().post(viewModel.loginChangeEvent)
                            (ctx as AppCompatActivity).finish()
                        } else {
                            val i = Intent(ctx, ClassVariantProvider.of(HomeActivity::class.java))
                            i.flags =
                                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                            startActivity(i)
                        }
                    }
                    else -> {
                        PrefUtils.onBoardingStep = 1
                        CompleteProfileActivity.start(
                            ctx = ctx,
                            source = "Onboarding_OTP_Input",
                            loginChangeEvent = viewModel.loginChangeEvent
                        )
                    }
                }
            } else if (it?.status != ResponseStatus.LOADING) {
                isLoginApiFired = false
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", "phone")
                    putString("loginStep", "otp")
                    putString("phoneNumber", viewModel.mobileNumber)
                    putString("networkType", NetworkUtils.getNetworkType(ctx))
                    putString("exception", it?.message)
                })
            }

            if (it?.status != ResponseStatus.LOADING) {
                binding.errorView.hide()
            }
        }
    }

    private fun startSMSListener(firstAttempt: Boolean) {
        try {
            smsReceiver = SMSReceiver()
            smsReceiver?.setOTPListener(this)

            val client = SmsRetriever.getClient(ctx)
            val intentFilter = IntentFilter()
            intentFilter.addAction(SmsRetriever.SMS_RETRIEVED_ACTION)
            ctx.registerReceiver(smsReceiver, intentFilter)
            val task = client.startSmsRetriever()

            task.addOnFailureListener { e ->
                AnalyticsBuilder.oldBuilder().apply {
                    putAnalyticsKey("error source", "sms retriever")
                    putAnalyticsKey("error name", e.message)
                }.also { builder ->
                    builder.send("error encountered")
                }
                e.printStackTrace()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        proceedToSendOtp(firstAttempt)
    }

    private fun proceedToSendOtp(firstAttempt: Boolean) {
        if (firstAttempt) {
            sendOtpForLogin(args.captchaToken)
        } else if (!viewModel.checkLoginCounter()) {
            displayCaptcha()
        } else {
            sendOtpForLogin(null)
        }
    }

    private fun displayCaptcha() {
        viewModel.displayCaptcha("otpPage") { result ->
            if (result.proceed) {
                sendOtpForLogin(result.token)
            }
            if (!result.displayMessage.isNullOrEmpty()) {
                SnackBarCustom.Builder()
                    .parentView(binding.root)
                    .text(result.displayMessage)
                    .show()
            }
            /*if (!result.errorMessage.isNullOrEmpty()) {
                //log event
            }*/
        }
    }

    override fun onDestroyView() {
        if (countDown != null) {
            countDown?.cancel()
            countDown = null
        }
        if (smsReceiver != null) {
            ctx.unregisterReceiver(smsReceiver)
        }
        super.onDestroyView()
    }

    override fun onOTPReceived(otp: String) {
        lifecycleScope.launch {
            binding.otpView.setOtp(otp)
            login()
        }
    }

    override fun onOTPTimeOut() {
        Log.d("OtpFragment", "otp timeout")
    }

    override fun onOTPReceivedError(error: String) {
        Log.d("OtpFragment", "message$error")
    }

    private fun getSelectedGenre() {
        profileViewModel.getUserPreference("genre", null).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                PrefUtils.selectedGenre = ArrayList(it.data?.data)
                MoEngageAnalytics.setAttributes(ctx, isLoggedIn = true)
            }
        }
    }
}