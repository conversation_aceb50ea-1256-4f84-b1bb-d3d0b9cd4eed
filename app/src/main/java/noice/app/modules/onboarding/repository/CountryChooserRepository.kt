package noice.app.modules.onboarding.repository


import noice.app.rest.BaseRepository
import noice.app.rest.apiinterfaces.CDNConfigApiInterface
import javax.inject.Inject

class CountryChooserRepository @Inject constructor(
    private val configApiInterface: CDNConfigApiInterface
): BaseRepository() {

    fun getCountries() = networkBoundResource(
        cachingConstant = null,
        apiCall = { configApiInterface.getCountries() }
    )
}