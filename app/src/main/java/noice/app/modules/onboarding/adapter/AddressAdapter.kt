package noice.app.modules.onboarding.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.listner.OnClickInterface
import noice.app.modules.onboarding.models.Address


class AddressAdapter(private val ctx: Context, private val addressList: ArrayList<Address>, private val clickListener :OnClickInterface<Address>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
           val v = LayoutInflater.from(ctx).inflate(R.layout.address_item, parent, false)
            return SectionViewHolder(v)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        holder as SectionViewHolder
        val item = addressList[position]
        holder.txtCity.text = item.city.plus(", ".plus(item.state))
        holder.itemView.setOnClickListener {
            clickListener.dataClicked(item)
        }
    }

    override fun getItemCount(): Int {
        return addressList.size
    }

    class SectionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val txtCity: TextView = itemView.findViewById(R.id.txtCity)
    }
}