package noice.app.modules.onboarding.fragments

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.StyleSpan
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentUsernameBinding
import noice.app.listner.OnOkay
import noice.app.modules.onboarding.viewmodel.CompleteProfileViewModel
import noice.app.modules.profile.PolicyTermsActivity
import noice.app.rest.ResponseStatus
import noice.app.utils.LowerCaseInputFilter
import noice.app.utils.TypingDelay
import noice.app.utils.Utils
import noice.app.views.SnackBarCustom

@AndroidEntryPoint
class UsernameFragment : Fragment() {

    companion object {
        fun newInstance() = UsernameFragment()
    }

    private lateinit var binding: FragmentUsernameBinding
    private lateinit var ctx: Context
    private val viewModel: CompleteProfileViewModel by activityViewModels()
    private val typingDelay = TypingDelay()
    private var isUserNameAvailable = false
    private val userNameRegex = Regex("^[a-z\\d_.]*$")

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentUsernameBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {

        if (viewModel.username.isNotEmpty()) {
            binding.username.setText(viewModel.username)
        }
        binding.usernameLayout.isEndIconVisible = false

        setTncSpan()

        typingDelay.setCallback(object : OnOkay<Boolean> {
            override fun okay(isOkay: Boolean) {
                if (isOkay) {
                    val text = binding.username.text.toString().trim()
                    if (text == viewModel.username) {
                        setUserNameDefault(true)
                    } else if(text.length in 4..15) {
                        if(text.matches(userNameRegex)) {
                            validateUserName(binding.username.text.toString().trim())
                        } else {
                            setUserNameError(getString(R.string.username_char_error))
                        }
                    } else {
                        setUserNameError(getString(R.string.username_length_error))
                    }
                }
            }
        })

        binding.username.apply {
            filters += LowerCaseInputFilter()
            addTextChangedListener(object : TextWatcher {

                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    typingDelay.removeCallbacks()
                }

                override fun afterTextChanged(s: Editable?) {
                    if(s.toString().isNotEmpty()) {
                        typingDelay.postDelayed()
                    } else {
                        setUserNameDefault(false)
                    }
                }
            })
        }

        binding.proceed.setOnClickListener {
            if (binding.username.text.toString().trim().isEmpty() ||
                binding.username.text.toString().length <= 3) {
                setUserNameError(getString(R.string.empty_user_name))
                return@setOnClickListener
            }

            if (!isUserNameAvailable) {
                return@setOnClickListener
            }

            if(!binding.acceptTncCheckBox.isChecked){
                SnackBarCustom.Builder()
                    .parentView(binding.root)
                    .text(getString(R.string.tnc_acceptance))
                    .show()
                return@setOnClickListener
            }

            viewModel.username = binding.username.text.toString()

            viewModel.invokeEvent(CompleteProfileViewModel.EVENT_UPDATE_DETAILS)
        }

        binding.username.post {
            binding.username.requestFocus()
        }
    }

    private fun ackValidation() {
        Utils.hideKeyboard(binding.username)
        isUserNameAvailable = true
        binding.progressBar.visibility = View.GONE
        binding.usernameLayout.error = null
        binding.usernameLayout.isEndIconVisible = true
        binding.usernameLayout.hintTextColor = ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.parrot_green))
        binding.usernameLayout.setHelperTextColor(ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.parrot_green)))
        binding.usernameLayout.helperText = getString(R.string.good_choise_of_username)
    }

    private fun ackValidationInProgress() {
        this.isUserNameAvailable = false
        binding.progressBar.visibility = View.VISIBLE
        binding.usernameLayout.error = null
        binding.usernameLayout.isEndIconVisible = false
        binding.usernameLayout.hintTextColor = ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.greyText1))
        binding.usernameLayout.setHelperTextColor(ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.neutral_grey)))
        binding.usernameLayout.helperText = getString(R.string.validation_in_progress)
    }

    private fun setUserNameError(error: String? = null) {
        isUserNameAvailable = false
        binding.progressBar.visibility = View.GONE
        binding.usernameLayout.error = error ?: getString(R.string.username_already_registered)
    }

    private fun setUserNameDefault(isUserNameAvailable : Boolean) {
        this.isUserNameAvailable = isUserNameAvailable
        binding.progressBar.visibility = View.GONE
        binding.usernameLayout.isEndIconVisible = false
        binding.usernameLayout.error = null
        binding.usernameLayout.hintTextColor = ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.greyText1))
        binding.usernameLayout.setHelperTextColor(ColorStateList.valueOf(ContextCompat.getColor(ctx, R.color.neutral_grey)))
        binding.usernameLayout.helperText = null
    }

    private fun validateUserName(name: String) {
        ackValidationInProgress()

        viewModel.validateUserName(name).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data == true) {
                ackValidation()
            } else {
                setUserNameError()
            }
        }
    }

    private fun setTncSpan() {
        val tnc = getString(R.string.accept_tnc)
        val span = SpannableString(tnc)
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                widget.cancelPendingInputEvents()
                PolicyTermsActivity.start(ctx, PolicyTermsActivity.TYPE_TERMS_AND_CONDITION,"Onboarding_Profile_Completion")
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.drawableState = null
                ds.color = ContextCompat.getColor(ctx, R.color.white)
            }
        }
        span.setSpan(StyleSpan(Typeface.BOLD),19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(clickableSpan, 19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.acceptTncCheckBox.highlightColor = Color.TRANSPARENT
        binding.acceptTncCheckBox.movementMethod = LinkMovementMethod.getInstance()
        binding.acceptTncCheckBox.text = span
    }
}