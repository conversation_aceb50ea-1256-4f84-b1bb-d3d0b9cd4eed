package noice.app.modules.onboarding.activity

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.activity.viewModels
import androidx.viewpager2.widget.ViewPager2
import noice.app.BaseApplication
import noice.app.R
import noice.app.databinding.ActivityOnBoardingBinding
import noice.app.listner.OnClickInterface
import noice.app.modules.BaseActivity
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.onboarding.adapter.OnBoardingPagerAdapter
import noice.app.modules.onboarding.fragments.ChooseGenreFragment.Companion.FIND_ENTERTAINMENT
import noice.app.modules.onboarding.models.OnBoardingComplete
import noice.app.modules.onboarding.viewmodel.NewOnBoardingViewModel
import noice.app.utils.*
import org.json.JSONObject

class OnBoardingActivity : BaseActivity() {

    companion object {
        const val SKIP = 1
        const val CONTINUE = 2

        private const val REDIRECTION_TIME = 2000L
    }

    private lateinit var binding : ActivityOnBoardingBinding
    private var pagerAdapter : OnBoardingPagerAdapter? = null
    private val viewModel : NewOnBoardingViewModel by viewModels()
    private var isApiCallCompleted = false
    private var runnableCompleted = true
    private val handler = Handler(Looper.getMainLooper())
    private val runnable = {
        runnableCompleted = true
        proceedForRedirection()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnBoardingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initViews()
    }

    override fun onBackPressed() {
        val currentItem = binding.viewPager.currentItem
        if (currentItem == 0 || currentItem == 1) {
            super.onBackPressed()
        } else {
            binding.viewPager.currentItem = currentItem - 1
        }
    }

    override fun onPause() {
        super.onPause()
        updateIsAppInstallFlag()
    }

    private fun initViews() {

        pagerAdapter = OnBoardingPagerAdapter(this)

        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.isUserInputEnabled = false
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                when (position) {
                    0 -> {
                        binding.progressBar.progress = 33
                        binding.progressBar.visibility = View.VISIBLE
                    }
                    1 -> {
                        binding.progressBar.progress = 66
                        binding.progressBar.visibility = View.VISIBLE
                    }
                    2 -> {
                        binding.progressBar.progress = 100
                        binding.progressBar.visibility = View.VISIBLE
                        onBoardingConcluded(false)
                    }
                }
            }
        })

        /* back, continue, submit */
        viewModel.clickedAction.observe(this) { action ->
            manageActions(action)
        }

        viewModel.successData.observe(this) { data ->
            if (data != null) {
                manageActions(CONTINUE)
            }
        }
    }

    private fun manageActions(action : Int) {
        when (action) {
            SKIP -> {
                showSkipDialog()
            }
            CONTINUE -> {
                binding.viewPager.currentItem = binding.viewPager.currentItem + 1
            }
        }
    }

    private fun showSkipDialog() {

        NoiceAlertDialog.Builder()
            .setTitle(getString(R.string.skip_on_boarding))
            .setMessage(getString(R.string.skip_on_boarding_desc))
            .setPositiveButtonText(getString(R.string.yes))
            .setListener(object : OnClickInterface<Boolean> {
                override fun dataClicked(data: Boolean, eventId: Int) {
                    if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {

                        AnalyticsBuilder.newBuilder().apply {
                            putAnalyticsKey("source", "Entertainment selection page")
                            putAnalyticsKey("motivation selected", FIND_ENTERTAINMENT)
                        }.also {
                            it.send("onboarding page skipped")
                        }

                        BaseApplication.firebaseAnalytics.setUserProperty("motivation_selected", FIND_ENTERTAINMENT)
                        onBoardingConcluded(true)
                    }
                }
            })
            .show(supportFragmentManager)
    }

    private fun onBoardingConcluded(
        isSkip: Boolean
    ) {
        if (!isSkip) {
            runnableCompleted = false
            handler.postDelayed(runnable, REDIRECTION_TIME)
        }

        viewModel.updateOnBoardingFlag(
            OnBoardingComplete(
                isOnboardingComplete = true,
                onboardingJourney = FIND_ENTERTAINMENT
            )
        ).observe(this) {
            PrefUtils.isIntroShown(true)
            isApiCallCompleted = true
            proceedForRedirection()
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        handler.removeCallbacks(runnable)
    }

    private fun proceedForRedirection() {
        if (!isApiCallCompleted || !runnableCompleted) {
            return
        }

        updateIsAppInstallFlag()

        val variant = ExperimentUtils.getExperiment("login-on-onboarding-registration", ExperimentUtils.VARIANT_ON)
        if (variant?.`is`(ExperimentUtils.VARIANT_ON).isTrue()) {
            Intent(this, LoginActivity::class.java)
        } else {
            Intent(this, ClassVariantProvider.of(HomeActivity::class.java))
        }.apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(this)
            finish()
        }
    }

    private fun updateIsAppInstallFlag() {
        /* This case handles the user attribution event 'deeplink_resolved' when user installs the
        * app first and then clicks the appsflyer link afterwards (type param in the event was going as 'install'
        * it should be 'app open'). */
        PrefUtils.isAppInstall = false
    }
}