package noice.app.modules.onboarding.fragments

import android.content.Context
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.i18n.phonenumbers.PhoneNumberUtil
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentMobileNumberBinding
import noice.app.model.eventbus.EventMessage
import noice.app.modules.onboarding.viewmodel.MobileLoginViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.ExperimentUtils
import noice.app.utils.Utils
import noice.app.utils.isTrue
import noice.app.views.SnackBarCustom
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class MobileNumberFragment : Fragment() {

    private lateinit var binding: FragmentMobileNumberBinding
    private lateinit var ctx: Context
    private val viewModel: MobileLoginViewModel by activityViewModels()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMobileNumberBinding.inflate(inflater, container, false)
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Onboarding_PhoneNumber_Input")
            putString("previousScreen", "Onboarding_Welcome_Page")
        })
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        getCountries()

        initViews()
    }

    private fun initViews() {
        viewModel.selectedCountry.observe(viewLifecycleOwner) {
            binding.countryEditText.setCountry(it)
        }

        binding.proceed.setOnClickListener {
            validateMobileNumber(true)
        }

        binding.countryEditText.setCountryChangeClick {
            navigateSafely(MobileNumberFragmentDirections.navigateToCountryChooser())
        }
    }

    override fun onResume() {
        super.onResume()

        binding.countryEditText.doAfterTextChanged {
            validateMobileNumber(false)
        }
    }

    private fun getCountries() {
        if (viewModel.countries.isNotEmpty()) {
            return
        }
        viewModel.getCountries().observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                var list = it.data.filter { country ->
                    country.supported == true
                }
                list = list.sortedBy { country ->
                    country.order
                }
                viewModel.countries.clear()
                viewModel.countries.addAll(list)
                var selectedCountry = list.find { country ->
                    country.default == true
                }
                if (selectedCountry == null) {
                    selectedCountry = list[0].apply {
                        default = true
                    }
                }
                viewModel.selectedCountry.value = selectedCountry
            }
        }
    }

    private fun validateMobileNumber(isClick: Boolean) {

        val number = binding.countryEditText.text

        if (number.isEmpty()) {
            if (isClick) {
                showSnackBar(getString(R.string.please_fill_mobile_number_first))
            }
            binding.countryEditText.error = getString(R.string.mobile_numer_is_required)
            return
        } else {
            var isValid = false
            try {
                val phoneUtils = PhoneNumberUtil.getInstance()
                val phoneNumber = phoneUtils.parse(number, viewModel.selectedCountry.value?.id)
                isValid = phoneUtils.isValidNumber(phoneNumber)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            if (!isValid) {
                if (isClick) {
                    showSnackBar(getString(R.string.make_sure_format))
                }
                binding.countryEditText.error = getString(R.string.mobile_number_formate_error)
                return
            } else {
                binding.countryEditText.error = null
            }
        }

        if (isClick) {
            if (!viewModel.checkLoginCounter()) {
                viewModel.displayCaptcha("phoneNumberPage") { result ->
                    if (result.proceed) {
                        redirectUser(number, result.token)
                    }
                    if (!result.displayMessage.isNullOrEmpty()) {
                        showSnackBar(result.displayMessage)
                    }
                }
            } else {
                redirectUser(number)
            }
        }
    }

    private fun redirectUser(number: String, captchaToken: String? = null) {
        EventBus.getDefault().post(EventMessage(null, Constants.STOP_AUDIO_SERVICE))
        Utils.hideKeyboard(binding.countryEditText)
        viewModel.mobileNumber = number
        val snaFlag = ExperimentUtils.getExperiment("login-on-sna-journey", ExperimentUtils.VARIANT_OFF)?.`is`(ExperimentUtils.VARIANT_ON)
        if (snaFlag.isTrue() && viewModel.selectedCountry.value?.snaSupported == true) {
            navigateSafely(MobileNumberFragmentDirections.navigateToSna(captchaToken))
        } else {
            navigateSafely(MobileNumberFragmentDirections.navigateToOtpFragment(captchaToken))
        }
    }

    private fun showSnackBar(message: String) {
        SnackBarCustom.Builder()
            .parentView(binding.root)
            .text(message)
            .show()
    }

    private fun navigateSafely(direction: NavDirections) {
        val controller = findNavController()
        if (controller.currentDestination?.id == R.id.navigationMobileNumber) {
            controller.navigate(direction)
        }
    }

    override fun onPause() {
        super.onPause()

        binding.countryEditText.removeTextChangedListener()
    }
}