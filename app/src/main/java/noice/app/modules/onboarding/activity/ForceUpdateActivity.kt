package noice.app.modules.onboarding.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import dagger.hilt.android.AndroidEntryPoint
import noice.app.databinding.ActivityForceUpdateDialogBinding

@AndroidEntryPoint
class ForceUpdateActivity : AppCompatActivity() {

    companion object {
        fun startForResult(ctx: Context, forceUpdateLauncher: ActivityResultLauncher<Intent>) {
            Intent(ctx, ForceUpdateActivity::class.java).apply {
                forceUpdateLauncher.launch(this)
            }
            (ctx as AppCompatActivity).overridePendingTransition(0,0)
        }
    }

    private lateinit var binding: ActivityForceUpdateDialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityForceUpdateDialogBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initViews()
    }

    private fun initViews() {
        binding.txtUpdate.setOnClickListener {
            setResult(Activity.RESULT_OK)
            finish()
            overridePendingTransition(0,0)
        }
    }

    override fun onBackPressed() {
        setResult(Activity.RESULT_CANCELED)
        finish()
        overridePendingTransition(0,0)
    }
}