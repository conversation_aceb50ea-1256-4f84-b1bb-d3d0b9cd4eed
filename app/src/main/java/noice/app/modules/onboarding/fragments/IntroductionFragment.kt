package noice.app.modules.onboarding.fragments

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import noice.app.databinding.FragmentIntroductionBinding
import noice.app.modules.onboarding.viewmodel.NewOnBoardingViewModel
import noice.app.utils.AnalyticsBuilder

class IntroductionFragment : Fragment() {

    companion object {
        fun newInstance() = IntroductionFragment()
    }

    private lateinit var binding : FragmentIntroductionBinding
    private val viewModel : NewOnBoardingViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentIntroductionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {

        AnalyticsBuilder.newBuilder().apply {
            putAnalyticsKey("source", "Onboarding_Opening_Page")
        }.also {
            it.send("onboarding page opened")
        }

        binding.startBtn.setOnClickListener {

            AnalyticsBuilder.oldBuilder().apply {
                putAnalyticsKey("source", "Onboarding_Opening_Page")
            }.also {
                it.send("onboarding start clicked")
            }

            viewModel.setSuccessData()
        }
    }
}