package noice.app.modules.onboarding.models

import android.os.Parcelable
import androidx.room.Entity
import kotlinx.parcelize.Parcelize
@Entity(tableName = "event_activation",primaryKeys = ["userId"])
@Parcelize
data class EventActivation(var userId:String="", var d1_30_reached:Int= 0,var aha_reached:Int = 0,var habit_reached:Int = 0, var listenCount:Int=0, var tslMin:Long=0L): Parcelable {
    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (other is EventActivation) {
            return other.userId == userId
        }
        return false
    }

    override fun hashCode(): Int {
        return userId.hashCode() ?: 0
    }
}