package noice.app.modules.onboarding.helper

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.view.PKeys
import androidx.lifecycle.lifecycleScope
import com.google.android.recaptcha.Recaptcha
import com.google.android.recaptcha.RecaptchaAction
import com.google.android.recaptcha.RecaptchaClient
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.listner.CommonListener
import noice.app.utils.AnalyticsUtil
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.putAnalyticsKey

class ReCaptchaHelper(
    private val activity: AppCompatActivity
) {

    private var recaptchaClient: RecaptchaClient? = null

    init {
        initialise()
    }

    private fun initialise(commonListener: CommonListener<Boolean>? = null) {
        activity.lifecycleScope.launch {
            Recaptcha.getClient(activity.application, PKeys.captcha_site_key.toString())
                .onSuccess {
                    recaptchaClient = it
                    commonListener?.onResult(true)
                }
                .onFailure {
                    it.printStackTrace()
                    commonListener?.onResult(false)
                }
        }
    }

    private fun checkAndInitialiseIfNot(commonListener: CommonListener<Boolean>) {
        if (recaptchaClient == null) {
            initialise(commonListener)
        } else {
            commonListener.onResult(true)
        }
    }

    fun displayCaptcha(source: String, phoneNumber: String,  completeListener: CommonListener<ReCaptchaResponse>) {
        checkAndInitialiseIfNot { initialised ->
            if (initialised) {
                AnalyticsUtil.sendEvent("recaptcha_requested", Bundle().apply {
                    putAnalyticsKey("loginType", "phone")
                    putAnalyticsKey("phoneNumber", phoneNumber)
                    putAnalyticsKey("networkType", NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()))
                    putAnalyticsKey("source", source)
                    putAnalyticsKey("numberOfAttempts", PrefUtils.loginCounter)
                })
                activity.lifecycleScope.launch {
                    recaptchaClient?.execute(RecaptchaAction.LOGIN)?.onSuccess { token ->
                        if (token.isNotEmpty()) {
                            sendReCaptchaResponse(phoneNumber = phoneNumber, source = source, status = "success")
                            completeListener.onResult(ReCaptchaResponse(true, token, null, null))
                        } else {
                            sendReCaptchaResponse(phoneNumber = phoneNumber, source = source, status = "failure", exception = activity.getString(R.string.recaptcha_token_not_received))
                            completeListener.onResult(
                                ReCaptchaResponse(
                                    false,
                                    null,
                                    activity.getString(R.string.check_before_verification),
                                    null
                                )
                            )
                        }
                    }?.onFailure {
                        sendReCaptchaResponse(phoneNumber = phoneNumber, source = source, status = "failure", exception = it.message)
                        completeListener.onResult(
                            ReCaptchaResponse(
                                true,
                                null,
                                null,
                                it.message.toString()
                            )
                        )
                    }
                }
            } else {
                val message = activity.getString(R.string.recaptcha_client_not_initalised)
                sendReCaptchaResponse(phoneNumber = phoneNumber, source = source, status = "failure", exception = message)
                completeListener.onResult(
                    ReCaptchaResponse(
                        true,
                        null,
                        null,
                        message
                    )
                )
            }
        }
    }

    private fun sendReCaptchaResponse(phoneNumber: String, source: String, status: String, exception: String? = null) {
        AnalyticsUtil.sendEvent("recaptcha_response", Bundle().apply {
            putAnalyticsKey("loginType", "phone")
            putAnalyticsKey("status", status)
            putAnalyticsKey("phoneNumber", phoneNumber)
            putAnalyticsKey("exception", exception)
            putAnalyticsKey("networkType", NetworkUtils.getNetworkType(BaseApplication.getBaseAppContext()))
            putAnalyticsKey("source", source)
            putAnalyticsKey("numberOfAttempts", PrefUtils.loginCounter)
        })
    }

    data class ReCaptchaResponse(
        val proceed: Boolean,
        val token: String?,
        val displayMessage: String?,
        val errorMessage: String?
    )
}