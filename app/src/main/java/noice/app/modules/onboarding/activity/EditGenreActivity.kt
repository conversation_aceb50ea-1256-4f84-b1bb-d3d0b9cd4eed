package noice.app.modules.onboarding.activity

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import com.google.android.flexbox.*
import noice.app.databinding.ActivityEditGenreBinding
import noice.app.modules.onboarding.adapter.MyGenreAdapter
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.UserPreferenceRequest
import noice.app.rest.ResponseStatus
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.model.eventbus.EventMessage
import noice.app.modules.profile.fragment.userprofile.UserProfileViewModel
import noice.app.utils.*

@AndroidEntryPoint
class EditGenreActivity : AppCompatActivity() {

    companion object {
        private const val GENRE_LIST = "GENRE_LIST"

        fun start(ctx : Context, genreList: ArrayList<Genre> = ArrayList()) {
            Intent(ctx, EditGenreActivity::class.java).apply {
                if (genreList.isNotEmpty()) {
                    putParcelableArrayListExtra(GENRE_LIST, genreList)
                }
                ctx.startActivity(this)
            }
        }
    }

    private lateinit var binding : ActivityEditGenreBinding
    private lateinit var adapter : MyGenreAdapter
    private var genreList = ArrayList<Genre>()
    private var selectedGenreList = ArrayList<Genre>()
    private val viewModel : UserProfileViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditGenreBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this)

        getDataFromIntent()

        setToolbar()

        initViews()

        binding.errorView.showLoading()

        getAllGenre()

        if (selectedGenreList.isEmpty()) {
            getMyGenre()
        }
    }

    private fun getDataFromIntent() {
        selectedGenreList = intent?.extras?.getParcelableArrayList(GENRE_LIST) ?: ArrayList()
    }

    private fun setToolbar() {
        binding.toolbar.setBackClick {
            onBackPressed()
        }
    }

    private fun initViews() {
        val layoutManager = SafeFlexboxLayoutManager(this)
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START
        binding.genreRecyclerView.layoutManager = layoutManager

        adapter = MyGenreAdapter(genreList, MyGenreAdapter.PILL_VIEW)
        binding.genreRecyclerView.adapter = adapter

        binding.saveBtn.setOnClickListener {
            when (selectedGenreList.size) {
                0 -> {
                    Utils.showSnackBar(binding, getString(R.string.choose_the_topic_you_want_to_listen_to_first))
                    return@setOnClickListener
                }
                1 -> {
                    Utils.showSnackBar(binding, getString(R.string.choose_two_more_topic))
                    return@setOnClickListener
                }
                2 -> {
                    Utils.showSnackBar(binding, getString(R.string.choose_one_more_topic))
                    return@setOnClickListener
                }
            }

            val selectedGenreIds = getSelectedGenre()
            binding.errorView.showLoading()
            updateGenre(selectedGenreIds)
        }
    }

    private fun getAllGenre() {
        viewModel.profileUseCase.getGenre("weight").observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (!it.data.isNullOrEmpty()) {
                    genreList.clear()
                    genreList.addAll(it.data)

                    if (selectedGenreList.isNotEmpty()) {
                        handleSelectedGenreResponse()
                    } else {
                        adapter.notifyDataSetChanged()
                    }

                    binding.errorView.hide()
                } else {
                    binding.errorView.showError(it.message)
                }
            } else {
                binding.errorView.showError(it.message, type = "onboarding", errorName = it.message)
            }
        }
    }

    private fun getMyGenre() {
        viewModel.profileUseCase.getUserPreferences("genre", null).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                selectedGenreList.clear()
                selectedGenreList.addAll(it.data?.data ?: ArrayList())

                if (genreList.isNotEmpty()) {
                    handleSelectedGenreResponse()
                }
            }
        }
    }

    private fun handleSelectedGenreResponse() {
        selectedGenreList.forEach { selectedGenre ->
            genreList.find { genre ->
                selectedGenre.id == genre.id
            }?.let { genre ->
                genre.isSelected = true
            }
        }
        adapter.notifyDataSetChanged()
    }

    private fun getSelectedGenre() : List<String> {
        return selectedGenreList.map {
            it.id ?: ""
        }
    }

    private fun updateGenre(selectedGenreIds : List<String>) {
        viewModel.profileUseCase.updateUserPreferences(UserPreferenceRequest("genre", selectedGenreIds)).observe(this) {
            if (it.status == ResponseStatus.SUCCESS) {
                binding.errorView.hide()
                EventBus.getDefault().post(EventMessage(selectedGenreList, Constants.GENRE_UPDATED))

                PrefUtils.selectedGenre = selectedGenreList
                MoEngageAnalytics.setAttributes(this, isLoggedIn = true)

                AnalyticsUtil.sendEvent("genres_selected", Bundle().apply {
                    putStringArrayList(
                        "genresTitle",
                        ArrayList(selectedGenreList.map { genre ->
                            genre.name ?: ""
                        })
                    )
                    putStringArrayList("genreSelected", selectedGenreIds as ArrayList)
                })

                finish()
            } else {
                Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGenreSelect(event: Genre) {
        val index = genreList.indexOf(event)
        if (index != -1) {
            adapter.notifyItemChanged(index)

            if (selectedGenreList.contains(event)) {
                if (!event.isSelected) {
                    selectedGenreList.remove(event)
                }
            } else if (event.isSelected) {
                selectedGenreList.add(event)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}