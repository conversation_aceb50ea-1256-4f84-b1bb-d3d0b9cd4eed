package noice.app.modules.onboarding.activity

import android.app.Activity
import android.graphics.Color
import android.graphics.Typeface
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.ActivityCompleteUserNameBinding
import noice.app.listner.OnOkay
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.modules.profile.PolicyTermsActivity
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.*

@AndroidEntryPoint
class CompleteUserNameActivity : AppCompatActivity() {

    private lateinit var binding : ActivityCompleteUserNameBinding
    private val viewModel : OnBoardingViewModel by viewModels()
    private var isUserNameAvailable = false
    private val typingDelay = TypingDelay()
    private val userNameRegex = Regex("^[a-z0-9_.]*$")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCompleteUserNameBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initViews()
    }

    private fun initViews() {

        binding.toolbar.setBackClick {
            setResult(Activity.RESULT_CANCELED)
            finish()
        }

        binding.userNameEditText.filters += LowerCaseInputFilter()

        setPrivacySpan()

        setTncSpan()

        binding.submitButton.setOnClickListener {

            Utils.hideKeyboard(binding.submitButton)

            if (binding.userNameEditText.text.toString().trim().isEmpty() ||
                binding.userNameEditText.text.toString().length <= 3) {
                Toast.makeText(this, getString(R.string.empty_user_name), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            if (!isUserNameAvailable) {
                return@setOnClickListener
            }

            if (!binding.acceptTncCheckBox.isChecked) {
                Toast.makeText(this, getString(R.string.tnc_acceptance), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            proceedToUploadData()
        }

        binding.userNameEditText.addTextChangedListener(object : TextWatcher {

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                typingDelay.typingHandler.removeCallbacks(typingDelay)
            }

            override fun afterTextChanged(s: Editable?) {
                if(s.toString().isNotEmpty()) {
                    typingDelay.lastTextEdit = System.currentTimeMillis()
                    typingDelay.typingHandler.postDelayed(typingDelay, typingDelay.delay)
                } else {
                    setUserNameDefault()
                }
            }
        })

        typingDelay.setCallback(object : OnOkay<Boolean> {
            override fun okay(isOkay: Boolean) {
                if (isOkay) {
                    val text = binding.userNameEditText.text.toString().trim()
                    if(text.length in 4..15) {
                        if(text.matches(userNameRegex)) {
                            binding.userProgressBar.visibility = View.VISIBLE
                            binding.userNameStatus.visibility = View.GONE
                            binding.nameDisclaimer.setTextColor(ContextCompat.getColor(this@CompleteUserNameActivity, R.color.whitish_grey))
                            binding.nameDisclaimer.text = getString(R.string.check_username_availability)
                            validateUserName(binding.userNameEditText.text.toString().trim())
                        } else {
                            setUserNameError(getString(R.string.username_char_error))
                        }
                    } else {
                        setUserNameError(getString(R.string.username_length_error))
                    }
                }
            }
        })
    }

    private fun proceedToUploadData() {

        binding.errorView.showLoading()

        val map = HashMap<String, RequestBody>()

        binding.userNameEditText.text.toString().let {
            map["userName"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
        }

        updateUserDetails(map)
    }

    private fun updateUserDetails(map: HashMap<String, RequestBody>) {
        viewModel.updateUserDetails(null, map).observe(this) {

            if (it?.status == ResponseStatus.SUCCESS && it.data == true) {
                PrefUtils.userDetails?.apply {
                    userName = binding.userNameEditText.text.toString()
                    PrefUtils.userDetails = this
                }
                setResult(Activity.RESULT_OK)
                finish()
            } else {
                Toast.makeText(this, it.message ?: "Something went wrong!", Toast.LENGTH_LONG)
                    .show()
            }

            binding.errorView.hide()
        }
    }

    private fun validateUserName(name: String) {
        viewModel.validateUserName(name).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data == true) {
                isUserNameAvailable = true
                binding.nameDisclaimer.setTextColor(
                    ContextCompat.getColor(
                        this,
                        R.color.whitish_grey
                    )
                )
                binding.nameDisclaimer.text = getString(R.string.username_available)
                binding.userNameStatus.setImageResource(R.drawable.ic_tick_green)
            } else {
                setUserNameError()
            }
            binding.userProgressBar.visibility = View.GONE
            binding.userNameStatus.visibility = View.VISIBLE
        }
    }

    private fun setPrivacySpan() {
        val tnc = getString(R.string.privacy_policy_text)
        val span = SpannableString(tnc)
        span.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, R.color.dull_yellow)),
            32,
            50,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                PolicyTermsActivity.start(this@CompleteUserNameActivity, PolicyTermsActivity.TYPE_POLICY,"Username_Completion")
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.drawableState = null
            }
        }
        span.setSpan(clickableSpan, 32, 50, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.privacyPolicy.highlightColor = Color.TRANSPARENT
        binding.privacyPolicy.movementMethod = LinkMovementMethod.getInstance()
        binding.privacyPolicy.text = span
    }

    private fun setTncSpan() {
        val tnc = getString(R.string.accept_tnc)
        val span = SpannableString(tnc)
        span.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, R.color.dull_yellow)),
            19,
            37,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                PolicyTermsActivity.start(this@CompleteUserNameActivity, PolicyTermsActivity.TYPE_TERMS_AND_CONDITION,"Username_Completion")
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.drawableState = null
            }
        }
        span.setSpan(StyleSpan(Typeface.BOLD),19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(clickableSpan, 19, 37, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.acceptTncCheckBox.highlightColor = Color.TRANSPARENT
        binding.acceptTncCheckBox.movementMethod = LinkMovementMethod.getInstance()
        binding.acceptTncCheckBox.text = span
    }

    private fun setUserNameError(error: String? = null) {
        isUserNameAvailable = false
        binding.nameDisclaimer.setTextColor(ContextCompat.getColor(this, R.color.red))
        binding.nameDisclaimer.text = error ?: getString(R.string.username_not_available)
        binding.userNameStatus.setImageResource(R.drawable.ic_cross_red)
        binding.userNameStatus.visibility = View.VISIBLE
    }

    private fun setUserNameDefault(isUserNameAvailable : Boolean = false) {
        this.isUserNameAvailable = isUserNameAvailable
        binding.userNameStatus.visibility = View.GONE
        binding.userProgressBar.visibility = View.GONE
        binding.nameDisclaimer.text = getString(R.string.name_disclaimer)
    }
}