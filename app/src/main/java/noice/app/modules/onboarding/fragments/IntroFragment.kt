package noice.app.modules.onboarding.fragments

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import noice.app.R
import noice.app.databinding.FragmentIntroBinding

class IntroFragment : Fragment() {

    companion object {

        private const val INTRO_POSITION = "INTRO_POSITION"

        fun newInstance(position: Int) =
            IntroFragment().apply {
                arguments = Bundle().apply {
                    putInt(INTRO_POSITION, position)
                }
            }
    }

    private var position = 0
    private lateinit var binding: FragmentIntroBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            position = it.getInt(INTRO_POSITION)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentIntroBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {
        when (position) {
            0 -> {
                binding.introImage.setImageResource(R.drawable.ic_dengerin)
                binding.introHeading.text = getString(R.string.intro_screen1_heading)
                binding.introDesc.text = getString(R.string.intro_screen1_desc)
            }
            1 -> {
                binding.introImage.setImageResource(R.drawable.ic_ngobrol)
                binding.introHeading.text = getString(R.string.intro_screen2_heading)
                binding.introDesc.text = getString(R.string.intro_screen2_desc)
            }
            2 -> {
                binding.introImage.setImageResource(R.drawable.ic_konten)
                binding.introHeading.text = getString(R.string.intro_screen3_heading)
                binding.introDesc.text = getString(R.string.intro_screen3_desc)
            }
        }
    }
}