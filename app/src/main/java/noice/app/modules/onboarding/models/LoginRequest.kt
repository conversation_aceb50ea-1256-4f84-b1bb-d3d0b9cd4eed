package noice.app.modules.onboarding.models

import noice.app.utils.PrefUtils
import noice.app.utils.Utils

data class LoginRequest(
    val authToken: String?,
    val type: String,
    val fcmToken: String?,
    val otp: String?,
    val otpId: String?,
    val deviceId: String?,
    val snaId: String?,
    val referralUserId:String?,
    val appsFlyerId: String?
) {
    constructor(
        type: String,
        otp: String?,
        otpId: String?,
        appsFlyerId: String?
    ) : this(null, type, PrefUtils.fcmToken, otp, otpId, Utils.getDeviceId(), null, PrefUtils.referralUserId, appsFlyerId)

    constructor(
        type: String,
        snaId: String?,
        isSna: Boolean,
        appsFlyerId: String?
    ) : this(null, type, PrefUtils.fcmToken, null, null, Utils.getDeviceId(), snaId, PrefUtils.referralUserId, appsFlyerId)

    constructor(
        authToken: String?,
        type: String,
        appsFlyerId: String?
    ) : this(authToken, type, PrefUtils.fcmToken, null, null, Utils.getDeviceId(), null, PrefUtils.referralUserId,appsFlyerId)
}