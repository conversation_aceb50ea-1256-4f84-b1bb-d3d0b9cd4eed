package noice.app.modules.onboarding.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.datepicker.MaterialDatePicker
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentBirthdayBinding
import noice.app.modules.onboarding.viewmodel.CompleteProfileViewModel
import noice.app.utils.DateUtils
import noice.app.views.SnackBarCustom
import java.util.Calendar
import java.util.Date

@AndroidEntryPoint
class BirthdayFragment : Fragment() {

    companion object {
        private const val ACTION_TAG_CLEAR = "CLEAR"
        private const val ACTION_TAG_ERROR = "ERROR"

        fun newInstance() = BirthdayFragment()
    }

    private lateinit var binding: FragmentBirthdayBinding
    private val viewModel: CompleteProfileViewModel by activityViewModels()
    private var selectedDate: Date? = null
    private var datePicker: MaterialDatePicker<Long>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBirthdayBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {

        binding.action.setOnClickListener {
            if (binding.action.tag == ACTION_TAG_CLEAR) {
                setDateText(null)
                showClearButton(false)
            }
        }

        binding.birthdayField.setOnClickListener {
            showDatePicker()
        }

        binding.proceed.setOnClickListener {

            if (binding.placeholder.text.isNullOrEmpty() || selectedDate == null) {
                showError(getString(R.string.no_date_entered_yet))
                showSnackBar(getString(R.string.fill_the_date_first))
                return@setOnClickListener
            }

            if (!validateAge(true)) {
                return@setOnClickListener
            }

            viewModel.birthDate = selectedDate

            findNavController().navigate(BirthdayFragmentDirections.navigateToUsername())
        }
    }

    private fun showDatePicker() {
        datePicker = MaterialDatePicker.Builder.datePicker()
            .setTheme(R.style.ThemeOverlay_App_MaterialCalendar4)
            .setSelection(selectedDate?.time)
            .build()
        datePicker?.addOnPositiveButtonClickListener {
            selectedDate = Date(it)
            if (validateAge(false)) {
                showError(null)
                showClearButton(true)
            }
            setDateText(it)
        }
        datePicker?.show(childFragmentManager, "DatePicker")
    }

    private fun setDateText(dateMs: Long?) {
        binding.placeholder.apply {
            text = if (dateMs != null) {
                setTextColor(ContextCompat.getColor(requireContext(), R.color.neutral_90))
                DateUtils.formatDateToLocalMonthName(Date(dateMs))
            } else {
                setTextColor(ContextCompat.getColor(requireContext(), R.color.neutral_70))
                getString(R.string.choose_a_date)
            }
        }
    }

    private fun showClearButton(show: Boolean) {
        binding.action.apply {
            visibility = if (show) {
                tag = ACTION_TAG_CLEAR
                setImageResource(R.drawable.ic_clear_textfield)
                View.VISIBLE
            } else {
                tag = null
                View.GONE
            }
        }
    }

    private fun showError(error: String?) {
        binding.errorText.text = error
        if (!error.isNullOrEmpty()) {
            binding.action.tag = ACTION_TAG_ERROR
            binding.hint.setTextColor(ContextCompat.getColor(requireContext(), R.color.pinkish_red))
            binding.action.setImageResource(R.drawable.ic_error_text_input)
            binding.action.visibility = View.VISIBLE
            binding.errorText.visibility = View.VISIBLE
            binding.line.setBackgroundResource(R.color.pinkish_red)
        } else {
            binding.hint.setTextColor(ContextCompat.getColor(requireContext(), R.color.neutral_90))
            showClearButton(!binding.placeholder.text.isNullOrEmpty())
            binding.errorText.visibility = View.GONE
            binding.line.setBackgroundResource(R.color.neutral_grey)
        }
    }

    private fun validateAge(isClick: Boolean): Boolean {
        val age = getAge()
        if (age < 13) {
            showError(getString(R.string.well_you_are_not_13_yet))
            if (isClick) {
                showSnackBar(getString(R.string.you_must_be_13_yrs_old))
            }
            return false
        }
        return true
    }

    private fun getAge(): Int {
        selectedDate ?: return 0
        val dob = Calendar.getInstance()
        dob.time = selectedDate!!
        val today = Calendar.getInstance()

        var age = today[Calendar.YEAR] - dob[Calendar.YEAR]
        if (today[Calendar.DAY_OF_YEAR] < dob[Calendar.DAY_OF_YEAR]) {
            age--
        }
        return age
    }

    private fun showSnackBar(message: String) {
        SnackBarCustom.Builder()
            .parentView(binding.root)
            .text(message)
            .show()
    }

    override fun onDestroyView() {
        datePicker?.dismiss()
        super.onDestroyView()
    }
}