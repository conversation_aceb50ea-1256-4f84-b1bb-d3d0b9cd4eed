package noice.app.modules.onboarding.fragments

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.JustifyContent
import noice.app.R
import noice.app.databinding.FragmentChooseGenreBinding
import noice.app.model.eventbus.EventMessage
import noice.app.modules.onboarding.activity.OnBoardingActivity
import noice.app.modules.onboarding.adapter.MyGenreAdapter
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.UserPreferenceRequest
import noice.app.modules.onboarding.viewmodel.NewOnBoardingViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class ChooseGenreFragment : Fragment() {

    companion object {
        const val FIND_ENTERTAINMENT = "genre"
        fun newInstance() = ChooseGenreFragment()
    }

    private val viewModel: NewOnBoardingViewModel by activityViewModels()

    private lateinit var ctx: Context
    private lateinit var binding: FragmentChooseGenreBinding
    private var adapter: MyGenreAdapter? = null
    private val selectedGenre = ArrayList<Genre>()
    private var genreList = ArrayList<Genre>()

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChooseGenreBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        getAllGenre()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        genreList.clear()
        selectedGenre.clear()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGenreSelect(event: Genre) {
        val index = genreList.indexOf(event)
        if (index != -1) {
            adapter?.notifyItemChanged(index)

            if (selectedGenre.contains(event)) {
                if (!event.isSelected) {
                    selectedGenre.remove(event)
                }
            } else if (event.isSelected) {
                selectedGenre.add(event)
            }
        }
    }

    private fun initViews() {

        AnalyticsBuilder.newBuilder().apply {
            putAnalyticsKey("source", "Entertainment_Selection_Page")
        }.also {
            it.send("onboarding page opened")
        }

        setRecycler()

        binding.skipButton.setOnClickListener {
            viewModel.setClickedAction(OnBoardingActivity.SKIP)
        }

        binding.continueBtn.setOnClickListener {
            when (selectedGenre.size) {
                0 -> {
                    Utils.showSnackBar(
                        binding,
                        getString(R.string.choose_the_topic_you_want_to_listen_to_first)
                    )
                    return@setOnClickListener
                }
                1 -> {
                    Utils.showSnackBar(binding, getString(R.string.choose_two_more_topic))
                    return@setOnClickListener
                }
                2 -> {
                    Utils.showSnackBar(binding, getString(R.string.choose_one_more_topic))
                    return@setOnClickListener
                }
            }

            updateGenre()
        }
    }

    private fun setRecycler() {
        val layoutManager = SafeFlexboxLayoutManager(ctx)
        layoutManager.flexWrap = FlexWrap.WRAP
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START
        binding.genreRecyclerView.layoutManager = layoutManager

        adapter = MyGenreAdapter(genreList, MyGenreAdapter.PILL_VIEW)
        binding.genreRecyclerView.adapter = adapter
    }

    private fun getAllGenre() {
        binding.errorView.showLoading()

        viewModel.getGenre("weight").observe(viewLifecycleOwner) {
            binding.errorView.hide()

            if (it?.status == ResponseStatus.SUCCESS) {
                if (!it.data.isNullOrEmpty()) {
                    genreList.clear()
                    genreList.addAll(it.data)

                    if (selectedGenre.isNotEmpty()) {
                        handleSelectedGenreResponse()
                    } else {
                        adapter?.notifyDataSetChanged()
                    }
                } else {
                    binding.errorView.showError(it.message)
                }
            } else {
                binding.errorView.showError(it.message, type = "onboarding", errorName = it.message)
            }
        }
    }

    private fun handleSelectedGenreResponse() {
        selectedGenre.forEach { selectedGenre ->
            genreList.find { genre ->
                selectedGenre.id == genre.id
            }?.let { genre ->
                genre.isSelected = true
            }
        }
        adapter?.notifyDataSetChanged()
    }

    private fun updateGenre() {

        binding.errorView.showLoading()

        val selectedGenreIds = selectedGenre.map { genre ->
            genre.id ?: ""
        }

        viewModel.updateUserPreference(UserPreferenceRequest("genre", selectedGenreIds))
            .observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS) {
                    binding.errorView.hide()
                    EventBus.getDefault().post(EventMessage(selectedGenre, Constants.GENRE_UPDATED))
                    val genres = selectedGenre.map { genre ->
                        genre.name ?: ""
                    }

                    AnalyticsUtil.sendEvent("genres_selected", Bundle().apply {
                        putStringArrayList(
                            "genresTitle",
                            ArrayList(genres)
                        )
                        putStringArrayList("genreSelected", ArrayList(selectedGenreIds))
                    })

                    AnalyticsBuilder.newBuilder().apply {
                        putAnalyticsKey("option selected", ArrayList(genres))
                        putAnalyticsKey("total option selected", genres.size)
                    }.send("onboarding selection finished")

                    viewModel.setGenreData(selectedGenre)
                } else {
                    binding.errorView.hide()
                    Toast.makeText(ctx, it.message, Toast.LENGTH_SHORT).show()
                }
            }
    }
}