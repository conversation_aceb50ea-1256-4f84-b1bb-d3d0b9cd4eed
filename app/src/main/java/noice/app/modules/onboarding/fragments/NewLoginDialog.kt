package noice.app.modules.onboarding.fragments

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.view.PKeys
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.appsflyer.AppsFlyerLib
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.GraphRequest
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.crashlytics.ktx.setCustomKeys
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.databinding.FragmentNewLoginDialogBinding
import noice.app.model.LoginDialogData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.BaseActivity
import noice.app.modules.onboarding.activity.CompleteProfileActivity
import noice.app.modules.onboarding.activity.LoginActivity
import noice.app.modules.onboarding.activity.MobileLoginActivity
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.modules.profile.PolicyTermsActivity
import noice.app.modules.profile.viewmodel.ProfileViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.DATA_VERSION_OLD
import noice.app.utils.FacebookAnalytics
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.unwrap
import noice.app.utils.ripplespan.RippleClickableSpan
import noice.app.utils.ripplespan.RippleLinkMovementMethod
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

@AndroidEntryPoint
class NewLoginDialog : BottomSheetDialogFragment() {

    companion object {
        const val LOGIN_TYPE_FACEBOOK = "facebook"
        private const val LOGIN_TYPE_GOOGLE = "google"
        private const val TAG = "login_dialog"

        private const val ID = "ID"
        private const val ACTION_NAME = "ACTION_NAME"
        private const val SOURCE = "SOURCE"
        private const val DIALOG_DATA = "DIALOG_DATA"

        fun show(
            fragmentManager: FragmentManager,
            id: String? = null,
            actionName: String? = null,
            source: String?,
            loginDialogData: LoginDialogData?
        ) = NewLoginDialog().apply {
            arguments = Bundle().apply {
                putString(ID, id)
                putString(ACTION_NAME, actionName)
                putString(SOURCE, source)
                putParcelable(DIALOG_DATA, loginDialogData)
            }
            val ft = fragmentManager.beginTransaction()
            ft.add(this, TAG)
            ft.commitAllowingStateLoss()
        }
    }

    private val viewModel: OnBoardingViewModel by viewModels()
    private val profileViewModel : ProfileViewModel by viewModels()

    private lateinit var ctx : Context
    private lateinit var binding : FragmentNewLoginDialogBinding
    private lateinit var mGoogleSignInClient: GoogleSignInClient
    private var facebookCallbackManager: CallbackManager? = null
    private var isSelf = false
    private var userEmail = ""
    private var id:String? = ""
    private var source:String? = ""
    private var actionName:String? = ""
    private var loginDialogData: LoginDialogData? = null
    private lateinit var gmailResultLauncher: ActivityResultLauncher<Intent>

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme2)

        id = arguments?.getString(ID) ?: ""
        source = arguments?.getString(SOURCE) ?: ""
        actionName = arguments?.getString(ACTION_NAME) ?: ""
        loginDialogData = arguments?.parcelable(DIALOG_DATA)
        EventBus.getDefault().register(this)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet =
                bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout?
            if (bottomSheet != null) {
                val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)
                bottomSheetBehavior.peekHeight = bottomSheet.height
            }
        }
        return dialog
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentNewLoginDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initGoogleLogin()

        initFacebookLogin()

        initViews()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {
        if (!isSelf){
            isSelf = false
            ctx.unwrap().let { activity ->
                if (activity is BaseActivity) {
                    activity.dismissLoginDialog()
                }
            }
        }

        if (!isDetached || !isRemoving || isAdded) {
            dismissAllowingStateLoss()
        }
    }

    private fun initGoogleLogin() {

        gmailResultLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                try {
                    val account = task.getResult(ApiException::class.java)
                    binding.errorView.showLoading()
                    account.idToken?.let { accessToken ->
                        userEmail = account.email ?: ""
                        login(accessToken, LOGIN_TYPE_GOOGLE)
                    }
                } catch (e: ApiException) {
                    handleLoginException(
                        LoginActivity.LOGIN_TYPE_GOOGLE,
                        "onActivityResult : " + e.message
                    )
                    AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                        putString("loginType", LoginActivity.LOGIN_TYPE_GOOGLE)
                        putString("email", userEmail)
                        putString("networkType", NetworkUtils.getNetworkType(ctx))
                    })
                }
            }
        }

        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken(PKeys.google_client_id.toString())
            .requestEmail()
            .build()

        mGoogleSignInClient = GoogleSignIn.getClient(ctx, gso)

        binding.googleLogin.setOnClickListener {
            val signInIntent = mGoogleSignInClient.signInIntent
            gmailResultLauncher.launch(signInIntent)
        }
    }

    private fun initFacebookLogin() {

        binding.facebookLoginOriginal.setPermissions("email", "public_profile")
        facebookCallbackManager = CallbackManager.Factory.create()

        facebookCallbackManager?.let { callbackManager ->
            binding.facebookLoginOriginal.registerCallback(
                callbackManager,
                object : FacebookCallback<LoginResult> {
                    override fun onSuccess(result: LoginResult) {
                        binding.errorView.showLoading()

                        result?.let {
                            getEmailFromFacebook(it)
                        }

                        result?.accessToken?.let { accessToken ->
                            login(accessToken.token, LOGIN_TYPE_FACEBOOK)
                        }
                    }

                    override fun onCancel() {
                        handleLoginException(LoginActivity.LOGIN_TYPE_FACEBOOK, null, false)
                    }

                    override fun onError(error: FacebookException) {
                        error.printStackTrace()
                        handleLoginException(LoginActivity.LOGIN_TYPE_FACEBOOK, "FacebookCallback onError : " + error.message)
                        AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                            putString("loginType", LoginActivity.LOGIN_TYPE_FACEBOOK)
                            putString("email", userEmail)
                            putString("networkType", NetworkUtils.getNetworkType(ctx))
                        })
                    }
                })
        }

        binding.facebookLogin.setOnClickListener {
            if(!AccessToken.getCurrentAccessToken()?.token.isNullOrEmpty()) {
                login(AccessToken.getCurrentAccessToken()?.token.toString(), LoginActivity.LOGIN_TYPE_FACEBOOK)
            } else {
                binding.facebookLoginOriginal.performClick()
            }
        }
    }

    private fun initViews() {

        val tnc = getString(R.string.terms_condition)
        val span = SpannableString(tnc)

        val clickSpan = object : RippleClickableSpan() {
            override fun onSpanClick(widget: View) {
                PolicyTermsActivity.start(ctx, PolicyTermsActivity.TYPE_TERMS_AND_CONDITION,"Onboarding_Welcome_Page")
            }

            override fun updateConfig(rippleTextPaint: RippleTextPaint) {
                super.updateConfig(rippleTextPaint)

                rippleTextPaint.underLine = true
            }
        }
        span.setSpan(clickSpan, 44, 62, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        span.setSpan(StyleSpan(Typeface.BOLD), 44, 62, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.termsAndConditions.movementMethod = RippleLinkMovementMethod.getInstance()
        binding.termsAndConditions.text = span

        binding.loginWithPhone.setOnClickListener {
            MobileLoginActivity.start(ctx, LoginChangeEvent(id, actionName))
        }

        binding.textTutup.setOnClickListener {
            dismissAllowingStateLoss()
        }

        ImageUtils.loadImageByUrl(binding.image, loginDialogData?.image, placeHolder = R.drawable.login_default_image, originalUrl = loginDialogData?.image)
        binding.title.text = loginDialogData?.text
    }

    private fun login(accessToken: String, type: String) {

        EventBus.getDefault().post(EventMessage(null, Constants.STOP_AUDIO_SERVICE))

        if (PrefUtils.appsFlyerId.isNullOrEmpty()) {
            PrefUtils.appsFlyerId = AppsFlyerLib.getInstance().getAppsFlyerUID(BaseApplication.application)
        }

        viewModel.login(LoginRequest(accessToken, type, PrefUtils.appsFlyerId), userEmail).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {

                PrefUtils.createSession(it.data, type)

                getUserPreferences()

                viewModel.fetchAndReplaceQueue()

                getSelectedGenre()

                PrefUtils.dataVersion = DATA_VERSION_OLD

                getUserDetails(type, it.data.newUser)

                PrefUtils.loginType = type
                PrefUtils.isLoggedIn = true
                PrefUtils.isLoggedInAsGuest = false

                if (it.data.newUser == true) {
                    MoEngageAnalytics.sendEvent(ctx, "user registered", "login type", type)
                } else {
                    AnalyticsUtil.sendEvent("login", Bundle().apply {
                        putString("loginType", type)
                        putString("email", "")
                    })
                    val eventValue: MutableMap<String, Any> = HashMap()
                    eventValue["af_login_method"] = type
                    AppsFlyerLib.getInstance()
                        .logEvent(BaseApplication.getBaseAppContext(), "af_login", eventValue)
                }
            } else {
                binding.errorView.hide()
                val showMessage = !it?.message.isNullOrEmpty()
                handleLoginException(type, "ApiErrorLogin : " + it?.message, showMessage)
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", type)
                    putString("email", userEmail)
                    putString("source", "Login")
                    putString("exception", it.message?:"")
                    putString("networkType", NetworkUtils.getNetworkType(ctx))
                })
            }
        }
    }

    private fun getSelectedGenre() {
        profileViewModel.getUserPreference("genre", null).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                PrefUtils.selectedGenre = ArrayList(it.data?.data)
                MoEngageAnalytics.setAttributes(ctx, isLoggedIn = true)
            }
        }
    }

    private fun getUserDetails(type: String, isNewUser : Boolean? = false) {

        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "userActions"

        viewModel.getUserDetails(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {

                PrefUtils.userDetails = it.data.data

                if (isNewUser == true) {
                    AnalyticsUtil.sendEvent("user_registered", Bundle().apply {
                        putString("loginType", type)
                        putString("email", userEmail)
                        putString("profileUserId", it.data.data?.id)
                    })
                }
                AppsFlyerLib.getInstance().setAdditionalData(HashMap<String,Any>().apply { put("userId",PrefUtils.userDetails?.id.toString()) })
                AppsFlyerLib.getInstance().setCustomerUserId(PrefUtils.userDetails?.id)
                AppsFlyerLib.getInstance().setCustomerIdAndLogSession(PrefUtils.userDetails?.id,ctx)

                AnalyticsUtil.setLoginProperties()
                Firebase.crashlytics.setCustomKeys {
                    key("userName",PrefUtils.userDetails?.userName.orEmpty())
                    key("displayName",PrefUtils.userDetails?.displayName.orEmpty())
                    key("userId",PrefUtils.userDetails?.id.orEmpty())
                }
                Firebase.crashlytics.setUserId(PrefUtils.userDetails?.id.orEmpty())
                MoEngageAnalytics.setUniqueId(ctx, PrefUtils.userDetails?.id ?: "")
                MoEngageAnalytics.setAttributes(ctx, isLoggedIn = true)
                MoEngageAnalytics.sendEvent(ctx, "logged in", "login type", type)

                if (it.data.data?.isOnboardingComplete == true) {
                    PrefUtils.onBoardingStep = 0
                    isSelf = true
                    EventBus.getDefault().post(LoginChangeEvent(id,actionName))
                } else {
                    PrefUtils.onBoardingStep = 1
                    CompleteProfileActivity.start(
                        ctx = ctx,
                        source = "Onboarding_Welcome_Page",
                        loginChangeEvent = LoginChangeEvent(id, actionName)
                    )
                }

                dismissAllowingStateLoss()
                ctx.unwrap().let { activity ->
                    if (activity is BaseActivity) {
                        activity.dismissLoginDialog()
                    }
                }
            } else if (it?.status != ResponseStatus.LOADING) {
                handleLoginException(type, "ApiErrorUserDetails : " + it?.message)
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putString("loginType", type)
                    putString("email", userEmail)
                    putString("source", "getUserDetail")
                    putString("exception", it?.message?:"")
                    putString("networkType", NetworkUtils.getNetworkType(ctx))
                })
            }

            if (it?.status != ResponseStatus.LOADING) {
                binding.errorView.hide()
            }
        }
    }

    private fun getEmailFromFacebook(loginResult : LoginResult) {
        val request = GraphRequest.newMeRequest(
            loginResult.accessToken
        ) { `object`, _ ->
            try {
                userEmail = `object`?.getString("email") ?: ""
            } catch (e : Exception) {
                e.printStackTrace()
            }
        }
        val parameters = Bundle()
        parameters.putString("fields", "email")
        request.parameters = parameters
        request.executeAsync()
    }

    private fun handleLoginException(type: String, message: String?, showMessage : Boolean = true) {

        if(BuildConfig.DEBUG && message != null) {
            Toast.makeText(ctx, message, Toast.LENGTH_LONG).show()
        }

        if(type == LoginActivity.LOGIN_TYPE_FACEBOOK) {
            if (showMessage)
                Utils.showSnackBar(ctx, getString(R.string.facebook_error_msg))
            LoginManager.getInstance().logOut()
        } else if(type == LoginActivity.LOGIN_TYPE_GOOGLE) {
            if (showMessage)
                Utils.showSnackBar(ctx, getString(R.string.google_error_msg))
            if(::mGoogleSignInClient.isInitialized) {
                mGoogleSignInClient.signOut()
            }
        }
    }

    private fun getUserPreferences() {
        viewModel.getUserPreference().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.userPreference = it.data
            }
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }
}