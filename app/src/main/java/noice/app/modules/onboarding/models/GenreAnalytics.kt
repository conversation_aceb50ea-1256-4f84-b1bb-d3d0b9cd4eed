package noice.app.modules.onboarding.models

import android.os.Parcel
import android.os.Parcelable

data class GenreAnalytics (
    val genreTitle : String?,
    val genreID : String?
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(genreTitle)
        parcel.writeString(genreID)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<GenreAnalytics> {
        override fun createFromParcel(parcel: Parcel): GenreAnalytics {
            return GenreAnalytics(parcel)
        }

        override fun newArray(size: Int): Array<GenreAnalytics?> {
            return arrayOfNulls(size)
        }
    }
}