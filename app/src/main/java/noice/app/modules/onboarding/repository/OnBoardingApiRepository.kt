package noice.app.modules.onboarding.repository

import android.os.Bundle
import android.util.Log
import androidx.annotation.OptIn
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import androidx.media3.common.util.UnstableApi
import com.google.gson.Gson
import kotlinx.coroutines.*
import noice.app.BaseApplication
import noice.app.enums.Priority
import noice.app.listner.GuestTokenListener
import noice.app.model.ExoNotificationData
import noice.app.model.generics.BaseModel
import noice.app.model.user.preference.PreferenceData
import noice.app.modules.media.model.Queue
import noice.app.modules.media.model.QueueObject
import noice.app.modules.onboarding.models.*
import noice.app.modules.onboarding.models.Address
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.player.managers.QueueManager
import noice.app.rest.*
import noice.app.rest.apiinterfaces.CatalogApiInterface
import noice.app.rest.apiinterfaces.CDNConfigApiInterface
import noice.app.rest.apiinterfaces.UserApiInterface
import noice.app.room.LocalCacheData
import noice.app.utils.*
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.utils.MediaUtil.getExoData
import okhttp3.*
import javax.inject.Inject

class OnBoardingApiRepository @Inject constructor(
    private var configApiInterface: CDNConfigApiInterface,
    private var userApiService: UserApiInterface,
    private var catalogApiService: CatalogApiInterface
) : BaseRepository() {
    private var genreJob : Job? = null
    private var homeJob : Job? = null

    fun login(request: LoginRequest,email:String): MutableLiveData<Resource<LoginResponse>> {

        val mutableData = MutableLiveData<Resource<LoginResponse>>()

        BaseApplication.doServerCall({ userApiService.login(PrefUtils.token, request) },
            object : ServerInterface<BaseModel<LoginResponse>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                    if (e.code !=401){
                        AnalyticsUtil.sendEvent("login", Bundle().apply {
                            putString("loginType", request.type)
                            putString("email", email)
                            putString("exception", e.message)
                        })
                    }
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<LoginResponse>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun validateUserName(name: String): MutableLiveData<Resource<Boolean>> {

        val mutableData = MutableLiveData<Resource<Boolean>>()

        BaseApplication.doServerCall({ userApiService.validateUserName(PrefUtils.token, name) },
            object : ServerInterface<BaseModel<Boolean>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Boolean>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getUserDetails(map: HashMap<String, String>) = networkBoundResource(
        cachingConstant = PrefUtils.userDetails?.id,
        apiCall = {
            userApiService.getUserDetails(PrefUtils.token, map)
        },
        apiResponse = { data ->
            BaseApplication.firebaseAnalytics.setUserId(data.data?.id)
            val param = Bundle()
            param.putString("id", data.data?.id)
            param.putString("name", data.data?.displayName)
            BaseApplication.firebaseAnalytics.logEvent("User", param)
        }
    )

    fun getUserPreference(): MutableLiveData<Resource<PreferenceData>> {

        val mutableData = MutableLiveData<Resource<PreferenceData>>()

        BaseApplication.doServerCall({
            userApiService.getUserPreference(
                PrefUtils.token,
                PrefUtils.appsFlyerId.toString()
            )},
            object : ServerInterface<BaseModel<PreferenceData>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<PreferenceData>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun setUserPreference(
        prefData : PreferenceData
    ): MutableLiveData<Resource<PreferenceData>> {

        val mutableData = MutableLiveData<Resource<PreferenceData>>()

        BaseApplication.doServerCall({ userApiService. setUserPreference(PrefUtils.token, prefData) },
            object : ServerInterface<BaseModel<PreferenceData>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<PreferenceData>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun updateUserDetails(
        filePart: MultipartBody.Part?,
        map: HashMap<String, RequestBody>
    ): MutableLiveData<Resource<Boolean>> {

        val mutableData = MutableLiveData<Resource<Boolean>>()

        BaseApplication.doServerCall({ userApiService.updateUserDetails(PrefUtils.token, filePart, map) },
            object : ServerInterface<BaseModel<Boolean>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Boolean>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun updateOnBoardingFlag(
        request: OnBoardingComplete
    ): MutableLiveData<Resource<Boolean>> {

        val mutableData = MutableLiveData<Resource<Boolean>>()

        BaseApplication.doServerCall({ userApiService.updateOnBoardingFlag(PrefUtils.token, request) },
            object : ServerInterface<BaseModel<Boolean>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Boolean>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun callGuestTokenApi(listener : GuestTokenListener<Resource<LoginResponse>>? = null): Job? {
        val jwt  = Utils.getJwtToken()

        val request = GuestTokenRequest(jwt, "dasd", PrefUtils.fcmToken, Utils.getDeviceId())

        return BaseApplication.doServerCall({ userApiService.getGuestToken(request) },
            object : ServerInterface<BaseModel<LoginResponse>> {
                override fun onCustomError(e: ApiError) {
                    listener?.onResponse(Resource.error(e.message))
                }

                override fun onError(e: Throwable) {
                    listener?.onResponse(Resource.error(Resource.DEFAULT_MESSAGE, Exception(e)))
                }

                override fun onSuccess(data: BaseModel<LoginResponse>, dataFromCache: Boolean) {
                    if(!PrefUtils.isLoggedIn) {
                        PrefUtils.token = data.data?.accessToken.toString()
                    }
                    listener?.onResponse(Resource.success(data.data, dataFromCache))
                }
            })
    }

    fun getLocations(map: HashMap<String, Any>): MutableLiveData<Resource<List<Address>>> {

        val mutableData = MutableLiveData<Resource<List<Address>>>()

        BaseApplication.doServerCall({ userApiService.getLocations(PrefUtils.token, map) },
            object : ServerInterface<BaseModel<List<Address>>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<List<Address>>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getGenre(selected: Int? = null, orderBy: String? = null): MutableLiveData<Resource<List<Genre>>> {

        val mutableData = MutableLiveData<Resource<List<Genre>>>()
        genreJob?.cancel()
        genreJob = BaseApplication.doServerCall({ catalogApiService.getGenre(PrefUtils.token, selected, orderBy) },
            object : ServerInterface<BaseModel<List<Genre>>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<List<Genre>>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun updateGenre(request: GenreUpdateRequest): MutableLiveData<Resource<Any>> {

        val mutableData = MutableLiveData<Resource<Any>>()

        BaseApplication.doServerCall({ catalogApiService.updateGenre(PrefUtils.token, request) },
            object : ServerInterface<BaseModel<Any>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Any>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun mobileLoginRequest(method: String, request: MobileLoginRequest) = networkBoundResource(
        apiCall = { userApiService.mobileLoginRequest(PrefUtils.token, method, request) }
    )

    @OptIn(UnstableApi::class)
    fun getHomeData(): LiveData<Boolean> {
        homeJob?.cancel()
        homeJob = Job()
        return liveData(homeJob!! + Dispatchers.IO) {
            supervisorScope {

                val map = HashMap<String, String>()
                map["page"] = "home"
                map["offset"] = "1"
                map["limit"] = "4"

                val call1 = async { NetworkRequests.getHomeSegment(map) }
                val call2 = async { NetworkRequests.getHomeBanner("home") }

                try {
                    val resp = call1.await().body()
                    CacheUtils.putDataToCache(
                        LocalCacheData(
                            Constants.BUAT_KAMU,
                            Gson().toJson(resp)
                        )
                    )
                } catch (e: Exception) {
                    Log.e("HomeSegmentException",e.message.toString())
                }

                try {
                    val resp = call2.await().body()
                    CacheUtils.putDataToCache(
                        LocalCacheData(
                            Constants.HOME_BANNER,
                            Gson().toJson(resp)
                        )
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                emit(true)
            }
        }
    }

    fun fetchAndReplaceQueue() {

        QueueManager.clearAndNotify()

        val contentIdList = ArrayList<String>()

        BaseApplication.doServerCall({ NetworkRequests.getQueue() },
            object : ServerInterface<BaseModel<Queue>> {
                override fun onCustomError(e: ApiError) {
                    handleQueueFetchError()
                }
                override fun onError(e: Throwable) {
                    handleQueueFetchError()
                }
                override fun onSuccess(data: BaseModel<Queue>, dataFromCache: Boolean) {
                    val queue = data.data
                    if (queue?.queueObject != null) {
                        PrefUtils.queueTitle = queue.queueObject.nextFromTitle
                        queue.queueObject.current.id?.let {
                            contentIdList.add(it)
                        }
                        val manualIds = queue.queueObject.manualList.mapNotNull { manualItem ->
                            manualItem.id
                        }
                        contentIdList.addAll(manualIds)

                        val autoIds = queue.queueObject.manualList.mapNotNull { autoItem ->
                            autoItem.id
                        }
                        contentIdList.addAll(autoIds)

                        if (queue.queueObject.entitySubType.equals(ENTITY_TYPE_RADIO,true)) {
                            getCatalogDetail(contentIdList, queue)
                        } else {
                            val stringArray = Gson().toJson(contentIdList)
                            getContents(stringArray, queue)
                        }
                    } else {
                        handleQueueFetchError()
                    }
                }
            })
    }

    fun handleQueueFetchError() {
        QueueManager.handleFetchedQueue(arrayListOf())
    }

    private fun getCatalogDetail(idList: ArrayList<String>, queue: Queue?) {
        var stringArray = Gson().toJson(idList)
        BaseApplication.doServerCall({ NetworkRequests.getChannelDetail(idList[0], HashMap()) },
            object : ServerInterface<BaseModel<Channel>> {
                override fun onCustomError(e: ApiError) {
                    getContents(stringArray, queue)
                }
                override fun onError(e: Throwable) {
                    getContents(stringArray, queue)
                }
                override fun onSuccess(data: BaseModel<Channel>, dataFromCache: Boolean) {
                    val channel = data.data
                    val exoList = ArrayList<ExoNotificationData>()
                    channel?.getExoData()?.let { exoData ->
                        exoList.add(exoData)
                    }
                    if (idList.isNotEmpty()) {
                        idList.removeAt(0)
                    }
                    stringArray = Gson().toJson(idList)
                    getContents(stringArray, queue, exoList)
                }
            })
    }

    private fun getContents(
        stringArray: String,
        queue: Queue?,
        exoList: ArrayList<ExoNotificationData> = arrayListOf()
    ) {
        val map = HashMap<String, String>()
        val include = "[\"userAction\"]"
        map["includeEntities"] = include
        map["contentArray"] = stringArray

        BaseApplication.doServerCall({ catalogApiService.getCatalog(PrefUtils.token, map) },
            object : ServerInterface<BaseModel<List<Content>>> {
                override fun onCustomError(e: ApiError) {
                    handleQueueFetchError()
                }

                override fun onError(e: Throwable) {
                    handleQueueFetchError()
                }

                override fun onSuccess(data: BaseModel<List<Content>>, dataFromCache: Boolean) {

                    val currentList = data.data?.getExoList(queue?.queueObject, Priority.PLAYING)
                    if (!currentList.isNullOrEmpty()) {
                        exoList.addAll(currentList)
                    }

                    val manualList = data.data?.getExoList(queue?.queueObject, Priority.MANUAL)
                    if (!manualList.isNullOrEmpty()) {
                        exoList.addAll(manualList)
                    }

                    val autoList = data.data?.getExoList(queue?.queueObject, Priority.AUTOMATIC)
                    if (!autoList.isNullOrEmpty()) {
                        exoList.addAll(autoList)
                    }

                    QueueManager.handleFetchedQueue(exoList)
                }
            })
    }

    private fun List<Content>.getExoList(
        queueObject: QueueObject?,
        priority: Priority
    ): List<ExoNotificationData>? {
        val list = when (priority) {
            Priority.MANUAL -> {
                queueObject?.manualList
            }
            Priority.AUTOMATIC -> {
                queueObject?.autoList
            }
            else -> {
                listOf(queueObject?.current)
            }
        }

        return list?.mapNotNull { queueItem ->
            find { content ->
                queueItem?.id == content.id
            }?.let { content ->
                MediaUtil.getExoNotificationData(
                    content,
                    priority.type,
                    Constants.ENTITY_TYPE_CONTENT,
                    queueObject?.pageSource.toString()
                )?.apply {
                    startType = priority.type
                }
            }
        }
    }

    fun getFirebaseToken(): MutableLiveData<Resource<Any>> {

        val mutableData = MutableLiveData<Resource<Any>>()

        BaseApplication.doServerCall({ userApiService.getFirebaseToken(PrefUtils.token) },
            object : ServerInterface<BaseModel<Any>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Any>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun updateOnBoardingDetails(completeProfileRequest: CompleteProfileRequest) = networkBoundResource(
        apiCall = { userApiService.updateOnBoardingDetails(PrefUtils.token, completeProfileRequest) }
    )

    fun getAppConfig() = networkBoundResource(
        apiCall = {
            configApiInterface.getAppConfig()
        }
    )


    fun getEvUrlResponse(url: String): Resource<String> {
        val okHttpClient = OkHttpClient.Builder()
            .build()
        val request = Request.Builder()
            .url(url)
            .build()
        return try {
            val response = okHttpClient.newCall(request).execute()
            val responseBody = response.body?.string()
            if (response.isSuccessful) {
                Resource.success(responseBody)
            } else if (!responseBody.isNullOrEmpty()) {
                Resource.error(responseBody, statusCodes = response.code)
            } else {
                Resource.error("No Response", statusCodes = response.code)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.error(e)
        }
    }

    suspend fun getLoginQR() = networkBoundFlowResource(
        apiCall = {
            NetworkRequests.getLoginQR()
        }
    )


    suspend fun validateLoginQR(code: String) = networkBoundFlowResource(
        apiCall = {
            NetworkRequests.validateLoginQR(code)
        }
    )

    suspend fun optSubscriptionOnNewHeadUnit(accessToken: String?) = networkBoundFlowResource(
        apiCall = {
            NetworkRequests.optSubscriptionOnNewHeadUnit(accessToken)
        }
    )
}