package noice.app.modules.onboarding.viewmodel

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import noice.app.modules.onboarding.models.CompleteProfileRequest
import noice.app.modules.onboarding.models.OnBoardingComplete
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import noice.app.utils.DateUtils.parseDate
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class CompleteProfileViewModel @Inject constructor(
    private val repository: OnBoardingApiRepository
): ViewModel() {

    companion object {
        const val EVENT_UPDATE_DETAILS = "EVENT_UPDATE_DETAILS"
    }

    private val activityEvents = MutableSharedFlow<String>()
    private val ioScope = CoroutineScope(Dispatchers.IO)
    var username = ""
    var birthDate: Date? = null

    fun invokeEvent(event: String) {
        ioScope.launch {
            activityEvents.emit(event)
        }
    }

    fun subscribeEvents(lifecycleOwner: LifecycleOwner, action: suspend (value: String) -> Unit) {
        lifecycleOwner.lifecycleScope.launch {
            activityEvents.collectLatest(action)
        }
    }

    fun getCompleteProfileRequest(): CompleteProfileRequest {
        val dob = birthDate.parseDate("yyyy-MM-dd")
        return CompleteProfileRequest(dob = dob, userName = username, isOnboardingComplete = true)
    }

    fun updateOnBoardingFlag(request : OnBoardingComplete) = repository.updateOnBoardingFlag(request)

    fun validateUserName(name: String) = repository.validateUserName(name)

    fun updateOnBoardingDetails(completeProfileRequest: CompleteProfileRequest) = repository.updateOnBoardingDetails(completeProfileRequest)
}