package noice.app.modules.onboarding.activity

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.ActivityGenreSelectionBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.adapter.GenreAdapter
import noice.app.modules.onboarding.models.*
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.ThreadMode
import org.greenrobot.eventbus.Subscribe

@AndroidEntryPoint
class GenreSelectionActivity : AppCompatActivity() {

    companion object {
        private const val GENRE_LIST = "GENRE_LIST"
        private const val IS_GUEST_LOGIN_FLOW = "IS_GUEST_LOGIN_FLOW"

        fun start(ctx: Context, genreList : ArrayList<Genre>?) {
            val intent = Intent(ctx, GenreSelectionActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            intent.putParcelableArrayListExtra(GENRE_LIST, genreList)
            ctx.startActivity(intent)
        }

        fun start(ctx: Context, isGuestLoginFlow : Boolean = false, clearPreviousActivities : Boolean = false) {
            val i = Intent(ctx, GenreSelectionActivity::class.java)
            i.putExtra(IS_GUEST_LOGIN_FLOW, isGuestLoginFlow)
            if (clearPreviousActivities) {
                i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }
            ctx.startActivity(i)
        }
    }

    private val viewModel : OnBoardingViewModel by viewModels()

    private lateinit var binding: ActivityGenreSelectionBinding
    private lateinit var adapter : GenreAdapter
    private var genreList = ArrayList<Genre>()
    private var isGuestLoginFlow = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGenreSelectionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this)

        setToolbar()

        getDataFromIntent()

        initViews()

        if(genreList.isNotEmpty()) {
            adapter.notifyDataSetChanged()
        } else {
            binding.errorView.showLoading()
            getGenre()
        }
    }

    private fun setToolbar() {
        binding.toolbar.setSkipButtonClick {
            goToHome()
        }

        binding.toolbar.setBackClick {
            onBackPressed()
        }
    }

    private fun getDataFromIntent() {
        if(intent.hasExtra(GENRE_LIST)) {
            genreList = intent.getParcelableArrayListExtra(GENRE_LIST) ?: ArrayList()
        }
        if(intent.hasExtra(IS_GUEST_LOGIN_FLOW)) {
            isGuestLoginFlow = intent.getBooleanExtra(IS_GUEST_LOGIN_FLOW, false)
        }
    }

    private fun initViews() {
        binding.errorView.setBackToHomeVisibility(View.GONE)
        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getGenre()
        }

        adapter = GenreAdapter(genreList)
        binding.genreRecyclerView.adapter = adapter
        binding.genreRecyclerView.addItemDecoration(RecyclerViewMargin(resources.getDimensionPixelSize(R.dimen.dp8), RecyclerViewMargin.GRID))

        binding.doneBtn.setOnClickListener {
            val selectedGenre = getSelectedGenre()
            if(selectedGenre.isEmpty()) {
                goToHome()
            } else {
                binding.errorView.showLoading()

                updateGenre(selectedGenre)
            }
        }
    }

    private fun getGenre() {
        viewModel.getGenre().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                genreList.clear()
                genreList.addAll(it.data)
                adapter.notifyDataSetChanged()
                binding.errorView.hide()
            } else {
                binding.errorView.showError(it.message, type = "onboarding", errorName = it.message)
            }
        }
    }

    private fun updateGenre(selectedGenre: List<String>) {
        viewModel.updateGenre(GenreUpdateRequest(selectedGenre)).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {

                updateOnBoardingFlag()
            } else if (!it?.message.isNullOrEmpty()) {
                Toast.makeText(this, it?.message, Toast.LENGTH_LONG).show()
                binding.errorView.hide()
            }
        }
    }

    private fun updateOnBoardingFlag() {
        viewModel.updateOnBoardingFlag(OnBoardingComplete(isOnboardingComplete = true)).observe(this) {
            goToHome()

            val list = ArrayList<GenreAnalytics>()
            genreList.filter { genre ->
                genre.isSelected
            }.forEach { genre ->
                list.add(GenreAnalytics(genre.name ?: "", genre.id ?: ""))
            }
            AnalyticsUtil.sendEvent("genres_selected", Bundle().apply {
                putStringArrayList("genresTitle", ArrayList(list.map { it.genreTitle ?: "" }))
                putStringArrayList("genreSelected", ArrayList(list.map { it.genreID }))
            })

            MoEngageAnalytics.sendEvent(
                this@GenreSelectionActivity,
                "genres completed",
                Bundle().apply {
                    putString("total genres selected", list.size.toString())
                    putStringArrayList(
                        "genres selected",
                        ArrayList(list.map { it.genreTitle ?: "" })
                    )
                })

            binding.errorView.hide()
        }
    }

    private fun getSelectedGenre(): List<String> {
        return genreList.filter {
            it.isSelected
        }.map {
            it.id ?: ""
        }
    }

    private fun goToHome() {
        PrefUtils.onBoardingStep = 0

        val i = Intent(this, ClassVariantProvider.of(HomeActivity::class.java))
        if(isGuestLoginFlow) {
            EventBus.getDefault().post(LoginChangeEvent())
        } else {
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        MoEngageAnalytics.sendEvent(this, "onboarding completed", "", null)

        startActivity(i)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onGenreSelect(event: Genre) {
        val index = genreList.indexOf(event)
        if (index != -1) {
            adapter.notifyItemChanged(index)
        }
    }

    override fun onBackPressed() {
        if(Utils.isActivityRunning(this, EditProfileActivity::class.java)) {
            super.onBackPressed()
        } else {
            startActivity(Intent(this, EditProfileActivity::class.java))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}