package noice.app.modules.onboarding.fragments

import android.content.Context
import android.content.Context.CONNECTIVITY_SERVICE
import android.content.Intent
import android.net.*
import android.net.ConnectivityManager.NetworkCallback
import android.os.*
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentSnaBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.activity.CompleteProfileActivity
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.models.MobileLoginRequest
import noice.app.modules.onboarding.viewmodel.MobileLoginViewModel
import noice.app.modules.profile.viewmodel.ProfileViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class SnaFragment : Fragment() {

    companion object {
        const val LOGIN_TYPE_SNA = "sna"
        private const val NETWORK_REQUEST_TIMEOUT = 10000

        fun newInstance() = SnaFragment()
    }

    private lateinit var binding: FragmentSnaBinding
    private val args: SnaFragmentArgs by navArgs()
    private val viewModel: MobileLoginViewModel by activityViewModels()
    private val profileViewModel : ProfileViewModel by viewModels()
    private var networkTimeout: SafeDelay<Unit>? = null
    private lateinit var ctx: Context

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentSnaBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        AnalyticsUtil.sendEvent("login_sna_requested", Bundle().apply {
            putAnalyticsKey("loginType", "phone")
            putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
            putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
            putAnalyticsKey("numberOfAttempts", PrefUtils.loginCounter)
        })

        processLoginRequest()
    }

    private fun processLoginRequest(token: String? = null) {
        val request = MobileLoginRequest(
            channels = listOf("sms"),
            countryCode = viewModel.dialingCode.drop(1),
            mobileNumber = viewModel.mobileNumber,
            recaptchaResponse = token ?: args.captchaToken
        )
        viewModel.mobileLoginRequest("sna", request).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data?.sna?.evurl.isNullOrEmpty()) {
                viewModel.updateLoginCounter()
                doAPIonCellularNetwork(it.data?.data?.sna?.evurl.toString(), it.data?.data?.id.toString())
            } else {
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putAnalyticsKey("loginType", "phone")
                    putAnalyticsKey("loginStep", "sna_evurl_req")
                    putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                    putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
                    putAnalyticsKey("exception", it?.message)
                })
                navigateToOtpLogin()
            }
        }
    }

    private fun doAPIonCellularNetwork(url: String, snaId: String) {

        val connectivityManager = requireContext().getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        val request = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        val networkCallBack = object : NetworkCallback() {
            override fun onAvailable(network: Network) {
                proceedWithEVUrl(url, snaId, true)
            }

            override fun onUnavailable() {
                proceedWithEVUrl(url, snaId, false)
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            connectivityManager.requestNetwork(request, networkCallBack, NETWORK_REQUEST_TIMEOUT)
        } else {
            connectivityManager.requestNetwork(request, networkCallBack)
            if (networkTimeout == null) {
                networkTimeout = SafeDelay(NETWORK_REQUEST_TIMEOUT.toLong()) { proceed, _ ->
                    if (proceed) {
                        connectivityManager.unregisterNetworkCallback(networkCallBack)
                        proceedWithEVUrl(url, snaId, false)
                    }
                }
            } else {
                networkTimeout?.removeCallbacks()
            }
            networkTimeout?.postDelayed(Unit)
        }
    }

    private fun proceedWithEVUrl(url: String, snaId: String, dataSwitchingStatus: Boolean) {
        requireActivity().runOnUiThread {
            networkTimeout?.removeCallbacks()
            getEvUrlResponse(url, snaId, dataSwitchingStatus)
        }
    }

    private fun getEvUrlResponse(url: String, snaId: String, dataSwitchingStatus: Boolean) {
        viewModel.getEvUrlResponse(url).observe(viewLifecycleOwner) { response ->
            if (response.status == ResponseStatus.SUCCESS && !response.data.isNullOrEmpty()) {
                proceedToLogin(response.data, snaId, dataSwitchingStatus)
            } else {
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putAnalyticsKey("loginType", "phone")
                    putAnalyticsKey("loginStep", "sna_evurl")
                    putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                    putAnalyticsKey("dataSwitchingStatus", dataSwitchingStatus)
                    putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
                    putAnalyticsKey("exception", response.toString())
                })
                navigateToOtpLogin()
            }
        }
    }

    private fun proceedToLogin(responseBody: String, snaId: String, dataSwitchingStatus: Boolean) {
        val bundle = Bundle()
        responseBody.split("&").forEach { keyValue ->
            val split = keyValue.split("=")
            bundle.putString(split[0], split[1])
        }
        val errorCode = bundle.getString("ErrorCode")
        if (errorCode == "0") {
            //Success
            login(snaId)
        } else {
            AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                putAnalyticsKey("loginType", "phone")
                putAnalyticsKey("loginStep", "sna_evurl")
                putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                putAnalyticsKey("dataSwitchingStatus", dataSwitchingStatus)
                putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
                putAnalyticsKey("exception", responseBody)
            })
            navigateToOtpLogin()
        }
    }

    private fun login(snaId: String) {

        viewModel.login(
            LoginRequest(type = LOGIN_TYPE_SNA, snaId = snaId, isSna = true, appsFlyerId = PrefUtils.appsFlyerId)
        ).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.createSession(it.data, LOGIN_TYPE_SNA)

                PrefUtils.clearQueueData()

                viewModel.fetchAndReplaceQueue()

                getSelectedGenre()

                PrefUtils.dataVersion = Constants.DATA_VERSION_OLD

                if (it.data.newUser == true) {
                    getUserDetails(true)
                    MoEngageAnalytics.sendEvent(
                        requireContext(),
                        "user registered",
                        "login type",
                        "phone"
                    )
                } else {
                    getUserDetails(false)
                    AnalyticsUtil.sendEvent("login", Bundle().apply {
                        putAnalyticsKey("loginType", "phone")
                        putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                    })
                }

                PrefUtils.isLoggedIn = true
                PrefUtils.isLoggedInAsGuest = false
                PrefUtils.loginType = LOGIN_TYPE_SNA

                AnalyticsUtil.sendEvent("otp_verified")
            } else {
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putAnalyticsKey("loginType", "phone")
                    putAnalyticsKey("loginStep", "sna")
                    putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                    putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
                    putAnalyticsKey("exception", it.message)
                })
                navigateToOtpLogin()
            }
        }
    }

    private fun getUserDetails(isNewUser:Boolean = false) {

        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "userActions|artist"

        viewModel.getUserDetails(map).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {

                PrefUtils.userDetails = it.data.data
                MoEngageAnalytics.setUniqueId(requireContext(), PrefUtils.userDetails?.id ?: "")
                MoEngageAnalytics.setAttributes(requireContext(), isLoggedIn = true)
                MoEngageAnalytics.sendEvent(requireContext(), "logged in", "login type", "phone")

                if (isNewUser) {
                    AnalyticsUtil.sendEvent("user_registered", Bundle().apply {
                        putAnalyticsKey("loginType", LOGIN_TYPE_SNA)
                        putAnalyticsKey("phoneNumber", viewModel.formattedMobileNumber)
                        putAnalyticsKey("profileUserId", it.data.data?.id)
                    })
                }
                when (it.data.data?.isOnboardingComplete) {
                    true -> {
                        PrefUtils.onBoardingStep = 0
                        if (viewModel.loginChangeEvent != null) {
                            EventBus.getDefault().post(viewModel.loginChangeEvent)
                            requireActivity().finish()
                        } else {
                            val i = Intent(requireContext(), ClassVariantProvider.of(HomeActivity::class.java))
                            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                            startActivity(i)
                        }
                    }
                    else -> {
                        PrefUtils.onBoardingStep = 1
                        CompleteProfileActivity.start(
                            ctx = requireContext(),
                            source = "Onboarding_OTP_Input",
                            loginChangeEvent = viewModel.loginChangeEvent
                        )
                    }
                }
            } else if (it?.status != ResponseStatus.LOADING) {
                AnalyticsUtil.sendEvent("login_error", Bundle().apply {
                    putAnalyticsKey("loginType", "phone")
                    putAnalyticsKey("loginStep", "sna")
                    putAnalyticsKey("phoneNumber", viewModel.mobileNumber)
                    putAnalyticsKey("networkType", NetworkUtils.getNetworkType(requireContext()))
                    putAnalyticsKey("exception", it?.message)
                })
                navigateToOtpLogin()
            }
        }
    }

    private fun navigateToOtpLogin() {
        val controller = findNavController()
        if (controller.currentDestination?.id == R.id.navigationSna) {
            controller.navigate(SnaFragmentDirections.navigateToOtpFragment(null))
        }
    }

    private fun getSelectedGenre() {
        profileViewModel.getUserPreference("genre", null).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                PrefUtils.selectedGenre = ArrayList(it.data?.data)
                MoEngageAnalytics.setAttributes(ctx, isLoggedIn = true)
            }
        }
    }
}