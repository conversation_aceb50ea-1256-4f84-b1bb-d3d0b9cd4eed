package noice.app.modules.onboarding.models

import android.os.Parcel
import android.os.Parcelable
import noice.app.utils.ImageUtils

data class Genre(
    val description: String?,
    val id: String?,
    var image: String?,
    val icon: String?,
    val isActive: Boolean?,
    var name: String?,
    var color: String?,
    var tritonGenreId : String?,

    //local
    var isSelected: Boolean = false
) : Parcelable {

    constructor(id: String?) : this(
        "",
        id,
        "",
        "",
        false,
        "",
        "",
        "",
        false
    )

    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readByte() != 0.toByte(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readByte() != 0.toByte()
    )

    override fun equals(other: Any?): Boolean {
        if (other is Genre) {
            return other.id == id
        }
        return false
    }

    override fun hashCode(): Int {
        return id?.hashCode() ?: 0
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(description)
        parcel.writeString(id)
        parcel.writeString(image)
        parcel.writeString(icon)
        parcel.writeByte(if (isActive == true) 1 else 0)
        parcel.writeString(name)
        parcel.writeString(color)
        parcel.writeString(tritonGenreId)
        parcel.writeByte(if (isSelected) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Genre> {
        override fun createFromParcel(parcel: Parcel): Genre {
            return Genre(parcel)
        }

        override fun newArray(size: Int): Array<Genre?> {
            return arrayOfNulls(size)
        }
    }

    fun getSmallImage():String?{
        return ImageUtils.getSmallImage(image?:"","300x300")
    }

    fun getCompImage():String?{
        return ImageUtils.getSmallImage(image?:"","compressed")
    }

    fun getSmallIcon():String?{
        return ImageUtils.getSmallImage(icon?:"","300x300")
    }
}