package noice.app.modules.onboarding.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Color
import android.location.Address
import android.net.Uri
import android.os.Bundle
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.RadioButton
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DividerItemDecoration
import com.appsflyer.AppsFlyerLib
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import noice.app.BaseApplication
import noice.app.R
import noice.app.databinding.ActivityEditProfileBinding
import noice.app.listner.OnClickInterface
import noice.app.listner.OnOkay
import noice.app.model.eventbus.EventMessage
import noice.app.model.user.User
import noice.app.modules.onboarding.adapter.AddressAdapter
import noice.app.modules.onboarding.fragments.LoginConfirmationDialog
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.modules.profile.PolicyTermsActivity
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.utils.Constants.Companion.CURRENT_LOCATION_ERROR
import noice.app.utils.Utils.disable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

@AndroidEntryPoint
class EditProfileActivity : AppCompatActivity(), DeviceLocationTracker.DeviceLocationListener {

    companion object {

        private const val LOCATION_PERMISSION_CODE = 7613
        private const val IS_GUEST_LOGIN_FLOW = "IS_GUEST_LOGIN_FLOW"
        private const val SOURCE_SCREEN = "SOURCE_SCREEN"

        fun start(ctx: Context, sourceScreen: String) {
            val i = Intent(ctx, EditProfileActivity::class.java)
            i.putExtra(SOURCE_SCREEN, sourceScreen)
            ctx.startActivity(i)
        }
    }

    private val viewModel: OnBoardingViewModel by viewModels()

    private lateinit var binding: ActivityEditProfileBinding
    private lateinit var deviceLocationTracker: DeviceLocationTracker
    private var confirmationDialog : LoginConfirmationDialog? = null
    private var user: User? = null
    private var userPhotoBitmap: Bitmap? = null
    private var userPhotoUri: Uri? = null
    private var province: String = ""
    private var city: String = ""
    private var addressAdapter: AddressAdapter? = null
    private var addressList = ArrayList<noice.app.modules.onboarding.models.Address>()
    private var forceSet = false
    private var locationId = ""
    private var isGuestLoginFlow = false
    private var isUserNameAvailable = false
    private val typingDelay = TypingDelay()
    private val typingDelayLocation = TypingDelay()
    private val userNameRegex = Regex("^[a-z0-9_.]*$")
    private var sourceScreen: String = ""

    private var queryMap = HashMap<String, Any>()

    private val startForImagePicker =
        registerForActivityResult(ActivityResultContracts.GetContent()) { imageUri ->
            ImageUtils.getBitmapFromUri(this, imageUri).observe(this) {
                if (it != null) {
                    userPhotoUri = imageUri
                    userPhotoBitmap = it
                    ImageUtils.loadImageByBitmap(binding.profilePic, it, true)
                }
            }
        }

    private val startForLocationTracker =
        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                runOnUiThread {
                    binding.pbLayout.visibility = View.VISIBLE
                    deviceLocationTracker.initializeLocationProviders()
                    binding.locationEditText.text?.clear()
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
        binding = ActivityEditProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getDataFromIntent()

        setToolBar()

        if (user == null) {
            binding.errorView.showLoading()
            getUserDetails()
        } else {
            initViews()
        }

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Onboarding_Profile_Completion")
            putString("previousScreen", sourceScreen)
        })
    }

    private fun setToolBar() {
        binding.toolbar.setBackClick {
            onBackPressed()
        }
    }

    private fun getDataFromIntent() {
        user = PrefUtils.userDetails

        if (intent.hasExtra(IS_GUEST_LOGIN_FLOW)) {
            isGuestLoginFlow = intent.getBooleanExtra(IS_GUEST_LOGIN_FLOW, false)
        }
        if (intent.hasExtra(SOURCE_SCREEN)) {
            sourceScreen = intent.getStringExtra(SOURCE_SCREEN)?:""
        }
    }

    private fun initViews() {
        preFillFields()

        binding.userNameEditText.filters += LowerCaseInputFilter()
        binding.yearEditText.inputType = InputType.TYPE_CLASS_NUMBER

        setPrivacySpan()

        val itemDecoration = DividerItemDecoration(this, DividerItemDecoration.VERTICAL)
        itemDecoration.setDrawable(ContextCompat.getDrawable(this, R.drawable.devider)!!)
        binding.rvAddress.addItemDecoration(itemDecoration)

        binding.uploadPicBtn.setOnClickListener {
            ImageUtils.showImagePicker(startForImagePicker)
        }

        binding.yearEditText.customSelectionActionModeCallback = object : ActionMode.Callback {
            override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }
            override fun onDestroyActionMode(mode: ActionMode?) {}
            override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                return false
            }
            override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                return false
            }
        }

        binding.submitButton.setOnClickListener {

            Utils.hideKeyboard(binding.submitButton)

            if (binding.fullNameEditText.text.toString().trim().isEmpty()) {
                Toast.makeText(this, getString(R.string.empty_full_name), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            if (binding.userNameEditText.text.toString().trim().isEmpty() ||
                    binding.userNameEditText.text.toString().length <= 3) {
                Toast.makeText(this, getString(R.string.empty_user_name), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            if (!isUserNameAvailable && binding.userNameEditText.text.toString().trim() != user?.userName) {
                return@setOnClickListener
            }

            if (binding.yearEditText.text.toString().trim().isEmpty() || binding.yearEditText.text.toString().length < 4 ) {
                Toast.makeText(this, getString(R.string.empty_year_of_birth), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            val currentYear = Calendar.getInstance().get(Calendar.YEAR)
            if (binding.yearEditText.text.toString().toInt() > currentYear) {
                Toast.makeText(this, getString(R.string.year_bigger_than_current_year), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            val year = binding.yearEditText.text.toString().toInt()
            if (year < 1901){
                Toast.makeText(this, getString(R.string.year_lower_than_limit), Toast.LENGTH_LONG).show()
                return@setOnClickListener
            }

            proceedToUploadData()
        }

        binding.genderRadioGroup.setOnCheckedChangeListener { _, _ ->
            Utils.hideKeyboard(binding.genderRadioGroup)
        }

        addressAdapter = AddressAdapter(
            this,
            addressList,
            object : OnClickInterface<noice.app.modules.onboarding.models.Address> {
                override fun dataClicked(data: noice.app.modules.onboarding.models.Address) {
                    binding.locationLayout.visibility = View.GONE
                    forceSet = true
                    locationId = data.id
                    binding.locationEditText.setText(data.city.plus(", ").plus(data.state))
                    binding.locationEditText.setSelection(binding.locationEditText.text.toString().length)
                    addressList.clear()
                    addressAdapter?.notifyDataSetChanged()
                }
            })

        binding.rvAddress.adapter = addressAdapter

        binding.locationEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence, start: Int,
                count: Int, after: Int
            ) {

            }

            override fun onTextChanged(
                s: CharSequence, start: Int,
                before: Int, count: Int
            ) {
                typingDelayLocation.typingHandler.removeCallbacks(typingDelayLocation)
            }

            override fun afterTextChanged(s: Editable) {
                forceSet = false

                if (s.isEmpty()) {
                    binding.locationLayout.visibility = View.GONE
                    binding.pbLayout.visibility = View.GONE
                    binding.locationEditText.text?.clear()
                    forceSet = true
                    addressList.clear()
                }

                if (!forceSet && s.isNotEmpty() && s.length >= 3) {
                    typingDelayLocation.lastTextEdit = System.currentTimeMillis()
                    typingDelayLocation.typingHandler.postDelayed(typingDelayLocation, typingDelayLocation.delay)
                }
            }
        })

        binding.txtCurrent.setOnClickListener {
            getCurrentLocation()
        }

        binding.userNameEditText.addTextChangedListener(object : TextWatcher {

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                typingDelay.typingHandler.removeCallbacks(typingDelay)
            }

            override fun afterTextChanged(s: Editable?) {
                if(s.toString().isNotEmpty()) {
                    typingDelay.lastTextEdit = System.currentTimeMillis()
                    typingDelay.typingHandler.postDelayed(typingDelay, typingDelay.delay)
                } else {
                    setUserNameDefault()
                }
            }
        })

        typingDelayLocation.setCallback(object : OnOkay<Boolean> {
            override fun okay(isOkay: Boolean) {
                val locationName = binding.locationEditText.text.toString().trim()
                loadLocation(query = locationName)
            }
        })

        typingDelay.setCallback(object : OnOkay<Boolean> {
            override fun okay(isOkay: Boolean) {
                if (isOkay) {
                    val text = binding.userNameEditText.text.toString().trim()
                    if (text == user?.userName) {
                        setUserNameDefault(true)
                    } else if(text.length in 4..15) {
                        if(text.matches(userNameRegex)) {
                            binding.userProgressBar.visibility = View.VISIBLE
                            binding.userNameStatus.visibility = View.GONE
                            binding.nameDisclaimer.setTextColor(ContextCompat.getColor(this@EditProfileActivity, R.color.whitish_grey))
                            binding.nameDisclaimer.text = getString(R.string.check_username_availability)
                            validateUserName(binding.userNameEditText.text.toString().trim())
                        } else {
                            setUserNameError(getString(R.string.username_char_error))
                        }
                    } else {
                        setUserNameError(getString(R.string.username_length_error))
                    }
                }
            }
        })
    }

    private fun getCurrentLocation() {
        if (ContextCompat.checkSelfPermission(
                this@EditProfileActivity,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            if (ActivityCompat.shouldShowRequestPermissionRationale(
                    this@EditProfileActivity,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
            ) {
                ActivityCompat.requestPermissions(
                    this@EditProfileActivity,
                    arrayOf(Manifest.permission.ACCESS_FINE_LOCATION), LOCATION_PERMISSION_CODE
                )
            } else {
                ActivityCompat.requestPermissions(
                    this@EditProfileActivity,
                    arrayOf(Manifest.permission.ACCESS_FINE_LOCATION), LOCATION_PERMISSION_CODE
                )
            }
        } else {
            deviceLocationTracker = DeviceLocationTracker(this, this, startForLocationTracker)
            binding.locationLayout.visibility = View.GONE

            binding.locationEditText.text?.clear()

            forceSet = true
            addressList.clear()
            addressAdapter?.notifyDataSetChanged()
        }
    }

    private fun getUserDetails() {

        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "userActions|artist"

        viewModel.getUserDetails(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {
                user = it.data.data
                PrefUtils.userDetails = user
                initViews()
            }
            if (it?.status != ResponseStatus.LOADING) {
                binding.errorView.hide()
            }
        }
    }

    private fun loadLocation(query: String) {
        queryMap.clear()
        queryMap["q"] = query
        queryMap["page"] = 1
        queryMap["limit"] = 25

        viewModel.getLocations(queryMap).observe(this) {
            binding.pbLayout.visibility = View.GONE
            if (it.data.isNullOrEmpty()) {
                binding.locationLayout.visibility = View.GONE
                addressList.clear()
                Utils.showSnackBar(this, "Lokasimu nggak terdaftar di database kami.", null)
            } else {
                if (query.isEmpty()) {
                    val searchResult = it.data.find { location ->
                        province.equals(
                            location.state,
                            true
                        ) || city.contains(location.city, true)
                    }
                    if (searchResult != null) {
                        binding.locationEditText.setText(
                            searchResult.city.plus(", ").plus(searchResult.state)
                        )
                        locationId = searchResult.id
                        Log.d("locationId - ", locationId)
                    }
                } else {
                    binding.locationLayout.visibility = View.VISIBLE
                    addressList.clear()
                    addressList.addAll(it.data)
                }
            }

            addressAdapter?.notifyDataSetChanged()
        }
    }

    private fun preFillFields() {
        binding.fullNameEditText.setText(user?.displayName)

        if(!user?.userName.isNullOrEmpty()) {
            binding.userNameInputLayout.disable()
            isUserNameAvailable = true
            binding.userNameEditText.setText(user?.userName?:"")
            binding.nameDisclaimer.visibility = View.GONE
        } else {
            binding.nameDisclaimer.visibility = View.VISIBLE
            binding.userNameEditText.filters += InputFilter.LengthFilter(15)
        }

        binding.yearEditText.setText(user?.dob)

        binding.userBio.setText(user?.bio)

        ImageUtils.getBitmapFromUrl(this, user?.originalImage).observe(this) {
            ImageUtils.loadImageByBitmap(binding.profilePic, it, true)
        }

        if (!user?.gender.isNullOrEmpty()) {
            if (user?.gender.equals("male", true)) {
                binding.genderMale.isChecked = true
            } else {
                binding.genderFemale.isChecked = true
            }
        }

        if(user?.location != null) {
            val loc = "${user?.location?.city}, ${user?.location?.state}"
            binding.locationEditText.setText(loc)
            locationId = user?.location?.id ?: ""
        }
    }

    private fun proceedToUploadData() {

        binding.errorView.showLoading()

        val map = HashMap<String, RequestBody>()

        binding.fullNameEditText.text.toString().let {
            if (user?.displayName!= it.trim()){
                user?.displayName = it
                map["displayName"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
            }
        }

        binding.userNameEditText.text.toString().let {
            if (user?.userName != it.trim()){
                user?.userName = it
                map["userName"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
            }
        }

        binding.userBio.text.toString().let {
            if (user?.bio != it){
                user?.bio = it
                map["bio"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
            }
        }

        binding.yearEditText.text.toString().let {
            if (user?.dob != it){
                user?.dob = it
                map["dob"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
            }
        }

        if (binding.genderRadioGroup.checkedRadioButtonId != -1) {
            val btn: RadioButton =
                binding.genderRadioGroup.findViewById(binding.genderRadioGroup.checkedRadioButtonId)
            if (btn.text == getString(R.string.male)) {
                user?.gender = "male"
            } else {
                user?.gender = "female"
            }
            map["gender"] =
                user?.gender.toString().toRequestBody("multipart/form-data".toMediaTypeOrNull())
        }

        if (locationId.isNotEmpty()) {
            map["locationId"] = locationId.toRequestBody("multipart/form-data".toMediaTypeOrNull())
        }

        if (userPhotoBitmap != null) {
            ImageUtils.getFileFromBitmap(this, userPhotoBitmap).observe(this) {
                if (it != null) {
                    val filePart = MultipartBody.Part.createFormData(
                        "photo", it.name, it.asRequestBody(
                            "image/*".toMediaTypeOrNull()
                        )
                    )
                    updateUserDetails(filePart, map)
                } else {
                    updateUserDetails(null, map)
                }
            }
        } else {
            updateUserDetails(null, map)
        }
    }

    private fun updateUserDetails(
        filePart: MultipartBody.Part?,
        map: HashMap<String, RequestBody>
    ) {
        viewModel.updateUserDetails(filePart, map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data == true) {
                if (userPhotoBitmap != null){
                    runBlocking {
                        getUserDetails()
                    }
                }
                PrefUtils.userDetails = user

                AnalyticsUtil.setLoginProperties()

                user?.localPhotoUri = userPhotoUri
                EventBus.getDefault().post(EventMessage(user, Constants.PROFILE_EDITED))
                finish()
            } else {
                Toast.makeText(this, it.message ?: "Some thing went wrong!", Toast.LENGTH_LONG)
                    .show()
            }

            binding.errorView.hide()

            AnalyticsUtil.sendEvent("profile_completed", Bundle().apply {
                putString("fullName", user?.displayName ?: "")
                putString("username", user?.userName ?: "")
                putString("birthYear", user?.dob ?: "")
                putString("gender", user?.gender ?: "")
                putString("location", user?.location?.city ?: "")
            })

            MoEngageAnalytics.sendEvent(
                this@EditProfileActivity,
                "profile completed",
                Bundle().apply {
                    putString("username", user?.userName ?: "")
                    putString("dob", user?.dob ?: "")
                    putString("gender", user?.gender ?: "")
                    putString("city", user?.location?.city ?: "")
                })

            /* Track Events in real time */
            val eventValue: MutableMap<String, Any> = HashMap()
            eventValue["af_registration_method"] =
                "Method the user chooses to sign up with " + PrefUtils.loginType
            AppsFlyerLib.getInstance().logEvent(
                BaseApplication.getBaseAppContext(),
                "af_complete_registration",
                eventValue
            )

            MoEngageAnalytics.sendEvent(
                this@EditProfileActivity,
                "user registered",
                "login type",
                PrefUtils.loginType
            )
        }
    }

    private fun validateUserName(name: String) {
        viewModel.validateUserName(name).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data == true) {
                isUserNameAvailable = true
                binding.nameDisclaimer.setTextColor(
                    ContextCompat.getColor(
                        this@EditProfileActivity,
                        R.color.whitish_grey
                    )
                )
                binding.nameDisclaimer.text = getString(R.string.username_available)
                binding.userNameStatus.setImageResource(R.drawable.ic_tick_green)
            } else {
                setUserNameError()
            }
            binding.userProgressBar.visibility = View.GONE
            binding.userNameStatus.visibility = View.VISIBLE
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == ImageUtils.STORAGE_PERMISSION_CODE) {
            if (permissions.isNotEmpty() && permissions[0] == Manifest.permission.READ_EXTERNAL_STORAGE
                && grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
            ) {
                ImageUtils.showImagePicker(startForImagePicker)
            }
        } else if (requestCode == LOCATION_PERMISSION_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (ContextCompat.checkSelfPermission(
                        this@EditProfileActivity,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    deviceLocationTracker = DeviceLocationTracker(this, this, startForLocationTracker)
                    binding.locationEditText.text?.clear()
                    deviceLocationTracker.initializeLocationProviders()
                }
            }
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onDeviceLocationChanged(results: List<Address>?) {
        if (!results.isNullOrEmpty()) {
            deviceLocationTracker.stopUpdate()
            if (results.size == 3) {
                if (!results[2].adminArea.isNullOrEmpty() && !results[2].subAdminArea.isNullOrEmpty()) {
                    province = results[2].adminArea
                    city = results[2].subAdminArea
                } else if (!results[1].adminArea.isNullOrEmpty() && !results[1].subAdminArea.isNullOrEmpty()) {
                    province = results[1].adminArea
                    city = results[1].subAdminArea
                } else if (!results[0].adminArea.isNullOrEmpty() && !results[0].subAdminArea.isNullOrEmpty()) {
                    province = results[0].adminArea
                    city = results[0].subAdminArea
                }
            } else if (results.size == 2) {
                if (!results[1].adminArea.isNullOrEmpty() && !results[1].subAdminArea.isNullOrEmpty()) {
                    province = results[1].adminArea
                    city = results[1].subAdminArea
                } else if (!results[0].adminArea.isNullOrEmpty() && !results[0].subAdminArea.isNullOrEmpty()) {
                    province = results[0].adminArea
                    city = results[0].subAdminArea
                }
            } else if (results.size == 1 && !results[0].adminArea.isNullOrEmpty() && !results[0].subAdminArea.isNullOrEmpty()) {
                province = results[0].adminArea
                city = results[0].subAdminArea
            }

            runOnUiThread {
                binding.locationLayout.visibility = View.GONE
                addressList.clear()
                addressAdapter?.notifyDataSetChanged()
                forceSet = true
                loadLocation("")
                binding.locationEditText.setSelection(binding.locationEditText.text.toString().length)
            }
        }
    }

    private fun setPrivacySpan() {
        val tnc = getString(R.string.privacy_policy_text)
        val span = SpannableString(tnc)
        span.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(this, R.color.dull_yellow)),
            32,
            50,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                PolicyTermsActivity.start(this@EditProfileActivity, PolicyTermsActivity.TYPE_POLICY,"Onboarding_Profile_Completion")
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.drawableState = null
            }
        }
        span.setSpan(clickableSpan, 32, 50, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.privacyPolicy.highlightColor = Color.TRANSPARENT
        binding.privacyPolicy.movementMethod = LinkMovementMethod.getInstance()
        binding.privacyPolicy.text = span
    }

    override fun onDestroy() {
        if (confirmationDialog?.isAdded == true) {
            confirmationDialog?.dismissAllowingStateLoss()
        }
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event:EventMessage){
        if (event.eventCode == CURRENT_LOCATION_ERROR){
            binding.pbLayout.visibility = View.GONE
        }
    }

    private fun setUserNameError(error: String? = null) {
        isUserNameAvailable = false
        binding.nameDisclaimer.setTextColor(ContextCompat.getColor(this@EditProfileActivity, R.color.red))
        binding.nameDisclaimer.text = error ?: getString(R.string.username_not_available)
        binding.userNameStatus.setImageResource(R.drawable.ic_cross_red)
        binding.userNameStatus.visibility = View.VISIBLE
    }

    private fun setUserNameDefault(isUserNameAvailable : Boolean = false) {
        this.isUserNameAvailable = isUserNameAvailable
        binding.userNameStatus.visibility = View.GONE
        binding.userProgressBar.visibility = View.GONE
        binding.nameDisclaimer.text = getString(R.string.name_disclaimer)
    }

    override fun showLoading() {
        runOnUiThread {
            binding.pbLayout.visibility = View.VISIBLE
        }
    }

    override fun hideLoading() {
        runOnUiThread {
            binding.pbLayout.visibility = View.GONE
        }
    }
}