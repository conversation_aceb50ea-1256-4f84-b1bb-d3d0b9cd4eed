package noice.app.modules.onboarding.fragments

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import noice.app.R
import noice.app.data.WalletController
import noice.app.databinding.FragmentLostBenefitBinding
import noice.app.modules.onboarding.activity.DeleteAccountActivity
import noice.app.modules.onboarding.viewmodel.DeleteAccountViewModel
import noice.app.utils.Constants
import noice.app.utils.ExperimentUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils

class LostBenefitFragment : Fragment() {

    private lateinit var binding : FragmentLostBenefitBinding
    private lateinit var ctx : Context
    private val viewModel : DeleteAccountViewModel by activityViewModels()

    companion object {
        fun newInstance() = LostBenefitFragment()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLostBenefitBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {

        binding.sincereCheckbox.setOnCheckedChangeListener { _, b ->
            if (b) {
                binding.showErrorToast.visibility = GONE
            }
        }

        binding.toolbar.setBackClick {
            requireActivity().onBackPressed()
        }

        binding.continueBtn.setOnClickListener {
            if (!binding.sincereCheckbox.isChecked) {
                binding.showErrorToast.visibility = VISIBLE
                return@setOnClickListener
            }

            viewModel.setClickedAction(DeleteAccountActivity.CONTINUE)
        }

        binding.apply {
            val colorSpan = ForegroundColorSpan(ContextCompat.getColor(ctx, R.color.white))
            val styleSpan = StyleSpan(Typeface.BOLD)

            if (ExperimentUtils.isCoinFeatureEnabled()) {
                val coins = WalletController.formattedCoins
                val diamonds = WalletController.formattedDiamonds

                val lostB1Spans = SpannableStringBuilder(getString(R.string.coin_and_diamond_will_be_lost, coins, diamonds))
                val coinSpan = ImageSpan(ctx, R.drawable.ic_noice_coin, ImageSpan.ALIGN_CENTER)
                lostB1Spans.setSpan(coinSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                val diamondSpan = ImageSpan(ctx, R.drawable.ic_diamond_16dp, ImageSpan.ALIGN_CENTER)
                lostB1Spans.setSpan(diamondSpan, 12 + coins.length, 13 + coins.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                lostB1Spans.setSpan(colorSpan, 0, 22 + coins.length + diamonds.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                lostB1Spans.setSpan(styleSpan, 0, 22 + coins.length + diamonds.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                lostBenefit1.text = lostB1Spans
                lostBenefit1.visibility = VISIBLE
            } else {
                lostBenefit1.visibility = GONE
            }

            val userDetails = PrefUtils.userDetails
            val followers = Utils.abbrNumberFormat(userDetails?.meta?.aggregations?.followers ?: 0, false)
            val followersUnit = Utils.getUnit(userDetails?.meta?.aggregations?.followers ?: 0)
            val followersText = "$followers $followersUnit"

            val followerSize = followersText.length
            val lostB3Spans = SpannableStringBuilder(getString(R.string.followers_will_be_lost, followersText))
            lostB3Spans.setSpan(colorSpan, 0, followerSize + 10, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            lostB3Spans.setSpan(styleSpan, 0, followerSize + 10, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            lostBenefit3.text = lostB3Spans
        }
    }
}