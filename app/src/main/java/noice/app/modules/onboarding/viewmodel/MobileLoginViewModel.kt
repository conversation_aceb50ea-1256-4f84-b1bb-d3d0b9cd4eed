package noice.app.modules.onboarding.viewmodel

import androidx.lifecycle.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import noice.app.listner.CommonListener
import noice.app.modules.onboarding.helper.ReCaptchaHelper
import noice.app.modules.onboarding.models.Country
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.models.MobileLoginRequest
import noice.app.modules.onboarding.repository.CountryChooserRepository
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import noice.app.utils.PrefUtils
import javax.inject.Inject

@HiltViewModel
class MobileLoginViewModel @Inject constructor(
    private val repository: OnBoardingApiRepository,
    private val countryRepository: CountryChooserRepository
): ViewModel() {

    lateinit var reCaptchaHelper: ReCaptchaHelper
    var loginChangeEvent: LoginChangeEvent? = null
    var mobileNumber = ""
    var countries = arrayListOf<Country>()
    var selectedCountry = MutableLiveData<Country>()

    val formattedMobileNumber: String
        get() {
            val number = mobileNumber.replace("....".toRegex(), "$0 ").trim()
            return  "$dialingCode $number"
        }

    val dialingCode: String
        get() = selectedCountry.value?.dialingCode.toString()

    fun updateLoginCounter() {
        val loginCounter = PrefUtils.loginCounterPair
        PrefUtils.loginCounterPair = if (loginCounter == null) {
            Pair(1, System.currentTimeMillis())
        } else {
            Pair((loginCounter.first) + 1, System.currentTimeMillis())
        }
    }

    fun checkLoginCounter(): Boolean {
        var loginCounter = PrefUtils.loginCounterPair
        loginCounter?.second?.let { lastUpdatedTime ->
            val hours24 = lastUpdatedTime + (24 * 60 * 60 * 1000)
            if (System.currentTimeMillis() > hours24) {
                loginCounter = null
                PrefUtils.loginCounterPair = null
            }
        }
        if ((loginCounter?.first ?: 0) >= 3) {
            return false
        }
        return true
    }

    fun displayCaptcha(source: String, completeListener: CommonListener<ReCaptchaHelper.ReCaptchaResponse>) = reCaptchaHelper.displayCaptcha(source, mobileNumber, completeListener)

    fun mobileLoginRequest(method: String, request: MobileLoginRequest) = repository.mobileLoginRequest(method, request)

    fun login(request: LoginRequest, userEmail: String = "") = repository.login(request,userEmail)

    fun fetchAndReplaceQueue() = repository.fetchAndReplaceQueue()

    fun getUserDetails(map : HashMap<String, String>) = repository.getUserDetails(map)

    fun getCountries() = countryRepository.getCountries()

    fun getEvUrlResponse(url: String)  = liveData(Dispatchers.IO) {
        val response = repository.getEvUrlResponse(url)
        emit(response)
    }
}