package noice.app.modules.onboarding.fragments

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.InsetDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import noice.app.R
import noice.app.databinding.FragmentLoginConfirmationDialogBinding
import noice.app.modules.onboarding.activity.LoginActivity
import noice.app.utils.PrefUtils

class LoginConfirmationDialog : DialogFragment() {

    private lateinit var binding : FragmentLoginConfirmationDialogBinding
    private lateinit var ctx : Context

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLoginConfirmationDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onStart() {
        super.onStart()

        dialog?.window?.setBackgroundDrawable(InsetDrawable(ColorDrawable(Color.TRANSPARENT), ctx.resources.getDimensionPixelSize(R.dimen.dp16)))
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        intiViews()
    }

    private fun intiViews() {
        binding.exit.setOnClickListener {
            dismiss()
            PrefUtils.logout(baseContext = ctx, redirect = false)
            val i = Intent(requireContext(), LoginActivity::class.java)
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(i)
        }

        binding.complete.setOnClickListener {
            dismiss()
        }
    }
}