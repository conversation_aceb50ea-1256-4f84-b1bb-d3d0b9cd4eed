package noice.app.modules.onboarding.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.modules.onboarding.models.Genre
import noice.app.views.GenreView
import noice.app.views.PillsView

class MyGenreAdapter(
    private val data: ArrayList<Genre>,
    private val type : Int
    ) : RecyclerView.Adapter<RecyclerView.ViewHolder>()  {

    companion object {
        const val PILL_VIEW = 1
        const val TILE_VIEW = 2
    }

    private lateinit var pillView: PillsView
    private lateinit var genreView: GenreView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (type == PILL_VIEW) {
            pillView = PillsView(parent.context)
            pillView.viewHolder
        } else {
            genreView = GenreView(parent.context)
            genreView.viewHolder
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PillsView.ViewHolder -> {
                pillView.viewHolder = holder
                pillView.setData(data[position])
            }
            is GenreView.ViewHolder -> {
                genreView.viewHolder = holder
                genreView.setData(data[position])
            }
        }
    }

    override fun getItemCount(): Int {
        return data.size
    }
}