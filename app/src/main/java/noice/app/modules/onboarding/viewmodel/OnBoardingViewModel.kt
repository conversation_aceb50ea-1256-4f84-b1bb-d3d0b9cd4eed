package noice.app.modules.onboarding.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import noice.app.model.user.preference.PreferenceData
import noice.app.modules.onboarding.models.GenreUpdateRequest
import noice.app.modules.onboarding.models.LoginRequest
import noice.app.modules.onboarding.models.OnBoardingComplete
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import okhttp3.MultipartBody
import okhttp3.RequestBody
import javax.inject.Inject

@HiltViewModel
class OnBoardingViewModel @Inject constructor(
    private val repository: OnBoardingApiRepository
) : ViewModel() {

    private val isAnimationComplete = MutableStateFlow(false)
    private val isTokenChecked = MutableStateFlow(false)
    private val isUpdateChecked = MutableStateFlow(false)

    val canProceedToHomeScreen =
        combine(isAnimationComplete, isTokenChecked, isUpdateChecked) { animation, token, update ->
            animation && token && update
        }

    fun login(request: LoginRequest, userEmail: String) = repository.login(request, userEmail)

    fun validateUserName(name: String) = repository.validateUserName(name)

    fun getUserDetails(map: HashMap<String, String>) = repository.getUserDetails(map)

    fun getUserPreference() = repository.getUserPreference()

    fun setUserPreference(prefData: PreferenceData) = repository.setUserPreference(prefData)

    fun updateUserDetails(filePart: MultipartBody.Part?, map: HashMap<String, RequestBody>) =
        repository.updateUserDetails(filePart, map)

    fun updateOnBoardingFlag(request: OnBoardingComplete) = repository.updateOnBoardingFlag(request)

    fun getLocations(map: HashMap<String, Any>) = repository.getLocations(map)

    fun getGenre() = repository.getGenre()

    fun fetchAndReplaceQueue() = repository.fetchAndReplaceQueue()

    fun updateGenre(request: GenreUpdateRequest) = repository.updateGenre(request)

    fun getHomeData() = repository.getHomeData()

    fun getAppConfig() = repository.getAppConfig()

    fun setCheckToken(isChecked: Boolean) = isTokenChecked.update { isChecked }
    fun setCheckUpdate(isChecked: Boolean) = isUpdateChecked.update { isChecked }
    fun setAnimationComplete(isComplete: Boolean) = isAnimationComplete.update { isComplete }
}