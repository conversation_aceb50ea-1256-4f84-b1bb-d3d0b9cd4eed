package noice.app.modules.onboarding.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.ActivityCompleteProfileBinding
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.onboarding.fragments.LoginConfirmationDialog
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.onboarding.viewmodel.CompleteProfileViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClassVariantProvider
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.parcelable
import noice.app.views.SnackBarCustom
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class CompleteProfileActivity : AppCompatActivity() {

    companion object {
        private const val LOGIN_CHANGE_EVENT = "LOGIN_CHANGE_EVENT"
        private const val SOURCE_SCREEN = "SOURCE_SCREEN"

        fun start(ctx: Context, source: String, loginChangeEvent: LoginChangeEvent? = null) {
            Intent(ctx, CompleteProfileActivity::class.java).apply {
                putExtra(LOGIN_CHANGE_EVENT, loginChangeEvent)
                putExtra(SOURCE_SCREEN, source)
                ctx.startActivity(this)
            }
        }
    }

    private lateinit var binding: ActivityCompleteProfileBinding
    private val viewModel: CompleteProfileViewModel by viewModels()
    private var navController: NavController? = null
    private var loginChangeEvent: LoginChangeEvent? = null
    private var source = ""
    private var confirmationDialog : LoginConfirmationDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCompleteProfileBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getDataFromIntent()

        initViews()

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Onboarding_Profile_Completion")
            putString("previousScreen", source)
        })
    }

    private fun getDataFromIntent() {
        if (intent.hasExtra(LOGIN_CHANGE_EVENT)) {
            loginChangeEvent = intent.parcelable(LOGIN_CHANGE_EVENT)
        }
        if (intent.hasExtra(SOURCE_SCREEN)) {
            source = intent.getStringExtra(SOURCE_SCREEN) ?: ""
        }
    }

    private fun initViews() {
        viewModel.subscribeEvents(this) { event ->
            if (event == CompleteProfileViewModel.EVENT_UPDATE_DETAILS) {
                updateDetails()
            }
        }

        val navHostFragment = supportFragmentManager.findFragmentById(R.id.navHostCompleteProfile) as NavHostFragment
        navController = navHostFragment.navController

        binding.toolBar.setBackClick {
            onBackPressedDispatcher.onBackPressed()
        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (navController?.currentDestination?.label == getString(R.string.birthday)) {
                    confirmationDialog = LoginConfirmationDialog()
                    confirmationDialog?.show(supportFragmentManager, "confirm")
                } else {
                    navController?.navigateUp()
                }
            }
        })

        navController?.addOnDestinationChangedListener { _, destination, _ ->
            if (destination.label == getString(R.string.birthday)) {
                binding.progressBar.progress = 50
            } else {
                binding.progressBar.progress = 85
            }
        }
    }

    private fun updateDetails() {
        binding.errorView.showLoading()

        val request = viewModel.getCompleteProfileRequest()
        viewModel.updateOnBoardingDetails(request).observe(this) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data == true) {
                binding.errorView.hide()
                goToHome()
            } else {
                binding.errorView.hide()
                SnackBarCustom.Builder()
                    .parentView(binding.root)
                    .text(it?.message ?: getString(R.string.something_is_wrong))
                    .show()
            }
        }
    }

    private fun goToHome() {
        PrefUtils.onBoardingStep = 0

        MoEngageAnalytics.sendEvent(this, "onboarding completed", "", null)

        val i = Intent(this, ClassVariantProvider.of(HomeActivity::class.java))
        if (loginChangeEvent != null) {
            EventBus.getDefault().post(loginChangeEvent)
        } else {
            i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        startActivity(i)
    }

    override fun onDestroy() {
        if (confirmationDialog?.isAdded == true) {
            confirmationDialog?.dismissAllowingStateLoss()
        }
        super.onDestroy()
    }
}