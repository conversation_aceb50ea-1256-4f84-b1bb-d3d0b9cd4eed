package noice.app.modules.media.model

import android.os.Parcelable
import androidx.room.Embedded
import androidx.room.Ignore
import kotlinx.parcelize.Parcelize
import noice.app.model.appconfig.LiveCreator

@Parcelize
data class Community(
    @Embedded
    var aggregations: Aggregations?,
    var entityId: String?,
    @Ignore
    var live: LiveCreator?,
    @Ignore
    var userId: String?,
    var timeElapsed : Long?,
    var markedAsPlayed :Boolean?,
    @Ignore
    var createdAt :  String?,
    @Ignore
    var participantCount :  Int?,
    @Ignore
    var speakerCount :  Int?,
    @Ignore
    @JvmField
    var isListened :  Int?,
    var totalDuration :  Long?,
    var contentCount :  Int?,
    @Ignore
    var userActions: ArrayList<MediaAction>? = ArrayList(),
    @Ignore
    @JvmField
    var isReported: Boolean? = false,
    @Ignore
    var parentId: String = "",
    @Ignore
    var reminderOptIn:Boolean = false,
    @Ignore
    var recommendationSource: String? = null,
    var state: Int?,
    var cuePointsCount: Int?,
    var showCue: Boolean? = false,
) : Parcelable {
    //Do not delete constructor. Need in Firebase Store
    constructor() : this(
        Aggregations(),
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        ArrayList(),
        false,
        "",
        false,
        "",
        null,
        null,
        false
    )
}