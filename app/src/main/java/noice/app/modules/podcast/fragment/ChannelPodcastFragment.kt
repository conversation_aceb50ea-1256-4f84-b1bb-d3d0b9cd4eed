package noice.app.modules.podcast.fragment

import android.Manifest
import android.content.Context
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentChannelBinding
import noice.app.enums.UserAction
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.ads.AdsResponse
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.model.user.User
import noice.app.modules.ads.GaManager
import noice.app.modules.audiobook.fragment.AudioBookDetailFragment
import noice.app.modules.audioseries.fragment.AudioSeriesFragment
import noice.app.modules.dashboard.home.fragment.CatalogMenu
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.podcast.adapter.ArtistAdapter
import noice.app.modules.podcast.adapter.ArtistAdapter.Companion.CREATOR_VIEW
import noice.app.modules.podcast.adapter.GenreAdapter
import noice.app.modules.podcast.adapter.SimilarCatalogPagerAdapter
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.model.Payload
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.modules.radio.fragment.RadioDetailFragment
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.ResponseStatus
import noice.app.room.LocalCacheData
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.AnalyticsUtil
import noice.app.utils.CacheUtils
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PODCAST
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.utils.Constants.Companion.IN_APP_FOLLOW_CLICKED
import noice.app.utils.Constants.Companion.USER_ACTIVITY_FOLLOW
import noice.app.utils.EventConstant
import noice.app.utils.ExperimentUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PermissionUtils
import noice.app.utils.PrefUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.parcelable
import noice.app.utils.putAnalyticsKey
import noice.app.views.CustomAdView
import noice.app.views.PopupFilterMenu
import noice.app.views.ReadMoreTextView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

@AndroidEntryPoint
class ChannelPodcastFragment : Fragment(), AppBarLayout.OnOffsetChangedListener {

    companion object {
        private const val PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR = 0.6f
        private const val PERCENTAGE_TO_HIDE_TITLE_DETAILS = 0.5f
        private const val ALPHA_ANIMATIONS_DURATION = 600
        private const val CHANNEL_ID = "channelId"
        private const val SOURCE = "SOURCE"
        private const val PAGE_SOURCE = "PAGE_SOURCE"
        private const val PLAY_CONTENT_DIRECTLY = "PLAY_CONTENT_DIRECTLY"
        private const val EXTRA_DATA = "EXTRA_DATA"
        private const val CHANNEL_PODCAST_PAGE = "Channel_Podcast_Page"

        fun newInstance(id : String, source : String, pageSource : String, playContentDirectly: Boolean = false, extraData: ExtraData? = null) = ChannelPodcastFragment().apply {
            arguments = Bundle().apply {
                putString(CHANNEL_ID, id)
                putString(SOURCE, source)
                putString(PAGE_SOURCE, pageSource)
                putBoolean(PLAY_CONTENT_DIRECTLY, playContentDirectly)
                putParcelable(EXTRA_DATA, extraData)
            }
        }
    }

    private var mediaList: ArrayList<Content>? = null
    private val podcastViewModel: ChannelPodcastViewModel by viewModels()

    private lateinit var binding: FragmentChannelBinding
    private lateinit var ctx: Context
    private var artistAdapter: ArtistAdapter? = null
    private var userList = ArrayList<User>()
    private var podcastId: String = ""
    private var title: String = ""
    private var podcast: Channel? = null
    private var mIsTheTitleVisible = false
    private var mIsTheTitleContainerVisible = true
    private var episodeList = ArrayList<Content>()
    private var isFollowChannel = 0.0
    private var isSamePage = false
    private var followClickTime: Long = 0
    private var source:String?=null
    private var pageSource : String = ""
    private var playContentDirectly = false
    private var extraData: ExtraData? = null
    private var isSubscribedFromPopup = false
    private lateinit var mediaActionFromPopup : MediaAction

    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null
    private var adsResponse: AdsResponse? = null

    private var pagerAdapter: SimilarCatalogPagerAdapter? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        podcastId = arguments?.getString(CHANNEL_ID) ?: ""
        source = arguments?.getString(SOURCE) ?: ""
        pageSource = arguments?.getString(PAGE_SOURCE) ?: ""
        playContentDirectly = arguments?.getBoolean(PLAY_CONTENT_DIRECTLY) ?: false
        extraData = arguments?.parcelable(EXTRA_DATA)

        if (extraData?.source.isNullOrEmpty()) {
            extraData?.source = CHANNEL_PODCAST_PAGE
        }

        EventBus.getDefault().register(this)

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentChannelBinding.inflate(LayoutInflater.from(ctx), container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()

        setToolbar()

        getData()
    }

    private fun setToolbar() {
        binding.threeDotMenu.setOnClickListener {
            val bundle = Bundle()
            bundle.putParcelable(CatalogMenu.CHANNEL, podcast)
            bundle.putString(CatalogMenu.ENTITY_SUB_TYPE, podcast?.type.toString())
            bundle.putString(CatalogMenu.PAGE_SOURCE, AnalyticsUtil.podcast_channel)
            val catalogMenu = CatalogMenu()
            catalogMenu.arguments = bundle
            catalogMenu.show(
                childFragmentManager,
                "catalogMenu"
            )
        }
    }

    /* Display ads are visible when it's active/enabled at both Catalog and Ads api level. */
    private fun getDisplayAds() {
        podcastViewModel.getAds(
            Constants.Ads.AdsEntityType.PAGE,
            Constants.Ads.AdsEntityValue.CATALOG_DETAIL
        ).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (it.data?.data?.isActive == true) {
                    adsResponse = it.data.data
                    setAd()
                }
            }
        }
    }

    private fun getData(ignoreCache: Boolean = false) {
        showLoading()

        getDataFromCache(ignoreCache)
    }

    override fun onStart() {
        super.onStart()

        MoEngageAnalytics.setOrResetInAppContext(setOf("catalog podcast"))
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(ctx)
        MoEInAppHelper.getInstance().showNudge(ctx)
    }

    private fun initView() {
        initSimilarContentUi()

        binding.rvArtist.viewHolder.recyclerView.apply {
            val orientation = RecyclerView.HORIZONTAL
            layoutManager = LinearLayoutManager(ctx, orientation, false)
            setHasFixedSize(true)
            addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp8)))
        }

        binding.mediaButton.setOnClickListener {
            if (DataController.isAdPlaying) {
                Utils.showSnackBar(ctx, getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            if (DataController.playingEntityId == podcastId) {
                if (ctx.getPlayerActivity()?.isPlayWhenReady() == true){
                    ContentPlayRequest.Builder()
                        .mediaButton(binding.mediaButton)
                        .playWhenReady(ctx, false)
                    AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST, "catalog_pause_clicked", podcastId, podcast?.title.toString(), "primary play button")
                } else {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, true)
                    AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST, "catalog_play_clicked", podcastId, podcast?.title.toString(), playButtonPosition = "primary play button")
                }
            } else {
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST, "catalog_play_clicked", podcastId, podcast?.title.toString(), playButtonPosition = "primary play button")

                val list = if (podcast?.selectedFilter.equals(PopupFilterMenu.FILTER_ASC, true)) {
                    episodeList
                } else {
                    podcast?.startingContents ?: ArrayList()
                }

                mediaList = list
                ContentPlayRequest.Builder()
                    .contents(list)
                    .mediaButton(binding.mediaButton)
                    .pageSource(AnalyticsUtil.podcast_channel)
                    .queueTitle(podcast?.title.toString())
                    .extraData(extraData)
                    .addCatalogContents(true)
                    .catalog(podcast)
                    .play(ctx)
            }

            DataController.setCurrentlyPlaying(DataController.POD_CAST_PAGE, podcastId, DataController.PRIMARY_PLAY_BUTTON)
        }

        /*binding.imgSearch.setOnClickListener {
            AnalyticsUtil.sendEventForOpenScreen("","search_bar_clicked",AnalyticsUtil.podcast_channel)
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Channel_Podcast_Page"))
        }*/

        binding.errorView.setOnReturnClick {
            showLoading()
            getChannelDetail()
        }

        binding.appBar.addOnOffsetChangedListener(this)
        startAlphaAnimation(binding.txtToolbarTitle, 0, View.INVISIBLE)
        binding.collapsingToolbar.collapsedTitleGravity = Gravity.START

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.layoutFollow.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (SystemClock.elapsedRealtime() - followClickTime < 800){
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-subscribe-catalog")
                ctx.getPlayerActivity()?.handleUserNotLoggedIn(loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            followClickTime = SystemClock.elapsedRealtime()

            val value = if (isFollowChannel > 0.0) {
                0.0
            } else {
                1.0
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (value == 1.0) { //catalog followed
                    /* notification permission for Android 13 */
                    if (permissionUtils?.isAllPermissionGranted() == false) {
                        permissionUtils?.requestPermissions()
                        return@setOnClickListener
                    }
                }
            }

            if (value == 0.0){
                var count = podcast?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count -= 1
                    podcast?.meta?.aggregations?.followers = count
                }
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST, "catalog_unfollowed", podcast?.id.toString(),podcast?.title.toString())

                MoEngageAnalytics.sendEvent(ctx, "catalog unfollowed", Bundle().apply {
                    putString("vertical", ENTITY_TYPE_PODCAST)
                    putString("catalog title", podcast?.title ?:"")
                    putString("catalog id", podcastId)
                    putStringArrayList(
                        "genre",
                        ArrayList(podcast?.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", podcast?.source?:"")
                })
            }else{
                var count = podcast?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count += 1
                    podcast?.meta?.aggregations?.followers = count
                }else{
                    podcast?.meta?.aggregations?.followers = 1
                }
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST, "catalog_followed", podcast?.id.toString(),podcast?.title.toString())
                MoEngageAnalytics.sendEvent(ctx, "catalog followed", Bundle().apply {
                    putString("vertical", ENTITY_TYPE_PODCAST)
                    putString("catalog title", podcast?.title ?:"")
                    putString("catalog id", podcastId)
                    putStringArrayList(
                        "genre",
                        ArrayList(podcast?.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", podcast?.source?:"")
                    putString("image url", podcast?.imageMeta?.size300 ?: "")
                })
            }

            isFollowChannel = value
            val eventName = if (value == 0.0){
                "catalog_unfollowed"
            }else{
                "catalog_followed"
            }
            AnalyticsUtil.sendEventForCatalogFollow(ENTITY_TYPE_PODCAST,  eventName, podcast?.id, podcast?.title?:"", "catalog_page")

            updateFollow()

            val mediaAction = if (PrefUtils.isLoggedIn) {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, podcastId,
                    "catalog",
                    PrefUtils.userDetails?.id.toString(),
                    podcast?.type,
                    null
                )
            } else {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, podcastId,
                    "catalog",
                    PrefUtils.guestId,
                    podcast?.type,
                    null
                )
            }

            podcastViewModel.performAction(if(!isSubscribedFromPopup) mediaAction else mediaActionFromPopup).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS && !it.data?.userActions.isNullOrEmpty()) {
                    it.data?.userActions?.find { mediaAction ->
                        mediaAction.action.equals(UserAction.FOLLOW.action, true)
                    }?.let { isFollow ->
                        isFollowChannel = isFollow.actionValue ?: 0.0
                        isSamePage = true
                        EventBus.getDefault().post(EventMessage(if(!isSubscribedFromPopup) mediaAction else mediaActionFromPopup, USER_ACTIVITY_FOLLOW))
                        updateFollow()

                        if (podcast?.meta?.userActions.isNullOrEmpty()) {
                            podcast?.meta?.userActions = ArrayList()
                            podcast?.meta?.userActions?.add(isFollow)
                        }
                    }
                }
            }
        }

        binding.imgShare.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            val dialog = ShareDialog.newInstance(podcast,false,null)
            dialog.show(childFragmentManager, "share")
        }
        artistAdapter = ArtistAdapter(userList, CREATOR_VIEW, object : OnClickInterface<User> {
            override fun dataClicked(data: User) {

            }
        },"Podcast_Catalog_Page")

        binding.rvArtist.getRecyclerView().adapter = artistAdapter

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handleCatalogPlayButton(it)
            }
    }

    private fun initSimilarContentUi() {
        pagerAdapter = SimilarCatalogPagerAdapter(this, podcastId, episodeList, podcast, extraData, CHANNEL_PODCAST_PAGE, "Podcast_Catalog_Page")
        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.isUserInputEnabled = false
        binding.viewPager.clearAnimation()

        val tabNames =
            BaseApplication.getBaseAppContext().resources.getStringArray(R.array.similar_content_tabs_name)

        TabLayoutMediator(binding.tabLayout, binding.viewPager, true, false) { tab, position ->
            tab.setCustomView(R.layout.tab_layout_custom)
            tab.customView?.let { view ->
                view.findViewById<TextView>(R.id.tabTitle)?.text = tabNames[position]

                if (position == 1) {
                    if (!PrefUtils.isSimilarCatalogTabClicked && PrefUtils.appCDNConfig?.tabLabelBaruSimilarCatalogs == true) {
                        view.findViewById<ImageView>(R.id.tabBadge).visibility = VISIBLE
                    } else {
                        view.findViewById<ImageView>(R.id.tabBadge).visibility = GONE
                    }
                }
            }
        }.attach()


        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }
        })

        for (i in 0 until binding.tabLayout.tabCount) {
            binding.tabLayout.getTabAt(i)?.view?.setBackgroundResource(R.drawable.custom_ripple_bg_4dp)
        }

        updateTabText(binding.tabLayout.getTabAt(0))

        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                if (position == 1) {
                    PrefUtils.isSimilarCatalogTabClicked = true
                    removeBadge(binding.tabLayout.getTabAt(1))

                    AnalyticsBuilder.newBuilder().apply {
                        putAnalyticsKey("catalog title", podcast?.title)
                        putAnalyticsKey("catalog id", podcast?.id)
                        putAnalyticsKey("vertical", ENTITY_TYPE_PODCAST)
                    }.send("similar catalog opened")
                }
            }
        })
    }

    private fun updateTabText(tab: TabLayout.Tab?) {
        tab?.customView?.let { view ->
            view.findViewById<TextView>(R.id.tabTitle).let { tabTitle ->
                if (tab.isSelected) {
                    tabTitle.typeface = Typeface.create(tabTitle.typeface, Typeface.BOLD)
                    tabTitle.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                } else {
                    tabTitle.typeface = Typeface.create(tabTitle.typeface, Typeface.NORMAL)
                    tabTitle.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                }
            }
        }
    }

    private fun removeBadge(tab: TabLayout.Tab?) {
        tab?.customView?.let { view ->
            view.findViewById<ImageView>(R.id.tabBadge).visibility = GONE
        }
    }

    private fun handleCatalogPlayButton(playerEvent: PlayerEvent) {
        if (DataController.playingEntityId == podcastId) {
            binding.mediaButton.handleMusicButton(playerEvent)
        }
    }

    private fun updateFollow() {
        if (isFollowChannel == 0.0) {
            binding.txtFollow.text = getString(R.string.subscribe)
            binding.layoutFollow.isSelected = false
            binding.txtFollow.isSelected = false
            binding.imgFollow.setImageResource(R.drawable.ic_follow_yellow)
        } else {
            binding.layoutFollow.isSelected = true
            binding.txtFollow.isSelected = true
            binding.txtFollow.text = getString(R.string.subscribed)
            binding.imgFollow.setImageResource(R.drawable.ic_follow_yellowed)
        }

        podcast?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            isFollow.actionValue = isFollowChannel
        }

        handleCatalogInfo()
    }

    private fun handleCatalogInfo() {
        Utils.getCatalogContract(podcast?.data).let { data ->
            binding.txtEpsCount.text =
                (podcast?.meta?.contentCount ?: 0).toString()
            binding.txtCount.text = Utils.abbrNumberFormat(podcast?.meta?.aggregations?.followers ?: 0)
        }
    }

    private fun openFragment(fragment: Fragment, tag : String) {
        if ( isRemoving || isDetached )
            return
        parentFragment?.childFragmentManager?.popBackStack()
        parentFragment?.childFragmentManager?.beginTransaction()
            ?.add(R.id.homeContainer, fragment)
            ?.addToBackStack(tag)
            ?.commit()
    }

    private fun getDataFromCache(ignoreCache: Boolean = false) {
        if (ignoreCache) {
            getChannelDetail()
        } else {
            CacheUtils.getParsedCachedData<BaseModel<Channel>>(this, podcastId) { data ->
                if (!checkForEntitySubType(data?.data)) {
                    if (data != null) {
                        handleChannelResponse(data, "", true)
                    }

                    getChannelDetail()
                }
            }
        }
    }

    private fun getChannelDetail(limit : Int = 5, from : String = "", offset: Int = -1) {

        val map = HashMap<String, String>()
        if (offset != -1) {
            map["offset"] = offset.toString()
        } else {
            map["page"] = "1"
        }
        map["limit"] = limit.toString()
        map["sort"] = if (podcast?.selectedFilter.equals(PopupFilterMenu.FILTER_ASC, true)) {
            "ASC"
        } else {
            "DESC"
        }
        map["orderBy"] = "published_at"

        if (isDetached || isRemoving || !isAdded)
            return

        podcastViewModel.getChannelDetail(podcastId, map, false).observe(viewLifecycleOwner) { resource ->

            if (resource?.status != ResponseStatus.LOADING &&
                checkForEntitySubType(resource?.data?.data)) {
                return@observe
            }

            when(resource?.status) {
                ResponseStatus.SUCCESS -> {
                    if (resource.data?.data != null) {
                        resource.data.data?.selectedFilter = null
                        handleChannelResponse(resource.data, from)
                        //displayGamAd()
                    } else {
                        handleError(resource.message)
                    }
                }
                ResponseStatus.ERROR -> {
                    handleError(resource.message)
                }
                else -> {

                }
            }
        }
    }

    private fun displayGamAd() {
        if ((!PrefUtils.appCDNConfig?.nativeAdsConfig?.nativeAdUnitId.isNullOrEmpty() ||
                    !PrefUtils.appCDNConfig?.nativeAdsConfig?.customNativeAdUnitId.isNullOrEmpty()) &&
            PrefUtils.userDetails?.isSubscribed == false
        ) {
            GaManager.refreshAd(podcast?.id, null, getGenresAsString())
        }
    }

    private fun checkForEntitySubType(data: Channel?) : Boolean {
        if (data?.type.equals(ENTITY_TYPE_RADIO, true)) {
            val fragment = RadioDetailFragment.newInstance(
                podcastId,
                sourcePage = "Deep_Link",
                extraData = extraData,
                playContentDirectly = playContentDirectly
            )
            openFragment(fragment, RadioDetailFragment::class.java.simpleName)
            return true
        } else if (data?.type.equals(Constants.ENTITY_TYPE_AUDIO_SERIES, true)) {
            val fragment = AudioSeriesFragment.newInstance(podcastId, source = "Deep_Link", pageSource = "Deep_Link", extraData = extraData)
            openFragment(fragment, AudioSeriesFragment::class.java.simpleName)
            return true
        } else if (data?.type.equals(ENTITY_TYPE_AUDIO_BOOK, true)) {
            val fragment = AudioBookDetailFragment.newInstance(podcastId, source = "Deep_Link", pageSource = "Deep_Link", extraData = extraData)
            openFragment(fragment, AudioBookDetailFragment::class.java.simpleName)
            return true
        }

        return false
    }

    private fun handleError(message :String?) {
        if (podcast == null) {
            binding.errorView.showError(message, type = "Podcast_Catalog_Page", errorName = message)
        } else {
            hideLoading()
        }
    }

    private fun handleChannelResponse(
        data: BaseModel<Channel>?,
        from: String,
        cachedData: Boolean = false
    ) {
        if (data?.data?.selectedFilter.isNullOrEmpty()) {
            data?.data?.selectedFilter = if (!podcast?.selectedFilter.isNullOrEmpty()) {
                podcast?.selectedFilter
            } else {
                PopupFilterMenu.FILTER_DESC
            }
        }

        podcast = data?.data

        if (!cachedData) {
            CacheUtils.putDataToCache(LocalCacheData(podcastId, Gson().toJson(data)))
        }

        if (podcast?.displayAdsEnabled == true && !cachedData && PrefUtils.userDetails?.isSubscribed == false) {
            getDisplayAds()
        }

        setChannelDetail(cachedData)

        if (DataController.playingEntityId == podcastId) {
            val buttonState =
                ctx.getPlayerActivity()?.getContentPlayState(DataController.playerContentId)
            if (buttonState?.playWhenReady == true) {
                handleCatalogPlayButton(PlayerEvent(PlayerEvent.PLAY))
            }
            if (buttonState?.waiting == true) {
                handleCatalogPlayButton(PlayerEvent(PlayerEvent.SHOW_LOADER))
            }
        }

        if (!podcast?.users.isNullOrEmpty()) {
            binding.rvArtist.visibility = VISIBLE
            userList.clear()
            userList.addAll(podcast?.users ?: ArrayList())
            artistAdapter?.notifyDataSetChanged()
        } else {
            binding.rvArtist.visibility = GONE
        }

        hideLoading()

        if (!cachedData) {
            sendEvents()
        }
    }

    private fun sendEvents() {
        AnalyticsUtil.sendEventForCatalog(
            "catalog_page_opened",
            podcastId,
            podcast?.title ?: "",
            "",
            podcast?.source ?: "",
            extraData
        )

        extraData?.apply {
            catalogLink = "${Constants.WEB_BASE_URL}catalog/${podcast?.id}"
        }

        MoEngageAnalytics.sendEventForPodcastDetail(
            ctx,
            "detail page viewed",
            source,
            pageSource,
            podcast,
            extraData
        )

        AnalyticsUtil.sendEvent(
            FirebaseAnalytics.Event.SCREEN_VIEW,
            Bundle().apply {
                putString(
                    FirebaseAnalytics.Param.SCREEN_NAME,
                    "Podcast_Catalog_Page"
                )
                putString("previousScreen", source)
                putString("catalogTitle", podcast?.title)
                putString("catalogId", podcast?.id)
                putString("entitiySubtype", podcast?.type)
            })
    }

    private fun setChannelDetail(isCached: Boolean = false) {
        if (isDetached || isRemoving || !isAdded)
            return
        Utils.getCatalogContract(podcast?.data).let { data ->

            if (!data?.donationUrl.isNullOrEmpty()) {
                binding.donateView.setRedirectionUrl(data?.donationUrl.toString())
                binding.donateView.visibility = VISIBLE
            } else {
                binding.donateView.visibility = GONE
            }

            //waiting for product response to keep or remove it

           /* val addInfo = ArrayList<RssAuthor>()

            if (!data?.rssAuthor.isNullOrEmpty()) {
                data?.rssAuthor?.groupBy { author ->
                    author.role ?: ""
                }?.forEach { groupedMap ->
                    groupedMap.value.map { mappedAuthor ->
                        mappedAuthor.name ?: ""
                    }.let { namesList ->
                        val names = Utils.getAuthorString(ArrayList(namesList))
                        addInfo.add(RssAuthor(names, groupedMap.key))
                    }
                }
            }

            if (!data?.copyright.isNullOrEmpty()) {
                addInfo.add(RssAuthor(data?.copyright, getString(R.string.copyright)))
            }

            if (addInfo.isNotEmpty()) {

                if (rssAuthorAdapter == null) {
                    rssAuthorAdapter = RssAuthorAdapter()
                    binding.authorGridRecycler.adapter = rssAuthorAdapter
                    binding.authorGridRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp16)))
                }
                rssAuthorAdapter?.setData(addInfo)

                binding.addInfoTitle.visibility = VISIBLE
                binding.authorGridRecycler.visibility = VISIBLE
            } else {
                binding.addInfoTitle.visibility = GONE
                binding.authorGridRecycler.visibility = GONE
            } */
        }

        /*if (!podcast?.contents.isNullOrEmpty()) {

            episodeList.clear()

            if ((podcast?.contents?.size ?: 0) > 4) {
                podcast?.contents?.take(4)?.let { episodes ->
                    episodeList.addAll(episodes)
                }

                val text = "Lihat semua episode (" + (podcast?.meta?.contentCount ?: 0) + ")"
                binding.txtLihatSemua.text = text
                binding.txtLihatSemua.visibility = VISIBLE
            } else {
                episodeList.addAll(podcast?.contents ?: ArrayList())
                binding.txtLihatSemua.visibility = GONE
            }

            adapter?.notifyDataSetChanged()

            if (!isCached) {
                pagerAdapter?.setCatalog(podcast)
            }
        }
*/
        handleCatalogInfo()

        binding.txtTitle.text = podcast?.title

        if (!podcast?.description.isNullOrEmpty() || !podcast?.htmlDescription.isNullOrEmpty()) {
            val description = if (!podcast?.htmlDescription.isNullOrEmpty()) {
                podcast?.htmlDescription.toString()
            } else {
                podcast?.description.toString()
            }

            binding.txtEpisodDesc.setText(
                description
            )

            binding.txtEpisodDesc.setListener(object : ReadMoreTextView.Listener {
                override fun onExpandClicked(isSeeMore: Boolean) {
                    if (isSeeMore) {
                        val containsURL =
                            if (description.contains("https://") || description.contains("http://")) {
                                "yes"
                            } else {
                                "no"
                            }

                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", CHANNEL_PODCAST_PAGE)
                        bundle.putAnalyticsKey("catalogId", podcast?.id)
                        bundle.putAnalyticsKey("catalogTitle", podcast?.title)
                        bundle.putAnalyticsKey("entitySubType", podcast?.type)
                        bundle.putAnalyticsKey("containsURL", containsURL)
                        AnalyticsUtil.sendEvent("expand_catalog_description", bundle)
                    }
                }

                override fun onLinkClicked(uri: Uri?) {
                    val url = uri?.toString() ?: return
                    if (url.isNotEmpty()) {
                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", CHANNEL_PODCAST_PAGE)
                        bundle.putAnalyticsKey("catalogId", podcast?.id)
                        bundle.putAnalyticsKey("catalogTitle", podcast?.title)
                        bundle.putAnalyticsKey("entitySubType", podcast?.type)
                        bundle.putAnalyticsKey("linkDetail", url)
                        AnalyticsUtil.sendEvent("description_link_clicked", bundle)
                    }
                }
            })

            binding.txtEpisodDesc.visibility = VISIBLE
        } else {
            binding.txtEpisodDesc.visibility = GONE
        }

        ImageUtils.loadImageByUrl(binding.imgPodcast, podcast?.imageMeta?.size300, placeHolder = R.drawable.ic_thumb_default, originalUrl = podcast?.image)
        var url = podcast?.imageMeta?.size300
        if (url.isNullOrEmpty()) url = podcast?.image
        ImageUtils.showBlurImage(url, binding.imgCover)

        title = podcast?.title.toString()
        binding.txtToolbarTitle.text = title

        podcast?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            if(isFollow.actionValue != null) {
                isFollowChannel = isFollow.actionValue!!
                updateFollow()
            }
        }

        if (podcast?.genres.isNullOrEmpty()) {
            binding.rvGenre.visibility = GONE
        } else {
            val genreAdapter = GenreAdapter(ctx, podcast?.genres ?: ArrayList(),"Podcast_Catalog_Page")
            if (binding.rvGenre.itemDecorationCount > 0) {
                binding.rvGenre.removeItemDecorationAt(0)
            }
            binding.rvGenre.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(
                R.dimen.dp8
            )))
            binding.rvGenre.adapter = genreAdapter
            binding.rvGenre.visibility = VISIBLE
        }

        //setRssContent()

        if (playContentDirectly) {
            playContentDirectly = false
            binding.mediaButton.performClick()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayerAction(event: EventMessage) {
        if (event.eventCode == USER_ACTIVITY_FOLLOW) {
            if (isSamePage) {
                isSamePage = false
            } else {
                val data = event.data as MediaAction
                val followAction = podcast?.meta?.userActions?.find { mediaAction ->
                    mediaAction.action.equals(UserAction.FOLLOW.action, true)
                }
                isFollowChannel = data.actionValue ?: 0.0
                if (followAction != null) {
                    followAction.actionValue = data.actionValue
                    val index = podcast?.meta?.userActions?.indexOf(followAction)
                    podcast?.meta?.userActions?.set(index ?: 0, followAction)
                } else {
                    podcast?.meta?.userActions?.add(data)
                }
                isSamePage = false
                updateFollow()
            }
        } else if (event.eventCode == IN_APP_FOLLOW_CLICKED) {
            val data = event.data as MediaAction
            mediaActionFromPopup = data
            binding.layoutFollow.performClick()
        }
    }

    /*private fun setRssContent() {
        val source = if (!podcast?.source.isNullOrEmpty()) {
            podcast?.source ?: ""
        } else if (episodeList.isNotEmpty() && !episodeList[0].catalog?.source.isNullOrEmpty()) {
            episodeList[0].catalog?.source ?: ""
        } else {
            ""
        }

        if (podcast?.type != ENTITY_TYPE_LIVE_STREAM && (source.equals(SOURCE_EXCLUSIVE, true) || source.equals(SOURCE_ORIGINAL, true) || source.equals(SOURCE_SPECIAL, true))) {
            binding.origExc.text = source.uppercase()
            binding.origExc.visibility = VISIBLE
        } else {
            binding.origExc.visibility = GONE
        }
    }*/

    private fun markAsPlayed(content: Content) {
        val analyticsAction: String
        val action: String
        if (content.meta?.markedAsPlayed == true){
            action = EventConstant.MARK_PLAYED
            analyticsAction = "content_marked_as_played"
        }else{
            action = EventConstant.MARK_UN_PLAYED
            analyticsAction = "content_marked_as_unplayed"
        }

        val payload = Payload(content.meta?.timeElapsed?.toInt(),content.id,"content",content.catalog?.type,AnalyticsUtil.podcast_channel,PrefUtils.uniqueId,content.duration?.toInt(),
            catalogId = content.catalog?.id ?: "")

        val event = EventPost(action,payload,contentId = content.id?:"")
        AnalyticsUtil.sendEvent(
            content.catalog?.type,
            content.id,
            podcastId,
            analyticsAction,
            AnalyticsUtil.podcast_channel,
            content.meta?.timeElapsed.toString(),
            content.duration.toString(),
            catalogTitle = content.catalog?.title?:"",
            contentTitle =  content.title?:""
        )

        if (NetworkUtils.isNetworkConnected(ctx)){
            podcastViewModel.postEvent(event)
        }else{
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.channel_loader)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    override fun onOffsetChanged(appBarLayout: AppBarLayout?, offset: Int) {
        val maxScroll = appBarLayout?.totalScrollRange
        val percentage = abs(offset).toFloat() / (maxScroll?.toFloat() ?: 1f)

        handleAlphaOnTitle(percentage)
        handleToolbarTitleVisibility(percentage)
    }

    private fun handleToolbarTitleVisibility(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR) {
            if (!mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    VISIBLE
                )
                mIsTheTitleVisible = true
            }
        } else {
            if (mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    View.INVISIBLE
                )
                mIsTheTitleVisible = false
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {
        Handler(Looper.getMainLooper()).postDelayed({
            episodeList.find { it.isPremium == true }?.let {
                if (it.catalogId == podcastId) {
                    getData(true)
                }
            }
        },500)
    }

    private fun handleAlphaOnTitle(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_HIDE_TITLE_DETAILS) {
            if (mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, View.INVISIBLE)
                mIsTheTitleContainerVisible = false
            }
        } else {
            if (!mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, VISIBLE)
                mIsTheTitleContainerVisible = true
            }
        }
    }

    private fun startAlphaAnimation(v: View, duration: Int, visibility: Int) {
        val alphaAnimation =
            if (visibility == VISIBLE) AlphaAnimation(0f, 1f) else AlphaAnimation(1f, 0f)
        alphaAnimation.duration = duration.toLong()
        alphaAnimation.fillAfter = true
        v.startAnimation(alphaAnimation)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    private fun setAd() {
        val adView = CustomAdView(ctx)
        adView.setData(
            size = adsResponse?.config?.adSize,
            adUnitId = adsResponse?.config?.adId,
            viewGroup = binding.frameAdView,
            genres = getGenresAsString(),
            vertical = ENTITY_TYPE_PODCAST,
            isPlayedPreview = mediaList?.getOrNull(0)?.isPurchaseNeeded,
            page = "podcast_catalog_page"
        )
        binding.frameAdView.visibility = VISIBLE
        binding.frameAdView.addView(adView)
    }

    private fun getGenresAsString(): String {
        val genresString = podcast?.genres?.joinToString(",") { genre ->
            genre.name ?: ""
        }
        return genresString?.trim() ?: ""
    }
}