package noice.app.modules.podcast.viewstate

import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import noice.app.modules.coins.model.SubscriptionPackage

class PremiumDialogViewState {
    val subscriptionPackages = arrayListOf<SubscriptionPackage>()
    val subLiveData = MutableLiveData<List<SubscriptionPackage.SubscriptionPrice>>()
    val episodeBenefits = arrayListOf<SubscriptionPackage.SubscriptionBenefit>()
    val benefits = arrayListOf<SubscriptionPackage.SubscriptionBenefit>()
    val selectedSubPrice = ObservableField<SubscriptionPackage.SubscriptionPrice>()
    val isSubscription = ObservableField<Boolean>()
    val coinPurchaseEnable = ObservableField<Boolean>()
    val atauTextVisible = ObservableField<Boolean>()
}