package noice.app.modules.live.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.text.InputFilter
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import hani.momanii.supernova_emoji_library.Actions.EmojIconActions
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.InputDialogBinding
import noice.app.listner.*
import noice.app.modules.live.model.message.LiveMessage
import noice.app.modules.podcast.model.MentionData
import noice.app.modules.podcast.model.MentionUser
import noice.app.modules.search.adapter.UserAdapter
import noice.app.modules.search.model.SearchResult
import noice.app.modules.search.viewmodel.SearchViewModel
import noice.app.utils.Constants.Companion.LIVE_CHAT_INPUT_SAVE
import noice.app.utils.Constants.Companion.LIVE_CHAT_SENT
import noice.app.utils.ImageUtils
import noice.app.utils.InterfaceAdapter
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.mention.Mention
import noice.app.utils.mention.Mentions

@AndroidEntryPoint
class TextInputDialog(private val onClickInterface: OnClickInterface<String>) :
    BottomSheetDialogFragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        const val PROFILE_IMAGE = "profile_image"
        const val PREVIOUS_TEXT = "previous_text"
        const val MENTION = "mention"
        const val MENTINABLE = "MENTINABLE"
        const val MAX_CHAR_LIMIT = "max_char_limit"

        fun newInstance(profileImage: String?, previousText: String,mentionList:HashMap<String, MentionUser>,mentionable:List<Mentionable>? = ArrayList(), maxCharLimit: Int? = null, onClickInterface: OnClickInterface<String>) =
            TextInputDialog(onClickInterface).apply {
                arguments = Bundle().apply {
                    putString(PROFILE_IMAGE, profileImage)
                    putString(PREVIOUS_TEXT, previousText)
                    putString(MENTION,Gson().toJson(mentionList))
                    putString(MENTINABLE,Gson().toJson(mentionable))
                    maxCharLimit?.let { putInt(MAX_CHAR_LIMIT, it) }
                }
            }
    }

    private val searchViewModel : SearchViewModel by viewModels()

    private var _binding: InputDialogBinding? = null
    private val binding get() = _binding!!
    private lateinit var ctx: Context

    private var profileImage: String = ""
    private var isTextAvailable = false
    private var previousText: String = ""
    private var userAdapter: UserAdapter? = null
    private var mentions: Mentions?=null
    private var mentionList = HashMap<String, MentionUser>()
    private var userList:ArrayList<SearchResult> = ArrayList()
    private val limit = 10
    private var keyWord = ""
    private var page = 0
    private var mentionableList:ArrayList<Mentionable>? = ArrayList()

    // using this flag to prevent crash when live comment-box's view hierarchy changes when in background
    private var isInBackground = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.BottomSheetDialogInputText)
        profileImage = arguments?.getString(PROFILE_IMAGE, "") ?: ""
        previousText = arguments?.getString(PREVIOUS_TEXT, "") ?: ""
        val mention = arguments?.getString(MENTION, "") ?: ""
        val mentionable = arguments?.getString(MENTINABLE, "") ?: ""
        if (mention.isNotEmpty()){
            mentionList = Gson().fromJson(
                mention, object : TypeToken<HashMap<String?, Any?>?>() {}.type
            )
        }
        if (mentionable.isNotEmpty() && mentionable != "[]"){
            val resultType = object : com.google.gson.reflect.TypeToken<ArrayList<Mentionable>>() {}.type
            val builder = GsonBuilder()
            builder.registerTypeAdapter(resultType, InterfaceAdapter())
            val gson = builder.create()
            val jsonArray: ArrayList<Mention>? = gson.fromJson(mentionable, object : com.google.gson.reflect.TypeToken<ArrayList<Mention>>() {}.type)
            if (jsonArray != null) {
                mentionableList?.clear()
                for (it in jsonArray){
                    mentionableList?.add(it)
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = InputDialogBinding.inflate(inflater, container, false)
        return binding.root
    }


    override fun onResume() {
        super.onResume()
        isInBackground = false
    }

    override fun onPause() {
        super.onPause()
        isInBackground = true
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
        setMentions()
        binding.editTextComment.requestFocus()
        setSourceAndPrevious()
    }

    override fun onDestroyView() {
        onClickInterface.dataClicked(binding.editTextComment.text.toString().trim(), LIVE_CHAT_INPUT_SAVE,mentionList, getMentionableList())
        super.onDestroyView()
        _binding = null
    }

    fun getMentionableList(): ArrayList<Mentionable> {
        return (mentions?.insertedMentions?:ArrayList()).toMutableList() as ArrayList<Mentionable>
    }

    private fun initViews() {
        if (previousText.isNotEmpty()) {
            binding.editTextComment.setText(previousText)
            isTextAvailable = true
            binding.imageSend.setImageResource(R.drawable.ic_send_active)
        }

        arguments?.getInt(MAX_CHAR_LIMIT)?.let { maxLength ->
            binding.editTextComment.filters += InputFilter.LengthFilter(maxLength)
        }
        binding.editTextComment.doAfterTextChanged { text ->
            text?.let {
                isTextAvailable = if (it.isEmpty()) {
                    binding.imageSend.setImageResource(R.drawable.ic_send)
                    false
                } else {
                    binding.imageSend.setImageResource(R.drawable.ic_send_active)
                    true
                }
            }
        }

        /* handling one click back to close sheet (text/chat input bottom sheet) */
        var isKeyboardVisible = false
        binding.editTextComment.viewTreeObserver.addOnGlobalLayoutListener {
            if (keyboardShown(binding.editTextComment.rootView)) {
                isKeyboardVisible = true
            } else {
                if (isKeyboardVisible && !isInBackground) {
                    dismissAllowingStateLoss()
                }
            }
        }

        binding.imageSend.setOnClickListener {
            if (isTextAvailable) {
                it.isEnabled = false
                it.isClickable = false
                onClickInterface.dataClicked(binding.editTextComment.text.toString().trim(), LIVE_CHAT_SENT,mentionList, getMentionableList())
                dismissAllowingStateLoss()
            }
        }

        binding.imageSmiley.setOnClickListener {
            setUpEmojiPopup()
        }

        profileImage.let {
            ImageUtils.loadImageByUrl(
                binding.imageUserProfile,
                PrefUtils.userDetails?.smallImage,
                circleCrop = true,
                placeHolder = R.drawable.ic_user_profile,PrefUtils.userDetails?.originalImage
            )
        }
    }

    private fun setUpEmojiPopup() {
        val emojiKeyboardTabsIconColor = if (Utils.isDarkTheme(ctx)) "#707070" else "#495C66"
        val emojiKeyboardTabsColor = if (Utils.isDarkTheme(ctx)) "#0F1116" else "#DCE1E2"
        val emojiKeyboardBackgroundColor = if (Utils.isDarkTheme(ctx)) "#1e222c" else "#E6EBEF"

        val emojIcon = EmojIconActions(
            ctx,
            binding.root,
            binding.editTextComment,
            binding.imageSmiley,
            emojiKeyboardTabsIconColor,
            emojiKeyboardTabsColor,
            emojiKeyboardBackgroundColor
        )

        emojIcon.setIconsIds(R.drawable.ic_keyboard, R.drawable.ic_smileys)
        emojIcon.setKeyboardListener(object : EmojIconActions.KeyboardListener {
            override fun onKeyboardOpen() {
                Log.e("Keyboard", "open")
            }

            override fun onKeyboardClose() {
                Log.e("Keyboard", "close")
            }
        })
        emojIcon.ShowEmojIcon()
    }

    private fun keyboardShown(rootView: View): Boolean {
        val softKeyboardHeight = 100
        val r = Rect()
        rootView.getWindowVisibleDisplayFrame(r)
        val dm = rootView.resources.displayMetrics
        val heightDiff = rootView.bottom - r.bottom
        return heightDiff > softKeyboardHeight * dm.density
    }


    private fun setMentions() {
        mentions = Mentions.Builder(ctx,binding.editTextComment)
            .suggestionsListener(object : SuggestionsListener {
                override fun displaySuggestions(display: Boolean) {
                    if (display) {
                        binding.rvUser.visibility = View.VISIBLE
                        binding.userCrd.visibility = View.VISIBLE
                    } else {
                        binding.rvUser.visibility = View.GONE
                        binding.userCrd.visibility = View.GONE
                    }
                }
            })
            .removeMentionListener(object : MentionRemoveListener {
                override fun onRemoveMention(mentionList: MutableList<Mentionable>, mentionData: String) {
                    if (<EMAIL>(mentionData)){
                        <EMAIL>(mentionData)
                    }
                }
            })
            .queryListener(object : QueryListener {
                override fun onQueryReceived(query: String) {
                    if (keyWord.equals(query,true))
                        return
                    keyWord = query
                    page = 0
                    userAdapter?.clearRecyclerView()
                    <EMAIL>(keyWord)
                }
            }).build()

        mentionableList?.let { mentions?.setMentionableList(it) }
    }



    private fun getListOfUserForComment(keyword: CharSequence,loadMore:Boolean = false) {
        val map = HashMap<String, String>()
        map["limit"] = limit.toString()
        map["page"] = page.toString()
        map["q"] = keyword.toString()
        map["filters"] = "user"
        map["field"] = "name"

        searchViewModel.search(map).observe(viewLifecycleOwner) { data ->
            if (!data.data.isNullOrEmpty()) {
                if (loadMore)
                    userAdapter?.addMore(ArrayList(data.data))
                else
                    userAdapter?.addNewList(ArrayList(data.data))
                //EventBus.getDefault().post(EventMessage(true,Constants.HIDE_SHOW_USER))
                binding.rvUser.visibility = View.VISIBLE
                binding.userCrd.visibility = View.VISIBLE
                binding.userEmptyView.visibility = View.GONE
            } else if (userAdapter?.itemCount == 0 || !loadMore) {
                userAdapter?.isLoadMoreEnabled(false)
                // EventBus.getDefault().post(EventMessage(true,Constants.HIDE_SHOW_USER_EMPTY_VIEW))
                binding.rvUser.visibility = View.GONE
                binding.userEmptyView.visibility = View.VISIBLE
                binding.emptyMessage.text = getString(R.string.search_empty_message, keyword)
            } else {
                userAdapter?.isLoadMoreEnabled(false)
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val bottomSheetDialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        bottomSheetDialog.setOnShowListener {
            val bottomSheet = bottomSheetDialog
                .findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                val behavior: BottomSheetBehavior<*> = BottomSheetBehavior.from(bottomSheet)
                behavior.isDraggable = false
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        }
        return bottomSheetDialog
    }


    private fun setSourceAndPrevious(){

        userAdapter = UserAdapter(binding.rvUser,this,ctx,userList,"Live Chat","Live Detail",object : OnClickInterface<SearchResult>{
            override fun dataClicked(data: SearchResult) {
                val cursorPosition  = binding.editTextComment.selectionStart
                val split = binding.editTextComment.text.toString().trim().toCharArray().toMutableList()
                var last = cursorPosition
                for (i in split.indices){
                    if (split[i].toString().contains("@")){
                        val secondlast = if (cursorPosition >i){
                            cursorPosition - i
                        }else{
                            i - cursorPosition
                        }
                        if (secondlast<last){
                            last = secondlast
                        }
                    }
                }
                val mention = Mention()
                mention.mentionName = "@" + data.user?.userName
                mention.mentionOffset = binding.editTextComment.text.toString().trim().length - last
                mention.mentionEnd = binding.editTextComment.text.toString().length
                mention.mentionLength = mention.mentionName?.length?:0
                mentions?.insertMention(mention)
                mention.mentionEnd = binding.editTextComment.text.toString().trimEnd().length
                mentions?.setMention(mention)
                val mentionUser = MentionUser(data.user?.id.toString(),mention.mentionOffset)
                val mentionData= MentionData(data.user?.displayName,data.user?.userName)
                mentionUser.data = mentionData
                if (!mentionList.contains(data.user?.userName) && mentionList.size<=10){
                    mentionList[data.user?.userName?:""] = mentionUser
                }
                // viewHolder.preLoaderPost.visibility = GONE
                binding.rvUser.visibility = View.GONE
            }
        })
        binding.rvUser.adapter = userAdapter
    }


    fun addMention(comment: LiveMessage){
        if (comment.userName.isNullOrEmpty() || comment.userName == PrefUtils.userDetails?.userName)
            return
        val mention = Mention()
        binding.editTextComment.setText("@".plus(comment.userName))
        binding.editTextComment.setSelection("@".plus(comment.userName).length)
        mention.mentionName = "@" + comment.userName
        mention.mentionOffset = 0
        mention.mentionEnd = binding.editTextComment.text.toString().trim().length
        mention.mentionLength = mention.mentionName?.length?:0
        mentions?.insertMention(mention)
        mention.mentionEnd = binding.editTextComment.text.toString().trimEnd().length
        mentions?.setMention(mention)
        val mentionUser = MentionUser(comment.userId.toString(),mention.mentionOffset)
        val mentionData= MentionData(comment.name,comment.userName)

        mentionUser.data = mentionData
        if (!mentionList.contains(comment.userName) && mentionList.size<=10){
            mentionList[comment.userName?:""] = mentionUser
        }
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        if (totalItemsCount == limit) {
            this.page = page + 1
            getListOfUserForComment(keyWord,true)
        } else {
            userAdapter?.isLoadMoreEnabled(false)
        }
    }




}