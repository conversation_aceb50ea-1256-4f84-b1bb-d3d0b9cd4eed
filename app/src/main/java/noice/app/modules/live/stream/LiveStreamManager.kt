package noice.app.modules.live.stream

import android.content.Context
import android.content.ServiceConnection
import android.util.Log
import android.view.TextureView
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.toMutableStateList
import androidx.databinding.ObservableField
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import io.agora.rtc2.Constants
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.video.VideoCanvas
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.agora.AGEventHandler
import noice.app.data.WalletController
import noice.app.enums.Moderate
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.modules.live.analytics.LiveAnalytics
import noice.app.modules.live.event.AppStateTrigger
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.event.ServiceCallback
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.live.model.RoomParticipant
import noice.app.modules.live.model.block.Moderation
import noice.app.modules.live.stream.common.Action
import noice.app.modules.live.stream.common.Observer
import noice.app.modules.live.stream.common.Presence
import noice.app.modules.live.stream.common.Session
import noice.app.modules.live.stream.common.StreamState
import noice.app.modules.live.stream.common.StreamViewState
import noice.app.modules.live.stream.common.UiAction
import noice.app.modules.live.stream.common.VideoState
import noice.app.modules.live.stream.core.LiveStreamSavedStateViewModel
import noice.app.modules.live.stream.core.LiveStreamService
import noice.app.modules.live.stream.core.ext.DELETED_CHAT_LABEL
import noice.app.modules.live.stream.core.ext.StreamUtils
import noice.app.modules.live.stream.core.ext.StreamUtils.trySnackBar
import noice.app.modules.live.stream.core.ext.activity
import noice.app.modules.live.stream.core.ext.addMessage
import noice.app.modules.live.stream.core.ext.fragmentManager
import noice.app.modules.live.stream.core.ext.`is`
import noice.app.modules.live.stream.core.ext.isAccepted
import noice.app.modules.live.stream.core.ext.isNavHandOrMuteIconHidden
import noice.app.modules.live.stream.core.ext.isPending
import noice.app.modules.live.stream.core.ext.isRejected
import noice.app.modules.live.stream.core.ext.isUpdated
import noice.app.modules.live.stream.core.ext.isVipRoom
import noice.app.modules.live.stream.core.ext.moderation
import noice.app.modules.live.stream.core.ext.onUiDispatcher
import noice.app.modules.live.stream.core.ext.role
import noice.app.modules.live.stream.core.ext.showState
import noice.app.modules.live.stream.core.ext.sortSpeakingSpeakers
import noice.app.modules.live.stream.core.ext.startLiveService
import noice.app.modules.live.stream.core.ext.stopLiveService
import noice.app.modules.live.stream.core.ext.toStateList
import noice.app.modules.live.stream.core.repository.ChatRepository
import noice.app.modules.live.stream.core.repository.GiftRepository
import noice.app.modules.live.stream.core.repository.MetaRepository
import noice.app.modules.live.stream.core.repository.ModerationRepository
import noice.app.modules.live.stream.core.repository.ReactionRepository
import noice.app.modules.live.stream.core.repository.SpeakerRepository
import noice.app.modules.live.stream.core.usecase.InvertedParticipantUseCases
import noice.app.modules.live.stream.player.LiveStreamSheet
import noice.app.modules.live.stream.player.components.CHATS_OLD_OUT_OF_SCREEN_POOL
import noice.app.modules.live.util.LiveUtils
import noice.app.modules.live.util.LiveUtils.isEncryptedChannel
import noice.app.modules.live.util.clientRole
import noice.app.modules.live.util.isSelf
import noice.app.modules.live.util.isTypeVideo
import noice.app.utils.Constants.Companion.ADAPTER_MEMBER_LIMIT
import noice.app.utils.Constants.Companion.EMPTY_STRING
import noice.app.utils.Constants.Companion.LIVE_CHAT_LISTENER_DELAY
import noice.app.utils.Constants.LiveChatType.TYPE_CHAT
import noice.app.utils.Constants.LiveChatType.TYPE_GIFT
import noice.app.utils.Constants.LiveRoom.Agora.SDK_CONNECTED
import noice.app.utils.Constants.LiveRoom.Agora.SDK_DISCONNECTED_TRYING_REJOIN
import noice.app.utils.Constants.LiveRoom.ClientRole.ROLE_HOST
import noice.app.utils.Constants.LiveRoom.ClientRole.ROLE_LISTENER
import noice.app.utils.Constants.LiveRoom.Key.INVITATION
import noice.app.utils.Constants.LiveRoom.Key.RAISE_HAND
import noice.app.utils.EventConstant
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.closeDialogByTag
import noice.app.utils.delayCallBack
import noice.app.utils.dropFirst
import noice.app.utils.elseZero
import noice.app.utils.isGreaterThanZero
import noice.app.utils.isInvalidIndex
import noice.app.utils.isLessThan
import noice.app.utils.isTrue
import noice.app.utils.isZero
import noice.app.utils.pubnub.PubNubChannel
import noice.app.utils.pubnub.PubNubController
import noice.app.utils.pubnub.PubNubController.toDecryptedJsonElement
import noice.app.utils.pubnub.SafePubNubCall
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.time.Duration.Companion.seconds


@Singleton
class LiveStreamManager @Inject constructor(
    private val speakerRepository: SpeakerRepository,
    private val moderationRepository: ModerationRepository,
    private val giftRepository: GiftRepository,
    private val chatRepository: ChatRepository,
    private val metaRepository: MetaRepository,
    private val reactionRepository: ReactionRepository,
    private val invertedParticipantUseCases: InvertedParticipantUseCases,
    private val analytics: LiveAnalytics
) {

    private var service: LiveStreamService? = null
    private var viewModel : LiveStreamSavedStateViewModel? = null
    private var context: Context? = null
    private var sheet: LiveStreamSheet? = null
    private var connection: ServiceConnection? = null
    private var scope: CoroutineScope? = null
    private var callback: Callback? = null
    val trigger = LiveTrigger
    val state = MutableStateFlow(StreamState())
    var viewState = StreamViewState()
    var status = Status.IDLE

    fun checkForProcessDeath(
        context: Context,
        callback: Callback?
    ) {
        viewModel = ViewModelProvider(context as AppCompatActivity)[LiveStreamSavedStateViewModel::class.java]

        val tryDismissBlankSheet = {
            try {
                context.fragmentManager()?.closeDialogByTag(LiveStreamSheet::class.java.name)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        this.viewModel?.onKilledByProcessDeath(
            onProcessDeath = {
                tryDismissBlankSheet()
                startStream(
                    context = context,
                    room = it,
                    extraData = null,
                    callback = callback
                )
            },
            onStatusEnded = {
                tryDismissBlankSheet()
            }
        )
    }

    private fun startStream(
        context: Context,
        room: LiveRoom,
        extraData: ExtraData?,
        callback: Callback?
    ) {
        this.scope = CoroutineScope(Dispatchers.Main)
        this.context = context
        this.callback = callback

        onSession(Session.New(room, extraData))
        onSession(Session.StartService(room))

        onApplicationState()
        onLiveTriggers()
        onServiceEvents()

        onAction(Action.PubnubInitialize)
        onAction(Action.HereNowSpeakers())
        onAction(Action.SubscribeByRole(
            isListener = room.role().`is`(ROLE_LISTENER),
            firstSub = true))
        onAction(Action.FetchOlderChatMessages(LiveUtils.currentPubNubTimeToken()))

        onObserve(Observer.Meta())
        onObserve(Observer.Chat())
        onObserve(Observer.Moderation())
        onObserve(Observer.RaiseHandRequests())
        onObserve(Observer.SpeakerPnpres())
        onObserve(Observer.SpeakerPresence())
        onObserve(Observer.Reaction())
        onObserve(Observer.Gift())
        onObserve(Observer.ReceiverWallet())
        onObserve(Observer.PubnubReconnection())
    }

    private fun onSession(session: Session) {
        when (session) {
            is Session.New -> {
                onState(StreamState())
                onAction(Action.ManagerStatus(Status.REQUESTED))
                onAction(Action.StreamRequested)

                //HereNow provides updated list, APIs stale, pre-response showing self if eligible
                val self = session.room.roomParticipants?.find { it.user?.id.isSelf() }
                val asSelf = self?.let { arrayListOf(it) } ?: arrayListOf()
                onState(
                    state.value.copy(
                        room = session.room.copy(
                            roomParticipants = asSelf
                        ),
                        speakers = asSelf.toStateList(),
                        speakingSpeakers = asSelf.toStateList(),
                        extraData = session.extraData,
                        startMillis = LiveUtils.currentTime(),
                    )
                )
                viewState.apply {
                    roomTitle.set(session.room.title)
                    roomDescription.set(session.room.description)
                    coverPhotoUrl.set(session.room.data?.coverPhotoUrl)
                    showRecording.set(session.room.showRecording.isTrue())
                    navHandOrMuteIconHidden.set(session.room.isNavHandOrMuteIconHidden())
                    joinFlags.apply {
                        gift.set(session.room.data?.isVirtualGiftsEnabled.isTrue())
                        reaction.set(session.room.data?.isLiveRoomReactionsEnabled.isTrue())
                    }
                    isVirtualGiftsEnabled.set(session.room.data?.isVirtualGiftsEnabled.isTrue())
                    isLiveRoomReactionsEnabled.set(session.room.data?.isLiveRoomReactionsEnabled.isTrue())
                    pinComment.set(session.room.pinnedComment)
                    isHost.set(session.room.clientRole() == ROLE_HOST)
                    isVipRoom.set(session.room.isVipRoom())
                    contentTypeVideo.set(session.room.isTypeVideo())
                }
            }

            is Session.StartService -> {
                context?.activity().startLiveService(
                    room = session.room,
                    onBinder = {
                        service = it.service
                    },
                    onConnect = {
                        connection = it
                    }
                )
            }

            Session.StopService -> {
                context?.activity()?.stopLiveService(connection)
            }

            Session.Destroy -> {
                onAction(Action.ManagerStatus(Status.IDLE))
                LiveTrigger.onLiveStreamManagerDestroyed(state.value.room)
                onState(StreamState())
                viewState = StreamViewState()
                onSession(Session.StopService)
                BaseApplication.application.stopChatTimer()
                context?.fragmentManager()?.let { sheet?.showState(it, false) }
                PubNubController.instantDestroy()
                RtcEngine.destroy()
                scope?.cancel()
                scope = null
                service = null
                sheet = null
            }
        }
    }

    private fun onApplicationState() = scope?.launch {
        AppStateTrigger.event.collect {
            when(it){
                AppStateTrigger.Event.AppBackground -> {
                    onAction(Action.VideoOnApplicationState(isForeground = false))
                }
                AppStateTrigger.Event.AppForeground -> {
                    onAction(Action.VideoOnApplicationState(isForeground = true))
                }

                else -> {}
            }
        }
    }

    private fun onLiveTriggers() = scope?.launch {
        LiveTrigger.event.collect {
            when(it){
                is LiveTrigger.Event.ChatTimer -> {
                    it.seconds?.let { seconds ->
                        viewState.chatInputBox.apply {
                            label.set(seconds)
                            enable.set(false)
                        }
                    }
                }
                LiveTrigger.Event.ChatTimerFinished ->  {
                    viewState.chatInputBox.apply {
                        label.set(BaseApplication.getBaseAppContext().getString(R.string.komentar))
                        enable.set(true)
                    }
                }
                is LiveTrigger.Event.ChatCommentClick ->  {
                    sheet?.onEvent(LiveStreamSheet.Event.ChatItemTextClick(it.message))
                }
                is LiveTrigger.Event.ChatProfileClick ->  {
                    sheet?.onEvent(LiveStreamSheet.Event.ChatItemParticipantClick(it.message))
                }
                is LiveTrigger.Event.ParticipantMute -> {
                    //Being triggered from ParticipantOptionDialog, no impl req
                }
                is LiveTrigger.Event.RoomEnded ->  {
                    onAction(Action.RequestStopStream)
                }
                else -> {}
            }
        }
    }

    private fun onObserve(observer: Observer) {
        when (observer) {
            is Observer.Meta -> {
                scope?.launch(observer.dispatcher) {
                    metaRepository.onRoomMeta(
                        room = state.value.room,
                        scope = this,
                        onMetaJSON = { metaJSON , viaObserved ->
                            metaRepository.onRoomMetaJSON(
                                room = state.value.room,
                                customObject = metaJSON,
                                viaObserved = viaObserved,
                                onParticipantCount = {
                                    viewState.participantCount.set(it)
                                },
                                onGuestParticipantCount = {
                                    viewState.guestCount.set(it)
                                },
                                onLiveConfig = {
                                    PrefUtils.liveConfig = it
                                },
                                onRoomData = {},
                                onUpdateTimeStamp = {
                                    PrefUtils.updatedTimeStamp = it
                                },
                                onLiveRoom = {
                                    val isChatInputEnabled =
                                        PrefUtils.liveConfig?.isLiveChatEnabled.isTrue()
                                    val context = BaseApplication.getBaseAppContext()
                                    val coverPhotoUrl = it.data?.coverPhotoUrl
                                    val isCoverUpdated =
                                        !coverPhotoUrl.isNullOrEmpty() && coverPhotoUrl != state.value.room.data?.coverPhotoUrl

                                    viewState.apply {
                                        roomTitle.set(it.title)
                                        roomDescription.set(it.description)
                                        if (isCoverUpdated){
                                            this.coverPhotoUrl.set(coverPhotoUrl)
                                        }
                                        chatInputBox.apply {
                                            enable.set(isChatInputEnabled)
                                            label.set(
                                                context.getString(
                                                    if (isChatInputEnabled) R.string.komentar
                                                    else R.string.dimatikan
                                                )
                                            )
                                        }
                                        isVirtualGiftsEnabled.set(it.data?.isVirtualGiftsEnabled.isTrue())
                                        isLiveRoomReactionsEnabled.set(it.data?.isLiveRoomReactionsEnabled.isTrue())
                                        navHandOrMuteIconHidden.set(it.isNavHandOrMuteIconHidden())
                                        pinComment.set(it.pinnedComment)
                                    }

                                    StreamUtils.onRoomUpdatedOnScreenMessage(
                                        currentRoom = state.value.room,
                                        updatedRoom = it,
                                        onScreenMessage = { message ->
                                            onAction(Action.TryStringSnackBar(message))
                                            onAction(Action.StreamEdited(message))
                                        }
                                    )

                                    val isMiniPlayerNotify = StreamUtils.isMiniPlayerNotify(
                                        currentRoom = state.value.room,
                                        updatedRoom = it
                                    )
                                    if (isMiniPlayerNotify){
                                        onUiAction(UiAction.Edited(StreamUtils.getExoNotificationData(it)))
                                    }

                                    onState(
                                        state.value.copy(
                                            room = state.value.room
                                                .copy(
                                                    title = it.title,
                                                    description = it.description,
                                                    data = it.data,
                                                    pinnedComment = it.pinnedComment
                                                )
                                        )
                                    )
                                    Log.d("room edited",state.value.room.description.orEmpty())
                                },
                                onStatus = {
                                    if (state.value.room.status != it){
                                        onState(state.value.copy(
                                            room = state.value.room.apply {
                                                status = it
                                            }
                                        ))
                                    }
                                },
                                onStatusUpComing = {
                                    viewState.waitingForHost.set(state.value.isSpeaker)
                                },
                                onStatusEnded = {
                                    onAction(Action.RequestStopStream)
                                    onAction(Action.RoomEnded)
                                    onAction(Action.RoomEndedForDetail)
                                    onAction(
                                        Action.ShowToastMessage(
                                            context?.getString(
                                                R.string.live_room_ended,
                                                state.value.room.title.orEmpty()
                                            ).orEmpty()
                                        )
                                    )
                                },
                                onStatusUnknown = {
                                    viewState.waitingForHost.set(false)
                                }
                            )
                        }
                    )
                }

            }
            is Observer.Chat -> {
                scope?.launch(observer.dispatcher) {
                    chatRepository.onChatObserve(
                        room = state.value.room,
                        scope = this,
                        onNewMessage = { message, timeToken, messageItem ->
                            message?.timetoken = timeToken
                            if (messageItem?.isUpdated().isTrue()) {
                                message?.isActive = false
                                message?.chatText = DELETED_CHAT_LABEL
                            }
                            state.value.chatsObservable.addMessage(message)
                            onAction(Action.NewChat)
                            onAction(Action.MaintainChatsPool)
                        },
                        onMessageUpdated = {
                            StreamUtils.getUpdatedChatIfDeleted(
                                messages = state.value.chatsObservable.toMutableStateList(),
                                result = it,
                                onUpdated = { message, index ->
                                    state.value.chatsObservable[index] = message
                                }
                            )
                        },
                        onReconnectionFetching = {
                            viewState.chatPaginating.set(it)
                            onState(
                                state.value.copy(
                                    isChatPaginating = it
                                )
                            )
                        },
                        onReconnectionTimeToken = {
                            val chats = state.value.chatsObservable
                            if (chats.isNotEmpty()) {
                                val lastIndex = chats.lastIndex
                                chats[lastIndex].timetoken
                            } else {
                                null
                            }
                        }
                    )
                }
            }

            is Observer.Moderation -> {
                scope?.launch(observer.dispatcher) {
                    moderationRepository.onModeration(
                        room = state.value.room,
                        scope = this,
                        onObserve = { _, moderation ->
                            if (!moderation.participantUserId.isSelf() ||
                                moderation.status == state.value.room.agoraToken?.moderation?.status
                            ) {
                                //As the status is same as we did earlier observe,
                                //so need not to handle for same status.
                                return@onModeration
                            }
                            state.value.room.agoraToken?.moderation = moderation
                            fun demoteAsListener(showModeratedDialog: Boolean = true) {
                                onAction(Action.DemotedAsListener(moderation = moderation))
                                if (showModeratedDialog) {
                                    onUiAction(UiAction.ModerationDialog(moderation))
                                }
                            }

                            val stopStream = {
                                onAction(Action.RequestStopStream)
                                onAction(Action.RoomEnded)
                            }

                            when (moderation.status) {
                                Moderate.RESTRICTED.action -> demoteAsListener()
                                Moderate.DEMOTED.action -> {
                                    demoteAsListener(showModeratedDialog = false)
                                    if (moderation.demotedBy.`is`(ROLE_HOST)){
                                        onAction(Action.TrySnackBar(R.string.host_removed_you_from_speaker))
                                    }
                                }
                                Moderate.KICKED.action, Moderate.BLOCKED.action -> {
                                    demoteAsListener()
                                    stopStream()
                                }
                                Moderate.ALLOWED.action -> {
                                    if (!state.value.room.moderation(Moderate.DEMOTED)) {
                                        onAction(Action.TrySnackBar(R.string.enabled_interaction_message))
                                    }
                                }
                            }

                            onState(
                                state.value.copy(
                                    room = state.value.room.copy(
                                        agoraToken = state.value.room.agoraToken?.copy(
                                            moderation = state.value.room.agoraToken?.moderation?.copy(
                                                id = moderation.id,
                                                status = moderation.status,
                                                demotedBy = moderation.demotedBy
                                            )
                                        )
                                    )
                                )
                            )
                        }
                    )
                }
            }

            is Observer.RaiseHandRequests -> {
                scope?.launch(observer.dispatcher) {
                    speakerRepository.onObserveRaisedHandRequests(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = state.value.isContentTypeAudio,
                        onObserve = { viaObserved, request ->
                            if (state.value.isHost) {
                                if (request.type == RAISE_HAND) {
                                    viewState.apply {
                                        showRaisedHandsNotification.set(request.status.isPending() && request.totalRaiseHands.isGreaterThanZero())
                                        raiseHand.set(
                                            viewState.raiseHand.get()?.copy(
                                                totalRaiseHands = request.totalRaiseHands,
                                                user = request.user
                                            )
                                        )
                                    }
                                }
                            } else {
                                if (viaObserved && request.status.isAccepted() &&
                                    (request.type == RAISE_HAND || request.type == INVITATION)
                                ) {
                                    onAction(Action.BecameSpeaker)
                                    onAction(
                                        Action.TrySnackBar(
                                            if (request.type == RAISE_HAND) R.string.speaker_request_accepted
                                            else R.string.cant_wait_to_listen
                                        )
                                    )
                                } else if (viaObserved && request.status.isRejected() &&
                                    (request.type == RAISE_HAND || request.type == INVITATION)
                                ) {
                                    onAction(
                                        Action.TrySnackBar(
                                            if (request.type == RAISE_HAND) R.string.speaker_request_rejected_listener_copy
                                            else R.string.speaker_request_rejected
                                        )
                                    )
                                    viewState.apply {
                                        handOrMuteClickable.set(true)
                                        showSpeakerProposed.set(false)
                                    }
                                    onState(
                                        state.value.copy(
                                            isHandRaised = false
                                        )
                                    )
                                } else if (state.value.isListener && request.type == INVITATION && request.status.isPending()) {
                                    viewState.showSpeakerProposed.set(true)
                                }
                            }
                        }
                    )
                }
            }

            is Observer.SpeakerPresence -> {
                scope?.launch(observer.dispatcher) {
                    speakerRepository.onPresence(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = false,
                        onPresence = {
                            speakerRepository.onSpeakerPresenceResultEvent(
                                result = it,
                                room = state.value.room,
                                scope = this,
                                onSpeaker = { presence ->
                                    onAction(Action.SpeakersUpdated(presence))
                                }
                            )
                        }
                    )
                }
            }

            is Observer.SpeakerPnpres -> {
                scope?.launch(observer.dispatcher) {
                    speakerRepository.onObservePnpres(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = false,
                        onObserve = {
                            speakerRepository.getSpeakersByIds(
                                room = state.value.room,
                                userIds = arrayListOf(it),
                                scope = this,
                                onSpeakers = { participants ->
                                    val _speakers = participants.filter { it.type != ROLE_LISTENER }
                                    if (_speakers.isNotEmpty()) {
                                        onAction(Action.SpeakersUpdated(Presence.SisterPnpres(_speakers.first())))
                                    }
                                }
                            )
                        }
                    )
                }
            }

            is Observer.Reaction -> {
                scope?.launch(observer.dispatcher) {
                    reactionRepository.onReaction(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = viewState.joinFlags.reaction.get(),
                        onObserve = { isObserved, reaction ->
                            if (!reaction.reactionCount.isZero()) {
                                viewState.reaction.count.set(reaction.reactionCount.elseZero())
                            }

                            if (isObserved && !reaction.userId.isSelf()) {
                                onAction(
                                    Action.ShowReaction(
                                        reaction = reaction,
                                        isSelf = false
                                    )
                                )
                            }
                        }
                    )
                }
            }

            is Observer.Gift -> {
                scope?.launch(observer.dispatcher) {
                    giftRepository.onObserve(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = viewState.joinFlags.gift.get(),
                        onObserve = { _, giftMessage ->
                            sheet?.onEvent(LiveStreamSheet.Event.ShowGiftAnimations(giftMessage))
                            //SendGiftSuccess here as maybe the dialog has been dismissed and
                            //we missed the callback of Success, hence for further sending
                            //it in chat.
                            val selfSentGift = state.value.giftState.sendingGiftTriplet?.first
                            if (selfSentGift?.gift?.giftId == giftMessage.giftId && giftMessage.sender?.id.isSelf()) {
                                onAction(Action.SendGiftSuccess)
                            }
                        }
                    )
                }
            }

            is Observer.ReceiverWallet -> {
                scope?.launch(observer.dispatcher) {
                    giftRepository.onObserveReceiverWallet(
                        room = state.value.room,
                        scope = this,
                        doSubscribe = viewState.joinFlags.gift.get(),
                        history = SafePubNubCall.History(
                            onEmpty = {
                                // If the PubNub History is Empty, we need to hit the wallet API to
                                // fetch ReceiverTotalDiamond for Receiver respective,
                                // whereas Room Diamonds are default 0
                                if (state.value.isGiftReceiver) {
                                    WalletController.refreshWallet()
                                }
                            }
                        ), onObserve = { _, wallet ->
                            val roomBalance =
                                state.value.giftHistoryState.receiverWallet?.liveRoomDiamondBalance.elseZero()
                            if (roomBalance.isLessThan(wallet.liveRoomDiamondBalance.elseZero())) {
                                state.value.giftHistoryState.receiverWallet = wallet
                                viewState.diamondCount.set(wallet.liveRoomDiamondBalance.elseZero())
                            }
                        }
                    )
                }
            }

            is Observer.PubnubReconnection -> {
                scope?.launch(observer.dispatcher) {
                    PubNubController.statusEvent.collect {
                        if (it is PubNubController.StatusEvent.ReConnected) {
                            onAction(Action.HereNowSpeakers(viaReconnect = true))
                        }
                    }
                }
            }
        }
    }

    private fun onServiceEvents() = scope?.launch(Dispatchers.Main) {
        ServiceCallback.event.collect {
            when (it) {

                is ServiceCallback.Event.ClientRoleChanges -> {
                    onState(state.value.copy(
                        isHandRaised = false
                    ))
                    viewState.showSpeakerProposed.set(false)
                }

                is ServiceCallback.Event.ExtraCallBack -> {
                    if (it.data == null) {
                        return@collect
                    }
                    val type = it.type ?: 0
                    val data = arrayOf(it.data)
                    when (type) {
                        AGEventHandler.EVENT_TYPE_ON_SPEAKER_STATS -> {

                            fun clearIndicator(isSelf: Boolean) {
                                speakerRepository.onClearSpeakingIndicatorInList(
                                    speakers = state.value.room.roomParticipants,
                                    isSelfClear = isSelf,
                                    agoraUid = state.value.agoraId,
                                    onUpdate = { speakers ->
                                        onAction(
                                            Action.SpeakersSpeaking(
                                                speakers
                                            )
                                        )
                                    }
                                )
                            }

                            StreamUtils.onAgoraSpeakerStats(
                                data = data,
                                agoraUid = state.value.agoraId,
                                onClearAll = {
                                    clearIndicator(isSelf = false)
                                },
                                onClearSelf = {
                                    clearIndicator(isSelf = true)
                                },
                                onPeerIds = { peerIds ->
                                    speakerRepository.onUpdateSpeakingPriority(
                                        speakers = state.value.room.roomParticipants,
                                        peerIds = peerIds,
                                        limit = ADAPTER_MEMBER_LIMIT,
                                        onUpdate = { speakers ->
                                            onAction(
                                                Action.SpeakersSpeaking(
                                                    speakers
                                                )
                                            )
                                        }
                                    )
                                }
                            )
                        }

                        AGEventHandler.EVENT_TYPE_ON_CONNECTION_LOST -> {
                            onAction(Action.AgoraConnectionLost)
                            StreamUtils.onAgoraConnectionLost(
                                data = data,
                                onNoNetworkConnection = {
                                    onAction(Action.ShowToast(R.string.live_internet_message))
                                    StreamUtils.onTryRejoinTimer(
                                        onInternetConnected = {
                                           /*  If user doesn't exist in DB then RequestStopStream */
                                        },
                                        onOfflineTimerTick = { stringRes ->
                                            onAction(Action.ShowToast(stringRes))
                                            onAction(Action.InternetIssue)
                                        },
                                        onOfflineTimerFinished = { stringRes ->
                                            onAction(Action.ShowToast(stringRes))
                                            onAction(Action.RequestStopStream)
                                        }
                                    )
                                }
                            )
                        }

                        AGEventHandler.EVENT_TYPE_CONNECTION_STATE -> {
                            if (data.isNotEmpty()){
                                val connectionState = (data[0] as? Array<*>)?.get(0) as? Int
                                if (connectionState != null){
                                    onAction(Action.AgoraConnectionState(connectionState))
                                }
                            }
                            StreamUtils.onAgoraConnectionState(
                                data = data,
                                onStatusResource = {},
                                onFailed = { stringRes ->
                                    onAction(Action.ShowToast(stringRes))
                                    onAction(Action.InternetIssue)
                                }
                            )
                        }

                        AGEventHandler.EVENT_TYPE_ON_AGORA_MEDIA_ERROR -> {
                            StreamUtils.onAgoraMediaError(
                                data = data,
                                onError = {
                                    onAction(Action.RoomTokenExpired)
                                }
                            )
                        }
                    }
                }

                is ServiceCallback.Event.JoinChannelSuccess -> {
                    onAction(Action.ManagerStatus(Status.STREAMING))
                    onState(
                        state.value.copy(
                            agoraId = it.agoraUid
                        )
                    )
                    invertedParticipantUseCases.updateInvertedParticipant.invoke(
                        agoraUid = state.value.room.agoraToken?.agoraUid.elseZero(),
                        roomId = state.value.room.id,
                        scope = this
                    )
                    analytics.onRoomJoinedEvent(
                        context = BaseApplication.getBaseAppContext(),
                        room = state.value.room,
                        extraData = state.value.extraData
                    )
                    StreamUtils.captureAndSendEvent(
                        room = state.value.room,
                        type = EventConstant.MARK_PLAY,
                        duration = 0
                    )
                    if (state.value.isContentTypeVideo) {
                        onAction(Action.SetupLocalVideo)
                    }
                    onAction(Action.StreamStarted)
                    onAction(Action.Expand)
                }

                is ServiceCallback.Event.LeaveChannel -> {
                    onState(
                        state.value.copy(
                            exitMillis = LiveUtils.currentTime()
                        )
                    )
                    invertedParticipantUseCases.deleteInvertedParticipant.invoke(
                        scope = this
                    )
                    if (!it.isTaskOnRemoveCalled) {
                        onAction(Action.Collapse)
                        onAction(Action.RoomEnded)
                        StreamUtils.captureAndSendEvent(
                            room = state.value.room,
                            type = EventConstant.MARK_PAUSE,
                            duration = it.stats?.totalDuration
                        )
                    }
                    analytics.onLeaveRoomEndedEvent(
                        context = BaseApplication.getBaseAppContext(),
                        room = state.value.room,
                        leaveTime = state.value.exitMillis,
                        joiningTime = state.value.startMillis,
                        extraData = state.value.extraData,
                        endedStatus = state.value.room.status.orEmpty()
                    )
                    StreamUtils.showEndStats(
                        activity = context?.activity(),
                        room = state.value.room,
                        isHost = state.value.isHost
                    )
                    onSession(Session.Destroy)
                }

                is ServiceCallback.Event.LeaveExpire -> {
                    onSession(Session.Destroy)
                    onAction(Action.TryAgainLeaveExpire)
                }

                is ServiceCallback.Event.RejoinJoinChannelSuccess -> {
                    invertedParticipantUseCases.getInvertedParticipant.invoke(
                        roomId = state.value.room.id.orEmpty(),
                        scope = this,
                        onNonExist = {
                            invertedParticipantUseCases.updateInvertedParticipant.invoke(
                                agoraUid = state.value.room.agoraToken?.agoraUid.elseZero(),
                                roomId = state.value.room.id,
                                scope = this
                            )
                        }
                    )
                }

                is ServiceCallback.Event.RemoteVideoStateChanged -> {
                    when (it.state) {
                        Constants.REMOTE_VIDEO_STATE_FROZEN -> {
                            onAction(Action.SetVideoState(VideoState.FROZEN))
                        }

                        Constants.REMOTE_VIDEO_STATE_PLAYING, Constants.REMOTE_VIDEO_STATE_STARTING -> {
                            onAction(Action.SetVideoState(VideoState.STREAMING))
                        }
                    }
                }

                ServiceCallback.Event.Unbind -> Unit
                is ServiceCallback.Event.UserJoined -> {
                    val canvas = viewState.video.canvas?.get()
                    canvas?.uid = it.uid
                    viewState.video.apply {
                        this.state.set(VideoState.STREAMING)
                        this.canvas?.set(canvas)
                    }
                    canvas?.let {
                        service?.setupRemoteVideo(canvas)
                    }
                }
                is ServiceCallback.Event.UserMuted -> {
                    if (it.uid.toLong() == state.value.room.agoraToken?.agoraUid){
                        service?.muteLocalAudioStream(it.isMuted)
                        viewState.handOrMuteClickable.set(true)
                        onState(
                            state.value.copy(
                                isHandRaised = false,
                                isMuted = it.isMuted
                            )
                        )
                    }
                }
                is ServiceCallback.Event.UserMuteVideo -> {
                    if (!state.value.isHost) {
                        onAction(Action.SetVideoState(if (it.mute) VideoState.OFF else VideoState.STREAMING))
                    }
                }

                is ServiceCallback.Event.UserOffline -> {
                    onAction(Action.SetVideoState(VideoState.PAUSED))
                }
            }
        }
    }

    fun onAction(action: Action) {
        when (action) {
            Action.StopChatTimer -> BaseApplication.application.stopChatTimer()
            is Action.SwitchToBroadcaster -> service?.doSwitchToBroadcaster(broadcaster = action.broadcaster)
            Action.Collapse -> collapse()
            Action.Expand -> expand()
            Action.InternetIssue -> trigger.onInternetIssue()
            Action.PubnubInitialize -> PubNubController.initialize()
            Action.RoomEnded -> {
                onUiAction(UiAction.Ended)
                trigger.onRoomEnded(state.value.room)
            }
            Action.RoomEndedForDetail -> {
                trigger.onRoomEndedForFragment(state.value.room)
            }
            Action.RoomTokenExpired -> service?.leaveChannelForTokenExpire()
            is Action.ShowToast -> onUiAction(UiAction.ShowToast(action.stringRes))
            is Action.ShowToastMessage -> onUiAction(UiAction.ShowToastMessage(action.string))
            is Action.TrySnackBar -> onUiAction(UiAction.TrySnackBar(action.stringRes))
            is Action.TryStringSnackBar -> onUiAction(UiAction.TryStringSnackBar(action.message))
            Action.RequestStopStream -> service?.doLeaveChannel()
            Action.TryAgainLeaveExpire -> trigger.onJoinLiveRoom(state.value.room.id.orEmpty())
            is Action.LocalAudioStream -> service?.muteLocalAudioStream(action.isMuted)
            Action.StreamRequested -> onUiAction(UiAction.Requested)
            Action.StreamStarted ->onUiAction(UiAction.Started(StreamUtils.getExoNotificationData(state.value.room)))
            is Action.StreamEdited -> {
                trigger.onRoomEdited(state.value.room, action.message)
            }

            is Action.SubscribeByRole -> {
                if (action.isListener) {
                    if (!action.firstSub) {
                        speakerRepository.unsubscribeFromSpeakers(state.value.room)
                        onAction(Action.SpeakersUpdated(Presence.Leave(PrefUtils.userDetails?.id.orEmpty())))
                    }

                    scope?.let {
                        speakerRepository.subscribeAsListener(
                            room = state.value.room,
                            scope = it
                        )
                    }
                } else {
                    if (!action.firstSub){
                        speakerRepository.unsubscribeFromListeners(state.value.room)
                    }
                    scope?.let {
                        speakerRepository.subscribeAsSpeaker(
                            room = state.value.room,
                            scope = it,
                            timeToken = null
                        )
                        speakerRepository.pushJoinEventAsPresenceState(
                            roomId = state.value.room.id.orEmpty(),
                            isJoin = !action.viaReconnect,
                            isMute = action.isMuted
                        )
                        onAction(
                            Action.MuteAudio(
                                isMuted = action.isMuted,
                                withPresence = true,
                                withAnalytics = false,
                                viaSelf = false
                            )
                        )
                    }
                }
            }

            Action.ConvertedAsSpeakerRemoveRestrictions -> scope?.let {
                moderationRepository.onModerateRestrictedAllowSelf(
                    room = state.value.room,
                    scope = it
                )
            }

            is Action.HereNowSpeakers -> scope?.let {
                speakerRepository.onHereNowSpeakers(
                    room = state.value.room,
                    scope = it,
                    onUserIds = { userIds ->
                        speakerRepository.getSpeakersByIds(
                            room = state.value.room,
                            userIds = userIds,
                            scope = it,
                            onSpeakers = { speakers ->

                                fun addAllHereNowSpeakers() {
                                    speakers.forEach { speaker ->
                                        onAction(Action.SpeakersUpdated(Presence.HereNow(speaker)))
                                    }
                                }


                                if (action.viaReconnect) {
                                    if (state.value.isContentTypeAudio && !state.value.isListener) {

                                        fun onClearSpeakers() {
                                            onState(
                                                state.value.copy(
                                                    room = state.value.room.copy(
                                                        roomParticipants = arrayListOf()
                                                    ),
                                                    speakers = mutableStateListOf(),
                                                    speakingSpeakers = mutableStateListOf()
                                                )
                                            )
                                        }

                                        fun onSubscribeByRole(isListener: Boolean) {
                                            onClearSpeakers()
                                            addAllHereNowSpeakers()
                                            onAction(
                                                Action.SubscribeByRole(
                                                    isListener = isListener,
                                                    isMuted = state.value.isMuted,
                                                    viaReconnect = true
                                                )
                                            )
                                        }


                                        moderationRepository.onHistory(
                                            room = state.value.room,
                                            onEmpty = {
                                                onSubscribeByRole(isListener = false)
                                            },
                                            onItems = { historyList ->
                                                var isExist = false
                                                historyList.let { list ->
                                                    list?.forEach { item ->
                                                        val jsonElement =
                                                            if (PubNubChannel.UserModeration.USER_MODERATION(
                                                                    state.value.room
                                                                ).isEncryptedChannel()
                                                            ) item.entry.toDecryptedJsonElement() else item.entry
                                                        val data =
                                                            Utils.getObjectFromJson<Moderation>(
                                                                jsonElement.toString()
                                                            )
                                                        if (data?.participantUserId == PrefUtils.userDetails?.id) {
                                                            isExist = true
                                                        }
                                                    }

                                                    if (!isExist) {
                                                        onSubscribeByRole(isListener = false)
                                                    }
                                                }
                                            },
                                            onHistory = { moderation ->
                                                if (moderation.participantUserId.isSelf()) {
                                                    val isStillSpeaker =
                                                        moderation.status == Moderate.ALLOWED.action || state.value.isHost
                                                    onSubscribeByRole(isListener = !isStillSpeaker)
                                                }
                                            }
                                        )
                                    }
                                } else {
                                    addAllHereNowSpeakers()
                                }
                            }
                        )
                    }
                )
            }

            is Action.SpeakersSpeaking -> {
                val sortedSpeakers = action.speakers?.sortSpeakingSpeakers()
                onState(state.value.copy(
                    speakingSpeakers = sortedSpeakers.toStateList()
                ))
            }

            is Action.SpeakersUpdated -> {

                val speakers = state.value.room.roomParticipants ?: arrayListOf()
                val uuids = speakers.map { it.user?.id }

                val affectedParticipant = { uuid: String ->
                    state.value.room.roomParticipants?.find { it.user?.id == uuid }
                }

                fun onLeaveJoinRoleChange(uuid: String) {
                    onAction(Action.ParticipantRoleChanged)
                    LiveTrigger.onParticipantStateChange(affectedParticipant(uuid))
                    trigger.onSpeakersPresenceUpdate(state.value.room)
                }

                fun updateSpeakers(
                    speakers: ArrayList<RoomParticipant>?,
                    affectedUuid: String?
                ) {
                    val sortedSpeakers = speakers?.sortSpeakingSpeakers()
                    onState(
                        state.value.copy(
                            room = state.value.room.copy(
                                roomParticipants = sortedSpeakers,
                            ),
                            speakers = sortedSpeakers.toStateList(),
                            speakingSpeakers = sortedSpeakers.toStateList()
                        )
                    )
                    onLeaveJoinRoleChange(affectedUuid.orEmpty())
                }

                fun onAddSpeaker(participant: RoomParticipant) {
                    if (!uuids.contains(participant.user?.id)) {
                        speakers.add(participant)
                        updateSpeakers(speakers, participant.user?.id)
                    }
                }

                fun onRemoveSpeaker(uuid: String) {
                    if (uuids.contains(uuid)) {
                        val index = speakers.indexOfFirst { it.user?.id == uuid }
                        if (!index.isInvalidIndex() && !(uuid.isSelf() && state.value.isHost)) {
                            speakers.removeAt(index.elseZero())
                            updateSpeakers(speakers, uuid)
                        }
                    }
                }


                when (action.presence) {
                    is Presence.HereNow -> {
                        onAddSpeaker(action.presence.participant)
                    }

                    is Presence.Join -> {
                        onAddSpeaker(action.presence.participant)
                        onLeaveJoinRoleChange(action.presence.participant.user?.id.orEmpty())
                    }

                    is Presence.Leave -> {
                        onRemoveSpeaker(action.presence.uuid)
                        onLeaveJoinRoleChange(action.presence.uuid)
                    }

                    is Presence.SisterPnpres -> {
                        onAddSpeaker(action.presence.participant)

                    }

                    is Presence.StateChange -> {
                        val index = speakers.indexOfFirst { it.user?.id == action.presence.uuid }
                        if (!index.isInvalidIndex()) {
                            speakers[index] = speakers[index].apply {
                                isMuted = action.presence.isMuted
                            }
                            onState(state.value.copy(
                                room = state.value.room.copy(
                                    roomParticipants = speakers
                                ),
                                speakingSpeakers = speakers.toStateList()
                            ))
                            val speaker = state.value.speakingSpeakers.find { it.user?.id == action.presence.uuid }
                            LiveTrigger.onParticipantStateChange(speaker)
                        }
                        if (action.presence.uuid.isSelf()){
                            onAction(Action.MuteAudio(
                                isMuted = action.presence.isMuted,
                                withPresence = false,
                                withAnalytics = false,
                                viaSelf = false
                            ))
                        }
                    }

                    is Presence.TimeOut -> {
                        onRemoveSpeaker(action.presence.uuid)
                        onLeaveJoinRoleChange(action.presence.uuid)
                    }

                    else -> {}
                }

            }

            is Action.BecameSpeaker  -> {
                val isMuted = true
                onAction(Action.SwitchToBroadcaster())
                onAction(Action.LocalAudioStream(isMuted = isMuted))
                onAction(Action.StopChatTimer)
                onAction(Action.SubscribeByRole(isListener = false))
                onAction(Action.ConvertedAsSpeakerRemoveRestrictions)
                viewState.apply {
                    viewState.chatInputBox.enable.set(true)
                    handOrMuteClickable.set(true)
                    showRaisedHandsNotification.set(false)
                    showSpeakerProposed.set(false)
                }
                onState(
                    state.value.copy(
                        isMuted = isMuted,
                        isHandRaised = false
                    )
                )
            }

            is Action.MuteAudio -> {

                val onMuted = { mute : Boolean ->
                    onState(
                        state.value.copy(
                            isHandRaised = false,
                            isMuted = mute
                        )
                    )
                    viewState.handOrMuteClickable.set(true)

                    val speakers = state.value.speakingSpeakers
                    val selfIndex = speakers.indexOfFirst { it.user?.id.isSelf() }
                    if (!selfIndex.isInvalidIndex()) {
                        speakers[selfIndex] = speakers[selfIndex].apply {
                            isMuted = mute
                        }

                        onState(state.value.copy(
                            speakingSpeakers = speakers.toMutableStateList()
                        ))
                    }

                    onAction(Action.LocalAudioStream(mute))
                }


                if (action.withPresence && !state.value.isListener) {
                    onMuted(action.isMuted)
                    speakerRepository.updatePresence(
                        roomId = state.value.room.id.orEmpty(),
                        isMute = action.isMuted,
                        viaHost = false,
                        onSuccess = {},
                        onFailure = {
                            onMuted(!action.isMuted)
                        }
                    )
                } else {
                    onMuted(action.isMuted)
                }

                if (action.withAnalytics && !state.value.isListener){
                    speakerRepository.onMuteToggleAnalytics(
                        isMuted = action.isMuted,
                        room = state.value.room
                    )
                }

            }
            is Action.DemotedAsListener -> {
                onAction(Action.SwitchToBroadcaster(false))
                onAction(Action.SubscribeByRole(isListener = true))
            }

            Action.NewChat -> sheet?.onEvent(LiveStreamSheet.Event.NewChat)
            Action.OldChatFetched -> sheet?.onEvent(LiveStreamSheet.Event.OldChatsFetched)

            is Action.FetchOlderChatMessages -> {
                chatRepository.fetchOlderMessages(
                    room = state.value.room,
                    startTimeToken = action.startTimeToken,
                    onMessage = { message, timeToken, messageItem ->
                        message?.timetoken = timeToken
                        if (messageItem?.isUpdated().isTrue()) {
                            message?.isActive = false
                            message?.chatText = DELETED_CHAT_LABEL
                        }
                        state.value.chatsObservable.addMessage(message)
                    },
                    onReachedEnd = {
                        onState(
                            state.value.copy(
                                isChatReachedEnd = true
                            )
                        )
                    },
                    onLoading = {
                        if (!it) onAction(Action.OldChatFetched)
                        viewState.chatPaginating.set(it)
                        onState(
                            state.value.copy(
                                isChatPaginating = it
                            )
                        )
                    }
                )
            }

            is Action.ChatTopItemIndex -> {
                onState(
                    state.value.copy(
                        chatTopItemIndex = action.index
                    )
                )
            }

            is Action.UnseenChats -> {
                viewState.unseenChats.apply {
                    show.set(action.show)
                    count.set(action.count)
                }
            }

            Action.MaintainChatsPool -> {
                val ceilItemIndex = state.value.chatTopItemIndex
                val totalItems = state.value.chatsObservable.size
                val currentOldPool = ceilItemIndex.inc()
                val overloadPool = currentOldPool.minus(CHATS_OLD_OUT_OF_SCREEN_POOL)
                val isChunkDelete = overloadPool > CHATS_OLD_OUT_OF_SCREEN_POOL
                if (isChunkDelete && currentOldPool in CHATS_OLD_OUT_OF_SCREEN_POOL..totalItems) {
                    state.value.chatsObservable.dropFirst(overloadPool)
                }
            }

            is Action.ChatInputTempSave -> {
                onState(state.value.copy(
                    chatInputTextField = action.field
                ))
            }
            Action.ProceedSendChat -> {
                val sendChat = {
                    val inputField = state.value.chatInputTextField
                    onAction(
                        Action.PublishMessage(
                            text = inputField.text,
                            type = TYPE_CHAT,
                            mentions = inputField.mentions
                        )
                    )

                    viewState.chatInputBox.apply {
                        label.set(BaseApplication.getBaseAppContext().getString(R.string.komentar))
                        loading.set(false)
                    }

                    onState(
                        state.value.copy(
                            chatInputTextField = state.value.chatInputTextField.copy(
                                text = EMPTY_STRING,
                                mentions = hashMapOf(),
                                mentionable = listOf()
                            )
                        )
                    )
                }
                val waitMillis =
                    (0..viewState.participantCount.get().times(PrefUtils.liveConfig?.maxLiveChatDelayFactor.elseZero())).random()
                when {
                    state.value.isListener && waitMillis > 0 -> {

                        viewState.chatInputBox.apply {
                            label.set(
                                BaseApplication.getBaseAppContext().getString(R.string.mengirim)
                            )
                            loading.set(true)
                        }
                        delayCallBack(waitMillis) {
                            sendChat()
                        }
                    }

                    else -> {
                        sendChat()
                    }
                }
            }
            is Action.PublishMessage -> {
                chatRepository.publishMessage(
                    text = action.text,
                    type = action.type,
                    gift = action.gift,
                    giftQuantity = action.giftQuantity,
                    giftPreGeneratedId = action.giftPreGeneratedId,
                    mentions = action.mentions,
                    room = state.value.room,
                    agoraId = state.value.agoraId,
                    role = state.value.role,
                    onSuccess = {
                        sheet?.onEvent(LiveStreamSheet.Event.ShowLatestChats)
                        if (state.value.isListener && action.type != TYPE_GIFT){
                            val intervalSecs = state.value.room.data?.chatWaitTime?: LIVE_CHAT_LISTENER_DELAY
                            BaseApplication.application.startChatTimer(
                                chatWaitTime = intervalSecs.seconds.inWholeMilliseconds
                            )
                        }
                    },
                    onLoading = {
                        viewState.chatInputBox.apply {
                            enable.set(!it)
                            loading.set(it)
                        }
                    }
                )
            }

            is Action.ShowReaction -> {
                sheet?.onEvent(
                    LiveStreamSheet.Event.ShowOnScreenReaction(
                        reaction = action.reaction,
                        isSelf = action.isSelf
                    )
                )
                if (action.isSelf) {
                    val selfToggleClick = { active: Boolean ->
                        viewState.reaction.active.set(active)
                    }
                    selfToggleClick(false)
                    delayCallBack(viewState.reaction.interval) {
                        selfToggleClick(true)
                    }
                }
            }

            is Action.LiveSheetOpenClose -> {
                onState(state.value.copy(
                    isPlayerExpanded = action.state == BottomSheetBehavior.STATE_EXPANDED
                ))
                if (role() != ROLE_HOST){
                    service?.showHideVidePreview(action.state)
                }
            }

            is Action.ManagerStatus -> {
                status = action.status
                viewModel?.saveState(
                    room = state.value.room,
                    status = status
                )
            }

            Action.SetupLocalVideo -> {
                val textureView = context?.activity()?.let { TextureView(it) }
                viewState.apply {
                    video.state.set(if (state.value.isHost) VideoState.STREAMING else VideoState.NIL)
                    video.textureView = ObservableField(textureView)
                }

                val canvas = textureView?.let {
                    VideoCanvas(
                        it,
                        viewState.video.renderMode,
                        state.value.agoraId.toInt()
                    )
                }

                canvas?.let {
                    viewState.video.canvas = ObservableField(it)
                }

                if (state.value.isHost && canvas != null) {
                    service?.displayLocalVideo(canvas)
                } else {
                    onAction(
                        Action.SetVideoState(
                            videoState = VideoState.PAUSED,
                            withAutoFallback = true
                        )
                    )
                }
            }

            is Action.SetVideoState -> {
                val onVideState = { videoState: VideoState ->
                    viewState.video.state.set(videoState)
                }

                if (action.withAutoFallback) {
                    delayCallBack(action.duration.inWholeMilliseconds) {
                        if (viewState.video.state.get() == VideoState.NIL) {
                            onVideState(action.videoState)
                        }
                    }
                } else {
                    onVideState(action.videoState)
                }
            }
            is Action.ToggleVideoStream -> {
                val videoState = if (viewState.video.state.get() == VideoState.STREAMING)
                    VideoState.OFF else VideoState.STREAMING
                val isVideoMuted = videoState == VideoState.OFF
                viewState.apply {
                    video.state.set(videoState)
                    video.isHostDisabledVideo.set(isVideoMuted && action.viaHost)
                }
                service?.muteLocalVideoStream(isVideoMuted, state.value.role)
            }

            is Action.AgoraConnectionState -> {
                val video = viewState.video
                when (action.state) {
                    SDK_DISCONNECTED_TRYING_REJOIN -> {
                        if (state.value.isHost && !video.isHostDisabledVideo.get() && video.state.get() != VideoState.NO_INTERNET) {
                            onAction(Action.SetVideoState(VideoState.FROZEN))
                        }
                    }

                    SDK_CONNECTED -> {
                        if (state.value.isHost && !video.isHostDisabledVideo.get()) {
                            onAction(Action.SetVideoState(VideoState.STREAMING))
                        }
                    }
                }
            }

            Action.AgoraConnectionLost -> {
                onAction(Action.SetVideoState(VideoState.NO_INTERNET))
            }

            is Action.VideoOnApplicationState -> {
                //1. Auto-Turn-Off video-stream if app-background
                //2. Auto-Turn-On on revert app-foreground
                //3. Exclude self auto-turn-on if self disabled
                if (state.value.isHost) {
                    if (!viewState.video.isHostDisabledVideo.get()) {
                        onAction(Action.ToggleVideoStream(viaHost = false))
                    }
                } else {
                    service?.muteLocalVideoStream(!action.isForeground, state.value.role)
                }
            }

            is Action.SendGiftSuccess -> {

                @Synchronized
                fun onSendGiftSucceeded() = state.value.giftState.sendingGiftTriplet?.let {
                    state.value.giftState.buttonActionLoading = false
                    val giftState = state.value.giftState.sendingGiftTriplet?.first
                    val giftQuantity =  state.value.giftState.sendingGiftTriplet?.second
                    val messageId = state.value.giftState.sendingGiftTriplet?.third

                    BaseApplication.application.applicationContext?.let { context ->
                        giftState?.gift?.let { gift ->
                            onAction(Action.PublishMessage(
                                text = context.getString(R.string.sent_pre_text_string, gift.name),
                                type = TYPE_GIFT,
                                mentions = hashMapOf(),
                                gift = gift,
                                giftQuantity = giftQuantity,
                                giftPreGeneratedId = messageId
                            ))
                        }
                    }
                    giftRepository.onSendGiftSuccessAnalyticsEvent(
                        state = state.value
                    )
                    state.value.giftState.sendingGiftTriplet = null
                }

                onSendGiftSucceeded()
            }

            Action.ParticipantRoleChanged -> Unit
            Action.ActionButtonCoachMarksEnded -> onState(
                state.value.copy(
                    isActionButtonCoachMarksEnded = true
                )
            )

            else -> {}
        }
    }

    private fun onUiAction(uiAction: UiAction) {
        (context as? AppCompatActivity)?.lifecycleScope?.onUiDispatcher {
            when (uiAction) {
                is UiAction.Edited -> callback?.onEdited(uiAction.exoNotificationData)
                UiAction.Ended -> callback?.onEnded()
                is UiAction.ModerationDialog -> StreamUtils.showModeratedDialog(
                    context,
                    uiAction.moderation
                )

                UiAction.Requested -> callback?.onRequested()
                is UiAction.Started -> callback?.onStarted(StreamUtils.getExoNotificationData(state.value.room))
                is UiAction.ShowToast -> StreamUtils.showToast(uiAction.stringRes, context)
                is UiAction.ShowToastMessage -> StreamUtils.showToast(uiAction.string, context)
                is UiAction.TrySnackBar -> sheet?.trySnackBar(uiAction.stringRes)
                is UiAction.TryStringSnackBar -> sheet?.onEvent(LiveStreamSheet.Event.ShowSnackBar(uiAction.message))
                else -> {}
            }


        }
    }

    fun expand() {
        context?.fragmentManager()?.let {
            if (sheet == null || sheet?.dialog == null) {
                sheet = LiveStreamSheet.newInstance()
                sheet?.showState(it, true)
            } else {
                (sheet?.dialog as? BottomSheetDialog)?.behavior?.state =
                    BottomSheetBehavior.STATE_EXPANDED
            }

        }
    }

    fun collapse() {
        (sheet?.dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_HIDDEN
    }

    fun stopStream() {
        onAction(Action.RequestStopStream)
        onAction(Action.ManagerStatus(Status.ENDING))
    }

    fun switchStream(room: LiveRoom) {
        stopStream()
        delayCallBack(1.seconds.inWholeMilliseconds) {
            context?.activity()?.let { activity ->
                startStream(
                    context = activity,
                    room = room,
                    extraData = null,
                    callback = callback
                )
            }
        }
    }

    fun isSessionActive() = status != Status.IDLE
    fun isJoinProcessing() = status == Status.REQUESTED
    fun isSessionActiveForRoom(room: LiveRoom?) = isSessionActive() && room?.id == state.value.room.id
    fun sessionRoomId() = state.value.room.id.orEmpty()

    fun role() = state.value.role

    private fun onState(state: StreamState) {
        this.state.value = state.apply { updateMs = System.currentTimeMillis() }
    }

    inner class Builder {
        private var room: LiveRoom = LiveRoom(null)
        private var extraData: ExtraData? = null
        private var callback: Callback? = null
        fun room(room: LiveRoom) = apply { <EMAIL> = room }
        fun extraData(extraData: ExtraData?) = apply { <EMAIL> = extraData }
        fun callback(callback: Callback) = apply { <EMAIL> = callback }
        fun startStream(context: Context) = startStream(
            context = context,
            room = room,
            extraData = extraData,
            callback = callback
        )
    }

    interface Callback {
        fun onRequested()
        fun onStarted(exoNotificationData: ExoNotificationData?)
        fun onEdited(exoNotificationData: ExoNotificationData?)
        fun onEnded()
    }

    enum class Status {
        REQUESTED,
        IDLE,
        STREAMING,
        ENDING
    }

}