package noice.app.modules.live.stream.player.components.sub

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalUriHandler
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.LivePlayerChatComposeItemBinding
import noice.app.modules.creator.dialog.BadgeInfoDialog
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.model.message.LiveMessage
import noice.app.modules.live.stream.LiveStreamManager
import noice.app.modules.live.stream.core.ext.roleChatLabel
import noice.app.utils.Constants
import noice.app.utils.Utils.unwrap
import org.greenrobot.eventbus.EventBus
import javax.inject.Inject


@AndroidEntryPoint
class PlayerChatViewItem : FrameLayout {

    lateinit var holder: ViewHolder

    @Inject
    lateinit var liveStreamManager: LiveStreamManager

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }


    private fun init() {
        layoutParams = LayoutParams(MATCH_PARENT, WRAP_CONTENT)
        val binding = DataBindingUtil.inflate<LivePlayerChatComposeItemBinding>(
            LayoutInflater.from(context),
            R.layout.live_player_chat_compose_item,
            this,
            true
        )

        holder = ViewHolder(this, binding)
    }

    fun setData(
        message: LiveMessage,
        isPinned: Boolean = false,
        pinnedBy: String? = null
    ) {
        holder.binding?.composeChatItem?.apply {
            setContent {
                val urlHandler = LocalUriHandler.current

                val stateCompose by liveStreamManager.state.collectAsState()
                val role = stateCompose.speakers.roleChatLabel(message)

                PlayerChatItem(
                    modifier = Modifier,
                    message = message,
                    role = role,
                    isTypeGift = message.type == Constants.LiveChatType.TYPE_GIFT,
                    pinnedBy = pinnedBy,
                    isClickableLinks = isPinned,
                    onProfileClick = {
                        LiveTrigger.onChatProfileClick(message = message)
                    },
                    onBadgeClick = {
                        val fragmentManager = (context.unwrap() as? AppCompatActivity)?.supportFragmentManager
                        fragmentManager?.let {
                            BadgeInfoDialog.newInstance()
                                .show(fragmentManager, BadgeInfoDialog::class.java.name)
                        }
                    },
                    onSubscribedClick = {
                        val fragmentManager = (context.unwrap() as? AppCompatActivity)?.supportFragmentManager
                        fragmentManager?.let {
                            BadgeInfoDialog.newInstance(BadgeInfoDialog.VIP_BADGE)
                                .show(fragmentManager, BadgeInfoDialog::class.java.name)
                        }
                    },
                    onMentionClick = { username ->
                        EventBus.getDefault().post(
                            OpenIndexEvent(
                                OpenIndexEvent.OPEN_USER_PAGE_WITH_USER_NAME,
                                username,
                                targetPageId = "Comment"
                            )
                        )
                    },
                    onLinkClick = { url ->
                        urlHandler.openUri(url)
                    },
                    onTextClick = {
                        LiveTrigger.onChatCommentClick(message = message)
                    }
                )
            }
        }

    }


    class ViewHolder(view: View, _binding: LivePlayerChatComposeItemBinding?) :
        RecyclerView.ViewHolder(view) {
        var chatView: PlayerChatViewItem? = null
        val binding = _binding

        init {
            chatView = view as? PlayerChatViewItem
        }
    }


}