package noice.app.modules.live.fragment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentLiveLihatSemuaNewBinding
import noice.app.layoutmanagers.CustomLLManager
import noice.app.model.ExtraData
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.adapter.HomeSegmentAdapter
import noice.app.modules.dashboard.model.HomeBanner
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.live.activity.EditRoomActivity.Companion.SOURCE_SCREEN
import noice.app.modules.live.core.ExConfigs
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.feature.createroom.CreateRoomPlusActivity
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.EVENT_CREATE_ROOM_CLICKED
import noice.app.utils.DownloadUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.unwrap
import kotlin.math.ceil

@AndroidEntryPoint
class LiveLihatSemuaNewFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener,
    SwipeRefreshLayout.OnRefreshListener {

    private val viewModel: DashboardViewModel by viewModels()

    companion object {
        const val PAGE_NAME = "LiveLihatSemua"
        const val SOURCE = "SOURCE"

        fun newInstance(source : String, extraData: ExtraData? = null) =
            LiveLihatSemuaNewFragment().apply {
                arguments = Bundle().apply {
                    putString(SOURCE, source)
                    putParcelable(Constants.EXTRA_DATA, extraData)
                }
            }
    }

    private lateinit var binding: FragmentLiveLihatSemuaNewBinding
    private lateinit var ctx: Context
    private lateinit var adapter: HomeSegmentAdapter
    private val dataList = ArrayList<HomeSegment?>()
    private val bannerList = ArrayList<HomeBanner>()
    private var offset = 1
    private val limit = 4
    private var totalCount = -1
    private var source : String = ""
    private var extraData: ExtraData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLiveLihatSemuaNewBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        source = arguments?.getString(SOURCE, "") ?: ""
        extraData = arguments?.getParcelable(Constants.EXTRA_DATA)

        AnalyticsUtil.sendEvent("segment_page_viewed", Bundle().apply {
            putString("segmentName", LiveLihatSemuaFragment.PAGE_NAME)
            putString("source", source)

            if (extraData != null) {
                if (!extraData?.source.isNullOrEmpty()) {
                    putString("source", extraData?.source ?: "")
                }
            }
        })
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
        observeLiveTrigger()
    }

    override fun onResume() {
        super.onResume()

        getData()
    }

    private fun initViews() {

        binding.toolbar.title = getString(R.string.live)

        binding.errorView.setBackToHomeVisibility(GONE)
        binding.pullToRefresh.setOnRefreshListener(this)
        binding.fabCreateRoom.visibility =
            if (PrefUtils.userDetails?.isVerified == true || PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) VISIBLE else GONE

        binding.podcastRecycler.layoutManager = CustomLLManager(ctx, RecyclerView.VERTICAL, false)
        if (!::adapter.isInitialized) {
            adapter = HomeSegmentAdapter(binding.podcastRecycler, dataList, this, PAGE_NAME)
        } else {
            adapter.bindListenerToRecyclerView(binding.podcastRecycler)
        }

        binding.podcastRecycler.adapter = adapter

        binding.errorView.setOnReturnClick {
            getData()
        }

        binding.fabCreateRoom.setOnClickListener {

            if (ExConfigs.ForceUpdate.LiveModule){
                LiveTrigger.onForceUpdate()
            } else {
                if (PrefUtils.isLiveSoFileLoaded){
                    AnalyticsUtil.sendEvent(EVENT_CREATE_ROOM_CLICKED)
                    val intent = Intent(requireActivity(), CreateRoomPlusActivity::class.java)
                    intent.putExtra(SOURCE_SCREEN,"Live_AllRoom")
                    intent.putExtra(Constants.EXTRA_DATA, extraData)
                    startActivity(intent)
                    AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                        putString(FirebaseAnalytics.Param.SCREEN_NAME, "Live_CreateRoom_Page")
                        putString("previousScreen", "Live_AllRoom")
                    })
                } else {
                    DownloadUtils.showDownLoadDialog((ctx.unwrap() as AppCompatActivity).supportFragmentManager)
                }
            }
        }

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.podcastRecycler.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (dy > 0 && binding.fabCreateRoom.isExtended) {
                    binding.fabCreateRoom.shrink()
                } else if (dy < 0 && !binding.fabCreateRoom.isExtended) {
                    binding.fabCreateRoom.extend()
                }
            }
        })

        binding.submitButton.setOnClickListener {
            if (PrefUtils.isLiveSoFileLoaded){
                if (PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) {
                    if (ExConfigs.ForceUpdate.LiveModule){
                        LiveTrigger.onForceUpdate()
                    } else {
                        AnalyticsUtil.sendEvent(EVENT_CREATE_ROOM_CLICKED)

                        val intent = Intent(ctx, CreateRoomPlusActivity::class.java)
                        intent.putExtra(SOURCE_SCREEN,"Live_lihat_semua")
                        ctx.startActivity(intent)

                        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                            putString(FirebaseAnalytics.Param.SCREEN_NAME, "Live_CreateRoom_Page")
                            putString("previousScreen", "Live_lihat_semua")
                        })
                    }
                } else {
                    (ctx as AppCompatActivity).onBackPressed()
                }
            } else {
                DownloadUtils.showDownLoadDialog((ctx.unwrap() as AppCompatActivity).supportFragmentManager)
            }
        }
    }

    private fun getData(forced: Boolean = false, showLoader: Boolean = true) {
        if (forced || dataList.isNullOrEmpty()) {
            if (showLoader) {
                showLoading()
            }
            getRoomSegments()
        }
    }

    private fun getRoomSegments(loadMore: Boolean = false) {
        if(isDetached || isRemoving)
            return
        if (!loadMore) {
            offset = 1
        }

        val map = HashMap<String, String>()
        map["page"] = "livestream"
        map["offset"] = offset.toString()
        map["limit"] = limit.toString()

        viewModel.getHomeSegment(map,"").observe(viewLifecycleOwner) {
            binding.pullToRefresh.isRefreshing = false

            it?.data?.data?.filter { segment ->
                !segment.content.isNullOrEmpty()
            }.let { segments ->
                it?.data?.data = segments
            }

            if (it?.status == ResponseStatus.LOADING) {
                if (!it.data?.data.isNullOrEmpty()) {
                    handleSegmentResponse(loadMore, it)
                }
            } else if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                handleSegmentResponse(loadMore, it)
            } else if (dataList.isEmpty() || !loadMore) {
                setEmptyView()
            } else {
                handleError(it?.message)
                adapter.isLoadMoreEnabled(false)
            }
        }
    }

    private fun handleError(message: String?) {
        if (dataList.isNullOrEmpty()) {
            showError(message)
        } else {
            hideLoading()
        }
    }

    private fun handleSegmentResponse(
        loadMore : Boolean,
        resource : Resource<BaseModel<List<HomeSegment>>>
    ) {
        totalCount = resource.data?.meta?.totalCount ?: -1

        resource.data?.data?.let { segments ->
            if (loadMore)
                adapter.addMore(segments)
            else {
                val seg = ArrayList(segments)
                sendEventForSegment(seg)
                if (bannerList.isNotEmpty()) {
                    seg.add(0, HomeSegment("banners", bannerList))
                }
                adapter.addNewList(seg)
            }

            hideLoading()
        }
    }

    private fun setEmptyView() {
        hideLoading()

        binding.textSubHeading.text =
            if (PrefUtils.userDetails?.isVerified == true || PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) getString(R.string.be_the_first_to_get_busy)
            else getString(R.string.dengerin_konten_lain_dulu_ya)

        binding.submitButton.text = if (PrefUtils.userDetails?.isVerified == true || PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) getString(R.string.start_live)
        else getString(R.string.return_to_home)

        binding.emptyView.visibility = VISIBLE
        binding.fabCreateRoom.visibility = GONE
        binding.podcastRecycler.visibility = GONE

        binding.appBar.setExpanded(false)
    }

    private fun sendEventForSegment(segments: ArrayList<HomeSegment>) {
        val segmentList = (segments.clone() as ArrayList<*>).filterIsInstance<HomeSegment>()
        val live  = ArrayList<HomeContent>()
        for(a in segmentList){
            a.content?.let {
                live.addAll((it.clone() as Collection<*>).filterIsInstance<HomeContent>())
            }
        }
    }

    fun onLoginChangedEvent() {
        dataList.clear()
        bannerList.clear()
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.default_view_loader)
        binding.emptyView.visibility = GONE
        binding.podcastRecycler.visibility = VISIBLE

        binding.fabCreateRoom.visibility =
            if (PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) VISIBLE else GONE
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun showError(message: String?) {
        adapter.isLoadMoreEnabled(false)
        binding.errorView.showError(message, type = "Live_AllRoom", errorName = message)
        binding.appBar.setExpanded(false)
    }

    override fun onDestroyView() {
        adapter.notifyBusUnregister()
        super.onDestroyView()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.offset = page + 1
        if (totalCount == -1 ||
            ceil((totalCount.toDouble() / limit.toDouble())).toInt() >= offset
        ) {
            getRoomSegments(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }

    override fun onRefresh() {
        getData(true, dataList.isNullOrEmpty() && bannerList.isNullOrEmpty())
    }

    private fun observeLiveTrigger() = viewLifecycleOwner.lifecycleScope.launchWhenStarted {
        LiveTrigger.event.collect{
            when(it){
                is LiveTrigger.Event.RoomEnded -> {
                    val findData = dataList.find { segment ->
                        segment?.id == it.room?.id
                    }
                    if (findData != null) {
                        val index = dataList.indexOf(findData)
                        dataList.removeAt(index)
                        adapter.notifyItemChanged(index)
                    }
                }
                else -> {}
            }
        }
    }
}