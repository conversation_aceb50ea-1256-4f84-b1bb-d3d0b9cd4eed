package noice.app.modules

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.lifecycle.ViewModel
import noice.app.ui.theme.NoiceDarkTheme

abstract class BaseComponentActivity<VIEW_MODEL: ViewModel>: AppCompatActivity() {

    lateinit var viewModel: VIEW_MODEL
        private set

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel = createViewModel()

        setContent {
            NoiceDarkTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    ContentView()
                }
            }
        }
    }

    abstract fun createViewModel(): VIEW_MODEL

    @Composable
    abstract fun ContentView()
}