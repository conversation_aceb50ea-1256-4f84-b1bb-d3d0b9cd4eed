package noice.app.modules.subscription.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.Composable
import androidx.lifecycle.ViewModelProvider
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.modules.BaseComponentActivity
import noice.app.modules.coins.model.UserSubscription
import noice.app.modules.subscription.composable.SubscriptionCancelScreen
import noice.app.modules.subscription.viewmodels.SubscriptionCancelViewModel
import noice.app.rest.GenericResult
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.Utils.parcelable
import noice.app.views.SnackBarCustom

@AndroidEntryPoint
class SubscriptionCancelActivity : BaseComponentActivity<SubscriptionCancelViewModel>() {

    companion object {
        private const val USER_SUBSCRIPTION = "USER_SUBSCRIPTION"

        fun start(
            ctx: Context,
            userSubscription: UserSubscription?,
            cancelCallback: ActivityResultLauncher<Intent>
        ) {
            Intent(ctx, SubscriptionCancelActivity::class.java).apply {
                putExtra(USER_SUBSCRIPTION, userSubscription)
            }.also {
                cancelCallback.launch(it)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getDataFromIntent()
    }

    @Composable
    override fun ContentView() {
        SubscriptionCancelScreen(
            viewModel,
            backClick = {
                finish()
            },
            proceedButton = {
                processCancellation()
            }
        )
    }

    override fun createViewModel(): SubscriptionCancelViewModel {
        return ViewModelProvider(this)[SubscriptionCancelViewModel::class.java]
    }

    private fun getDataFromIntent() {
        viewModel.userSubscription = intent.parcelable(USER_SUBSCRIPTION)
    }

    private fun processCancellation() {
        AnalyticsBuilder.newBuilder().apply {
            putAnalyticsKey("package id", viewModel.userSubscription?.id)
            putAnalyticsKey("package name", viewModel.userSubscription?.packageName)
            putAnalyticsKey("price coin", viewModel.userSubscription?.priceCoin)
            putAnalyticsKey("expire period", viewModel.userSubscription?.expirePeriod)
        }.also {
            it.send("cancel subscription confirmed")
        }

        viewModel.unsubscribe {
            if (it is GenericResult.Loading) {
                return@unsubscribe
            }
            viewModel.confirmationDialogState.value = false
            if (it is GenericResult.Success) {
                setResult(Activity.RESULT_OK)
                finish()
            } else if (it is GenericResult.Error) {
                SnackBarCustom.Builder()
                    .parentView(window.decorView.rootView)
                    .marginBottom(resources.getDimensionPixelSize(R.dimen.dp36))
                    .text(it.message.toString())
                    .show()
            }

            val status = it is GenericResult.Success

            AnalyticsBuilder.newBuilder().apply {
                putAnalyticsKey("package id", viewModel.userSubscription?.id)
                putAnalyticsKey("package name", viewModel.userSubscription?.packageName)
                putAnalyticsKey("price coin", viewModel.userSubscription?.priceCoin)
                putAnalyticsKey("expire period", viewModel.userSubscription?.expirePeriod)
                putAnalyticsKey("status", if (status) {
                    "success"
                } else {
                    "failed"
                })
                if (it is GenericResult.Error) {
                    putAnalyticsKey("error message", it.message)
                }
            }.also { builder ->
                builder.send("cancel subscription finished")
            }
        }
    }
}