package noice.app.modules.subscription.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.ViewModelProvider
import noice.app.BuildConfig
import noice.app.R
import noice.app.modules.BaseComponentActivity
import noice.app.modules.browser.InAppBrowserActivity
import noice.app.modules.coins.model.UserSubscription
import noice.app.modules.subscription.composable.PackageDetailScreen
import noice.app.modules.subscription.viewmodels.PackageDetailViewModel
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.sendSupportEmail

class PackageDetailActivity : BaseComponentActivity<PackageDetailViewModel>() {

    companion object {
        private const val USER_SUBSCRIPTION = "USER_SUBSCRIPTION"

        fun start(
            ctx: Context,
            userSubscription: UserSubscription,
            subCancelListener: ActivityResultLauncher<Intent>?
        ) {
            Intent(ctx, PackageDetailActivity::class.java).apply {
                putExtra(USER_SUBSCRIPTION, userSubscription)
            }.also {
                subCancelListener?.launch(it) ?: ctx.startActivity(it)
            }
        }
    }

    private lateinit var cancelCallback: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getDataFromIntent()

        cancelCallback = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { activityResult ->
            if (activityResult.resultCode == Activity.RESULT_OK) {
                viewModel.isCancelledNow = true
                viewModel.userSubState.value?.isCancelled = true
                viewModel.dialogState.value = true
            }
        }
    }

    override fun createViewModel(): PackageDetailViewModel {
        return ViewModelProvider(this)[PackageDetailViewModel::class.java]
    }

    @Composable
    override fun ContentView() {
        PackageDetailScreen(
            viewModel,
            onSheetDismiss = {
                var link = PrefUtils.appCDNConfig?.survey_subscription_cancel_reason
                if (link.isNullOrEmpty()) {
                    link = BuildConfig.SUB_SURVEY_LINK
                }
                InAppBrowserActivity.startBrowser(this, link, "")
            },
            supportEmailClick = {
                emailSupport()
            },
            actionButtonClick = {
                if (viewModel.userSubState.value?.isCancelled == true) {
                    if (viewModel.isCancelledNow) {
                        setResult(Activity.RESULT_OK)
                    }
                    finish()
                } else {
                    SubscriptionCancelActivity.start(this, viewModel.userSubState.value, cancelCallback)
                }
            },
            backClick = {
                finish()
            }
        )
    }

    private fun emailSupport() {
        val sub = viewModel.userSubState.value
        val text = getString(
            R.string.subscription_package_support,
            PrefUtils.userDetails?.displayName,
            PrefUtils.userDetails?.userName.toString(),
            BuildConfig.VERSION_NAME,
            if (sub?.isCancelled == true) {
                getString(R.string.tidak)
            } else {
                getString(R.string.yes)
            },
            sub?.packageName
        )
        sendSupportEmail(text, "[App Support] - ${sub?.id}")
    }

    private fun getDataFromIntent() {
        viewModel.userSubState.value = intent.parcelable(USER_SUBSCRIPTION)
    }
}