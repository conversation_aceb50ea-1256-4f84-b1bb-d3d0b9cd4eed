package noice.app.modules.subscription.composable

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ModalBottomSheetLayout
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.material3.Divider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.toLowerCase
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import kotlinx.coroutines.launch
import noice.app.R
import noice.app.enums.MonthFormat
import noice.app.modules.subscription.enum.SubscriptionType
import noice.app.modules.subscription.viewmodels.PackageDetailViewModel
import noice.app.ui.components.SpannableClickText
import noice.app.ui.components.ToolbarCompose
import noice.app.ui.icon.Icons
import noice.app.ui.theme.Neutral0
import noice.app.ui.theme.Neutral50
import noice.app.ui.theme.Neutral80
import noice.app.ui.theme.Primary20
import noice.app.ui.theme.ReadexProTypography
import noice.app.ui.utils.vectorResource
import noice.app.utils.DateUtils
import noice.app.utils.Utils.fetchActivity
import java.util.Locale

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun PackageDetailScreen(
    viewModel: PackageDetailViewModel?,
    onSheetDismiss: () -> Unit,
    backClick: () -> Unit,
    supportEmailClick: (String) -> Unit,
    actionButtonClick: () -> Unit
) {
    val subState = remember {
        viewModel?.userSubState
    }
    val dialogState = remember {
        viewModel?.dialogState
    }

    val modalSheetState = rememberModalBottomSheetState(
        initialValue = ModalBottomSheetValue.Hidden,
        skipHalfExpanded = true
    )

    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current

    BackHandler {
        if (modalSheetState.currentValue == ModalBottomSheetValue.Expanded) {
            coroutineScope.launch {
                modalSheetState.hide()
            }
        } else {
            context.fetchActivity()?.let { activity ->
                if (subState?.value?.isCancelled == true) {
                    activity.setResult(Activity.RESULT_OK)
                }
                activity.finish()
            }
        }
    }

    var currentState = remember {
        modalSheetState.currentValue
    }

    LaunchedEffect(key1 = Unit, block = {
        snapshotFlow { modalSheetState.currentValue }.collect {
            if (currentState != it) {
                currentState = it
                if (currentState == ModalBottomSheetValue.Hidden) {
                    onSheetDismiss()
                }
            }
        }
    })

    LaunchedEffect(key1 = dialogState?.value) {
        if (dialogState?.value == false && modalSheetState.currentValue == ModalBottomSheetValue.Expanded) {
            modalSheetState.hide()
        } else if (dialogState?.value == true && modalSheetState.currentValue == ModalBottomSheetValue.Hidden) {
            modalSheetState.show()
        }
    }

    ModalBottomSheetLayout(
        sheetContent = {
            CancelSuccessDialog(dialogState)
        },
        sheetState = modalSheetState,
        sheetShape = RoundedCornerShape(16.dp)
    ) {
        ConstraintLayout(
            modifier = Modifier
                .background(Color.Black)
                .fillMaxSize()
        ) {
            val (toolbar, bigImage, header, packageStatus, packageStatusValue, autoUpdate,
                autoUpdateValue, activePeriod, activePeriodValue,method,methodValue,
                divider, needHelp, emailSupport, actionButton) = createRefs()

            ToolbarCompose (
                modifier = Modifier
                    .constrainAs(toolbar) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                    },
                text = stringResource(id = R.string.my_package),
                backClick = backClick
            )

            Image(
                modifier = Modifier
                    .constrainAs(bigImage) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(toolbar.bottom, 24.dp)
                    },
                painter = painterResource(id = R.drawable.sub_detail_image),
                contentDescription = stringResource(id = R.string.app_name)
            )

            Text(
                modifier = Modifier
                    .constrainAs(header) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(bigImage.bottom, 16.dp)
                    },
                text = subState?.value?.packageName ?: stringResource(id = R.string.my_package),
                style = ReadexProTypography.titleLarge,
                textAlign = TextAlign.Center
            )

            Text(
                modifier = Modifier
                    .constrainAs(packageStatus) {
                        start.linkTo(parent.start, 16.dp)
                        top.linkTo(header.bottom, 32.dp)
                    },
                text = stringResource(id = R.string.package_status),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.neutral_grey)
            )

            Text(
                modifier = Modifier
                    .constrainAs(packageStatusValue) {
                        end.linkTo(parent.end, 16.dp)
                        top.linkTo(packageStatus.top)
                        bottom.linkTo(packageStatus.bottom)
                    }
                    .background(
                        color = colorResource(id = R.color.dark_green1),
                        RoundedCornerShape(4.dp)
                    )
                    .padding(start = 8.dp, end = 8.dp),
                text = stringResource(id = R.string.active_label),
                style = ReadexProTypography.labelSmall
            )

            Text(
                modifier = Modifier
                    .constrainAs(autoUpdate) {
                        start.linkTo(packageStatus.start)
                        top.linkTo(packageStatus.bottom, 16.dp)
                    },
                text = stringResource(id = R.string.auto_update),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.neutral_grey)
            )

            Text(
                modifier = Modifier
                    .constrainAs(autoUpdateValue) {
                        end.linkTo(packageStatusValue.end)
                        top.linkTo(autoUpdate.top)
                        bottom.linkTo(autoUpdate.bottom)
                    },
                text = if (subState?.value?.isCancelled == true) {
                    stringResource(id = R.string.tidak)
                } else {
                    stringResource(id = R.string.yes)
                },
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.white)
            )

            Text(
                modifier = Modifier
                    .constrainAs(activePeriod) {
                        start.linkTo(packageStatus.start)
                        top.linkTo(autoUpdate.bottom, 16.dp)
                    },
                text = stringResource(id = R.string.active_period),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.neutral_grey)
            )

            Text(
                modifier = Modifier
                    .constrainAs(activePeriodValue) {
                        end.linkTo(packageStatusValue.end)
                        top.linkTo(activePeriod.top)
                        bottom.linkTo(activePeriod.bottom)
                    },
                text = DateUtils.dateWithMonthNameAndTime(subState?.value?.endTime.toString(), MonthFormat.THREE_LETTER),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.white)
            )

            Text(
                modifier = Modifier
                    .constrainAs(method) {
                        start.linkTo(activePeriod.start)
                        top.linkTo(activePeriod.bottom,16.dp)
                    },
                text = stringResource(id = R.string.subscription_method),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                color = colorResource(id = R.color.neutral_grey)
            )

            Row(
                modifier = Modifier
                    .constrainAs(methodValue) {
                        end.linkTo(packageStatusValue.end)
                        top.linkTo(method.top)
                        bottom.linkTo(method.bottom)
                    }
                    .background(Color.Transparent)
                    .padding(0.dp)
            ) {

                val text = if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.VOUCHER.value){
                        stringResource(id = R.string.kode_voucher)
                } else if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.COIN.value){
                    stringResource(id = R.string.coin)
                } else if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.IAP_ANDROID.value){
                    stringResource(id = R.string.google_pay)
                } else {
                    ""
                }
                val icon = if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.VOUCHER.value){
                    painterResource(id = R.drawable.ic_voucher)
                } else if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.COIN.value){
                    painterResource(id = R.drawable.ic_noice_coin)
                } else if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.IAP_ANDROID.value){
                    painterResource(id = R.drawable.googleg_standard_color_18)
                } else{
                    painterResource(id = R.drawable.ic_noice_coin)
                }

                Text(
                    text = text,
                    fontSize = 14.sp,
                    fontFamily = FontFamily(Font(R.font.readex_pro)),
                    fontWeight = FontWeight(400),
                    color = colorResource(id = R.color.white)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Image(
                    painter = icon,
                    contentDescription = "image description",
                    contentScale = ContentScale.None
                )
            }



            Divider(
                modifier = Modifier
                    .constrainAs(divider) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(method.bottom, 32.dp)
                    },
                color = Neutral50
            )

            Text(
                modifier = Modifier
                    .constrainAs(needHelp) {
                        top.linkTo(divider.bottom, 16.dp)
                        start.linkTo(packageStatus.start)
                    },
                text = stringResource(id = R.string.need_help),
                fontSize = 12.sp,
                lineHeight = 15.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(600),
                letterSpacing = 0.5.sp,
                color = Neutral80
            )

            SpannableClickText(
                modifier = Modifier
                    .constrainAs(emailSupport) {
                        top.linkTo(needHelp.bottom, 8.dp)
                        start.linkTo(packageStatus.start)
                    },
                fullText = stringResource(id = R.string.coin_detail_help_str_normal),
                spanTexts = listOf("<EMAIL>"),
                spanColor = colorResource(id = R.color.dull_yellow),
                bold = true,
                fontSize = 14.sp,
                lineHeight = 17.sp,
                fontFamily = FontFamily(Font(R.font.readex_pro)),
                fontWeight = FontWeight(400),
                letterSpacing = 0.1.sp,
                color = Neutral80,
                onClick = supportEmailClick
            )

            /* Cancel Subscription button is visible only in case of Coin Purchase */
            if (viewModel?.userSubState?.value?.paymentSource?.lowercase(Locale.ROOT) == SubscriptionType.COIN.value) {
                Button(
                    modifier = Modifier
                        .height(52.dp)
                        .constrainAs(actionButton) {
                            width = Dimension.fillToConstraints
                            start.linkTo(packageStatus.start)
                            end.linkTo(packageStatusValue.end)
                            top.linkTo(emailSupport.bottom, 24.dp)
                        },
                    shape = RoundedCornerShape(14.dp),
                    border = BorderStroke(
                        2.dp, color = if (subState?.value?.isCancelled == true) {
                            colorResource(id = R.color.dull_yellow)
                        } else {
                            Primary20
                        }
                    ),
                    onClick = actionButtonClick,
                    colors = ButtonDefaults.buttonColors(
                        backgroundColor = if (subState?.value?.isCancelled == true) {
                            colorResource(id = R.color.dull_yellow)
                        } else {
                            Color.Transparent
                        }
                    )
                ) {
                    Text(
                        text = if (subState?.value?.isCancelled == true) {
                            stringResource(id = R.string.subscribe_again)
                        } else {
                            stringResource(id = R.string.unsubscribe)
                        },
                        style = ReadexProTypography.bodyLarge,
                        color = if (subState?.value?.isCancelled == true) {
                            Neutral0
                        } else {
                            colorResource(id = R.color.dull_yellow)
                        }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun PackageDetailPreview() {
    PackageDetailScreen(null, {},{}, {}, {})
}