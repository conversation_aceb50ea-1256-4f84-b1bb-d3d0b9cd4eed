package noice.app.modules.subscription.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import noice.app.R
import noice.app.ui.theme.Neutral100
import noice.app.ui.theme.Neutral30
import noice.app.ui.theme.Neutral60
import noice.app.ui.theme.Neutral90
import noice.app.ui.theme.Primary0
import noice.app.ui.theme.ReadexPro
import noice.app.ui.theme.ReadexProTypography

@Composable
fun CancelSuccessDialog(
    dialogState: MutableState<Boolean>?
) {
    Column (
        modifier = Modifier
            .background(color = Neutral30)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Divider(
            modifier = Modifier
                .width(36.dp)
                .clip(shape = RoundedCornerShape(size = 16.dp)),
            thickness = 5.dp,
            color = Neutral60
        )

        Text(
            modifier = Modifier
                .padding(top = 24.dp),
            text = stringResource(id = R.string.sub_cancelled),
            style = ReadexPro.BOLD.titleMedium,
            color = Neutral100
        )

        Image(
            modifier = Modifier
                .padding(top = 26.dp),
            painter = painterResource(id = R.drawable.transaction_subscription_canceled),
            contentDescription = stringResource(id = R.string.app_name)
        )

        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 24.dp),
            textAlign = TextAlign.Center,
            text = stringResource(id = R.string.sub_cancelled_desc),
            style = ReadexProTypography.bodyMedium,
            color = Neutral90
        )

        Button(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp, bottom = 8.dp)
                .height(52.dp),
            onClick = {
                dialogState?.value = false
            },
            shape = RoundedCornerShape(14.dp),
            colors = ButtonDefaults.buttonColors(backgroundColor = colorResource(id = R.color.dull_yellow))
        ) {
            Text(
                text = stringResource(id = R.string.give_your_opinion),
                style = ReadexProTypography.bodyLarge,
                color = Primary0
            )
        }
    }
}

@Preview
@Composable
fun ScreenPreview() {
    CancelSuccessDialog(null)
}