package noice.app.modules.subscription.repository

import noice.app.rest.BaseRepo
import noice.app.rest.apiinterfaces.SubscriptionApiInterface
import javax.inject.Inject

class SubscriptionRepository @Inject constructor(
    private val subscriptionInterface: SubscriptionApiInterface
): BaseRepo() {
    fun processCancellation() = networkBoundFlowResource(
        cachingConstant = null,
        apiCall = {
            subscriptionInterface.unsubscribe()
        }
    )
}