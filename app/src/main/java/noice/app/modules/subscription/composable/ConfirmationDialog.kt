package noice.app.modules.subscription.composable

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import noice.app.R
import noice.app.ui.components.LoaderButton
import noice.app.ui.theme.Neutral100
import noice.app.ui.theme.Neutral30
import noice.app.ui.theme.Neutral80
import noice.app.ui.theme.Primary50
import noice.app.ui.theme.ReadexProTypography

@Composable
fun ConfirmationDialog(
    title: String?,
    desc: String?,
    positiveBtn: String? = stringResource(id = R.string.yes_process),
    negativeBtn: String? = stringResource(id = R.string.not),
    dialogState: MutableState<Boolean>?,
    positiveBtnClick: () -> Unit
) {
    Column (
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Neutral30, RoundedCornerShape(4.dp))
            .padding(16.dp)
    ) {
        Text (
            modifier = Modifier
                .fillMaxWidth(),
            text = title.toString(),
            style = ReadexProTypography.bodyLarge,
            color = Neutral100
        )

        Text (
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            text = desc.toString(),
            style = ReadexProTypography.bodyMedium,
            color = Neutral80
        )

        Row (
            modifier = Modifier.
                padding(top = 24.dp)
        ) {
            OutlinedButton(
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp),
                onClick = {
                    dialogState?.value = false
                },
                border = BorderStroke(0.dp, Color.Transparent),
                colors = ButtonDefaults.buttonColors(backgroundColor = Color.Transparent)
            ) {
                Text(
                    text = negativeBtn.toString(),
                    style = ReadexProTypography.labelLarge,
                    color = Primary50
                )
            }

            LoaderButton(
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp),
                text = positiveBtn.toString(),
                onClick = positiveBtnClick
            )
        }
    }
}

@Preview
@Composable
fun DialogPreview() {
    ConfirmationDialog(
        title = "Title",
        desc = "Desc",
        dialogState = null,
        positiveBtnClick = {}
    )
}