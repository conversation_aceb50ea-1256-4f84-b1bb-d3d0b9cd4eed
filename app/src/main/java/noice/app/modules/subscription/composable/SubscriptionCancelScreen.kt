package noice.app.modules.subscription.composable

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import noice.app.R
import noice.app.enums.MonthFormat
import noice.app.modules.subscription.viewmodels.SubscriptionCancelViewModel
import noice.app.ui.components.ToolbarCompose
import noice.app.ui.theme.Neutral90
import noice.app.ui.theme.Neutral99
import noice.app.ui.theme.Primary0
import noice.app.ui.theme.ReadexProTypography
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.DateUtils

@Composable
fun SubscriptionCancelScreen(
    viewModel: SubscriptionCancelViewModel?,
    backClick: () -> Unit,
    proceedButton: () -> Unit
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxSize()
            .background(color = colorResource(id = R.color.black))
    ) {
        val (toolbar, title, desc, benefit1, benefit2, benefit3, benefit4, cancelButton) = createRefs()
        
        ToolbarCompose(
            modifier = Modifier
                .constrainAs(toolbar) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                },
            text = stringResource(id = R.string.unsubscribe),
            backClick = backClick
        )

        Text(
            modifier = Modifier
                .constrainAs(title) {
                    width = Dimension.fillToConstraints
                    start.linkTo(parent.start, 16.dp)
                    end.linkTo(parent.end, 16.dp)
                    top.linkTo(toolbar.bottom, 16.dp)
                },
            text = stringResource(id = R.string.cancel_screen_title),
            style = ReadexProTypography.titleLarge
        )

        Text(
            modifier = Modifier
                .constrainAs(desc) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    end.linkTo(title.end)
                    top.linkTo(title.bottom, 8.dp)
                },
            text = stringResource(R.string.sub_cancel_desc, DateUtils.dateWithMonthNameAndTime(viewModel?.userSubscription?.endTime.toString(), MonthFormat.THREE_LETTER)),
            style = ReadexProTypography.bodyMedium
        )

        Row (
            modifier = Modifier
                .constrainAs(benefit1) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    end.linkTo(title.end)
                    top.linkTo(desc.bottom, 20.dp)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier
                    .width(48.dp)
                    .height(48.dp),
                painter = painterResource(id = R.drawable.ad_cross_icon),
                contentDescription = stringResource(id = R.string.app_name)
            )
            Column(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.advertisement),
                    style = ReadexProTypography.labelLarge,
                    color = Neutral99
                )
                Text(
                    modifier = Modifier.padding(top = 8.dp),
                    text = stringResource(R.string.benefit_removed_desc1),
                    style = ReadexProTypography.labelMedium,
                    color = Neutral90
                )
            }
        }

        Row (
            modifier = Modifier
                .constrainAs(benefit2) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    end.linkTo(title.end)
                    top.linkTo(benefit1.bottom, 16.dp)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier
                    .width(48.dp)
                    .height(48.dp),
                painter = painterResource(id = R.drawable.benefit_removed_2),
                contentDescription = stringResource(id = R.string.app_name)
            )
            Column(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.vip_content),
                    style = ReadexProTypography.labelLarge,
                    color = Neutral99
                )
                Text(
                    modifier = Modifier.padding(top = 8.dp),
                    text = stringResource(R.string.benefit_removed_desc2),
                    style = ReadexProTypography.labelMedium,
                    color = Neutral90
                )
            }
        }

        Row (
            modifier = Modifier
                .constrainAs(benefit3) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    end.linkTo(title.end)
                    top.linkTo(benefit2.bottom, 16.dp)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier
                    .width(48.dp)
                    .height(48.dp),
                painter = painterResource(id = R.drawable.benefit_removed_3),
                contentDescription = stringResource(id = R.string.app_name)
            )
            Column(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.video_content),
                    style = ReadexProTypography.labelLarge,
                    color = Neutral99
                )
                Text(
                    modifier = Modifier.padding(top = 8.dp),
                    text = stringResource(R.string.benefit_removed_desc3),
                    style = ReadexProTypography.labelMedium,
                    color = Neutral90
                )
            }
        }

        Row (
            modifier = Modifier
                .constrainAs(benefit4) {
                    width = Dimension.fillToConstraints
                    start.linkTo(title.start)
                    end.linkTo(title.end)
                    top.linkTo(benefit3.bottom, 16.dp)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                modifier = Modifier
                    .width(48.dp)
                    .height(48.dp),
                painter = painterResource(id = R.drawable.benefit_removed_4),
                contentDescription = stringResource(id = R.string.app_name)
            )
            Column(
                modifier = Modifier.padding(start = 8.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.download),
                    style = ReadexProTypography.labelLarge,
                    color = Neutral99
                )
                Text(
                    modifier = Modifier.padding(top = 8.dp),
                    text = stringResource(R.string.benefit_removed_desc4),
                    style = ReadexProTypography.labelMedium,
                    color = Neutral90
                )
            }
        }

        val dialogState = remember {
            viewModel?.confirmationDialogState
        }

        Button(
            modifier = Modifier
                .height(52.dp)
                .constrainAs(cancelButton) {
                    width = Dimension.fillToConstraints
                    start.linkTo(parent.start, 16.dp)
                    end.linkTo(parent.end, 16.dp)
                    bottom.linkTo(parent.bottom, 16.dp)
                },
            onClick = {
                AnalyticsBuilder.newBuilder().apply {
                    putAnalyticsKey("package id", viewModel?.userSubscription?.id)
                    putAnalyticsKey("package name", viewModel?.userSubscription?.packageName)
                    putAnalyticsKey("price coin", viewModel?.userSubscription?.priceCoin)
                    putAnalyticsKey("expire period", viewModel?.userSubscription?.expirePeriod)
                }.also {
                    it.send("cancel subscription clicked")
                }
                dialogState?.value = true
            },
            shape = RoundedCornerShape(14.dp),
            colors = ButtonDefaults.buttonColors(backgroundColor = colorResource(id = R.color.dull_yellow))
        ) {
            Text(
                text = stringResource(id = R.string.continue_txt),
                style = ReadexProTypography.bodyLarge,
                color = Primary0
            )
        }

        if (dialogState?.value == true) {
            Dialog(
                onDismissRequest = {

                },
                content = {
                    ConfirmationDialog(
                        title = stringResource(id = R.string.unsubscribe_process),
                        desc = stringResource(id = R.string.unsubscribe_desc),
                        dialogState = dialogState,
                        positiveBtnClick = proceedButton
                    )
                }
            )
        }
    }
}

@Preview
@Composable
fun PreviewScreen() {
    SubscriptionCancelScreen(null, {}, {})
}