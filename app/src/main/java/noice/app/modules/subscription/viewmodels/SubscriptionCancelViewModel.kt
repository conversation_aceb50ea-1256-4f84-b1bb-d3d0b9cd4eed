package noice.app.modules.subscription.viewmodels

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import noice.app.model.generics.BaseModel
import noice.app.modules.coins.model.UserSubscription
import noice.app.modules.subscription.repository.SubscriptionRepository
import noice.app.rest.GenericResult
import javax.inject.Inject

@HiltViewModel
class SubscriptionCancelViewModel @Inject constructor(
    private val repository: SubscriptionRepository
): ViewModel() {

    var userSubscription: UserSubscription? = null
    val confirmationDialogState = mutableStateOf(false)

    fun unsubscribe(listener: (GenericResult<BaseModel<UserSubscription>?>?) -> Unit) {
        viewModelScope.launch {
            repository.processCancellation().collect {
                listener(it)
            }
        }
    }
}