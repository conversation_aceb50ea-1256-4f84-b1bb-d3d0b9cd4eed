package noice.app.modules.profile.fragment

import android.Manifest
import android.content.Context
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import androidx.vectordrawable.graphics.drawable.AnimatedVectorDrawableCompat
import com.google.android.material.appbar.AppBarLayout.VISIBLE
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.analytics.FirebaseAnalytics
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.data.WalletController
import noice.app.databinding.FragmentProfileBinding
import noice.app.enums.UserAction
import noice.app.layoutmanagers.CustomLLManager
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.model.user.User
import noice.app.modules.browser.InAppBrowserActivity
import noice.app.modules.coins.activity.CoinTopUpActivity
import noice.app.modules.coins.fragments.VipSubscriptionDialog
import noice.app.modules.creator.dialog.BadgeInfoDialog
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.adapter.ScheduledRoomAdapter
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.activity.EditProfileActivity
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.podcast.model.Content
import noice.app.modules.profile.activity.ProfileMenu
import noice.app.modules.profile.adapter.ProfilePagerAdapter
import noice.app.modules.profile.model.BaseAggregatedModel
import noice.app.modules.profile.viewmodel.ProfileViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_USER
import noice.app.utils.Constants.Companion.EPISODE_VIEW_PLAY_CLICK
import noice.app.utils.Constants.Companion.FOLLOW_USER_PROFILE
import noice.app.utils.Constants.Companion.UNFOLLOW_USER_PROFILE
import noice.app.utils.ExperimentUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PermissionUtils
import noice.app.utils.PrefUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.unwrap
import noice.app.utils.isTrue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.TimeUnit
import kotlin.math.abs

@AndroidEntryPoint
class ProfileFragment : Fragment(), SwipeRefreshLayout.OnRefreshListener {

    private val viewModel: ProfileViewModel by viewModels()

    private lateinit var ctx: Context
    private lateinit var binding: FragmentProfileBinding
    private lateinit var pagerAdapter: ProfilePagerAdapter
    private var noiceMakerDialog: BadgeInfoDialog? = null
    private var userId = ""
    private var userName = ""
    private var userDetails: User? = null
    private var clickTime = 0L
    private var isFollowUser = 0.0
    private var isSamePage = false
    private var mIsTheTitleVisible = false
    private var source: String = ""
    private var initialize = true
    private var isCreativesAvailable = false
    private var showCreatorStudioButton = false
    private var extraData: ExtraData? = null
    private var liveRoom: LiveRoom? = null
    private var upcomingRooms: List<LiveRoom> = ArrayList()
    private var analyticsHit = false

    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null

    companion object {
        const val USER_ID = "USER_ID"
        private const val SOURCE = "SOURCE"
        private const val USER_NAME = "USER_NAME"
        const val NON_LOGGED_IN_TAG = "NON_LOGGED_IN_TAG"
        private const val EXTRA_DATA = "EXTRA_DATA"

        private const val PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR = 0.6f
        private const val ALPHA_ANIMATIONS_DURATION = 600

        fun newInstance(userId: String, source: String, extraData: ExtraData? = null) =
            ProfileFragment().apply {
                arguments = Bundle().apply {
                    putString(USER_ID, userId)
                    putString(SOURCE, source)
                    putParcelable(EXTRA_DATA, extraData)
                }
            }

        fun newInstanceWithUserName(userName: String, source: String) = ProfileFragment().apply {
            arguments = Bundle().apply {
                putString(USER_NAME, userName)
                putString(SOURCE, source)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        showCreatorStudioButton =
            PrefUtils.appConfig?.common_config?.show_creator_studio_button ?: false

        userId = if (
            PrefUtils.isLoggedIn
            && !PrefUtils.isLoggedInAsGuest
            && arguments?.getString(USER_ID).isNullOrEmpty()
        ) PrefUtils.userDetails?.id
            ?: ""
        else arguments?.getString(USER_ID) ?: ""

        userName = arguments?.getString(USER_NAME) ?: ""
        source = arguments?.getString(SOURCE) ?: ""
        extraData = arguments?.parcelable(EXTRA_DATA)

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentProfileBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initialize = true

        initViews()

        if (userId.isNotEmpty() || userName.isNotEmpty()) {
            getData()
        }
    }

    override fun onStart() {
        super.onStart()

        MoEngageAnalytics.setOrResetInAppContext(setOf("profile"))
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(ctx)
        MoEInAppHelper.getInstance().showNudge(ctx)
    }

    private fun initViews() {
        pagerAdapter = ProfilePagerAdapter(this, null, extraData)
        binding.viewPager.adapter = pagerAdapter

        WalletController.observeWallet(viewLifecycleOwner) { wallet ->
            if (wallet != null) {
                if ((wallet.totalCoins ?: 0) < 0) {
                    binding.coins.setTextColor(ContextCompat.getColor(ctx, R.color.pinkish_red))
                } else {
                    binding.coins.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                }
                binding.coins.text = WalletController.formattedCoins
                binding.diamonds.text = WalletController.formattedDiamonds
            }
        }

        binding.pullToRefresh.setOnRefreshListener(this)
        binding.errorView.setBackToHomeVisibility(GONE)
        binding.errorView.setLayoutBackground(R.color.black)
        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()

            getData()
        }

        binding.appBar.addOnOffsetChangedListener { appBarLayout, verticalOffset ->
            if (abs(verticalOffset) == appBarLayout?.totalScrollRange) {
                binding.pullToRefresh.isEnabled = false
            } else {
                binding.pullToRefresh.isEnabled = verticalOffset == 0
            }

            val percentage =
                abs(verticalOffset).toFloat() / (appBarLayout?.totalScrollRange?.toFloat() ?: 1f)

            handleToolbarTitleVisibility(percentage)

            if (appBarLayout.totalScrollRange - abs(verticalOffset) == appBarLayout.totalScrollRange) {
                binding.fabCreateKarya.extend()
            } else {
                binding.fabCreateKarya.shrink()
            }
        }

        binding.toolbarActionButton.setOnClickListener {
            if (isMe()) {
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Setting_Support")
                    putString("previousScreen", "Profile_Page")
                })
                userDetails?.let {
                    openFragment(
                        UserSettingsFragment.newInstance(it),
                        UserSettingsFragment::class.java.simpleName
                    )
                }
            } else {
                EventBus.getDefault().post(
                    OpenIndexEvent(
                        OpenIndexEvent.OPEN_SEARCH_PAGE,
                        targetPageId = "Profile_Page"
                    )
                )
            }
        }

        binding.upcomingRoomRecycler.getRecyclerView().apply {
            layoutManager = CustomLLManager(ctx, RecyclerView.HORIZONTAL, false)
            addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp8)))
        }

        binding.userProfileLayout.setOnClickListener {
            if (liveRoom != null) {
                LiveTrigger.onOpenLiveDetailPage(
                    roomId = liveRoom?.id,
                    roomStatus = null,
                    playContentDirectly = false
                )
            }
        }

        binding.layoutFollow.setOnClickListener {

            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(
                    ctx as AppCompatActivity,
                    ctx.getString(R.string.this_action_requires_internet)
                )
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-follow-artist")
                (ctx as HomeActivity).handleUserNotLoggedIn(loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            if (SystemClock.elapsedRealtime() - clickTime < 500) {
                return@setOnClickListener
            }

            clickTime = SystemClock.elapsedRealtime()

            val value = if (isFollowUser > 0.0) {
                0.0 //unfollow
            } else {
                1.0 //follow
            }

            isFollowUser = value

            if (value == 0.0) { //unfollow
                var count = userDetails?.meta?.aggregations?.followers
                if (count != null && count > 0L) {
                    count -= 1
                    userDetails?.meta?.aggregations?.followers = count
                }
            } else { //follow
                var count = userDetails?.meta?.aggregations?.followers
                if (count != null && count > 0L) {
                    count += 1
                    userDetails?.meta?.aggregations?.followers = count
                } else {
                    userDetails?.meta?.aggregations?.followers = 1
                }
            }


            val eventName = if (value == 0.0) {
                "user_unfollowed"
            } else {
                "user_followed"
            }
            AnalyticsUtil.sendEventForUserFollow(
                "user",
                eventName,
                userDetails?.id,
                userDetails?.userName ?: "",
                userDetails?.displayName ?: "",
                "profile_page"
            )

            updateFollow()

            val mediaAction = if (PrefUtils.isLoggedIn) {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value,
                    userDetails?.id,
                    "user",
                    PrefUtils.userDetails?.id.toString(),
                    "user",
                    null
                )
            } else {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value,
                    userDetails?.id,
                    "user",
                    PrefUtils.guestId,
                    "user",
                    null
                )
            }

            viewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS && !it.data?.userActions.isNullOrEmpty()) {
                    it.data?.userActions?.find { mediaAction ->
                        mediaAction.action.equals(UserAction.FOLLOW.action, true)
                    }?.let { isFollow ->
                        isFollowUser = isFollow.actionValue ?: 0.0
                        isSamePage = true

                        // updateFollow()
                        if (value > 0.0) { //follow
                            EventBus.getDefault()
                                .post(EventMessage(userDetails, FOLLOW_USER_PROFILE))
                        } else { //unfollow
                            EventBus.getDefault()
                                .post(EventMessage(userDetails, UNFOLLOW_USER_PROFILE))
                        }

                        if (userDetails?.meta?.userActions.isNullOrEmpty()) {
                            userDetails?.meta?.userActions = ArrayList()
                            userDetails?.meta?.userActions?.add(isFollow)
                        }
                    }
                }
            }
        }

        binding.layoutFollower.setOnClickListener {
            EventBus.getDefault().post(
                OpenIndexEvent(
                    OpenIndexEvent.OPEN_FOLLOWER,
                    userDetails?.id ?: "",
                    targetPageId = "Profile_Page"
                )
            )
        }

        binding.layoutFollowing.setOnClickListener {
            EventBus.getDefault().post(
                OpenIndexEvent(
                    OpenIndexEvent.OPEN_FOLLOWEE,
                    userDetails?.id ?: "",
                    targetPageId = "Profile_Page"
                )
            )
        }

        binding.threeDotMenu.setOnClickListener {
            userDetails?.let { user ->
                ProfileMenu.start(ctx, user)
            }
        }

        binding.fabCreateKarya.setOnClickListener {
            AnalyticsUtil.sendEvent("tambah_karya_clicked", Bundle().apply {
                putString("userId", userId)
                putString("username", userName)
            })
            InAppBrowserActivity.startBrowser(
                ctx,
                BuildConfig.STUDIO_URL,
                ctx.getString(R.string.creator_studio)
            )
        }

        binding.coins.setOnClickListener {
            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_COIN_HISTORY, false))
        }


        binding.diamonds.setOnClickListener {
            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            InAppBrowserActivity.startBrowser(ctx, BuildConfig.STUDIO_URL, "", "diamond")
        }

        binding.addCoins.setOnClickListener {
            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            CoinTopUpActivity.start(ctx, "Profile_Page")
        }
    }

    private fun setTabs(data: BaseAggregatedModel) {

        if (initialize) {
            initialize = false

            pagerAdapter = ProfilePagerAdapter(this, data, extraData)
            binding.viewPager.adapter = pagerAdapter
            binding.viewPager.isUserInputEnabled = false
            binding.viewPager.clearAnimation()

            val tabNames =
                BaseApplication.getBaseAppContext().resources.getStringArray(R.array.profile_tabs_name)

            TabLayoutMediator(binding.tabLayout, binding.viewPager, true, false) { tab, position ->
                tab.text = tabNames[position]
            }.attach()

            binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    updateTabText(tab)
                    handleAddCreation(tab)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    updateTabText(tab)
                }

                override fun onTabReselected(tab: TabLayout.Tab?) {
                    updateTabText(tab)
                }
            })

            updateTabText(binding.tabLayout.getTabAt(0))
        } else {
            pagerAdapter.refreshFragments(data)
        }

        if (isCreativesAvailable) {
            binding.viewPager.setCurrentItem(1, false)
        } else {
            binding.viewPager.setCurrentItem(0, false)
        }
    }

    private fun updateFollow() {
        if (isFollowUser == 0.0) { //unfollow
            binding.txtFollow.text = getString(R.string.follow)
            binding.layoutFollow.isSelected = false
            binding.txtFollow.isSelected = false
        } else { //follow
            binding.layoutFollow.isSelected = true
            binding.txtFollow.isSelected = true
            binding.txtFollow.text = getString(R.string.following)
        }

        userDetails?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            isFollow.actionValue = isFollowUser
        }

        binding.txtFollowers.text =
            Utils.abbrNumberFormat(userDetails?.meta?.aggregations?.followers ?: 0, false)
        binding.txtFollowerUnit.text =
            Utils.getUnit(userDetails?.meta?.aggregations?.followers ?: 0)
    }

    private fun checkSelf() {
        if (isMe()) {
            binding.threeDotMenu.visibility = GONE
            binding.editButton.visibility = VISIBLE
            binding.editButton.setOnClickListener {
                EditProfileActivity.start(ctx, "Profile_Page")

                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Profile_Edit")
                    putString("previousScreen", "Profile_Page")
                })
            }
            binding.toolbarActionButton.setImageResource(R.drawable.ic_settings)
            binding.toolbar.navigationIcon = null
        } else {
            binding.threeDotMenu.visibility = VISIBLE
            binding.editButton.visibility = GONE
            binding.toolbarActionButton.setImageResource(R.drawable.ic_search_white_16dp)
            binding.toolbar.navigationIcon =
                ContextCompat.getDrawable(ctx, R.drawable.ic_left_arrow_white)
            binding.toolbar.setNavigationOnClickListener {
                (ctx as AppCompatActivity).onBackPressed()
            }
        }
        binding.toolbarActionButton.visibility = VISIBLE
    }

    fun getData(refreshed: Boolean = false) {
        if (userName.isNotEmpty()) {
            if (!refreshed)
                binding.errorView.showLoading()
            getUserDetailByUserName(userName)
        } else {
            if (isMe()) {
                getCoinFeatureFlag()
                binding.layoutFollow.visibility = GONE
            } else if (userId.isNotEmpty()) {
                binding.layoutFollow.visibility = VISIBLE
            }

            if (!refreshed) {
                binding.errorView.showLoading()
            }

            getProfileData()
        }
    }

    private fun getCoinFeatureFlag() {
        if (ExperimentUtils.isCoinFeatureEnabled()) {
            visibilityOfWalletUI(VISIBLE)
            WalletController.refreshWallet()
        } else {
            visibilityOfWalletUI(GONE)
        }
    }

    private fun getUserDetailByUserName(userName: String) {

        viewModel.getUserDetailsByUserName(userName, cachingConstant = "")
            .observe(viewLifecycleOwner) {
                binding.pullToRefresh.isRefreshing = false

                when (it?.status) {
                    ResponseStatus.LOADING -> {
                        if (it.data?.data != null) {
                            handleGetUserByNameResponse(it)
                        }
                    }
                    ResponseStatus.SUCCESS -> {
                        if (it.data?.data != null) {
                            handleGetUserByNameResponse(it)
                        } else {
                            showError(it.message)
                        }
                    }
                    else -> {
                        showError(it?.message)
                    }
                }
            }
    }

    private fun showError(message: String?) {
        binding.errorView.showError(
            message,
            type = "Profile_Page",
            errorName = message
        )
    }

    private fun handleGetUserByNameResponse(
        resource: Resource<BaseModel<User>>
    ) {
        userId = resource.data?.data?.id ?: ""

        getProfileData()
    }

    private fun getProfileData() {

        if (isDetached || isRemoving || !isAdded)
            return

        val map = HashMap<String, String>()
        map["profileUserId"] = userId
        map["aggregations"] = "listeningTime|followers|followees"
        map["includeEntities"] = "[\"userActions\",\"user\",\"live\"]"
        map["limit"] = "10"
        map["page"] = "1"

        viewModel.getProfileData(userId, map, binding.pullToRefresh.isRefreshing)
            .observe(viewLifecycleOwner) {
                binding.pullToRefresh.isRefreshing = false

                when (it?.status) {
                    ResponseStatus.LOADING -> {
                        if (it.data?.user?.data != null) {
                            handleGetUserResponse(it)
                        }
                    }
                    ResponseStatus.SUCCESS -> {
                        if (it.data?.user?.data != null) {
                            handleGetUserResponse(it)
                            sendAnalytics()
                        } else {
                            handleError(it.message)
                        }
                    }
                    else -> {
                        handleError(it?.message)
                    }
                }
            }
    }

    private fun handleError(message: String?) {
        if (userDetails == null) {
            binding.errorView.showError(message)
        } else {
            binding.errorView.hide()
        }
    }

    private fun handleGetUserResponse(
        resource: Resource<BaseAggregatedModel>
    ) {

        if (isMe()) {
            if (resource.data?.user?.data != null) {
                PrefUtils.userDetails = resource.data.user?.data
            }
        }

        resource.data?.let { data ->
            isCreativesAvailable = !data.catalog?.data.isNullOrEmpty()

            userDetails = data.user?.data

            handleLiveProfile(data.live?.data)

            setUserDetails()

            setTabs(data)
        }

        binding.errorView.hide()
    }

    private fun sendAnalytics() {
        if (PrefUtils.isLoggedIn) {
            AnalyticsUtil.sendEvent("profile_page_viewed", Bundle().apply {
                putString("is_live", if (liveRoom != null) "yes" else "no")
                putString("is_profile_owner", if (isMe()) "yes" else "no")
                putString("is_scheduled_live", if (upcomingRooms.isNotEmpty()) "yes" else "no")
                putString("profileUserId", userId)
                putString("username", userDetails?.userName ?: "")
                putString("userType", "regular")

                if (extraData != null) {
                    putString("segmentName", extraData?.segmentName ?: "")
                    putString("tileName", extraData?.title ?: "")
                    putString("tabName", extraData?.tabName ?: "")
                    putInt("ranking", extraData?.ranking ?: -1)

                    if (!extraData?.source.isNullOrEmpty()) {
                        putString("source", extraData?.source ?: "")
                    }
                }
            })
        }

        if (!isMe()) {
            AnalyticsUtil.sendEvent("profile_viewed", Bundle().apply {
                putString("profileUserId", userId)
                putString("username", userDetails?.userName ?: "")
                putString("userType", "regular")

                if (extraData != null) {
                    putString("segmentName", extraData?.segmentName ?: "")
                    putString("tileName", extraData?.title ?: "")
                    putString("tabName", extraData?.tabName ?: "")
                    putInt("ranking", extraData?.ranking ?: -1)
                }
            })
        }
    }

    private fun sendAnalytics(hitEvent: Boolean) {
        if (!hitEvent) {
            if (PrefUtils.isLoggedIn) {
                AnalyticsUtil.sendEvent("profile_page_viewed", Bundle().apply {
                    putString("is_live", if (liveRoom != null) "yes" else "no")
                    putString("is_profile_owner", if (isMe()) "yes" else "no")
                    putString("is_scheduled_live", if (upcomingRooms.isNotEmpty()) "yes" else "no")

                    if (!extraData?.source.isNullOrEmpty()) {
                        putString("source", extraData?.source ?: "")
                    }
                })
            }

            <EMAIL> = true
        }
    }

    private fun handleLiveProfile(live: List<LiveRoom>?) {
        liveRoom = live?.find { room ->
            room.status == "live" && room.roomParticipants?.find { roomParticipant ->
                roomParticipant.user?.id == userId
            } != null
        }

        val animated = AnimatedVectorDrawableCompat.create(ctx, R.drawable.animated_ring)
        animated?.registerAnimationCallback(object : Animatable2Compat.AnimationCallback() {
            override fun onAnimationEnd(drawable: Drawable?) {
                binding.animatedRing.post { animated.start() }
            }
        })
        binding.animatedRing.setImageDrawable(animated)

        //update Live UI
        if (liveRoom != null) {
            binding.liveTag.visibility = VISIBLE
            binding.animatedRing.visibility = VISIBLE
            animated?.start()
        } else {
            binding.liveTag.visibility = GONE
            binding.animatedRing.visibility = GONE
            animated?.stop()
        }

        if (live != null) {
            upcomingRooms = live.filter { room ->
                room.status == "upcoming"
            }
        }

        if (upcomingRooms.isNotEmpty()) {
            binding.upcomingRoomRecycler.visibility = VISIBLE

            //update Upcoming UI
            binding.upcomingRoomRecycler.getRecyclerView().adapter =
                ScheduledRoomAdapter(ctx, upcomingRooms, permissionUtils)
        } else {
            binding.upcomingRoomRecycler.visibility = GONE
        }

        sendAnalytics(analyticsHit)
    }

    private fun setUserDetails() {

        if (userId.isNotEmpty()) {
            checkSelf()
        }

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, "Profile_Page")
            putString("previousScreen", source)
            putString("ProfileUserId", userDetails?.id ?: "")
            putString("userName", userDetails?.userName ?: "")
        })

        if (userDetails?.isVerified == true) {
            binding.verifiedBadge.visibility = VISIBLE
        } else {
            binding.verifiedBadge.visibility = GONE
        }

        if (userDetails?.creatorProfile?.isActive.isTrue()) {
            if (userDetails?.creatorProfile?.nPlusBadge == true) {
                binding.noicemakerBadge.setImageResource(R.drawable.ic_n_plus_badge)
            } else {
                binding.noicemakerBadge.setImageResource(R.drawable.ic_noicemaker_badge)
            }
            binding.noicemakerBadge.visibility = VISIBLE
        } else {
            binding.noicemakerBadge.visibility = GONE
        }

        if (userDetails?.isSubscribed == true) {
            binding.vipBadge.visibility = VISIBLE
        } else {
            binding.vipBadge.visibility = GONE
        }

        binding.noicemakerBadge.setOnClickListener {
            val badgeType = if (userDetails?.creatorProfile?.nPlusBadge == true) {
                BadgeInfoDialog.N_PLUS
            } else {
                BadgeInfoDialog.POD_CASTER
            }
            noiceMakerDialog = BadgeInfoDialog.newInstance(badgeType)
            noiceMakerDialog?.show((ctx.unwrap() as AppCompatActivity).supportFragmentManager, "NoiceMakerBadgeDialog")
        }

        binding.vipBadge.setOnClickListener {
            noiceMakerDialog = BadgeInfoDialog.newInstance(BadgeInfoDialog.VIP_BADGE)
            noiceMakerDialog?.show(
                (ctx.unwrap() as AppCompatActivity).supportFragmentManager, "NoiceMakerBadgeDialog")
        }

        if (userDetails?.localPhotoUri != null) {
            ImageUtils.loadImageByUri(
                binding.userProfileImageBack,
                userDetails?.localPhotoUri,
                true,
                blur = true
            )
            ImageUtils.loadImageByUri(binding.userProfileImage, userDetails?.localPhotoUri, true)
        } else {
            ImageUtils.showBlurImage(
                userDetails?.originalImage,
                binding.userProfileImageBack,
                placeHolder = R.drawable.ic_user_profile
            )
            ImageUtils.loadImageByUrl(
                binding.userProfileImage,
                userDetails?.smallImage,
                true,
                R.drawable.ic_user_profile, originalUrl = userDetails?.originalImage
            )
        }

        binding.title.text = userDetails?.displayName
        binding.userFullName.text = userDetails?.displayName
        if (!userDetails?.userName.isNullOrEmpty()) {
            binding.userName.text = getString(R.string.at_username, userDetails?.userName)
        } else {
            binding.userName.visibility = GONE
        }

        if (!userDetails?.bio.isNullOrEmpty()) {
            binding.description.text = userDetails?.bio
            binding.description.visibility = VISIBLE
        } else {
            binding.description.visibility = GONE
        }

        val formattedMinutes = getFormattedMinutes(userDetails?.meta?.aggregations?.listeningTime?.toLong() ?: 0)
        binding.minutesListened.text = formattedMinutes.first
        binding.txtListenUnit.text = formattedMinutes.second

        binding.txtFollowers.text =
            Utils.abbrNumberFormat(userDetails?.meta?.aggregations?.followers ?: 0, false)
        binding.txtFollowerUnit.text =
            Utils.getUnit(userDetails?.meta?.aggregations?.followers ?: 0)
        binding.txtFollowe.text = Utils.getCount(userDetails?.meta?.aggregations?.followees ?: 0)
        binding.txtUnitFollowee.text =
            Utils.getUnit(userDetails?.meta?.aggregations?.followees ?: 0)
        val follow = userDetails?.meta?.userActions?.find { it.action == UserAction.FOLLOW.action }
        if (follow != null) {
            isFollowUser = follow.actionValue ?: 0.0
            if (follow.actionValue == 1.0) {
                binding.layoutFollow.isSelected = true
                binding.txtFollow.isSelected = true
                binding.txtFollow.text = getString(R.string.following)
            } else {
                binding.txtFollow.text = getString(R.string.follow)
                binding.layoutFollow.isSelected = false
                binding.txtFollow.isSelected = false
            }
        } else {
            binding.txtFollow.text = getString(R.string.follow)
            binding.layoutFollow.isSelected = false
            binding.txtFollow.isSelected = false
        }
    }

    private fun isMe() =
        (userId == PrefUtils.userDetails?.id || userName == PrefUtils.userDetails?.userName)

    fun handleBackPress() {
        when {
            childFragmentManager.backStackEntryCount > 0 -> {
                childFragmentManager.popBackStack()
            }
            else -> {
                (ctx as HomeActivity).openHome()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        if (event.eventCode == Constants.PROFILE_EDITED && isMe()) {
            userDetails = event.data as User
            setUserDetails()
        } else if (event.eventCode == Constants.FOLLOW_USER) {
            updateFollowUi(1.0)
        } else if (event.eventCode == Constants.UNFOLLOW_USER) {
            updateFollowUi(0.0)
        } else if (event.eventCode == FOLLOW_USER_PROFILE) {
            if (isSamePage) {
                isSamePage = false
            } else if (!isMe()) {
                updateStatsForOtherProfile(1.0)
            } else {
                updateStatsForSelfProfile(1.0)
            }
        } else if (event.eventCode == UNFOLLOW_USER_PROFILE) {
            if (isSamePage) {
                isSamePage = false
            } else if (!isMe()) {
                updateStatsForOtherProfile(0.0)
            } else {
                updateStatsForSelfProfile(0.0)
            }
        } else if(event.eventCode == EPISODE_VIEW_PLAY_CLICK) {
            val content = event.data as Content

            if (!PrefUtils.isLoggedIn) {
                (context?.unwrap() as HomeActivity).handleUserNotLoggedIn()
            } else {
                ClickHandler.openPurchaseDialog(childFragmentManager, content, "Profile_Page")
            }
        }
    }

    private fun updateStatsForSelfProfile(actionValue: Double) {
        val follow = userDetails?.meta?.userActions?.find {
            it.action.equals(
                UserAction.FOLLOW.action,
                true
            )
        }
        if (actionValue == 1.0) {
            if (follow != null) {
                follow.actionValue = 1.0
                val index = userDetails?.meta?.userActions?.indexOf(follow) ?: 0
                userDetails?.meta?.userActions?.set(index, follow)
            } else {
                val mediaAction = MediaAction(
                    UserAction.FOLLOW.action,
                    1.0,
                    userDetails?.id,
                    ENTITY_TYPE_USER,
                    PrefUtils.userDetails?.id.toString(),
                    ENTITY_TYPE_USER,
                    null
                )
                mediaAction.action = UserAction.FOLLOW.action
                mediaAction.actionValue = 1.0
                if (userDetails?.meta == null) {
                    userDetails?.meta = Community()

                }
                if (userDetails?.meta?.userActions.isNullOrEmpty()) {
                    userDetails?.meta?.userActions = ArrayList()
                }
                userDetails?.meta?.userActions?.add(mediaAction)
            }
            val followees = (userDetails?.meta?.aggregations?.followees ?: 0) + 1
            userDetails?.meta?.aggregations?.followees = followees
            binding.txtFollowe.text = Utils.getCount(followees)
            binding.txtUnitFollowee.text = Utils.getUnit(followees)

        } else {
            if (follow != null) {
                follow.actionValue = 0.0
                val index = userDetails?.meta?.userActions?.indexOf(follow) ?: 0
                userDetails?.meta?.userActions?.set(index, follow)
            } else {
                val mediaAction = MediaAction(
                    UserAction.FOLLOW.action,
                    0.0,
                    userDetails?.id,
                    "user",
                    PrefUtils.userDetails?.id.toString(),
                    "user",
                    null
                )
                mediaAction.action = UserAction.FOLLOW.action
                mediaAction.actionValue = 0.0
                if (userDetails?.meta == null) {
                    userDetails?.meta = Community()

                }
                if (userDetails?.meta?.userActions.isNullOrEmpty()) {
                    userDetails?.meta?.userActions = ArrayList()
                }
                userDetails?.meta?.userActions?.add(mediaAction)
            }
            val followees = (userDetails?.meta?.aggregations?.followees ?: 0) - 1
            userDetails?.meta?.aggregations?.followees = followees
            binding.txtFollowe.text = Utils.getCount(followees)
            binding.txtUnitFollowee.text = Utils.getUnit(followees)
        }
    }

    private fun updateStatsForOtherProfile(actionValue: Double) {
        isFollowUser = actionValue

        if (actionValue == 1.0) {
            binding.layoutFollow.isSelected = true
            binding.txtFollow.isSelected = true
            binding.txtFollow.text = getString(R.string.following)
            val followers = (userDetails?.meta?.aggregations?.followers ?: 0).plus(1)
            binding.txtFollowers.text = Utils.abbrNumberFormat(followers, false)
            binding.txtFollowerUnit.text = Utils.getUnit(followers)
        } else {
            binding.txtFollow.text = getString(R.string.follow)
            binding.layoutFollow.isSelected = false
            binding.txtFollow.isSelected = false
            val followers = (userDetails?.meta?.aggregations?.followers ?: 0).minus(1)
            binding.txtFollowers.text = Utils.abbrNumberFormat(followers, false)
            binding.txtFollowerUnit.text = Utils.getUnit(followers)
        }
    }

    private fun updateFollowUi(actionValue: Double) {
        if (actionValue == 0.0) {
            //unfollow
            updateStatsForSelfProfile(0.0)
        } else {
            //follow
            updateStatsForSelfProfile(1.0)
        }
    }

    private fun updateTabText(tab: TabLayout.Tab?) {
        val views = arrayListOf<View>()
        tab?.view?.findViewsWithText(views, tab.text, View.FIND_VIEWS_WITH_TEXT)
        views.forEach { view ->
            if (view is TextView) {
                if (tab?.isSelected == true) {
                    view.typeface = Typeface.create(view.typeface, Typeface.BOLD)
                } else {
                    view.typeface = Typeface.create(view.typeface, Typeface.NORMAL)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event: LoginChangeEvent) {
        binding.errorView.showLoading()

        if (userId.isEmpty() && !PrefUtils.userDetails?.id.isNullOrEmpty()) {
            userId = PrefUtils.userDetails?.id.toString()
        }

        checkSelf()

        getData()
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }

    private fun openFragment(fragment: Fragment?, clazz: String? = null, tag: String? = null) {
        if (fragment != null) {
            childFragmentManager.beginTransaction()
                .add(R.id.homeContainer, fragment, tag).apply {
                    if (clazz != null) {
                        addToBackStack(clazz)
                    }
                    commitAllowingStateLoss()
                }
        }
    }

    private fun handleToolbarTitleVisibility(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR) {
            if (!mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.title,
                    VISIBLE
                )
                mIsTheTitleVisible = true
            }
        } else {
            if (mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.title,
                    View.INVISIBLE
                )
                mIsTheTitleVisible = false
            }
        }
    }

    private fun startAlphaAnimation(v: View, visibility: Int) {
        val alphaAnimation =
            if (visibility == VISIBLE) AlphaAnimation(0f, 1f) else AlphaAnimation(1f, 0f)
        alphaAnimation.duration = ALPHA_ANIMATIONS_DURATION.toLong()
        alphaAnimation.fillAfter = true
        v.startAnimation(alphaAnimation)
    }

    private fun getFormattedMinutes(seconds: Long): Pair<String, String> {
        if (seconds < 3600) {
            val minutes = seconds / 60L
            return Pair(minutes.toString(), "mnt")
        }

        val hours = seconds / 3600L
        if (hours.toString().length <= 5) {
            return Pair(hours.toString(), "jam")
        }

        val months = (TimeUnit.SECONDS.toDays(seconds) / 30.417).toInt()
        return if (months > 0) {
            Pair(months.toString(), "bln")
        } else {
            Pair("0", "mnt")
        }
    }

    private fun handleAddCreation(tab: TabLayout.Tab?) {
        val creationTab = binding.tabLayout.getTabAt(1)
        if (showCreatorStudioButton && isMe() && isCreativesAvailable && tab?.text == creationTab?.text) {
            binding.fabCreateKarya.visibility = VISIBLE
        } else {
            binding.fabCreateKarya.visibility = GONE
        }
    }

    private fun visibilityOfWalletUI(visibility: Int) {
        binding.coins.visibility = visibility
        binding.addCoins.visibility = visibility
        binding.diamonds.visibility = visibility
    }

    override fun onRefresh() {
        getData(true)
    }
}