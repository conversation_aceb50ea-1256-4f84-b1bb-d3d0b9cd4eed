package noice.app.modules.profile.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import noice.app.databinding.FragmentNotificationSettingsBinding
import noice.app.modules.notification.viewmodel.NotificationViewModel
import noice.app.modules.profile.model.NotificationPreferenceRequest
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PrefUtils
import noice.app.utils.isTrue

class NotificationSettings : Fragment() {

    private lateinit var binding : FragmentNotificationSettingsBinding
    private lateinit var viewModel : NotificationViewModel
    private lateinit var ctx: Context

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentNotificationSettingsBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(this)[NotificationViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.errorView.showLoading()

        getNotificationTypes()

        if (PrefUtils.isLoggedIn)
            getNotificationPreferences()

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getNotificationTypes()
            getNotificationPreferences()
        }
    }

    private fun getNotificationTypes() {
        viewModel.getNotificationTypes().observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                it.data.forEach { preference ->
                    preference.type?.let { notificationType ->
                        if (preference.isActive == true) {
                            setActive(notificationType)
                        }
                    }
                }
                binding.errorView.hide()
            } else {
                binding.errorView.showError(
                    it?.message,
                    type = "Notification_Settings",
                    errorName = it?.message
                )
            }
        }
    }

    private fun getNotificationPreferences() {
        viewModel.getNotificationPreferences().observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.preferences.isNullOrEmpty()) {
                it.data?.preferences?.forEach { preference ->
                    preference.notificationType?.let { notificationType ->
                        setStatus(notificationType, preference.isEnabled ?: true)
                    }
                }
            }

            setListeners()
        }
    }

    private fun setActive(notificationType: String) {
        when (notificationType) {
            "newContentAdded" -> {
                binding.contentToBeFollowedLayout.visibility = View.VISIBLE
                binding.contentLabel.visibility = View.VISIBLE
            }
            "radioProgramLive" -> {
                binding.radioFavLiveLayout.visibility = View.VISIBLE
                binding.contentLabel.visibility = View.VISIBLE
            }
            "contentRecommendation" -> {
                binding.contentSuggestedLayout.visibility = View.VISIBLE
                binding.contentLabel.visibility = View.VISIBLE
            }
            "playlistUpdate" -> {
                binding.updateFromPlaylistLayout.visibility = View.VISIBLE
                binding.contentLabel.visibility = View.VISIBLE
            }
            "liveNotification" -> {
                binding.updateFromNoiceLiveLayout.visibility = View.VISIBLE
                binding.contentLabel.visibility = View.VISIBLE
            }
            "userActions" -> {
                binding.yourAccountActivityLayout.visibility = View.VISIBLE
                binding.socialLabel.visibility = View.VISIBLE
            }
            "followeeActions" -> {
                binding.friendActivityLayout.visibility = View.VISIBLE
                binding.socialLabel.visibility = View.VISIBLE
            }
            "appUpdate" -> {
                binding.latestApplicationVersionLayout.visibility = View.VISIBLE
                binding.noiceApplication.visibility = View.VISIBLE
            }
            "generalInformation" -> {
                binding.generalInformationLayout.visibility = View.VISIBLE
                binding.noiceApplication.visibility = View.VISIBLE
            }
            "creatorNotification" -> {
                if (PrefUtils.userDetails?.creatorProfile?.isActive.isTrue()) {
                    binding.creatorCommentLabel.visibility = View.VISIBLE
                    binding.creatorNotificationLayout.visibility = View.VISIBLE
                }
            }
        }

        /* This method runs for all the available notification type of a user. We don't get if setting
        * is enabled or disabled by the user here, so we pass "on" by default. */
        MoEngageAnalytics.setAttributes(ctx, notificationType, "on")
    }

    private fun setStatus(notificationType: String, isEnabled: Boolean) {
        when (notificationType) {
            "newContentAdded" -> {
                binding.contentToBeFollowed.isChecked = isEnabled
            }
            "radioProgramLive" -> {
                binding.radioFavLive.isChecked = isEnabled
            }
            "contentRecommendation" -> {
                binding.contentSuggested.isChecked = isEnabled
            }
            "playlistUpdate" -> {
                binding.updateFromPlaylist.isChecked = isEnabled
            }
            "liveNotification" -> {
                binding.updateFromNoiceLive.isChecked = isEnabled
            }
            "userActions" -> {
                binding.yourAccountActivity.isChecked = isEnabled
            }
            "followeeActions" -> {
                binding.friendActivity.isChecked = isEnabled
            }
            "appUpdate" -> {
                binding.latestApplicationVersion.isChecked = isEnabled
            }
            "generalInformation" -> {
                binding.generalInformation.isChecked = isEnabled
            }
            "creatorNotification" -> {
                binding.switchUpdateCreatorComment.isChecked = isEnabled
            }
        }

        /* Here we get the status of each setting available to a user so here we update the actual value
        * on MoEngage. It returns only turned off setting's value and will run only for those items. */
        MoEngageAnalytics.setAttributes(ctx, notificationType, if (isEnabled) "on" else "off")
    }

    private fun setListeners() {
        binding.contentToBeFollowed.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "newContentAdded", isChecked
            ))
        }
        binding.radioFavLive.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "radioProgramLive", isChecked
            ))
        }
        binding.contentSuggested.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "contentRecommendation", isChecked
            ))
        }
        binding.updateFromPlaylist.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "playlistUpdate", isChecked
            ))
        }
        binding.updateFromNoiceLive.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "liveNotification", isChecked
            ))
        }
        binding.yourAccountActivity.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "userActions", isChecked
            ))
        }
        binding.friendActivity.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "followeeActions", isChecked
            ))
        }
        binding.latestApplicationVersion.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "appUpdate", isChecked
            ))
        }
        binding.generalInformation.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "generalInformation", isChecked
            ))
        }
        binding.switchUpdateCreatorComment.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationPreference(NotificationPreferenceRequest(
                "creatorNotification", isChecked
            ))
        }
    }

    private fun updateNotificationPreference(request: NotificationPreferenceRequest) {

        val event = if (request.isEnabled) {
            "turn_notification_on"
        } else {
            "turn_notification_off"
        }

        AnalyticsUtil.sendEvent(event, Bundle().apply {
            putString("notificationType", request.notificationType)
        })

        MoEngageAnalytics.setAttributes(ctx, request.notificationType, if (request.isEnabled) "on" else "off")

        viewModel.updateNotificationPreference(request).observe(viewLifecycleOwner) {

        }
    }
}