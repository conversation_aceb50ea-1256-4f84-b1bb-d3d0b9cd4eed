package noice.app.modules.notification.adpater

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.model.ExtraData
import noice.app.modules.notification.model.NoiceNotification
import noice.app.views.NotificationView

class NotificationAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<NoiceNotification?>,
    private val extraData: ExtraData?,
    listener: LoadMoreAdapterListener?
) : LoadMoreAdapter<NoiceNotification>(
    recyclerView, dataSet, listener
) {
    private lateinit var notificationView: NotificationView
    var showSelectView = false

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        notificationView = NotificationView(parent.context)
        return notificationView.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        dataSet[position]?.let { data ->
            notificationView.viewHolder = holder as NotificationView.ViewHolder
            notificationView.setData(data, showSelectView)
            notificationView.setExtraData(extraData)
        }
    }

    fun showSelectView(showSelectView : Boolean) {
        this.showSelectView = showSelectView
        notifyDataSetChanged()
    }
}