package noice.app.modules.notification.model

data class NoiceNotification(
    val userId : String?,
    val notificationId : String?,
    val isRead : Boolean?,
    val data : NotificationData?,
    var createdAt : String?,
    var isSelected : Boolean
) {
    constructor(notificationId: String) : this(null, notificationId, null, null, null, false)

    override fun equals(other: Any?): Boolean {
        if (other is NoiceNotification) {
            return notificationId == other.notificationId
        }
        return false
    }

    override fun hashCode(): Int {
        return notificationId?.hashCode() ?: 0
    }
}
