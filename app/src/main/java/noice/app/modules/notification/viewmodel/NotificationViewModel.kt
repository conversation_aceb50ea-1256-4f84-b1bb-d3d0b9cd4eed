package noice.app.modules.notification.viewmodel

import androidx.lifecycle.ViewModel
import noice.app.modules.notification.model.NotificationRequest
import noice.app.modules.profile.model.NotificationPreferenceRequest
import noice.app.modules.profile.repository.NotificationApiRepository

class NotificationViewModel : ViewModel() {

    private val repository = NotificationApiRepository()

    fun getNotificationPreferences() = repository.getNotificationPreferences()

    fun getNotificationTypes() = repository.getNotificationTypes()

    fun updateNotificationPreference(request: NotificationPreferenceRequest) = repository.updateNotificationPreference(request)

    fun getNotifications(map: HashMap<String, String>, cachingConstant : String?) = repository.getNotifications(map, cachingConstant)

    fun markAllRead(request : NotificationRequest) = repository.markAllRead(request)

    fun deleteNotifications(request: NotificationRequest) = repository.deleteNotifications(request)
}