package noice.app.modules.notification.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.NotificationFragmentBinding
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.notification.adpater.NotificationAdapter
import noice.app.modules.notification.model.NoiceNotification
import noice.app.modules.notification.model.NotificationRequest
import noice.app.modules.notification.viewmodel.NotificationViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Utils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class NotificationFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        fun newInstance() = NotificationFragment()
    }
    private lateinit var ctx: Context
    private lateinit var binding: NotificationFragmentBinding
    private lateinit var viewModel: NotificationViewModel
    private lateinit var notificationAdapter : NotificationAdapter
    private var notifications = ArrayList<NoiceNotification?>()
    private var page = 1
    private var isMarkedRead = false
    private val extraData = ExtraData(source = "Notification Center")//For any notification click from notification center would have source as "Notification Center"

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = NotificationFragmentBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(this)[NotificationViewModel::class.java]
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sendAnalyticsEvent()
        initViews()

        binding.errorView.showLoading()

        getNotifications()
    }

    private fun initViews() {
        notificationAdapter = NotificationAdapter(binding.notificationRecycler, notifications, extraData, this)
        binding.notificationRecycler.adapter = notificationAdapter

        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getNotifications()
        }

        binding.toolbar.setRightTextListener {
            if (notificationAdapter.showSelectView) {
                val selected = notifications.find {
                    it?.isSelected == true
                }
                if (selected != null) {
                    NoiceAlertDialog.Builder()
                        .setTitle(getString(R.string.delete_notification))
                        .setMessage(getString(R.string.delete_notification_msg))
                        .setPositiveButtonText(getString(R.string.yes))
                        .setListener(object : OnClickInterface<Boolean> {
                            override fun dataClicked(data: Boolean, eventId: Int) {
                                if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                                    deleteNotifications()
                                }
                            }
                        }).show(childFragmentManager)
                }
            } else {
                binding.toolbar.toolbarSecondaryVisibility(View.VISIBLE)
                notificationAdapter.showSelectView(true)
            }
        }

        binding.toolbar.setSecondaryTextListener {
            if (!isDeleteAll()) {
                binding.toolbar.setSecondaryButtonText(getString(R.string.de_select_all))
                notifications.forEach {
                    it?.isSelected = true
                }
            } else {
                binding.toolbar.setSecondaryButtonText(getString(R.string.select_all))
                notifications.forEach {
                    it?.isSelected = false
                }
            }
            notificationAdapter.notifyDataSetChanged()
        }
    }

    private fun getNotifications(loadMore : Boolean = false) {

        if (!loadMore) {
            page = 1
        }

        val map = HashMap<String, String>()
        map["page"] = page.toString()
        map["limit"] = "10"

        viewModel.getNotifications(map, null).observe(viewLifecycleOwner) {

            when(it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleNotificationResponse(it, loadMore)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleNotificationResponse(it, loadMore)
                    } else if (notifications.isEmpty() || !loadMore) {
                        showEmptyView()
                    } else {
                        notificationAdapter.isLoadMoreEnabled(false)
                        hideLoading()
                    }
                }
                else -> {
                    showError(it?.message)
                }
            }
        }
    }

    private fun handleNotificationResponse(
        resource: Resource<BaseModel<List<NoiceNotification>>>?,
        loadMore: Boolean
    ) {
        if (isDeleteAll()) {
            resource?.data?.data?.forEach { notification ->
                notification.isSelected = true
            }
        }

        if (loadMore) {
            notificationAdapter.addMore(ArrayList(resource?.data?.data ?: ArrayList()))
        } else {
            notificationAdapter.addNewList(ArrayList(resource?.data?.data ?: ArrayList()))
        }

        markAllRead()

        hideLoading()

        binding.toolbar.rightTextVisibility(View.VISIBLE)
    }

    private fun markAllRead() {
        if (isMarkedRead)
            return

        isMarkedRead = true

        viewModel.markAllRead(NotificationRequest(ArrayList(), true)).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                EventBus.getDefault().post(EventMessage(null, Constants.NOTIFICATION_READ))
            } else {
                isMarkedRead = false
            }
        }
    }

    private fun deleteNotifications() {

        binding.errorView.showLoading()

        val ids = ArrayList<String>()

        val request = if (isDeleteAll()) {
            NotificationRequest(ArrayList())
        } else {
            notifications.filter {
                it?.isSelected == true
            }.map {
                it?.notificationId ?: ""
            }.let {
                ids.addAll(it)
                NotificationRequest(it)
            }
        }

        viewModel.deleteNotifications(request).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {

                Utils.showSnackBar(binding, getString(R.string.delete_successful))

                if (isDeleteAll()) {
                    notifications.clear()
                } else {
                    ids.forEach { notificationId ->
                        notifications.remove(NoiceNotification(notificationId))
                    }
                }
                notificationAdapter.showSelectView(false)
                binding.toolbar.toolbarSecondaryVisibility(View.GONE)

                if (isDeleteAll()) {
                    showEmptyView()
                } else {
                    hideLoading()
                }

                AnalyticsUtil.sendEvent("notifications_cleared")
            } else {
                hideLoading()
            }
        }
    }

    private fun isDeleteAll() = binding.toolbar.getSecondaryButtonText() == getString(R.string.de_select_all)

    private fun hideLoading() {
        binding.emptyView.visibility = View.GONE
        binding.errorView.hide()
    }

    private fun showEmptyView() {
        binding.errorView.hide()
        binding.emptyView.visibility = View.VISIBLE
        binding.toolbar.rightTextVisibility(View.GONE)
    }

    private fun showError(message : String?) {
        if (notifications.isEmpty()) {
            notificationAdapter.isLoadMoreEnabled(false)
            binding.emptyView.visibility = View.GONE
            binding.errorView.showError(message, type = "Notification_Page", errorName = message)
        } else {
            hideLoading()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event : EventMessage) {
        if (event.eventCode == Constants.NOTIFICATION_SELECTED) {

            val unSelected = notifications.find {
                it?.isSelected == false
            }

            if (unSelected == null) {
                binding.toolbar.setSecondaryButtonText(getString(R.string.de_select_all))
            } else {
                binding.toolbar.setSecondaryButtonText(getString(R.string.select_all))
            }

            val index = notifications.indexOf(NoiceNotification(event.data as String))
            if (index != -1) {
                notificationAdapter.notifyItemChanged(index)
            }
        }
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > 9) {
            getNotifications(true)
        } else {
            notificationAdapter.isLoadMoreEnabled(false)
        }
    }

    private fun sendAnalyticsEvent() {
        AnalyticsUtil.sendEvent("open_notification_center", Bundle().apply {
            putString("source", "Notification")
        })
    }
}