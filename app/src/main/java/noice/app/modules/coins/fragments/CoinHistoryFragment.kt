package noice.app.modules.coins.fragments

import android.animation.ValueAnimator
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.WalletController
import noice.app.databinding.FragmentCoinHistoryBinding
import noice.app.listner.OnClickInterface
import noice.app.model.eventbus.EventMessage
import noice.app.modules.coins.adapter.CoinHistoryAdapter
import noice.app.modules.coins.activity.CoinTopUpActivity
import noice.app.modules.coins.model.CoinTransactionHistory
import noice.app.modules.coins.viewmodel.CoinHistoryViewModel
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.utils.Utils.formatNumberID
import noice.app.views.tooltip.NoiceSpotlightDialog
import noice.app.views.tooltip.TooltipBuilder
import noice.app.views.tooltip.TooltipObject
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class CoinHistoryFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener, SwipeRefreshLayout.OnRefreshListener {

    companion object {
        private const val TOP_UP_FLOW = "TOP_UP_FLOW"

        fun newInstance(topUpFlow : Boolean) = CoinHistoryFragment().apply {
            arguments = Bundle().apply {
                putBoolean(TOP_UP_FLOW, topUpFlow)
            }
        }
    }

    private lateinit var ctx : Context
    private lateinit var binding : FragmentCoinHistoryBinding
    private val viewModel: CoinHistoryViewModel by viewModels()
    private var coinHistoryAdapter : CoinHistoryAdapter? = null
    private var coinHistory = arrayListOf<CoinTransactionHistory?>()
    private var page = 1
    private val limit = 20
    private var topUpFlow = false
    private var tooltipDialog : NoiceSpotlightDialog? = null
    private var negativeBalanceDialog : NoiceSpotlightDialog? = null
    private var checkedForOnBoarding = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        topUpFlow = arguments?.getBoolean(TOP_UP_FLOW, false) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCoinHistoryBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        checkTopUpFlow(false)

        getData()
    }

    private fun checkForOnBoarding() {
        checkedForOnBoarding = true

        if (ExperimentUtils.isCoinFeatureEnabled() && !PrefUtils.coinsIntroShown) {
            initOnBoarding()
        }
    }

    private fun getData() {

        binding.errorView.showLoading()

        getCoinWalletHistory()
    }

    private fun initViews() {
        binding.apply {
            pullToRefresh.setOnRefreshListener(this@CoinHistoryFragment)

            coinHistoryAdapter = CoinHistoryAdapter(
                coinHistoryRecycler,
                coinHistory,
                this@CoinHistoryFragment,
                viewModel
            ) { transaction ->
                if (transaction.isHandledTxn) {
                    CoinTransactionDetails.show(childFragmentManager, transaction.id.toString())
                } else {
                    CoinsUpdateDialog.show(childFragmentManager, transaction.id.toString())
                }
            }
            coinHistoryRecycler.adapter = coinHistoryAdapter
            coinHistoryRecycler.addItemDecoration(RecyclerViewMargin(resources.getDimensionPixelSize(R.dimen.dp16)))

            toolbar.setBackClick {
                activity?.onBackPressed()
            }

            toolbar.setRightIconListener {
                CoinInfoDialog.show(childFragmentManager)
            }

            topUpBtn.setOnClickListener {
                handleTopUpClick()
            }

            topUpBtnEmptyView.setOnClickListener {
                handleTopUpClick()
            }
        }

        WalletController.observeWallet(viewLifecycleOwner) { wallet ->
            if (wallet != null && !topUpFlow) {
                updateCoinBalance()
            }
        }
    }

    private fun animateTextView(initialValue: Int, finalValue: Int) {
        val valueAnimator = ValueAnimator.ofInt(initialValue, finalValue)
        valueAnimator.duration = 2000
        valueAnimator.addUpdateListener { animator ->
            binding.coins.text = (animator.animatedValue as Int).formatNumberID()
        }
        valueAnimator.start()
    }

    private fun checkTopUpFlow(refresh : Boolean) {
        if (topUpFlow) {
            topUpFlow = false
            Utils.showSnackBar(binding, getString(R.string.top_up_success))

            if (refresh) {
                getCoinWalletHistory()
            }

            val initialValue = binding.coins.text.toString().replace(".", "").toIntOrNull() ?: 0
            val finalValue = WalletController.totalCoins ?: 0
            animateTextView(initialValue, finalValue)

            binding.coinImg.playAnimation()
        } else {
            updateCoinBalance()
        }
    }

    private fun updateCoinBalance() {
        if ((WalletController.totalCoins ?: 0) < 0) {
            binding.coins.setTextColor(ContextCompat.getColor(ctx, R.color.pinkish_red))
            showNegativeBalanceDialog()
        } else {
            negativeBalanceDialog?.dismiss()
            binding.coins.setTextColor(ContextCompat.getColor(ctx, R.color.white))
        }
        binding.coins.text = WalletController.formattedCoins
    }

    private fun handleTopUpClick() {
        CoinTopUpActivity.start(ctx, "Coin_History_Page")
    }

    private fun getCoinWalletHistory(loadMore : Boolean = false) {

        if (!loadMore) {
            page = 1
        }

        val map = mapOf(
            "page" to page.toString(),
            "limit" to limit.toString()
        )

        viewModel.getCoinWalletHistory(
            "coin_wallet_history",
            map,
            binding.pullToRefresh.isRefreshing
        ).observe(viewLifecycleOwner) {
            when(it?.status) {
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleHistoryResponse(loadMore, it.data?.data)
                    } else if (coinHistory.isEmpty()) {
                        showEmptyView()
                    } else {
                        hideLoader()
                        coinHistoryAdapter?.isLoadMoreEnabled(false)
                    }

                    if (!checkedForOnBoarding) {
                        checkForOnBoarding()
                    }
                }
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleHistoryResponse(loadMore, it.data?.data)

                        if (!checkedForOnBoarding) {
                            checkForOnBoarding()
                        }
                    }
                }
                else -> {
                    if (coinHistory.isEmpty()) {
                        showEmptyView()
                    } else {
                        hideLoader()
                        coinHistoryAdapter?.isLoadMoreEnabled(false)
                    }
                }
            }
        }
    }

    private fun handleHistoryResponse(
        loadMore: Boolean,
        data: List<CoinTransactionHistory>?
    ) {
        if (data.isNullOrEmpty()) {
            return
        }

        coinHistoryAdapter?.addList(loadMore, data)

        binding.emptyView.visibility = View.GONE
        binding.coinHistoryRecycler.visibility = View.VISIBLE
        hideLoader()
    }

    private fun showEmptyView() {
        coinHistoryAdapter?.isLoadMoreEnabled(false)
        binding.emptyView.visibility = View.VISIBLE
        binding.coinHistoryRecycler.visibility = View.GONE

        hideLoader()
    }

    private fun hideLoader() {
        binding.errorView.hide()
        binding.pullToRefresh.isRefreshing = false
    }

    private fun initOnBoarding() {

        binding.coinImgForToolTip.visibility = View.VISIBLE
        binding.coinsHeaderLayout.background = ContextCompat.getDrawable(ctx, R.drawable.gradient_coins_bg)

        tooltipDialog = TooltipBuilder()
            .setListener(object : OnClickInterface<Boolean> {
                override fun dataClicked(data: Boolean, eventId: Int) {
                    binding.coinImgForToolTip.visibility = View.GONE
                    binding.coinsHeaderLayout.background = null
       
                    if (eventId == NoiceAlertDialog.NEGATIVE_BTN_CLICK) {
                        tooltipDialog?.close()
                        PrefUtils.coinsIntroShown = true
                    } else if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                        PrefUtils.coinsIntroShown = data
                    }
                    if (data) {
                        binding.onBoardingLayout.layoutParams.apply {
                            height = MATCH_PARENT
                        }
                    }
                }
            })
            .setPositiveButtonText(ctx.getString(R.string.continue_txt))
            .setNegativeButtonText(ctx.getString(R.string.skip_eng))
            .dialogBg(R.color.black70)
            .hideSkipOnComplete(true)
            .setCustomWidth(ctx.resources.getDimensionPixelSize(R.dimen.dp264))
            .build()

        val tooltips = ArrayList<TooltipObject>()

        tooltips.add(
            TooltipObject(
                binding.coinsHeaderLayout,
                getString(R.string.tooltip_title_coin1),
                tintBackgroundColor = ContextCompat.getColor(ctx, android.R.color.transparent),
                roundedCorner = 20
            )
        )

        tooltips.add(
            TooltipObject(
                binding.topUpBtn,
                getString(R.string.tooltip_title_coin2),
                tintBackgroundColor = ContextCompat.getColor(ctx, android.R.color.transparent),
                roundedCorner = 0
            )
        )

        if (coinHistoryAdapter?.itemCount == 0) {
            binding.onBoardingLayout.layoutParams.apply {
                height = WRAP_CONTENT
            }
        }

        tooltips.add(
            TooltipObject(
                binding.onBoardingLayout,
                getString(R.string.tooltip_title_coin3),
                tintBackgroundColor = ContextCompat.getColor(ctx, android.R.color.transparent),
                nextButtonText = getString(R.string.done),
                roundedCorner = 0,
                gravity = Gravity.CENTER_HORIZONTAL or Gravity.BOTTOM
            )
        )

        tooltipDialog?.show(requireActivity(), childFragmentManager, tooltips)
    }

    private fun showNegativeBalanceDialog() {
        if (negativeBalanceDialog?.isAdded == true) {
            return
        }
        negativeBalanceDialog = TooltipBuilder()
            .dialogBg(android.R.color.transparent)
            .textColorRes(R.color.brown)
            .textSizeRes(R.dimen.sp12)
            .tooltipBackgroundColorRes(R.color.light_red)
            .setNegativeButtonVisibility(View.GONE)
            .setPositiveButtonVisibility(View.GONE)
            .clickable(true)
            .build()

        val toolTip = TooltipObject(
            binding.coins,
            getString(R.string.negative_balance_text),
            tintBackgroundColor = ContextCompat.getColor(ctx, android.R.color.transparent)
        )
        negativeBalanceDialog?.show(requireActivity(), childFragmentManager, arrayListOf(toolTip))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCoinTopUpEvent(event : EventMessage) {
        if (event.eventCode == Constants.COIN_TOP_UP_EVENT) {
            topUpFlow = true
            checkTopUpFlow(true)
        }
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > (limit - 1)) {
            getCoinWalletHistory(true)
        } else {
            coinHistoryAdapter?.isLoadMoreEnabled(false)
        }
    }

    override fun onRefresh() {
        getCoinWalletHistory()

        updateCoinBalance()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        EventBus.getDefault().unregister(this)
    }
}