package noice.app.modules

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import dagger.hilt.android.AndroidEntryPoint
import noice.app.listner.IBasePlayerActivity
import noice.app.model.LoginDialogData
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.onboarding.activity.CompleteUserNameActivity
import noice.app.modules.onboarding.fragments.NewLoginDialog
import noice.app.utils.ExperimentUtils
import noice.app.utils.PrefUtils

@AndroidEntryPoint
abstract class BaseActivity : AppCompatActivity(), IBasePlayerActivity {

    internal val viewModelBase: DashboardViewModel by viewModels()

    private lateinit var fillUserNameLauncher: ActivityResultLauncher<Intent>
    private var fillUserNameLiveData: MutableLiveData<Boolean>? = null
    private var newLoginDialog: NewLoginDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        fillUserNameLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            fillUserNameLiveData?.value = result.resultCode == Activity.RESULT_OK
            fillUserNameLiveData = null
        }
    }

    fun handleUserNameNotFound(): MutableLiveData<Boolean>? {
        fillUserNameLiveData = MutableLiveData()

        val intent = Intent(this, CompleteUserNameActivity::class.java)
        fillUserNameLauncher.launch(intent)

        return fillUserNameLiveData
    }

    override fun handleUserNotLoggedIn(
        id: String?,
        actionName: String?,
        source: String?,
        loginDialogData: LoginDialogData?
    ) {
        dismissLoginDialog()
        val data = loginDialogData ?: ExperimentUtils.getLoginDialogData("")
        newLoginDialog = NewLoginDialog.show(supportFragmentManager, id, actionName, source, data)
    }

    fun checkLoginState(entityType: String, entityId: String, source: String?): Boolean {
        if (PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) {
            handleUserNotLoggedIn(entityId, entityType, source)
            return false
        }
        return true
    }

    fun dismissLoginDialog() {
        if (newLoginDialog?.isAdded == true) {
            newLoginDialog?.dismissAllowingStateLoss()
            newLoginDialog = null
        }
    }
}