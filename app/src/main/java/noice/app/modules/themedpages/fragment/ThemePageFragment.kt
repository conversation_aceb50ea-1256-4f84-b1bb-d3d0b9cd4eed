package noice.app.modules.themedpages.fragment

import android.Manifest
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentThemedBinding
import noice.app.model.ExtraData
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.adapter.HomeInnerAdapter
import noice.app.modules.dashboard.adapter.HomeSegmentAdapter
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.PermissionUtils
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.parcelable

@AndroidEntryPoint
class ThemePageFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        const val SCREEN_NAME_THEMED_PAGE = "ThemePage"
        private const val THEME_PAGE_VALUE = "theme_page_value"
        private const val THEME_PAGE_TITLE = "theme_page_title"
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun newInstance(value: String, title: String? = null, extraData: ExtraData? = null) =
            ThemePageFragment().apply {
                arguments = Bundle().apply {
                    putString(THEME_PAGE_VALUE, value)
                    putString(THEME_PAGE_TITLE, title)
                    putParcelable(EXTRA_DATA, extraData)
                }
            }
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var ctx: Context
    private var _binding: FragmentThemedBinding? = null
    private val binding get() = _binding!!
    private var adapter: HomeSegmentAdapter? = null

    private var segmentList = ArrayList<HomeSegment?>()
    private var themePageValue: String? = null
    private var title: String? = null
    private var offset = 1
    private val limit = 4

    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null

    private var extraData: ExtraData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        themePageValue = arguments?.getString(THEME_PAGE_VALUE)
        title = arguments?.getString(THEME_PAGE_TITLE)
        extraData = arguments?.parcelable(EXTRA_DATA)

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentThemedBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()

        if (title.isNullOrEmpty()) {
            themePageValue?.let { value ->
                getPageName(value)
            }
        }

        getData(false)
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        offset = page + 1
        getData(true)
    }

    private fun initViews() {
        binding.errorView.showLoading(R.layout.shimmer_theme_page)

        binding.toolbar.setToolbarTitle(title ?: "")
        binding.toolbar.setBackClick {
            requireActivity().onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading(R.layout.shimmer_theme_page)
            getData(false)
        }

        setRecycler()

        if (!title.isNullOrEmpty()) {
            AnalyticsBuilder.oldBuilder().apply {
                putAnalyticsKey("theme page name", title ?: "")
                putAnalyticsKey("segment name", extraData?.segmentName ?: "")
                putAnalyticsKey("segment position", extraData?.segmentPosition ?: "")
                putAnalyticsKey("entity position", extraData?.entityPosition ?: "")
            }.also {
                it.send("theme page opened")
            }
        }
    }

    private fun setRecycler() {
        adapter = HomeSegmentAdapter(
            binding.recyclerThemedPages,
            segmentList,
            this,
            SCREEN_NAME_THEMED_PAGE,
            permissionUtils,
        )
        binding.recyclerThemedPages.adapter = adapter

        if (!title.isNullOrEmpty()) {
            adapter?.setScreenTitle(title)
        }
    }

    private fun getData(isLoadMore: Boolean) {
        if (isDetached || isRemoving)
            return

        if (!isLoadMore) {
            offset = 1
        }

        val map = HashMap<String, String>()
        map["page"] = "$themePageValue"
        map["offset"] = offset.toString()
        map["limit"] = limit.toString()

        viewModel.getHomeSegment(map, "").observe(viewLifecycleOwner) {
            it?.data?.data?.filter { segment ->
                if (segment.viewOptions?.viewType == HomeSegmentAdapter.VIEW_TYPE_ADS) {
                    !segment.content.isNullOrEmpty()
                            && (PrefUtils.userDetails?.isSubscribed == false
                            || PrefUtils.userDetails?.isSubscribed == null)
                } else {
                    !segment.content.isNullOrEmpty() ||
                            (segment.viewOptions?.viewType == "livestream" && (PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true)) || segment.viewOptions?.viewType == HomeInnerAdapter.TYPE_MOENGAGE_CARDS
                }
            }?.let { segments ->
                it.data.data = segments
            }

            when (it?.status) {
                ResponseStatus.LOADING -> {
                    /* NO-OP */
                }

                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(isLoadMore, it)
                    } else if (segmentList.isNotEmpty()) {
                        hideLoading()
                        adapter?.isLoadMoreEnabled(false)
                    } else {
                        hideLoading()
                        handleError(it.message)
                    }
                }

                else -> {
                    hideLoading()
                    handleError(it?.message)
                }
            }
        }
    }

    private fun handleSegmentResponse(
        loadMore: Boolean,
        resource: Resource<BaseModel<List<HomeSegment>>>
    ) {
        resource.data?.data?.let { segments ->
            if (loadMore)
                adapter?.addMore(segments)
            else {
                val seg = ArrayList(segments)
                adapter?.addNewList(seg)
            }

            getMoEngageCards()

            hideLoading()
        }
    }

    private fun getPageName(value: String) {
        viewModel.getPageName(value).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                title = it.data?.data?.title
                binding.toolbar.setToolbarTitle(title ?: "")
                adapter?.setScreenTitle(title)

                AnalyticsBuilder.oldBuilder().apply {
                    putAnalyticsKey("theme page name", title ?: "")
                    putAnalyticsKey("segment name", extraData?.segmentName ?: "")
                    putAnalyticsKey("segment position", extraData?.segmentPosition ?: "")
                    putAnalyticsKey("entity position", extraData?.entityPosition ?: "")
                }.also {
                    it.send("theme page opened")
                }
            }
        }
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun handleError(message: String?) {
        if (segmentList.isEmpty()) {
            showError(message)
        } else {
            hideLoading()
        }
    }

    private fun showError(message: String?) {
        adapter?.isLoadMoreEnabled(false)
        binding.errorView.showError(message, type = THEME_PAGE_VALUE, errorName = message)
    }

    private fun getMoEngageCards() {
        lifecycleScope.launch(Dispatchers.Main) {
            val moEngageCardSegments = segmentList.filter { homeSegment ->
                homeSegment?.viewOptions?.viewType == HomeInnerAdapter.TYPE_MOENGAGE_CARDS
            }

            moEngageCardSegments.forEach { homeSegment ->
                val index = segmentList.indexOf(homeSegment)

                Utils.updateMoEngageCardSegment(
                    ctx,
                    homeSegment?.viewOptions?.moengageCategories?.get(0) ?: "",
                    index,
                    segmentList,
                    adapter
                )
            }
        }
    }
}