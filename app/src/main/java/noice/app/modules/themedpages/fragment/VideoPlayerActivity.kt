package noice.app.modules.themedpages.fragment

import android.content.Context
import android.content.pm.ActivityInfo
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.OrientationEventListener
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.lifecycleScope
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.PlayerConstants
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.AbstractYouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.YouTubePlayerFullScreenListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.options.IFramePlayerOptions
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.utils.loadOrCueVideo
import com.pierfrancescosoffritti.androidyoutubeplayer.core.ui.DefaultPlayerUiController
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import noice.app.databinding.ActivityVideoPlayerBinding
import noice.app.utils.AnalyticsBuilder

class VideoPlayerActivity : AppCompatActivity() {

    companion object {
        const val VIDEO_ID = "video_id"
        const val SOURCE = "source"
    }

    private lateinit var ctx: Context
    private lateinit var binding: ActivityVideoPlayerBinding

    private var videoId: String? = null
    private var source: String? = null
    private var previousOrientation: Int = -1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVideoPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        videoId = intent.extras?.getString(VIDEO_ID)
        source = intent.extras?.getString(SOURCE)

        initViews()
        hideSystemUi()
    }

    override fun onDestroy() {
        super.onDestroy()
        binding.youtubePlayerView.release()
        sendEvents("video paused") //sending a pause event when user hits back or click on cross button
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            binding.youtubePlayerView.enterFullScreen()
        } else {
            binding.youtubePlayerView.exitFullScreen()
        }
    }

    private fun initViews() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                    rotatePortrait()
                    showSystemUi()
                } else {
                    finish()
                }
            }
        })

        binding.imageCrossButton.setOnClickListener {
            finish()
        }

        setPlayer()
        setPlayerFullScreenListener()
        setSystemUiVisibilityListener()
        handleRotate()
    }

    private fun setPlayer() {
        lifecycle.addObserver(binding.youtubePlayerView)

        val listener = object : AbstractYouTubePlayerListener() {
            override fun onReady(youTubePlayer: YouTubePlayer) {
                val defaultPlayerUiController =
                    DefaultPlayerUiController(binding.youtubePlayerView, youTubePlayer)
                binding.youtubePlayerView.setCustomPlayerUi(defaultPlayerUiController.rootView)

                youTubePlayer.loadOrCueVideo(
                    lifecycle,
                    videoId ?: "",
                    0f
                )
            }

            override fun onStateChange(
                youTubePlayer: YouTubePlayer,
                state: PlayerConstants.PlayerState
            ) {
                if (state == PlayerConstants.PlayerState.PLAYING) {
                    sendEvents("video played")
                } else if (state == PlayerConstants.PlayerState.PAUSED) {
                    sendEvents("video paused")
                }
            }
        }

        val iFrameOptions = IFramePlayerOptions.Builder()
            .controls(0)
            .ccLoadPolicy(0)//subtitle disabled (can't disable default sub title in video)
            .build()

        binding.youtubePlayerView.initialize(listener, iFrameOptions)
    }

    private fun setPlayerFullScreenListener() {
        binding.youtubePlayerView.addFullScreenListener(object : YouTubePlayerFullScreenListener {
            override fun onYouTubePlayerEnterFullScreen() {
                rotateLandscape()
            }

            override fun onYouTubePlayerExitFullScreen() {
                rotatePortrait()
            }
        })
    }

    private fun hideSystemUi() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.decorView.windowInsetsController?.hide(
                android.view.WindowInsets.Type.statusBars()
            )
        } else {
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
        }
    }

    private fun showSystemUi() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.decorView.windowInsetsController?.show(
                android.view.WindowInsets.Type.statusBars()
            )
        }
    }

    private fun setSystemUiVisibilityListener() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowInsetsController =
                WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

            window.decorView.setOnApplyWindowInsetsListener { view, windowInsets ->
                if (windowInsets.isVisible(WindowInsetsCompat.Type.navigationBars())
                    || windowInsets.isVisible(WindowInsetsCompat.Type.statusBars())
                ) {
                    hideSystemUi()
                } else {
                    showSystemUi()
                }
                view.onApplyWindowInsets(windowInsets)
            }
        } else {
            window.decorView.setOnSystemUiVisibilityChangeListener { visibility ->
                if (visibility and View.SYSTEM_UI_FLAG_FULLSCREEN == 0) {
                    hideSystemUi()
                } else {
                    showSystemUi()
                }
            }
        }
    }

    private fun rotatePortrait() {
        binding.youtubePlayerView.exitFullScreen()
        requestedOrientation = SCREEN_ORIENTATION_PORTRAIT
    }

    private fun rotateLandscape() {
        binding.youtubePlayerView.enterFullScreen()
        requestedOrientation = SCREEN_ORIENTATION_LANDSCAPE
    }

    private fun sendEvents(eventName: String) {
        AnalyticsBuilder.oldBuilder().apply {
            putAnalyticsKey("video ad link", "https://www.youtube.com/embed/${videoId}")
            putAnalyticsKey("source", source)
        }.also {
            it.send(eventName)
        }
    }

    private fun handleRotate() {
        val orientationEventListener: OrientationEventListener =
            object : OrientationEventListener(this) {
                override fun onOrientationChanged(orientation: Int) {
                    val isPortrait =
                        (orientation > 340 || orientation < 20 || orientation in 160..200) &&
                                requestedOrientation == SCREEN_ORIENTATION_PORTRAIT
                    val isLandscape = (orientation in 250..290 || orientation in 70..110) &&
                            requestedOrientation == SCREEN_ORIENTATION_LANDSCAPE

                    if (isPortrait || isLandscape) {
                        lifecycleScope.launch {
                            if (previousOrientation == -1) {
                                previousOrientation = if (isPortrait) 1 else 0
                            }

                            if (isPortrait) {
                                rotatePortrait()
                            } else {
                                rotateLandscape()
                            }
                            delay(700)
                            val currentOrientation = if (isPortrait) 1 else 0
                            if (previousOrientation == currentOrientation) {
                                previousOrientation = -1
                                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR
                            }
                        }
                    }
                }
            }

        val autoRotationOn = Settings.System.getInt(
            contentResolver,
            Settings.System.ACCELEROMETER_ROTATION,
            0
        ) == 1

        if (autoRotationOn) {
            orientationEventListener.enable()
        } else {
            orientationEventListener.disable()
        }
    }
}