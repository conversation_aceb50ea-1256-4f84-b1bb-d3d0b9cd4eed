package noice.app.modules.radio.fragment

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.format.DateFormat
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.firestore.ListenerRegistration
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BuildConfig
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentRadioDetailBinding
import noice.app.enums.Priority
import noice.app.enums.UserAction
import noice.app.exoplayer.BasePlayerActivity
import noice.app.listner.OnClickInterface
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.model.user.User
import noice.app.modules.ads.GaManager
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.indexpages.model.LihatSemuaEvent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.adapter.ArtistAdapter
import noice.app.modules.podcast.adapter.ArtistAdapter.Companion.ARTIST_VIEW
import noice.app.modules.podcast.adapter.GenreAdapter
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.modules.radio.NoiceRadioMenu
import noice.app.modules.radio.adapter.RadioPodcastAdapter
import noice.app.modules.radio.adapter.RadioScheduleAdapter
import noice.app.observers.GlobalObservers
import noice.app.player.managers.QueueManager
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.RadioPlayRequest
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CATALOG
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CONTENT
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.utils.Constants.Companion.EVENT_RADIO_PAGE_OPENED
import noice.app.utils.DateUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.unwrap
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Calendar
import java.util.Date
import kotlin.math.abs

@AndroidEntryPoint
class RadioDetailFragment : Fragment(), AppBarLayout.OnOffsetChangedListener {

    companion object {
        private const val PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR = 0.6f
        private const val PERCENTAGE_TO_HIDE_TITLE_DETAILS = 0.3f
        private const val ALPHA_ANIMATIONS_DURATION = 600
        private const val RADIO_ID = "RADIO_ID"
        private const val PLAY_CONTENT_DIRECTLY = "PLAY_CONTENT_DIRECTLY"
        private const val SOURCE_PAGE = "SOURCE_PAGE"
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun newInstance(radioId: String, sourcePage:String, playContentDirectly: Boolean = false, extraData: ExtraData? = null) = RadioDetailFragment().apply {
            val bundle = Bundle()
            bundle.putString(RADIO_ID, radioId)
            bundle.putString(SOURCE_PAGE, sourcePage)
            bundle.putBoolean(PLAY_CONTENT_DIRECTLY, playContentDirectly)
            bundle.putParcelable(EXTRA_DATA, extraData)
            arguments = bundle
        }
    }

    private val viewModel: ChannelPodcastViewModel by viewModels()
    private lateinit var binding: FragmentRadioDetailBinding
    private lateinit var mContext: Context
    private var radioId: String? = null
    private var radio: Channel? = null
    private var isFollowChannel = 0.0
    private var artistAdapter: ArtistAdapter? = null
    private var artistList = ArrayList<User>()
    private var scheduleAdapter: RadioScheduleAdapter?=null
    private var mIsTheTitleVisible = false
    private var mIsTheTitleContainerVisible = true
    private var liveContent: Content? = null
    private var page = 1
    private var limit = 8
    private var isSamePage = false
    var endTime:String? = null
    private var listenerRegistration: ListenerRegistration? = null
    private var chatRegistration: ListenerRegistration? = null
    private var sourcePage:String?=null
    private var playContentDirectly = false
    private var extraData: ExtraData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        radioId = arguments?.getString(RADIO_ID)
        sourcePage = arguments?.getString(SOURCE_PAGE)
        playContentDirectly = arguments?.getBoolean(PLAY_CONTENT_DIRECTLY) ?: false
        extraData = arguments?.parcelable(EXTRA_DATA)

        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        binding = FragmentRadioDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()

        getRadioDetail()
    }

    override fun onStart() {
        super.onStart()

        MoEngageAnalytics.setOrResetInAppContext(setOf("catalog radio"))
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(mContext)
        MoEInAppHelper.getInstance().showNudge(mContext)
    }

    private fun initView(){
        binding.rvSchedule.setHeaderText(mContext.getString(R.string.setelah_ini))

        binding.rvSchedule.viewHolder.recyclerView.apply {
            val orientation = RecyclerView.VERTICAL
            layoutManager = LinearLayoutManager(mContext, orientation, false)
            setHasFixedSize(true)
        }
        binding.rvArtist.viewHolder.recyclerView.apply {
            val orientation = RecyclerView.HORIZONTAL
            layoutManager = LinearLayoutManager(mContext, orientation, false)
            setHasFixedSize(true)
            addItemDecoration(RecyclerViewMargin(mContext.resources.getDimensionPixelSize(R.dimen.dp8)))
        }

        binding.rvGenre.addItemDecoration(
                RecyclerViewMargin(
                        mContext.resources.getDimensionPixelSize(
                                R.dimen.dp8
                        )
                )
        )

        binding.imgSearch.setOnClickListener {
            AnalyticsUtil.sendEventForOpenScreen("","search_bar_clicked",AnalyticsUtil.radio)
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Radio_channel_Page"))
        }

        binding.rvPodcast.showSeeAll(true)
        binding.rvPodcast.setOnRightButtonClickListener {
            EventBus.getDefault().post(
                    OpenIndexEvent(
                            OpenIndexEvent.OPEN_LIHAT_SEMUA,
                            LihatSemuaEvent(
                                    Utils.getCatalogContract(radio?.data)?.playlistId ?: "",
                                    radio?.title.toString(),
                                    "radioPodcast",
                                    "Radio_Channel_Podcast",
                                    true,parrentId = radio?.id,source = "Radio_channel_Page"
                            )
                    )
            )
        }
        binding.rvPodcast.viewHolder.recyclerView.apply {
            val orientation = RecyclerView.HORIZONTAL
            layoutManager = GridLayoutManager(mContext, 2, orientation, false)
            setHasFixedSize(true)
            addItemDecoration(RecyclerViewMargin(mContext.resources.getDimensionPixelSize(R.dimen.dp8)))
        }
        scheduleAdapter = RadioScheduleAdapter(ArrayList(), mContext)
        binding.rvSchedule.viewHolder.recyclerView.adapter = scheduleAdapter
        binding.layoutShare.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext.unwrap() as BasePlayerActivity)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            if (radio!=null){
                val dialog =  ShareDialog.newInstance(radio, true, endTime)
                dialog.show(childFragmentManager, "share")
            }
        }

        binding.errorView.setOnReturnClick { getRadioDetail() }
        binding.toolbar.setNavigationOnClickListener {
            (mContext.unwrap() as AppCompatActivity).onBackPressed()
        }
        binding.threeDotMenu.setOnClickListener {

            val bundle = Bundle()
            bundle.putBoolean("fromDetail", true)
            if (liveContent!=null){
                bundle.putString("endTime", liveContent?.contentSchedule?.endTime)
            }

            val data = ExoNotificationData(
                radio?.id,
                "",
                radio?.title,
                radio?.description ?: "",
                radio?.imageMeta?.size500,
                ENTITY_TYPE_CATALOG,
                "radio_detail",
                0,
                0,
                priority = 1,
                description = "",
                entitySubType = ENTITY_TYPE_RADIO,
                catalogId = radio?.id,
                catalogTitle = radio?.title,
                isRadio = true,
                source = radio?.source,
                displayAds = radio?.displayAds,
                contentNumber = 0,
                isPremium = null,
                hasPurchased = null,
                isDownloadable = null,
                videoUrl = null,
                isVideoPremium = null,
                showVideo = null,
                hasVideoAccess = null,
                prerollEnabled = radio?.prerollEnabled,
                midrollEnabled = radio?.midrollEnabled,
                isSubscriptionAvailable = false,
                displayAdsEnabled = radio?.displayAdsEnabled,
                earlyAccessFinishDate = null
            )
            bundle.putParcelable(NoiceRadioMenu.RADIO, data)
            val noiceMenu =
                NoiceRadioMenu()
            noiceMenu.arguments = bundle
            noiceMenu.show(
                    childFragmentManager,
                    "noiceMenu"
            )
        }

        binding.txtLihatSemua.setOnClickListener {
            val fragment = SchedulePagerFragment.newInstance(
                    radio,
                    radio?.id.toString(),
                    true,
                    endTime
            )
            parentFragment?.childFragmentManager?.beginTransaction()
                ?.add(R.id.homeContainer, fragment)
                ?.addToBackStack(SchedulePagerFragment::class.java.simpleName)
                ?.commit()
        }
        binding.mediaButton.setOnClickListener {
            if (DataController.isAdPlaying) {
                Utils.showSnackBar(mContext, getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            if (BuildConfig.VERSION_CODE < (PrefUtils.appConfig?.app_update_config?.android?.liveRadioUpdateVersionCode ?: 0)){
                EventBus.getDefault().post(EventMessage(null,Constants.FORCE_UPDATE))
                return@setOnClickListener
            }

            if (DataController.playingEntityId == radio?.id) {
                if (mContext.getPlayerActivity()?.isPlayWhenReady() == true) {
                    RadioPlayRequest.Builder()
                        .playWhenReady(mContext, false)
                } else {
                    RadioPlayRequest.Builder()
                        .playWhenReady(mContext, true)
                }
                return@setOnClickListener
            }

            if (!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(mContext, getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            val data = ExoNotificationData(
                radio?.id,
                Utils.getCatalogContract(radio?.data)?.wowzaUrl ?: "",
                radio?.description,
                radio?.title,
                radio?.imageMeta?.size500,
                ENTITY_TYPE_CONTENT,
                "Radio Detail",
                0L,
                0L,
                priority = Priority.PLAYING.type,
                description = radio?.description.toString(),
                entitySubType = ENTITY_TYPE_RADIO,
                isRadio = true,
                catalogId = radio?.id,
                catalogTitle = radio?.title,
                artists = commonSaperatedString(radio?.users ?: ArrayList()),
                source = radio?.source,
                displayAds = radio?.displayAds,
                extraData = extraData,
                contentNumber = null,
                isPremium = null,
                hasPurchased = null,
                isDownloadable = null,
                videoUrl = null,
                isVideoPremium = null,
                showVideo = null,
                hasVideoAccess = null,
                prerollEnabled = radio?.prerollEnabled,
                midrollEnabled = radio?.midrollEnabled,
                isSubscriptionAvailable = false,
                displayAdsEnabled = radio?.displayAdsEnabled,
                earlyAccessFinishDate = null
            )

            if (liveContent !=null){
                if (!liveContent?.imageMeta?.size300.isNullOrEmpty()){
                    data.image = liveContent?.imageMeta?.size300
                }
                data.title = liveContent?.title
            }

            RadioPlayRequest.Builder()
                .mediaButton(binding.mediaButton)
                .exoContent(data)
                .play(mContext)

            AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                putString(FirebaseAnalytics.Param.SCREEN_NAME, "Radio_MediaPlayer")
                putString("previousScreen", "Radio_Channel_Page")
                putString("radioId", radioId)
                putString("radioTitle", radio?.title)
                putString("entitySubtype", "radio")
            })

            DataController.setCurrentlyPlaying(DataController.RADIO_PAGE, radio?.id.toString(), DataController.PRIMARY_PLAY_BUTTON)
        }

        binding.layoutFav.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext.unwrap() as BasePlayerActivity)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (context as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            val value = if (isFollowChannel > 0.0) {
                0.0
            } else {
                1.0
            }

            if (value == 0.0) {
                var count = radio?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count -= 1
                    radio?.meta?.aggregations?.followers = count
                }
            }else{
                var count = radio?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count += 1
                    radio?.meta?.aggregations?.followers = count
                }else{
                    radio?.meta?.aggregations?.followers = 1
                }
            }
            val eventName = if (value == 0.0){
                "catalog_unfollowed"
            }else{
                "catalog_followed"
            }
            AnalyticsUtil.sendEventForCatalogFollow(
                ENTITY_TYPE_CATALOG,
                eventName,
                radio?.id,
                radio?.title ?: "",
                "catalog_page"
            )

            if (value != 0.0) {
                MoEngageAnalytics.sendEvent(mContext, "radio favorited", Bundle().apply {
                    putString("radio title", radio?.title?:"")
                    putString("radio id", radioId?:"")
                })
            }

            isFollowChannel = value

            updateFollow()

            val mediaAction = if (PrefUtils.isLoggedIn) {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, radio?.id,
                    ENTITY_TYPE_CATALOG,
                    PrefUtils.userDetails?.id.toString(),
                    radio?.type,
                    null
                )
            } else {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, radio?.id,
                    ENTITY_TYPE_CATALOG,
                    PrefUtils.guestId,
                    radio?.type,
                    null
                )
            }

            AnalyticsUtil.sendEventForCatalogFollow(
                ENTITY_TYPE_CATALOG,
                eventName,
                radio?.id,
                radio?.title ?: "",
                AnalyticsUtil.radio
            )
            viewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS && !it.data?.userActions.isNullOrEmpty()) {
                    it.data?.userActions?.find { mediaAction ->
                        mediaAction.action.equals(UserAction.FOLLOW.action, true)
                    }?.let { isFollow ->
                        isFollowChannel = isFollow.actionValue ?: 0.0
                        isSamePage = true
                        EventBus.getDefault().post(
                            EventMessage(
                                mediaAction,
                                Constants.USER_ACTIVITY_FOLLOW
                            )
                        )
                        updateFollow()
                        if (radio?.meta?.userActions.isNullOrEmpty()) {
                            radio?.meta?.userActions = ArrayList()
                            radio?.meta?.userActions?.add(isFollow)
                        }
                    }
                }
            }
        }

        artistAdapter = ArtistAdapter(artistList, ARTIST_VIEW, object : OnClickInterface<User> {
            override fun dataClicked(data: User) {

            }
        },"Radio_Channel_Page")
        binding.rvArtist.setHeaderText(mContext.getString(R.string.host))
        binding.rvArtist.viewHolder.recyclerView.adapter = artistAdapter
        binding.appBar.addOnOffsetChangedListener(this)
        startAlphaAnimation(binding.txtToolbarTitle, 0, View.INVISIBLE)
        binding.collapsingToolbar.collapsedTitleGravity = Gravity.START

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handleCatalogPlayButton(it)
            }

        binding.mediaButton.setMusicButtonImageResource(
            if (isCurrentRadioPlaying()) R.drawable.ic_radio_pause else R.drawable.ic_play_black_yellow
        )
    }

    private fun isCurrentRadioPlaying() =
        QueueManager.getPlayingData()?.id?.let { id ->
            (DataController.isAdPlaying || DataController.isPlaying) && id == radioId
        } ?: false

    private fun handleCatalogPlayButton(playerEvent: PlayerEvent) {
        if (playerEvent.exoData?.id == radio?.id) {

            binding.mediaButton.handleMusicButton(playerEvent)

            when (playerEvent.event) {
                PlayerEvent.PLAY_SHOW_LOADER -> {
                    binding.equalizer.animateBars()
                    binding.equalizer.visibility = View.VISIBLE
                }
                PlayerEvent.PLAY -> {
                    binding.equalizer.animateBars()
                    binding.equalizer.visibility = View.VISIBLE
                }
                PlayerEvent.PAUSE_END_LOADER -> {
                    binding.equalizer.stopBars()
                    binding.equalizer.visibility = View.GONE
                }
            }
        }
    }

    private fun getRadioDetail() {
        binding.errorView.showLoading(R.layout.radio_fragment_loader)

        viewModel.getChannelDetail(radioId ?: "", retrieveCache = true).observe(viewLifecycleOwner) { resource ->

            when (resource?.status) {
                ResponseStatus.LOADING -> {
                    if (resource.data?.data != null) {
                        handleRadioResponse(resource.data.data, true)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (resource.data?.data != null) {
                        handleRadioResponse(resource.data.data)
                        //displayGamAd()
                    } else {
                        handleError(resource.message)
                    }
                }
                else -> {
                    handleError(resource?.message)
                }
            }
        }
    }

    private fun displayGamAd() {
        if ((!PrefUtils.appCDNConfig?.nativeAdsConfig?.nativeAdUnitId.isNullOrEmpty() ||
                    !PrefUtils.appCDNConfig?.nativeAdsConfig?.customNativeAdUnitId.isNullOrEmpty()) &&
            PrefUtils.userDetails?.isSubscribed == false
        ) {
            GaManager.refreshAd(radio?.id, null, getGenresAsString())
        }
    }

    private fun getGenresAsString(): String {
        val genresString = radio?.genres?.joinToString(",") { genre ->
            genre.name ?: ""
        }
        return genresString?.trim() ?: ""
    }

    private fun handleError(message :String?) {
        if (radio == null) {
            binding.errorView.showError(message, type = "Radio_channel_Page", errorName = message)
            binding.errorView.setBackToHomeVisibility(View.GONE)
        } else {
            binding.errorView.hide()
        }
    }

    private fun handleRadioResponse(data: Channel?, cachedData : Boolean = false) {

        radio = data

        if (!Utils.getCatalogContract(radio?.data)?.playlistId.isNullOrEmpty()){
            getFmPodcast()
        }

        setRadioDetail()

        radio?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            if (isFollow.actionValue != null) {
                isFollowChannel = isFollow.actionValue!!
                updateFollow()
            }
        }

        updateFollow()

        if (!cachedData) {
            sendAnalytics()

            /***
             * The reason why this autoplay radio placed here and will only trigger if it's not
             * cached data is because the cached radio.data is null and will make the player broken
            ***/
            if (playContentDirectly && !isCurrentRadioPlaying()) {
                playContentDirectly = false
                binding.mediaButton.performClick()
            }
        }

        binding.errorView.hide()
    }

    private fun sendAnalytics() {
        AnalyticsUtil.sendEventForCatalog(
            EVENT_RADIO_PAGE_OPENED,
            radioId,
            radio?.title ?: "",
            "",
            extraData = extraData
        )

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, "Radio_Channel_Page")
            putString("previousScreen", sourcePage)
            putString("radioId", radioId)
            putString("radioTitle", radio?.title)
            putString("entitySubtype", "radio")
        })

        extraData?.apply {
            //contentLink = "${Constants.WEB_BASE_URL}content/${radio?.id}"
            catalogLink = "${Constants.WEB_BASE_URL}catalog/${radio?.id}"
        }

        MoEngageAnalytics.sendEventForRadioDetail(mContext, "detail page viewed", radio, extraData)
    }

    private fun setLiveDetail() {
        val date = Calendar.getInstance()
        val dayToday = DateFormat.format("EEE", date).toString().lowercase()

        liveContent = radio?.contents?.find { DateUtils.isInBetweenTime(
            it.contentSchedule?.startTime
                ?: "", it.contentSchedule?.endTime ?: ""
        ) && it.contentSchedule?.days?.contains(dayToday) == true }

        if (liveContent != null){
            listenersUpdateListener(radio?.id.toString())
            binding.liveLayout.visibility = View.VISIBLE
            binding.noLiveView.visibility = View.GONE
            binding.img.clipToOutline = true
            ImageUtils.loadImageByUrl(binding.img, liveContent?.imageMeta?.size300, originalUrl = ImageUtils.getOriginalImage(liveContent))
            binding.txtTitle.text = Utils.camelCase(liveContent?.title ?: "")
            binding.txtliveDesc.text = liveContent?.description

            if (liveContent?.contentSchedule?.startTime != null && liveContent?.contentSchedule?.endTime != null){
                binding.txtWib.text = DateUtils.getLocalTimeFromUTC(liveContent?.contentSchedule?.startTime.toString())
                    .plus(" WIB - ")
                    .plus(DateUtils.getLocalTimeFromUTC(liveContent?.contentSchedule?.endTime.toString()))
                    .plus(" WIB")
            }
        } else {
            listenersUpdateListener(radio?.id.toString())
            binding.liveLayout.visibility = View.VISIBLE
            binding.noLiveView.visibility = View.GONE
            binding.img.clipToOutline = true
            ImageUtils.loadImageByUrl(binding.img, radio?.imageMeta?.size300, originalUrl = ImageUtils.getOriginalImage(radio))
            binding.txtTitle.text = radio?.title
            binding.txtliveDesc.text = Utils.camelCase(radio?.description?:"")
        }

        if (DataController.playingEntityId == radio?.id) {
            val buttonState = mContext.getPlayerActivity()?.getContentPlayState(DataController.playerContentId)
            if (buttonState?.playWhenReady == true) {
                handleCatalogPlayButton(PlayerEvent(PlayerEvent.PLAY))
            }
            if (buttonState?.waiting == true) {
                handleCatalogPlayButton(PlayerEvent(PlayerEvent.SHOW_LOADER))
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayerAction(event: EventMessage) {
        if (event.eventCode == Constants.USER_ACTIVITY_FOLLOW){
            if (isSamePage){
                isSamePage = false
            }else{
                val data = event.data as MediaAction
                val followAction =  radio?.meta?.userActions?.find { mediaAction ->
                    mediaAction.action.equals(UserAction.FOLLOW.action, true)
                }
                isFollowChannel = data.actionValue?:0.0
                if (followAction != null){
                    followAction.actionValue = data.actionValue
                    val index =  radio?.meta?.userActions?.indexOf(followAction)
                    radio?.meta?.userActions?.set(index ?: 0, followAction)
                }else{
                    radio?.meta?.userActions?.add(data)
                }
                isSamePage = false
                updateFollow()
            }
        }
    }


    private fun listenersUpdateListener(radioId: String?) {

        listenerRegistration =  DataController.fireStoreChat.collection("Content").document(radioId ?: "Noice")
            .addSnapshotListener { value, error ->
                if (error == null && value != null) {
                    val data = value.get("listeners")
                    if (data!=null && (data as Long)>0L){
                        binding.txtListeners.text = Utils.abbrNumberFormat(data.toInt())
                    }else{
                        binding.txtListeners.text = "0"
                    }

                    Log.d("listeners - ", data.toString())
                }
            }
        val before24Hour = Date().time - 24 * 3600 * 1000
        chatRegistration =  DataController.fireStoreChat.collection("Comment")
            .document(radioId ?: "Noice")
            .collection("messages")
            .whereEqualTo("parentId", radioId)
            .orderBy("postedTime")
            .startAt(before24Hour)
            .addSnapshotListener { value, error ->
                if (error == null && value != null) {
                    val data = value.size()
                    binding.txtCommentCount.text = Utils.abbrNumberFormat(data)
                    Log.w("CommentCount - ", data.toString())
                    Log.w("radioId - ", radioId.toString())
                }else{
                    binding.txtCommentCount.text = "0"
                }
            }
    }

    private fun setRadioDetail() {

        Utils.getCatalogContract(radio?.data)?.donationUrl.let { donationUrl ->
            if (!donationUrl.isNullOrEmpty()) {
                binding.donateView.setRedirectionUrl(donationUrl)
                binding.donateView.visibility = View.VISIBLE
            } else {
                binding.donateView.visibility = View.GONE
            }
        }

        ImageUtils.showBlurImage(radio?.imageMeta?.size300, binding.imgCover)
        ImageUtils.loadImageByUrl(binding.imgRadio, radio?.imageMeta?.size300, originalUrl = ImageUtils.getOriginalImage(radio))

        binding.txtRadioName.text = radio?.title
        binding.txtToolbarTitle.text = radio?.title
        binding.txtDescription.text = Utils.camelCase(radio?.description?:"")

        setLiveDetail()
        if (radio?.contents.isNullOrEmpty() || (radio?.contents?.size == 1 && liveContent!=null)){
            binding.txtLihatSemua.visibility = View.GONE
            binding.rvSchedule.visibility = View.GONE
            binding.radioLayout.visibility = View.VISIBLE
        }else{

            filterProgram()
            if (radio?.contents!!.size>3){
                binding.txtLihatSemua.visibility = View.VISIBLE
                val currentDate = DateUtils.getFormattedDate(null)
                val date = Calendar.getInstance()
                val dayToday = DateFormat.format("EEE", date).toString().lowercase()
                Log.d("current - ", currentDate.toString())
                val list = ArrayList<Content>()
                for (schedule in radio?.contents!!){
                    var hr = DateUtils.getFormattedDate(schedule.contentSchedule?.startTime ?: "")?.run {
                        val cal = Calendar.getInstance()
                        cal.time = this
                        return@run cal.get(Calendar.HOUR_OF_DAY)
                    } ?: 0.0
                    hr = hr.toDouble() + 5.30

                    if (schedule.contentSchedule?.days?.contains(dayToday) == true &&
                            DateUtils.getFormattedDate(schedule.contentSchedule?.startTime ?: "")?.after(currentDate) == true
                        && hr<24)
                            {
                        Log.e(schedule.title, DateUtils.getFormattedDate(schedule.contentSchedule?.startTime
                                ?: "").toString())
                                list.add(schedule)
                    }
                }
                var sublist = radio?.contents?.filter {
                    it.contentSchedule?.days?.contains(dayToday) == true &&
                            (DateUtils.getFormattedDate(it.contentSchedule?.startTime ?: "")?.after(currentDate) == true)  &&
                            ((DateUtils.getFormattedDate(it.contentSchedule?.startTime ?: "")?.run {
                                val cal = Calendar.getInstance()
                                cal.time = this
                                return@run cal.get(Calendar.HOUR_OF_DAY)
                            } ?: 0.0).toDouble() + 5.30) < 24
                }

               if (sublist.isNullOrEmpty()){
                   binding.rvSchedule.visibility = View.GONE
                   binding.radioLayout.visibility = View.VISIBLE
               }else{
                   binding.rvSchedule.visibility = View.VISIBLE
                   binding.radioLayout.visibility = View.GONE
               }

                if ((sublist?.size ?: 0) > 3){
                    sublist = radio?.contents?.subList(0, 3)
                }
                sublist = sublist?.sortedBy { DateUtils.getLocalTimeFromUTC(it.contentSchedule?.startTime
                        ?: "") }
                scheduleAdapter?.setData(sublist?.toMutableList() as ArrayList<Content>)
            }else{
                scheduleAdapter?.setData(radio?.contents?.toMutableList() as ArrayList<Content>)
                binding.txtLihatSemua.visibility = View.GONE
            }
        }

        if (!radio?.genres.isNullOrEmpty()){
            val genreAdapter = GenreAdapter(mContext, radio?.genres!!,"Radio_Channel_Page")
            binding.rvGenre.adapter = genreAdapter
            binding.rvGenre.visibility = View.VISIBLE
        }else{
            binding.rvGenre.visibility = View.GONE
        }

        if (Utils.getCatalogContract(radio?.data) != null){
            if (Utils.getCatalogContract(radio?.data)?.phoneNumber.isNullOrEmpty() ||
                Utils.getCatalogContract(radio?.data)?.phoneNumber?.trim().isNullOrEmpty()){
                binding.txtMobile.visibility = View.GONE
            }else{
                binding.txtMobile.visibility = View.VISIBLE
                binding.txtMobile.text = Utils.getCatalogContract(radio?.data)?.phoneNumber?:""
            }
            if (Utils.getCatalogContract(radio?.data)?.website!=null){
                var url = Utils.getCatalogContract(radio?.data)?.website

                if (url.isNullOrEmpty() ){
                    binding.imgWeb.visibility = View.GONE
                }else{
                    binding.imgWeb.visibility = View.VISIBLE
                }
                binding.imgWeb.setOnClickListener {
                    if (!url!!.startsWith("http") || !url!!.startsWith("https"))
                        url = "https://".plus(url)
                    val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    startActivity(browserIntent)
                }
            }
        }

        if (!radio?.users.isNullOrEmpty()) {
            binding.rvArtist.visibility = View.VISIBLE
            artistList.clear()
            radio?.users?.let { artistList.addAll(it) }
            artistAdapter?.notifyDataSetChanged()
        } else {
            binding.rvArtist.visibility = View.GONE
        }
    }

    private fun getFmPodcast() {
        viewModel.getPlaylistContent(
            Utils.getCatalogContract(radio?.data)?.playlistId ?: "",
            page,
            limit
        ).observe(viewLifecycleOwner) {
            if (it.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                binding.rvPodcast.setHeaderText(radio?.title.plus(" Podcast"))
                binding.rvPodcast.visibility = View.VISIBLE
                val adapter = RadioPodcastAdapter(
                    it.data as ArrayList<Content>,
                    object : OnClickInterface<Content> {
                        override fun dataClicked(data: Content) {

                        }
                    },
                    ENTITY_TYPE_CONTENT,
                    "Radio Detail"
                )
                binding.rvPodcast.viewHolder.recyclerView.adapter = adapter
            } else {
                binding.rvPodcast.visibility = View.GONE
            }
        }
    }


    private fun filterProgram() {

        val radioMap = HashMap<String, HashSet<Content>>()

        val monList = HashSet<Content>()
        val tueList = HashSet<Content>()
        val wedList = HashSet<Content>()
        val thrList = HashSet<Content>()
        val friList = HashSet<Content>()
        val satList = HashSet<Content>()
        val sunList = HashSet<Content>()

        radio?.contents?.forEach { radio ->
            radio.catalog = this.radio
            if (!radio.contentSchedule?.days.isNullOrEmpty()) {
                if (radio.contentSchedule?.days!!.contains(DataController.MON_DAY)){
                    monList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.TUES_DAY)){
                    tueList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.WED_DAY)){
                    wedList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.THUR_DAY)){
                    thrList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.FRI_DAY)){
                    friList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.SAT_DAY)){
                    satList.add(radio)
                }
                if (radio.contentSchedule?.days!!.contains(DataController.SUN_DAY)){
                    sunList.add(radio)
                }
            }
        }

        radioMap[DataController.MON_DAY] = monList
        radioMap[DataController.TUES_DAY] = tueList
        radioMap[DataController.WED_DAY] = wedList
        radioMap[DataController.THUR_DAY] = thrList
        radioMap[DataController.FRI_DAY] = friList
        radioMap[DataController.SAT_DAY] = satList
        radioMap[DataController.SUN_DAY] = sunList

        val arr = arrayOf(
            DataController.SUN_DAY,
            DataController.MON_DAY,
            DataController.TUES_DAY,
            DataController.WED_DAY,
            DataController.THUR_DAY,
            DataController.FRI_DAY,
            DataController.SAT_DAY
        )

        val finalRadioMap = LinkedHashMap<String, HashSet<Content>>()

        val currentDay = when (Calendar.getInstance()[Calendar.DAY_OF_WEEK]) {
            Calendar.SUNDAY -> {
                0
            }
            Calendar.MONDAY -> {
                1
            }
            Calendar.TUESDAY -> {
                2
            }
            Calendar.WEDNESDAY -> {
                3
            }
            Calendar.THURSDAY -> {
                4
            }
            Calendar.FRIDAY -> {
                5
            }
            Calendar.SATURDAY -> {
                6
            }
            else -> {
                0
            }
        }

        var j = currentDay

        do {
            finalRadioMap[arr[j]] = radioMap[arr[j]] ?: HashSet()
            if(j == 6) {
                j = -1
            }
            j++
        } while (j != currentDay)

        DataController.radioMap = finalRadioMap
    }

    private fun updateFollow() {
        if (isFollowChannel == 0.0) {
            binding.txtFav.text = getString(R.string.favorite)
            binding.txtFav.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_heart_white,
                0,
                0,
                0
            )
        } else {
            binding.txtFav.text = getString(R.string.favorited)
            binding.txtFav.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_heart_selected,
                0,
                0,
                0
            )
        }

        radio?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            isFollow.actionValue = isFollowChannel
        }
    }



    override fun onOffsetChanged(appBarLayout: AppBarLayout?, offset: Int) {
        val maxScroll = appBarLayout!!.totalScrollRange
        val percentage = abs(offset).toFloat() / maxScroll.toFloat()

        handleAlphaOnTitle(percentage)
        handleToolbarTitleVisibility(percentage)
    }

    private fun handleToolbarTitleVisibility(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR) {
            if (!mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    View.VISIBLE
                )
                mIsTheTitleVisible = true
            }
        } else {
            if (mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    View.INVISIBLE
                )
                mIsTheTitleVisible = false
            }
        }
    }

    private fun handleAlphaOnTitle(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_HIDE_TITLE_DETAILS) {
            if (mIsTheTitleContainerVisible) {
                startAlphaAnimation(
                    binding.headerLayout,
                    ALPHA_ANIMATIONS_DURATION, View.INVISIBLE
                )
                mIsTheTitleContainerVisible = false
            }
        } else {
            if (!mIsTheTitleContainerVisible) {
                startAlphaAnimation(
                    binding.headerLayout,
                    ALPHA_ANIMATIONS_DURATION, View.VISIBLE
                )
                mIsTheTitleContainerVisible = true
            }
        }
    }

    private fun startAlphaAnimation(v: View, duration: Int, visibility: Int) {
        val alphaAnimation =
            if (visibility == View.VISIBLE) AlphaAnimation(0f, 1f) else AlphaAnimation(1f, 0f)
        alphaAnimation.duration = duration.toLong()
        alphaAnimation.fillAfter = true
        v.startAnimation(alphaAnimation)
    }

    private fun commonSaperatedString(list: ArrayList<User>?): String {
        val sb = StringBuffer()
        if (!list.isNullOrEmpty()){
            for (actionObject in list) {
                sb.append(actionObject.displayName).append(", ")
            }
            sb.deleteCharAt(sb.lastIndexOf(", "))
        }
        return sb.toString()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
    override fun onDetach() {
        super.onDetach()
        if (chatRegistration!=null){
            chatRegistration?.remove()
        }
        if (listenerRegistration!=null){
            listenerRegistration?.remove()
        }
    }
}