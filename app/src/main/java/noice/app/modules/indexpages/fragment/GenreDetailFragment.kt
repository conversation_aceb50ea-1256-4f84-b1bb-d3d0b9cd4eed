package noice.app.modules.indexpages.fragment

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentGenreDetailBinding
import noice.app.model.ExtraData
import noice.app.modules.indexpages.adpater.GenreIndexAdapter
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.indexpages.viewmodel.IndexPageViewModel
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.podcast.model.Channel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class GenreDetailFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        private const val GENRE = "GENRE"
        private const val SOURCE = "SOURCE"

        fun newInstance(genre : Genre,source:String, extraData: ExtraData? = null) = GenreDetailFragment().apply {
            arguments = Bundle().apply {
                putParcelable(GENRE, genre)
                putString(SOURCE,source)
                putParcelable(Constants.EXTRA_DATA, extraData)
            }
        }
    }

    private val viewModel: IndexPageViewModel by viewModels()

    private lateinit var binding : FragmentGenreDetailBinding
    private lateinit var ctx : Context
    private var genreContent = ArrayList<Channel?>()
    private var genre : Genre? = null
    private var genreIndexAdapter : GenreIndexAdapter? = null
    private var page = 1
    private var source:String?=null
    private var extraData: ExtraData? = null
    private var isFromDeepLink = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        genre = arguments?.getParcelable(GENRE)
        source = arguments?.getString(SOURCE)
        extraData = arguments?.getParcelable(Constants.EXTRA_DATA)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentGenreDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        isFromDeepLink = genre?.name.isNullOrEmpty()

        initViews()

        binding.errorView.showLoading()

        if (isFromDeepLink) {
            getGenreDetail()
        }

        getData(false)
    }

    private fun initViews() {
        if (!isFromDeepLink) {
            sendAnalyticsOnScreenOpen()
            setImageAndTitle()
        }

        binding.errorView.setOnReturnClick  {
            binding.errorView.showLoading()
            genreIndexAdapter?.isLoadMoreEnabled(true)
            getData(false)
        }

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.search.setOnClickListener {
            AnalyticsUtil.sendEventForOpenScreen("","search_bar_clicked","genre")

            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Genre_Page"))
        }

        genreIndexAdapter = GenreIndexAdapter(binding.podCastRecycler, genreContent, this, extraData)
        binding.podCastRecycler.adapter = genreIndexAdapter
        binding.podCastRecycler.addItemDecoration(RecyclerViewMargin(resources.getDimensionPixelSize(R.dimen.dp16)))
    }

    private fun getData(loadMore: Boolean) {
        genre?.id?.let { genreId ->

            if (!loadMore) {
                page = 1
            }

            val map = HashMap<String, String>()
            map["limit"] = "10"
            map["page"] = page.toString()
            map["orderBy"] = "ranking"

            viewModel.getGenreLists(genreId, map).observe(viewLifecycleOwner) {

                when(it?.status) {
                    ResponseStatus.LOADING -> {
                        if (!it.data?.data.isNullOrEmpty()) {
                            handleResponse(loadMore, it.data?.data ?: ArrayList())
                        }
                    }
                    ResponseStatus.SUCCESS -> {
                        if (!it.data?.data.isNullOrEmpty()) {
                            handleResponse(loadMore, it.data?.data ?: ArrayList())
                        } else {
                            handleError(it.message)
                        }
                    }
                    else -> {
                        handleError(it?.message)
                    }
                }
            }
        }
    }

    private fun handleError(message : String?) {
        if (genreContent.isEmpty()) {
            binding.errorView.showError(message, type = "Genre_Page", errorName = message)
        } else {
            binding.errorView.hide()
            genreIndexAdapter?.isLoadMoreEnabled(false)
        }
    }

    private fun handleResponse(loadMore: Boolean, data: List<Channel>) {
        if (loadMore)
            genreIndexAdapter?.addMore(ArrayList(data))
        else
            genreIndexAdapter?.addNewList(ArrayList(data))

        binding.errorView.hide()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > 9) {
            getData(true)
        } else {
            genreIndexAdapter?.isLoadMoreEnabled(false)
        }
    }

    private fun getGenreDetail() {
        viewModel.getGenreDetail(genre?.id ?: "").observe(viewLifecycleOwner) {
            when(it?.status) {
                ResponseStatus.LOADING -> {
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data?.id.isNullOrEmpty()) {
                        genre?.name = it.data?.data?.name
                        genre?.image = it.data?.data?.image
                        sendAnalyticsOnScreenOpen()
                        setImageAndTitle()
                    }
                }
                else -> {}
            }
        }
    }

    private fun setImageAndTitle() {
        ImageUtils.loadImageByUrl(binding.imgCover, genre?.getSmallImage(), placeHolder = R.drawable.background_transparent, originalUrl = genre?.image, enableTransition = true)
        binding.collapsingToolbar.title = genre?.name
    }

    private fun sendAnalyticsOnScreenOpen() {
        AnalyticsUtil.sendEvent("genre_page_opened", Bundle().apply {
            putString("genreTitle", genre?.name ?: "")
            putString("genreId", genre?.id ?: "")

            if (!extraData?.source.isNullOrEmpty()) {
                putString("source", extraData?.source ?: "")
            }
        })
    }
}