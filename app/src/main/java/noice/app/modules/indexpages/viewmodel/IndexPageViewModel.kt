package noice.app.modules.indexpages.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.modules.indexpages.repository.IndexPageRepository
import javax.inject.Inject

@HiltViewModel
class IndexPageViewModel @Inject constructor(
    private val repository: IndexPageRepository
) : ViewModel() {

    fun getGenreLists(id: String, map: HashMap<String, String>) = repository.getGenreLists(id, map)

    fun getGenreDetail(id: String) = repository.getGenreDetail(id)
}