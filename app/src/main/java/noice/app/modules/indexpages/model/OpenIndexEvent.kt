package noice.app.modules.indexpages.model

import noice.app.model.ExtraData

data class OpenIndexEvent(
    val eventId : String,
    val data : Any? = null,
    val targetPageId : String = HOME_ACTIVITY,
    val pageSource:String = "",
    val playContentDirectly:Boolean = false,
    val extraData: ExtraData? = null
) {
    companion object {
        const val OPEN_NOTIFICATION_CENTER = "OPEN_NOTIFICATION_CENTER"
        const val OPEN_LIHAT_SEMUA = "OPEN_LIHAT_SEMUA"
        const val OPEN_PLAY_LIST_PAGE = "OPEN_PLAY_LIST_PAGE"
        const val OPEN_LIVE_DETAIL_PAGE = "OPEN_LIVE_DETAIL_PAGE" //HomeActivity: Live Deeplink
        const val OPEN_USER_PAGE = "OPEN_USER_PAGE" //Excluded from Live module
        const val OPEN_USER_PAGE_WITH_USER_NAME = "OPEN_USER_PAGE_WITH_USER_NAME"
        const val OPEN_SEARCH_PAGE = "OPEN_SEARCH_PAGE"
        const val OPEN_RADIO_PAGE = "OPEN_RADIO_PAGE"
        const val OPEN_CONTENT_PAGE = "OPEN_CONTENT_PAGE"
        const val OPEN_CATALOG_PAGE = "OPEN_CATALOG_PAGE"
        const val OPEN_AUDIO_SERIES_PAGE = "OPEN_AUDIO_SERIES_PAGE"
        const val OPEN_AUDIO_BOOK_PAGE = "OPEN_AUDIO_BOOK_PAGE"
        const val OPEN_GENRE_PAGE = "OPEN_GENRE_PAGE"
        const val MY_GENRE_PAGE = "MY_GENRE_PAGE"
        const val OPEN_RADIO_SCHEDULE_PAGE = "OPEN_RADIO_SCHEDULE_PAGE"
        const val OPEN_DOWNLOADS = "OPEN_DOWNLOADS"
        const val HOME_ACTIVITY = "HOME_ACTIVITY"
        const val OPEN_FOLLOWER = "OPEN_FOLLOWER"
        const val OPEN_FOLLOWEE = "OPEN_FOLLOWEE"
        const val OPEN_IN_APP_BROWSER = "OPEN_IN_APP_BROWSER"
        const val OPEN_CLIPS_DETAIL_PAGE = "OPEN_CLIPS_DETAIL_PAGE"
        const val OPEN_POST_COMMENT = "OPEN_POST_COMMENT"
        const val OPEN_POST_REPLY = "OPEN_POST_REPLY"
        const val OPEN_CLIPS_SCREEN = "OPEN_CLIPS_SCREEN"
        const val OPEN_VERTICALS_SCREEN = "OPEN_VERTICALS_SCREEN"
        const val OPEN_TOP_RANKING = "OPEN_TOP_RANKING"
        const val OPEN_TRENDING_EPISODE = "OPEN_TRENDING_EPISODE"
        const val OPEN_COIN_HISTORY = "OPEN_COIN_HISTORY"
        const val OPEN_PAGE = "OPEN_PAGE"
        const val OPEN_USER_SETTINGS = "OPEN_USER_SETTINGS"
        const val OPEN_TRANSACTION_DETAIL_PAGE = "OPEN_TRANSACTION_DETAIL_PAGE"
        const val OPEN_THEME_PAGE = "OPEN_THEME_PAGE"
        const val OPEN_SUB_DETAIL_PAGE = "OPEN_SUB_DETAIL_PAGE"
        const val OPEN_KARYA = "OPEN_KARYA"
        const val OPEN_LIKED_CONTENT = "OPEN_LIKED_CONTENT"
        const val OPEN_ALL_EPISODE_PAGE = "OPEN_ALL_EPISODE_PAGE"
        const val OPEN_EPISODE_LIST_PAGE = "OPEN_EPISODE_LIST_PAGE"
        const val OPEN_NATIVE_DEEPLINK = "OPEN_NATIVE_DEEPLINK"
    }
}