package noice.app.modules.indexpages.fragment

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.exoplayer2.offline.Download
import com.google.firebase.analytics.FirebaseAnalytics
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.DataController
import noice.app.databinding.FragmentPlaylistDetailBinding
import noice.app.enums.UserAction
import noice.app.exoplayer.BasePlayerActivity
import noice.app.exoplayer.EpisodeViewDownloadHelper
import noice.app.listner.CustomClickListener
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.viewmodel.CollectionViewModel
import noice.app.modules.dashboard.home.activity.EditPlaylistActivity
import noice.app.modules.dashboard.home.fragment.NoicePlayListMenu
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.MediaAction
import noice.app.modules.media.model.Playlist
import noice.app.modules.podcast.adapter.CatalogEpisodeListAdapter
import noice.app.modules.podcast.adapter.EpisodeAdapter
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.model.Payload
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.parcelable
import noice.app.utils.extensions.printLog
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.collections.ArrayList

@AndroidEntryPoint
class PlaylistDetailFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        private const val PLAYLIST_ID = "PLAYLIST_ID"
        private const val SOURCE = "source"

        fun newInstance(id : String, source:String="", extraData: ExtraData? = null) = PlaylistDetailFragment().apply {
            arguments = Bundle().apply {
                putString(PLAYLIST_ID, id)
                putString(SOURCE, source)
                putParcelable(Constants.EXTRA_DATA, extraData)
            }
        }
    }

    private val viewModel : CollectionViewModel by viewModels()

    private var contentAdapter : CatalogEpisodeListAdapter? = null
    private lateinit var binding : FragmentPlaylistDetailBinding
    private lateinit var ctx : Context
    private lateinit var playListId : String
    private var contentList = ArrayList<Content>()
    private var playList : Playlist? = null
    private var shareDialog : ShareDialog? = null
    private var noiceMenu : NoicePlayListMenu? = null
    private val entityType = "playlist"
    private var source = ""
    private var isFollowed = 0.0
    private lateinit var episodeViewDownloadHelper : EpisodeViewDownloadHelper
    private var playlistDownloadState = Download.STATE_COMPLETED
    private var extraData: ExtraData? = null

    private val limit = 10
    private var page = 1

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        playListId = arguments?.getString(PLAYLIST_ID) ?: ""
        source = arguments?.getString(SOURCE) ?: ""
        extraData = arguments?.parcelable(Constants.EXTRA_DATA)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPlaylistDetailBinding.inflate(inflater, container, false)
        episodeViewDownloadHelper = EpisodeViewDownloadHelper(viewLifecycleOwner, null, binding.contentRecycler, childFragmentManager, "Playlist_Page")
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        showLoading()
        getPlaylistDetail()
    }

    override fun onStart() {
        super.onStart()

        MoEngageAnalytics.setOrResetInAppContext(setOf("playlist"))
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(ctx)
        MoEInAppHelper.getInstance().showNudge(ctx)
    }

    override fun onDestroy() {
        shareDialog?.dismiss()
        noiceMenu?.dismiss()
        noiceMenu = null
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1

        if (totalItemsCount > (limit - 1)) {
            getPlaylistDetail(true)
        } else {
            contentAdapter?.isLoadMoreEnabled(false)
        }
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handlePlayListPlayButton(it)
                handleEpisodeViewPlayStates(it)
            }

        binding.errorView.setLayoutBackground(R.color.black)

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            showLoading()
            getPlaylistDetail()
        }

        binding.threeDotMenu.setOnClickListener {
            playList?.let {
                noiceMenu = NoicePlayListMenu.newInstance(it, playlistDownloadState)
                noiceMenu?.show(childFragmentManager, "menu")
            }
        }

        binding.search.setOnClickListener {
            AnalyticsUtil.sendEventForOpenScreen("","search_bar_clicked",AnalyticsUtil.playlist)
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Playlist_Page"))
        }

        binding.editPlayList.setOnClickListener(CustomClickListener ({
            playList?.let {
                AnalyticsUtil.sendEventForPlaylist("playlist","playlist_edit",playList?.title?:"",playList?.id?:"",playList?.user?.displayName?:"")
                EditPlaylistActivity.start(ctx, it,"Playlist_Page")
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME,"Playlist_Edit")
                    putString("previousScreen", "Playlist_Page")
                    putString("playlistTitle", it.title?:"")
                    putString("playlistId", it.id?:"")
                    putString("playlistCreator", it.user?.userName?:"")
                })
            }
        },"playlist"))
        binding.share.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(ctx, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            shareDialog = ShareDialog.newInstance(playList,false,null)
            shareDialog?.show(childFragmentManager, "share_dialog")
        }
        binding.imgShare.setOnClickListener {
            binding.share.performClick()
        }

        binding.followBtn.setOnClickListener(CustomClickListener ({

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(ctx, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            if(!PrefUtils.isLoggedIn) {
                ctx.getPlayerActivity()?.handleUserNotLoggedIn(source = "playlist detail")
                return@CustomClickListener
            }

            val value = if (isFollowed > 0) {
                0.0
            } else {
                1.0
            }

            isFollowed = value
            val eventName = if (value == 0.0){
                "playlist_unfollowed"
            } else {
                "playlist_followed"
            }
            AnalyticsUtil.sendEventForPlaylist("playlist",eventName,playList?.title?:"",playList?.id?:"",playList?.user?.displayName?:"")
            updateFollowing()

            performAction(UserAction.FOLLOW.action, value)
        },"playlist detail"))

        contentAdapter = CatalogEpisodeListAdapter(binding.contentRecycler, contentList, episodeViewDownloadHelper, DataController.PLAY_LIST_PAGE,
            Channel().apply {
                id = playListId
                title = playList?.title?:""
                type = "content"
            },
            listener = this
        )
        binding.contentRecycler.adapter = contentAdapter
        binding.contentRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp1)))

        /* we have added this assignment to support LoadMoreAdapter functionality, as this load-more
        * adapter accepts only nullable lists but we are casting our non-nullable list there. Due to
        * this casting our 'episodeList' reference gets changed there and we get empty list here hence,
        * a lot of functionality breaks. */
        contentAdapter?.dataSet?.let {
            contentList = it as? ArrayList<Content> ?: arrayListOf()

            // for first page api response items item's state wasn't updating automatically for exm.
            // downloading/downloaded item unless scrolled
            contentAdapter?.updateListRef(contentList)
        }

        binding.mediaButton.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(ctx, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (DataController.isAdPlaying) {
                Utils.showSnackBar(ctx, getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            val playContent = contentList.find { it.id == DataController.playerContentId }

            if (DataController.playingEntityId == playListId && playContent != null) {
                if (ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, false)
                    AnalyticsUtil.sendEventForPlaylist(
                        "playlist",
                        "playlist_play_clicked",
                        playList?.title ?: "",
                        playList?.id ?: "",
                        playList?.user?.displayName ?: ""
                    )
                } else {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, true)
                    AnalyticsUtil.sendEventForPlaylist(
                        "playlist",
                        "playlist_pause_clicked",
                        playList?.title ?: "",
                        playList?.id ?: "",
                        playList?.user?.displayName ?: ""
                    )
                }
            } else if (contentList.isNotEmpty()) {
                val list = Utils.trimList(ctx, contentList)

                if (list.isEmpty()) {
                    return@setOnClickListener
                }

                ContentPlayRequest.Builder()
                    .contents(list)
                    .mediaButton(binding.mediaButton)
                    .pageSource(AnalyticsUtil.playlist)
                    .queueTitle(playList?.title ?: "Playlist")
                    .extraData(extraData)
                    .play(ctx)
            }

            DataController.setCurrentlyPlaying(DataController.PLAY_LIST_PAGE, playListId)
        }

        binding.download.setOnClickListener(CustomClickListener ({

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(ctx, ctx.getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-download")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "playlist detail", loginDialogData = loginDialogData)
                return@CustomClickListener
            }

            if (contentList.isNotEmpty()) {
                episodeViewDownloadHelper.queueDownloads("Playlist_Detail")
            }

            playlistDownloadState = Download.STATE_DOWNLOADING

            manageState()
        },"playlist"))

        binding.imgDownload.setOnClickListener(CustomClickListener ({
            binding.download.performClick()
        },"playlist"))

        enableDownload(false)

        episodeViewDownloadHelper.observeDownloadComplete().observe(viewLifecycleOwner) {
            if (it) {
                playlistDownloadState = Download.STATE_COMPLETED
                manageState()
            }
        }
    }

    private fun handlePlayListPlayButton(playerEvent: PlayerEvent) {

        val playContent = contentList.find {
            it.id == DataController.playerContentId
        }

        if (DataController.playingEntityId == playListId && playContent != null) {
            binding.mediaButton.handleMusicButton(playerEvent)
        }
    }

    private fun handleEpisodeViewPlayStates(playerEvent: PlayerEvent) {
        contentList.filter { content ->
            (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content.isPlaying = false
            content.showLoader = false

            val index = contentList.indexOf(content)
            if (index != -1) {
                contentAdapter?.notifyItemChanged(index)
            }
        }

        val content = contentList.find {
            it.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = contentList.indexOf(content)
            if (index != -1) {
                contentAdapter?.notifyItemChanged(index)
            }
        }
    }

    private fun performAction(action: String, value: Double) {

        val mediaAction = MediaAction(
            action,
            value,
            playListId,
            entityType,
            PrefUtils.userDetails?.id.toString(),
            entityType,
            null
        )

        viewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.userActions.isNullOrEmpty()) {
                playList?.meta?.userActions = it.data?.userActions
                playList?.meta?.userActions?.find { mediaAction ->
                    mediaAction.action.equals(UserAction.FOLLOW.action, true)
                }.let { isFollow ->
                    isFollowed = isFollow?.actionValue ?: 0.0
                    updateFollowing()
                }
            }
        }
    }

    private fun getPlaylistDetail(loadMore: Boolean = false) {
        val map = HashMap<String, String>()

        map["page"] = page.toString()
        map["limit"] = limit.toString()

        if (isDetached || isRemoving || !isAdded)
            return

        viewModel.getPlaylistDetails(playListId, map).observe(viewLifecycleOwner) {

            when (it?.status) {
                ResponseStatus.LOADING -> {
                    if (it.data != null) {
                        handleResponse(it, loadMore)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (it.data != null) {
                        handleResponse(it, loadMore)
                    } else if (contentList.isNotEmpty()) {
                        contentAdapter?.isLoadMoreEnabled(false)
                    } else {
                        binding.errorView.showError(
                            error = it.message,
                            type = "Playlist_Page",
                            errorName = it.message
                        )
                    }
                }
                else -> {
                    handleError(it?.message)
                }
            }
        }
    }

    private fun handleError(message :String?) {
        if (playList == null) {
            binding.errorView.showError(
                error = message,
                type = "Playlist_Page",
                errorName = message
            )
        } else {
            hideLoading()
        }
    }

    private fun handleResponse(resource: Resource<BaseModel<Playlist>>?, loadMore: Boolean = false) {
        playList = resource?.data?.data

        if (loadMore) {
            if (!resource?.data?.data?.playlistContent.isNullOrEmpty()) {
                contentAdapter?.addMore(resource?.data?.data?.playlistContent ?: ArrayList())
                enableDisablePlaylistDownload(resource?.data?.data?.playlistContent)
            }
        } else {
            contentAdapter?.addNewList(resource?.data?.data?.playlistContent ?: ArrayList())
        }

        decideDownloadState()
        binding.emptyView.visibility = if (contentList.isEmpty()) VISIBLE else GONE

        if (page == 1) {
            setData()

            if (resource?.wasCacheAvailable == false) {
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Playlist_Page")
                    putString("previousScreen", source)
                    putString("playlistTitle", playList?.title)
                    putString("playlistId", playList?.id)
                    putString("playlistCreator", playList?.user?.userName ?: "")
                })
                AnalyticsUtil.sendEventForPlaylist(
                    "playlist",
                    "playlist_opened",
                    playList?.title ?: "",
                    playList?.id ?: "",
                    playList?.user?.displayName ?: "",
                    extraData = extraData
                )
                MoEngageAnalytics.sendEvent(ctx, "playlist page viewed", Bundle().apply {
                    putString("playlist title", playList?.title ?: "")
                    putString("playlist id", playList?.id ?: "")
                    putString("playlist creator username", playList?.user?.userName ?: "")
                    putString("created at", playList?.createdAt ?: "")
                })
            }
        }

        hideLoading()
    }

    private fun decideDownloadState() {
        CoroutineScope(Dispatchers.IO).launch {
            val contentIds = contentList.map {
                it.id
            }
            BaseApplication.application.getDownloadTracker().getDownloads(Download.STATE_COMPLETED).let { downloadedContent ->
                contentIds.forEach { id ->
                    val content = downloadedContent.find { content ->
                        id == content.id
                    }
                    if(content == null) {
                        playlistDownloadState = -1
                        (ctx as AppCompatActivity).runOnUiThread {
                            manageState()
                        }
                        return@launch
                    }
                }
            }

            (ctx as AppCompatActivity).runOnUiThread {
                manageState()
            }
        }
    }

    private fun manageState() {
        when (playlistDownloadState) {
            Download.STATE_COMPLETED -> {
                binding.download.text = getString(R.string.downloaded)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white50))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.ic_download_grey,0,0)
                binding.imgDownload.setImageResource(R.drawable.ic_download_grey)
                binding.imgDownload.background = ContextCompat.getDrawable(ctx, R.drawable.border_grey_radius_4dp)

                enableDownload(false)
            }
            Download.STATE_DOWNLOADING -> {
                binding.download.text = getString(R.string.downloading)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.ic_download,0,0)
                binding.imgDownload.setImageResource(R.drawable.ic_download_yellow)
                binding.imgDownload.background = ContextCompat.getDrawable(ctx, R.drawable.border_yellow_radius_4dp)

                enableDownload(false)
            }
            else -> {
                binding.download.text = getString(R.string.download)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.ic_download,0,0)
                binding.imgDownload.setImageResource(R.drawable.ic_download)
                binding.imgDownload.background = ContextCompat.getDrawable(ctx, R.drawable.border_white_radius_4dp)

                enableDownload(true)
            }
        }

        if (noiceMenu?.isVisible == true) {
            noiceMenu?.manageState(playlistDownloadState)
        }
    }

    private fun setData() {
        if (DataController.playingEntityId == playListId) {
            val buttonState =
                ctx.getPlayerActivity()?.getContentPlayState(DataController.playerContentId)
            if (buttonState?.playWhenReady == true) {
                handlePlayListPlayButton(PlayerEvent(PlayerEvent.PLAY))
            }
            if (buttonState?.waiting == true) {
                handlePlayListPlayButton(PlayerEvent(PlayerEvent.SHOW_LOADER))
            }
        }

        if (playList?.localImageUri != null) {
            binding.coverImage.setImageURI(playList?.localImageUri)
        } else {
            ImageUtils.loadImageByUrl(
                binding.coverImage,
                playList?.imageMeta?.size300 ?: "",
                originalUrl = ImageUtils.getOriginalImage(playList)
            )
        }

        binding.playListName.text = playList?.title
        binding.stats.text = getString(
            R.string.content_duration,
            playList?.meta?.contentCount ?: 0,
            DateUtils.convertSeconds(playList?.meta?.totalDuration ?: 0)
        )

        binding.followers.text =
            getString(R.string.no_of_followers, playList?.meta?.aggregations?.followers ?: 0)

        val text = getString(R.string.playlist_by, playList?.user?.displayName)
        val spannableString = SpannableString(text)
        spannableString.setSpan(
            ForegroundColorSpan(
                ContextCompat.getColor(
                    ctx,
                    R.color.dull_yellow
                )
            ),
            text.length - (playList?.user?.displayName?.length ?: 0),
            text.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                if (!playList?.user?.id.isNullOrEmpty() && playList?.user?.displayName?.equals(
                        "noice",
                        true
                    ) == false
                ) {
                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_USER_PAGE,
                            playList?.userId,
                            targetPageId = "Playlist_Page"
                        )
                    )
                }
            }

            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
                ds.drawableState = null
            }
        }

        spannableString.setSpan(
            clickableSpan,
            text.length - (playList?.user?.displayName?.length ?: 0),
            text.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        binding.creator.highlightColor = Color.TRANSPARENT
        binding.creator.movementMethod = LinkMovementMethod.getInstance()
        binding.creator.text = spannableString

        if (playList?.userId == PrefUtils.userDetails?.id) {
            binding.homeBtn.setOnClickListener {
                val activity = ctx.fetchActivity()
                if (activity is HomeActivity) {
                    activity.openHome()
                } else {
                    activity?.onBackPressed()
                }
            }

            binding.playlistOptions.visibility = GONE
            binding.myPlaylistOptions.visibility = VISIBLE
            binding.homeBtn.visibility = VISIBLE
            binding.tv1.text = getString(R.string.my_playlist_no_content_msg)

            if (playList?.isPublic == true) {
                binding.privacy.visibility = VISIBLE
            } else {
                binding.privacy.visibility = GONE
            }
        } else {
            binding.homeBtn.visibility = GONE
            binding.myPlaylistOptions.visibility = GONE
            binding.playlistOptions.visibility = VISIBLE
            binding.tv1.text = getString(R.string.playlist_no_content_msg)
            binding.privacy.visibility = GONE
        }

        playList?.meta?.userActions?.find {
            it.action.equals(UserAction.FOLLOW.action, true)
        }.let { isFollow ->
            isFollowed = isFollow?.actionValue ?: 0.0
            updateFollowing()
        }
    }

    /** This method checks if the playlist download option should be enabled/disabled with only on the
    * paginated data instead of running the check on complete data list again and again. */
    private fun enableDisablePlaylistDownload(playlistContents: List<Content>?) {
        val disabledContent = playlistContents?.find {
            it.isDownloadable == false
        }

        if (disabledContent != null) {
            binding.imgDownload.visibility = GONE
            binding.download.visibility = GONE
        } else {
            binding.imgDownload.visibility = VISIBLE
            binding.download.visibility = VISIBLE
        }
    }

    private fun updateFollowing() {
        if (isFollowed == 1.0) {
            binding.txtFollow.text = getString(R.string.following)
            binding.txtFollow.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_following_tick,0,0,0)
        } else {
            binding.txtFollow.text = getString(R.string.follow)
            binding.txtFollow.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_follower_plus,0,0,0)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        if(event.eventCode == Constants.PLAYLIST_EDITED) {
            if(event.data is Playlist) {
                playList = event.data
                setData()
                contentList.clear()
                contentList.addAll(playList?.playlistContent ?: ArrayList())

                if (contentList.isNotEmpty()) {
                    binding.emptyView.visibility = GONE
                } else {
                    binding.emptyView.visibility = VISIBLE
                }

                enableDisablePlaylistDownload(playList?.playlistContent)

                contentAdapter?.notifyDataSetChanged()
            }
        } else if(event.eventCode == Constants.PLAYLIST_DELETED) {
            (ctx as AppCompatActivity).onBackPressed()
        } else if (event.eventCode == Constants.PLAYLIST_MARK_PLAYED) {
            markAsPlayed(event.data as Content)
        } else if (event.eventCode == Constants.PLAYLIST_DOWNLOAD) {
            if (binding.download.isEnabled) {
                binding.download.performClick()
            }
        }
    }

    private fun enableDownload(enable : Boolean) {
        binding.download.isEnabled = enable
        binding.imgDownload.isEnabled = enable
    }

    private fun markAsPlayed(content: Content) {
        val analyticsAction: String
        val action = if (content.meta?.markedAsPlayed == true){
            analyticsAction = "content_marked_as_played"
            EventConstant.MARK_PLAYED
        } else {
            analyticsAction = "content_marked_as_unplayed"
            EventConstant.MARK_UN_PLAYED
        }
        val index = contentList.indexOf(content)
        contentList[index] = content
        contentAdapter?.notifyItemChanged(index)

        val payload = Payload(content.meta?.timeElapsed?.toInt(),content.id,"content",content.catalog?.type,AnalyticsUtil.playlist,
            PrefUtils.uniqueId,content.duration?.toInt(),
            catalogId = content.catalogId ?: ""
        )
        val event = EventPost(action,payload,contentId = content.id?:"")


        AnalyticsUtil.sendEvent(
            content.catalog?.type.toString(),
            content.id,
            content.catalog?.id,
            analyticsAction,
            AnalyticsUtil.playlist,
            content.meta?.timeElapsed.toString(),
            content.duration.toString(),
            catalogTitle = content.catalog?.title?:""
        )

        if (NetworkUtils.isNetworkConnected(ctx)){
            viewModel.postEvent(event)
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading()
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }
}