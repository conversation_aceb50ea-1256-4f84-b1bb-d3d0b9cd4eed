package noice.app.modules.indexpages.adpater

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.listner.LoadManualListener
import noice.app.model.ExtraData
import noice.app.modules.podcast.model.Channel
import noice.app.views.PlayListGridView

class ArtistCreationAdapter(
    private val dataSet: ArrayList<Channel>,
    private val listener: LoadManualListener,
    private val extraData: ExtraData?
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_CATALOG = 1
        const val TYPE_LOADER = 2
    }

    private lateinit var content: PlayListGridView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_CATALOG) {
            content = PlayListGridView(parent.context)
            content.setExtraData(extraData)
            content.viewHolder
        } else {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.load_more_button, parent, false)
            view.layoutParams = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                parent.context.resources.getDimensionPixelSize(R.dimen.dp48)
            )
            LoaderViewHolder(view)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PlayListGridView.ViewHolder -> {
                content.viewHolder = holder
                content.setData(dataSet[position])
            }
            is LoaderViewHolder -> {
                if (dataSet[position].loading == true) {
                    holder.loadMoreButton.visibility = View.GONE
                    holder.progressBar.visibility = View.VISIBLE
                } else {
                    holder.loadMoreButton.visibility = View.VISIBLE
                    holder.progressBar.visibility = View.GONE
                }
                holder.view.setOnClickListener {
                    listener.onLoadMore()
                }
            }
        }
    }

    override fun getItemCount(): Int {
        return dataSet.size
    }

    override fun getItemViewType(position: Int): Int {
        return if (dataSet[position].isLoader == true) {
            TYPE_LOADER
        } else {
            TYPE_CATALOG
        }
    }

    class LoaderViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        val loadMoreButton : TextView = view.findViewById(R.id.loadMoreButton)
        val progressBar : ProgressBar = view.findViewById(R.id.progressBar)
    }
}