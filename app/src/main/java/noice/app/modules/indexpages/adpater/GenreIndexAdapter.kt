package noice.app.modules.indexpages.adpater

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.model.ExtraData
import noice.app.modules.podcast.model.Channel
import noice.app.views.homesegmentviews.DefaultSegmentView

class GenreIndexAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Channel?>,
    listener: LoadMoreAdapterListener?,
    val extraData: ExtraData?
) : LoadMoreAdapter<Channel>(recyclerView, dataSet, listener) {

    private lateinit var content : DefaultSegmentView

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        content = DefaultSegmentView(parent.context,"","Genre","Genre_Page")
        content.setWidth()
        content.shouldHandleAudioBook(true)
        content.setExtraData(extraData)
        return content.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        if(dataSet[position] != null) {
            content.viewHolder = holder as DefaultSegmentView.ViewHolder
            content.setData(dataSet[position] as Channel, "Genre_Page", false)
        }
    }
}