package noice.app.modules.indexpages.repository

import noice.app.rest.BaseRepository
import noice.app.rest.apiinterfaces.CatalogApiInterface
import noice.app.utils.PrefUtils
import javax.inject.Inject

class IndexPageRepository @Inject constructor(
    private val catalogApiService: CatalogApiInterface
) : BaseRepository() {

    fun getGenreLists(id: String, map : HashMap<String, String>) = networkBoundResource(
        cachingConstant = id,
        apiPage = map["page"]?.toInt(),
        apiCall = { catalogApiService.getGenreLists(PrefUtils.token, id, map) }
    )

    fun getGenreDetail(id: String) = networkBoundResource(
        cachingConstant = id,
        apiCall = { catalogApiService.getGenreDetail(PrefUtils.token, id) }
    )
}