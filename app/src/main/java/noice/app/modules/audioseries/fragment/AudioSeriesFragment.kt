package noice.app.modules.audioseries.fragment

import android.Manifest
import android.content.Context
import android.graphics.Typeface
import android.net.Uri
import android.os.*
import android.transition.Fade
import android.transition.Transition
import android.transition.TransitionManager
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentAudioSeriesBinding
import noice.app.enums.UserAction
import noice.app.exoplayer.EpisodeViewDownloadHelper
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.ads.AdsResponse
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.model.user.User
import noice.app.modules.ads.GaManager
import noice.app.modules.coins.model.SubscriptionPackage
import noice.app.modules.dashboard.home.fragment.ShareDialog
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.podcast.adapter.ArtistAdapter
import noice.app.modules.podcast.adapter.ArtistAdapter.Companion.CREATOR_VIEW
import noice.app.modules.podcast.adapter.EpisodeAdapter
import noice.app.modules.podcast.adapter.GenreAdapter
import noice.app.modules.podcast.adapter.RssAuthorAdapter
import noice.app.modules.podcast.adapter.SimilarCatalogPagerAdapter
import noice.app.modules.podcast.fragment.PodCastPagerFragment
import noice.app.modules.podcast.model.*
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.modules.radio.model.RssAuthor
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.ResponseStatus
import noice.app.room.LocalCacheData
import noice.app.utils.*
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_SERIES
import noice.app.utils.Constants.Companion.SOURCE_EXCLUSIVE
import noice.app.utils.Constants.Companion.SOURCE_ORIGINAL
import noice.app.utils.Constants.Companion.SOURCE_SPECIAL
import noice.app.utils.Constants.Companion.USER_ACTIVITY_FOLLOW
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import noice.app.views.CustomAdView
import noice.app.views.PopupFilterMenu
import noice.app.views.ReadMoreTextView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import kotlin.math.abs

@AndroidEntryPoint
class AudioSeriesFragment : Fragment(), AppBarLayout.OnOffsetChangedListener {

    companion object {
        private const val PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR = 0.6f
        private const val PERCENTAGE_TO_HIDE_TITLE_DETAILS = 0.5f
        private const val ALPHA_ANIMATIONS_DURATION = 600
        private const val AUDIO_SERIES_ID = "audioSeriesId"
        private const val SOURCE = "SOURCE"
        private const val PAGE_SOURCE = "PAGE_SOURCE"
        private const val PLAY_CONTENT_DIRECTLY = "PLAY_CONTENT_DIRECTLY"
        private const val EXTRA_DATA = "EXTRA_DATA"
        private const val CHANNEL_AUDIOSERIES_PAGE = "Channel_Audioseries_Page"

        fun newInstance(id : String, source : String, pageSource : String, playContentDirectly: Boolean = false, extraData: ExtraData? = null) = AudioSeriesFragment().apply {
            arguments = Bundle().apply {
                putString(AUDIO_SERIES_ID, id)
                putString(SOURCE, source)
                putString(PAGE_SOURCE, pageSource)
                putBoolean(PLAY_CONTENT_DIRECTLY, playContentDirectly)
                putParcelable(EXTRA_DATA, extraData)
            }
        }
    }

    private var mediaContent: Content? = null
    private val podcastViewModel: ChannelPodcastViewModel by viewModels()

    private lateinit var binding: FragmentAudioSeriesBinding
    private lateinit var ctx: Context
    private var adapter: EpisodeAdapter? = null
    private var artistAdapter: ArtistAdapter? = null
    private var userList = ArrayList<User>()
    private var seriesId: String = ""
    private var title: String = ""
    private var series: Channel? = null
    private var mIsTheTitleVisible = false
    private var mIsTheTitleContainerVisible = true
    private var seriesList = ArrayList<Content>()
    private var isFollowChannel = 0.0
    private var isSamePage = false
    private lateinit var episodeViewDownloadHelper : EpisodeViewDownloadHelper
    private var followClickTime: Long = 0
    private var source:String?=null
    private var pageSource : String = ""
    private var rssAuthorAdapter : RssAuthorAdapter? = null
    private var playContentDirectly = false
    private var extraData: ExtraData? = null
    private var allContents = mutableSetOf<Content>()

    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null

    private var adsResponse: AdsResponse? = null
    private var pagerAdapter: SimilarCatalogPagerAdapter? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        seriesId = arguments?.getString(AUDIO_SERIES_ID) ?: ""
        source = arguments?.getString(SOURCE) ?: ""
        pageSource = arguments?.getString(PAGE_SOURCE) ?: ""
        playContentDirectly = arguments?.getBoolean(PLAY_CONTENT_DIRECTLY) ?: false
        extraData = arguments?.parcelable(EXTRA_DATA)
        if (extraData?.source.isNullOrEmpty()) {
            extraData?.source = CHANNEL_AUDIOSERIES_PAGE
        }
        EventBus.getDefault().register(this)

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAudioSeriesBinding.inflate(LayoutInflater.from(ctx), container, false)
        episodeViewDownloadHelper = EpisodeViewDownloadHelper(viewLifecycleOwner, seriesList, binding.rvEpisode, childFragmentManager, "Audioseries_Catalog_Page",extraData)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()

        getData()
    }

    private fun getData(ignoreCache: Boolean = false) {
        showLoading()

        getDataFromCache(ignoreCache)
    }

    override fun onStart() {
        super.onStart()

        MoEngageAnalytics.setOrResetInAppContext(setOf("catalog audioseries"))
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(ctx)
        MoEInAppHelper.getInstance().showNudge(ctx)
    }

    private fun initView() {
        /* similar catalogs is not enabled for Audiobook but will come later. Hence, assigning
        * default variant 'a' to enable default(non similar content) functionality. */
        val defaultVariant = "a"
        //if (PrefUtils.firebaseVariantSimilarCatalog?.variant == ExperimentUtils.VARIANT_B) {
        if (defaultVariant == ExperimentUtils.VARIANT_B) {
            initSimilarContentUi()
            binding.filterLayout.visibility = GONE
            binding.nestedScrollDefault.visibility = GONE
            binding.tabLayout.visibility = VISIBLE
            binding.viewPager.visibility = VISIBLE
        } else {
            initDefaultEpisodeRecycler()
            binding.filterLayout.visibility = VISIBLE
            binding.nestedScrollDefault.visibility = VISIBLE
            binding.tabLayout.visibility = GONE
            binding.viewPager.visibility = GONE
        }

        binding.rvArtist.viewHolder.recyclerView.apply {
            val orientation = RecyclerView.HORIZONTAL
            layoutManager = LinearLayoutManager(ctx, orientation, false)
            setHasFixedSize(true)
            addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp8)))
        }

        binding.mediaButton.setOnClickListener {
            if (DataController.isAdPlaying) {
                Utils.showSnackBar(ctx, getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            if (DataController.playingEntityId == seriesId) {
                if (ctx.getPlayerActivity()?.isPlayWhenReady() == true){
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, false)
                    AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES, "catalog_pause_clicked", seriesId, series?.title.toString(), "primary play button")
                } else {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, true)
                    AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES, "catalog_play_clicked", seriesId, series?.title.toString(), playButtonPosition = "primary play button")
                }
            } else {
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES, "catalog_play_clicked", seriesId, series?.title.toString(), playButtonPosition = "primary play button")

                val list = if (series?.selectedFilter.equals(PopupFilterMenu.FILTER_ASC, true)) {
                    seriesList
                } else {
                    series?.startingContents ?: ArrayList()
                }

                ContentPlayRequest.Builder()
                    .contents(list)
                    .mediaButton(binding.mediaButton)
                    .pageSource(AnalyticsUtil.audioseries_channel)
                    .queueTitle(series?.title.toString())
                    .extraData(extraData)
                    .addCatalogContents(true)
                    .catalog(series)
                    .play(ctx)
            }

            DataController.setCurrentlyPlaying(DataController.AUDIO_SERIES_PAGE, seriesId, DataController.PRIMARY_PLAY_BUTTON)
        }

        /*binding.imgSearch.setOnClickListener {
            AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES,"search_bar_clicked",AnalyticsUtil.audioseries_channel)
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Channel_Audioseries_Page"))
        }*/

        binding.layoutFilter.setOnClickListener {
            val popupMenuTimeline = PopupFilterMenu(ctx, series?.selectedFilter.toString())
            popupMenuTimeline.setClickListener(object : OnClickInterface<String> {
                override fun dataClicked(data: String) {
                    series?.selectedFilter = data
                    binding.txtFilter.text = series?.selectedFilter.toString()
                    getChannelDetail(from = "filter")
                }
            })
            popupMenuTimeline.setData()
            val mPopup = PopupWindow(
                popupMenuTimeline, LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT, true
            )
            mPopup.elevation = 8f
            mPopup.setBackgroundDrawable(
                ContextCompat.getDrawable(
                    ctx,
                    R.drawable.background_shadow
                )
            )
            popupMenuTimeline.setPopup(mPopup)
            mPopup.animationStyle = android.R.style.Animation_Dialog
            mPopup.setBackgroundDrawable(
                ContextCompat.getDrawable(
                    ctx,
                    R.drawable.transparent
                )
            )
            mPopup.isOutsideTouchable = false
            mPopup.showAsDropDown(binding.layoutFilter, binding.layoutFilter.right - 100, -90)
        }

        binding.errorView.setOnReturnClick {
            showLoading()
            getChannelDetail()
        }

        binding.appBar.addOnOffsetChangedListener(this)
        startAlphaAnimation(binding.txtToolbarTitle, 0, View.INVISIBLE)
        binding.collapsingToolbar.collapsedTitleGravity = Gravity.START

        binding.txtLihatSemua.setOnClickListener {
            val fragment = PodCastPagerFragment.newInstance(series?.title.toString(),series?.id.toString(), channelData = series, extraData = extraData)
            parentFragment?.childFragmentManager?.beginTransaction()
                ?.add(R.id.homeContainer, fragment)
                ?.addToBackStack(PodCastPagerFragment::class.java.simpleName)
                ?.commit()
        }

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.layoutFollow.setOnClickListener {

            if (!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (SystemClock.elapsedRealtime() - followClickTime < 800){
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-subscribe-catalog")
                ctx.getPlayerActivity()?.handleUserNotLoggedIn(loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            followClickTime = SystemClock.elapsedRealtime()

            val value = if (isFollowChannel > 0.0) {
                 0.0
            } else {
                 1.0
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (value == 1.0) { //catalog followed
                    /* notification permission for Android 13 */
                    if (permissionUtils?.isAllPermissionGranted() == false) {
                        permissionUtils?.requestPermissions()
                        return@setOnClickListener
                    }
                }
            }

            if (value == 0.0){
                var count = series?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count -= 1
                    series?.meta?.aggregations?.followers = count
                }
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES, "catalog_unfollowed", series?.id.toString(),series?.title.toString())

                MoEngageAnalytics.sendEvent(ctx, "catalog unfollowed", Bundle().apply {
                    putString("vertical", ENTITY_TYPE_AUDIO_SERIES)
                    putString("catalog title", series?.title ?:"")
                    putString("catalog id", seriesId)
                    putStringArrayList(
                        "genre",
                        ArrayList(series?.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", series?.source?:"")
                })
            }else{
                var count = series?.meta?.aggregations?.followers
                if (count != null && count>0L) {
                    count += 1
                    series?.meta?.aggregations?.followers = count
                }else{
                    series?.meta?.aggregations?.followers = 1
                }
                AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES, "catalog_followed", series?.id.toString(),series?.title.toString())

                MoEngageAnalytics.sendEvent(ctx, "catalog followed", Bundle().apply {
                    putString("vertical", ENTITY_TYPE_AUDIO_SERIES)
                    putString("catalog title", series?.title ?:"")
                    putString("catalog id", seriesId)
                    putStringArrayList(
                        "genre",
                        ArrayList(series?.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", series?.source?:"")
                    putString("image url", series?.imageMeta?.size300 ?: "")
                })
            }

            isFollowChannel = value
            val eventName = if (value == 0.0){
                "catalog_unfollowed"
            }else{
                "catalog_followed"
            }
            AnalyticsUtil.sendEventForCatalogFollow(series?.type,  eventName, series?.id, series?.title?:"", "catalog_page")

            updateFollow()

            val mediaAction = if (PrefUtils.isLoggedIn) {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, seriesId,
                    "catalog",
                    PrefUtils.userDetails?.id.toString(),
                    series?.type,
                    null
                )
            } else {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value, seriesId,
                    "catalog",
                    PrefUtils.guestId,
                    series?.type,
                    null
                )
            }

            podcastViewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS && !it.data?.userActions.isNullOrEmpty()) {
                    it.data?.userActions?.find { mediaAction ->
                        mediaAction.action.equals(UserAction.FOLLOW.action, true)
                    }?.let { isFollow ->
                        isFollowChannel = isFollow.actionValue ?: 0.0
                        isSamePage = true
                        EventBus.getDefault().post(EventMessage(mediaAction, USER_ACTIVITY_FOLLOW))
                        updateFollow()

                        if (series?.meta?.userActions.isNullOrEmpty()) {
                            series?.meta?.userActions = ArrayList()
                            series?.meta?.userActions?.add(isFollow)
                        }
                    }
                }
            }
        }

        binding.imgShare.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            val dialog = ShareDialog.newInstance(series,false,null)
            dialog.show(childFragmentManager, "share")
        }
        artistAdapter = ArtistAdapter(userList, CREATOR_VIEW, object : OnClickInterface<User> {
            override fun dataClicked(data: User) {

            }
        },"Audioseries_Catalog_Page")

        binding.rvArtist.getRecyclerView().adapter = artistAdapter

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handleCatalogPlayButton(it)
                handleEpisodeViewPlayStates(it)
            }

        GlobalObservers.contentPurchaseObserver
            .subscribe(viewLifecycleOwner) { response ->
                if (response is Content && response.catalogId == seriesId) {
                    val index = seriesList.indexOf(response)
                    if (index != -1) {
                        Utils.showSnackBarWithDrawable(
                            rootView = binding.root.rootView,
                            viewToAnchor = null,
                            messageText = getString(R.string.open_content2),
                            drawableResId = R.drawable.ic_tick_green_success
                        )
                        seriesList[index] = response
                        adapter?.notifyItemChanged(index)
                        pagerAdapter?.updateEpisodeAtIndex(index)
                    }
                } else if (response is SubscriptionPackage) {
                    getData(ignoreCache = true)
                }
            }
    }

    private fun initDefaultEpisodeRecycler() {
        adapter = EpisodeAdapter(seriesList, episodeViewDownloadHelper, DataController.AUDIO_SERIES_PAGE, series, extraData = extraData, CHANNEL_AUDIOSERIES_PAGE)

        binding.rvEpisode.addItemDecoration(
            RecyclerViewMargin(
                ctx.resources.getDimensionPixelSize(
                    R.dimen.dp1
                )
            )
        )

        binding.rvEpisode.adapter = adapter
    }

    private fun initSimilarContentUi() {
        pagerAdapter = SimilarCatalogPagerAdapter(this, seriesId, seriesList, series, extraData, CHANNEL_AUDIOSERIES_PAGE, "Audioseries_Catalog_Page")
        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.isUserInputEnabled = false

        val tabNames =
            BaseApplication.getBaseAppContext().resources.getStringArray(R.array.similar_content_tabs_name)

        TabLayoutMediator(binding.tabLayout, binding.viewPager, true, false) { tab, position ->
            tab.setCustomView(R.layout.tab_layout_custom)
            tab.customView?.let { view ->
                view.findViewById<TextView>(R.id.tabTitle)?.text = tabNames[position]

                if (position == 1) {
                    if (!PrefUtils.isSimilarCatalogTabClicked) {
                        view.findViewById<ImageView>(R.id.tabBadge).visibility = VISIBLE
                    } else {
                        view.findViewById<ImageView>(R.id.tabBadge).visibility = GONE
                    }
                }
            }
        }.attach()

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }
        })

        updateTabText(binding.tabLayout.getTabAt(0))

        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                if (position == 1) {
                    PrefUtils.isSimilarCatalogTabClicked = true
                    removeBadge(binding.tabLayout.getTabAt(1))
                }
            }
        })
    }

    private fun updateTabText(tab: TabLayout.Tab?) {
        tab?.customView?.let { view ->
            view.findViewById<TextView>(R.id.tabTitle).let { tabTitle ->
                if (tab.isSelected) {
                    tabTitle.typeface = Typeface.create(tabTitle.typeface, Typeface.BOLD)
                    tabTitle.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                } else {
                    tabTitle.typeface = Typeface.create(tabTitle.typeface, Typeface.NORMAL)
                    tabTitle.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                }
            }
        }
    }

    private fun removeBadge(tab: TabLayout.Tab?) {
        tab?.customView?.let { view ->
            view.findViewById<ImageView>(R.id.tabBadge).visibility = GONE
        }
    }

    private fun handleCatalogPlayButton(playerEvent: PlayerEvent) {
        if (DataController.playingEntityId == seriesId) {
            binding.mediaButton.handleMusicButton(playerEvent)
        }
    }

    private fun handleEpisodeViewPlayStates(playerEvent: PlayerEvent) {
        seriesList.filter { content ->
            (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content.isPlaying = false
            content.showLoader = false

            if (playerEvent.prevData?.id == content.id && playerEvent.prevData?.timeElapsed != null) {
                content.meta?.timeElapsed = playerEvent.prevData.timeElapsed
            }

            val index = seriesList.indexOf(content)
            if (index != -1) {
                adapter?.notifyItemChanged(index)
                pagerAdapter?.updateEpisodeAtIndex(index)
            }
        }

        val content = seriesList.find {
            it.id == playerEvent.exoData?.id
        }
        mediaContent = content
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER

                if (playerEvent.currentContentPositionMs != null) {
                    content.meta?.timeElapsed = playerEvent.currentContentPositionMs.msToSeconds().toLong()
                }
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
                if (playerEvent.currentContentPositionMs != null) {
                    content.meta?.timeElapsed = playerEvent.currentContentPositionMs.msToSeconds().toLong()
                }
            }
            val index = seriesList.indexOf(content)
            if (index != -1) {
                adapter?.notifyItemChanged(index)
                pagerAdapter?.updateEpisodeAtIndex(index)
            }
        }
    }

    private fun updateFollow() {
        if (isFollowChannel == 0.0) {
            binding.txtFollow.text = getString(R.string.subscribe)
            binding.layoutFollow.isSelected = false
            binding.txtFollow.isSelected = false
            binding.imgFollow.setImageResource(R.drawable.ic_follow_yellow)
        } else {
            binding.layoutFollow.isSelected = true
            binding.txtFollow.isSelected = true
            binding.txtFollow.text = getString(R.string.subscribed)
            binding.imgFollow.setImageResource(R.drawable.ic_follow_yellowed)
        }

        series?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            isFollow.actionValue = isFollowChannel
        }

        handleCatalogInfo()
    }

    private fun handleCatalogInfo() {
        Utils.getCatalogContract(series?.data).let { data ->
            binding.txtEpsCount.text =
                (series?.meta?.contentCount ?: 0).toString()
            binding.txtCount.text = Utils.abbrNumberFormat(series?.meta?.aggregations?.followers ?: 0)
        }
    }

    private fun getDataFromCache(ignoreCache: Boolean = false) {
        if (ignoreCache) {
            getChannelDetail()
        } else {
            CacheUtils.getParsedCachedData<BaseModel<Channel>>(this, seriesId) { data ->
                if (data != null) {
                    handleChannelResponse(data, "", true)
                }
                getChannelDetail()
            }
        }
    }

    private fun getChannelDetail(limit : Int = 5, from : String = "", offset: Int = -1) {

        val map = HashMap<String, String>()
        if (offset != -1) {
            map["offset"] = offset.toString()
        } else {
            map["page"] = "1"
        }
        map["limit"] = limit.toString()
        map["sort"] = if (series?.selectedFilter.equals(PopupFilterMenu.FILTER_DESC, true)) {
            "DESC"
        } else {
            "ASC"
        }
        map["orderBy"] = "published_at"

        if (isDetached || isRemoving || !isAdded)
            return

        podcastViewModel.getChannelDetail(seriesId, map, false).observe(viewLifecycleOwner) { resource ->

            when(resource?.status) {
                ResponseStatus.SUCCESS -> {
                    if (resource.data?.data != null) {
                        resource.data.data?.selectedFilter = null
                        handleChannelResponse(resource.data, from)
                        //displayGamAd()
                    } else {
                        handleError(resource.message)
                    }
                }
                ResponseStatus.ERROR -> {
                    handleError(resource.message)
                }
                else -> {

                }
            }
        }
    }

    private fun displayGamAd() {
        if ((!PrefUtils.appCDNConfig?.nativeAdsConfig?.nativeAdUnitId.isNullOrEmpty() ||
                    !PrefUtils.appCDNConfig?.nativeAdsConfig?.customNativeAdUnitId.isNullOrEmpty()) &&
            PrefUtils.userDetails?.isSubscribed == false
        ) {
            GaManager.refreshAd(series?.id, null, getGenresAsString())
        }
    }

    private fun handleError(message :String?) {
        if (series == null) {
            binding.errorView.showError(message, type = "Audioseries_Catalog_Page", errorName = message)
        } else {
            hideLoading()
        }
    }

    private fun handleChannelResponse(
        data: BaseModel<Channel>?,
        from: String,
        cachedData: Boolean = false
    ) {
        if (data?.data?.selectedFilter.isNullOrEmpty()) {
            data?.data?.selectedFilter = if (!series?.selectedFilter.isNullOrEmpty()) {
                series?.selectedFilter
            } else {
                PopupFilterMenu.FILTER_ASC
            }
        }

        series = data?.data

        series?.contents?.let { contents ->
            allContents.addAll(contents)
        }

        binding.txtFilter.text = series?.selectedFilter

        adapter?.setCatalog(series)

        if (!cachedData) {
            CacheUtils.putDataToCache(LocalCacheData(seriesId, Gson().toJson(data)))
        }

        if (series?.displayAdsEnabled == true && !cachedData && PrefUtils.userDetails?.isSubscribed == false) {
            getDisplayAds()
        }

        when (from) {
            "filter" -> {
                seriesList.clear()
                if ((series?.contents?.size ?: 0) > 4) {
                    series?.contents?.take(4)?.let { episodes ->
                        seriesList.addAll(episodes)
                    }
                } else {
                    seriesList.addAll(series?.contents ?: ArrayList())
                }
                adapter?.notifyDataSetChanged()
            }
            else -> {

                setChannelDetail(cachedData)

                if (DataController.playingEntityId == seriesId) {
                    val buttonState = ctx.getPlayerActivity()?.getContentPlayState(DataController.playerContentId)
                    if (buttonState?.playWhenReady == true) {
                        handleCatalogPlayButton(PlayerEvent(PlayerEvent.PLAY))
                    }
                    if (buttonState?.waiting == true) {
                        handleCatalogPlayButton(PlayerEvent(PlayerEvent.SHOW_LOADER))
                    }
                }

                if (!series?.users.isNullOrEmpty()) {
                    binding.rvArtist.visibility = VISIBLE
                    userList.clear()
                    userList.addAll(series?.users ?: ArrayList())
                    artistAdapter?.notifyDataSetChanged()
                } else {
                    binding.rvArtist.visibility = GONE
                }

                hideLoading()

                if (!cachedData) {
                    sendEvents()
                }
            }
        }
    }

    private fun sendEvents() {

        AnalyticsUtil.sendEventForCatalog(
            "catalog_page_opened",
            seriesId,
            series?.title ?: "",
            "",
            extraData = extraData
        )

        extraData?.apply {
            catalogLink = "${Constants.WEB_BASE_URL}catalog/${series?.id}"
        }

        MoEngageAnalytics.sendEventForPodcastDetail(
            ctx,
            "detail page viewed",
            source,
            pageSource,
            series,
            extraData
        )

        AnalyticsUtil.sendEvent(
            FirebaseAnalytics.Event.SCREEN_VIEW,
            Bundle().apply {
                putString(
                    FirebaseAnalytics.Param.SCREEN_NAME,
                    "Audioseries_Catalog_Page"
                )
                putString("previousScreen", source)
                putString("catalogTitle", series?.title)
                putString("catalogId", series?.id)
                putString("entitiySubtype", series?.type)
            })
    }

    private fun setChannelDetail(isCached: Boolean = false) {
        if (isDetached || isRemoving || !isAdded)
            return
        Utils.getCatalogContract(series?.data).let { data ->

            if (!data?.donationUrl.isNullOrEmpty()) {
                binding.donateView.setRedirectionUrl(data?.donationUrl.toString())
                binding.donateView.visibility = VISIBLE
            } else {
                binding.donateView.visibility = GONE
            }

            if (PrefUtils.firebaseVariantQuickPicks?.variant == ExperimentUtils.VARIANT_B) {
                if (!isCached) {
                    pagerAdapter?.setRssAuthorData(data)
                }
            } else {
                val addInfo = ArrayList<RssAuthor>()

                if (!data?.rssAuthor.isNullOrEmpty()) {
                    data?.rssAuthor?.groupBy { author ->
                        author.role ?: ""
                    }?.forEach { groupedMap ->
                        groupedMap.value.map { mappedAuthor ->
                            mappedAuthor.name ?: ""
                        }.let { namesList ->
                            val names = Utils.getAuthorString(ArrayList(namesList))
                            addInfo.add(RssAuthor(names, groupedMap.key))
                        }
                    }
                }

                if (!data?.copyright.isNullOrEmpty()) {
                    addInfo.add(RssAuthor(data?.copyright, getString(R.string.copyright)))
                }

                if (addInfo.isNotEmpty()) {

                    if (rssAuthorAdapter == null) {
                        rssAuthorAdapter = RssAuthorAdapter()
                        binding.authorGridRecycler.adapter = rssAuthorAdapter
                        binding.authorGridRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp1)))
                    }
                    rssAuthorAdapter?.setData(addInfo)

                    binding.addInfoTitle.visibility = VISIBLE
                    binding.authorGridRecycler.visibility = VISIBLE
                } else {
                    binding.addInfoTitle.visibility = GONE
                    binding.authorGridRecycler.visibility = GONE
                }
            }
        }

        if (!series?.contents.isNullOrEmpty()) {

            seriesList.clear()

            if ((series?.contents?.size ?: 0) > 4) {
                series?.contents?.take(4)?.let { episodes ->
                    seriesList.addAll(episodes)
                }

                val text = "Lihat semua episode (" + (series?.meta?.contentCount ?: 0) + ")"
                binding.txtLihatSemua.text = text
                binding.txtLihatSemua.visibility = VISIBLE
            } else {
                seriesList.addAll(series?.contents ?: ArrayList())
                binding.txtLihatSemua.visibility = GONE
            }

            adapter?.notifyDataSetChanged()

            if (!isCached) {
                pagerAdapter?.setCatalog(series)
            }
        }

        handleCatalogInfo()

        binding.txtTitle.text = series?.title

        if (!series?.description.isNullOrEmpty() || !series?.htmlDescription.isNullOrEmpty()) {

            val description = if (!series?.htmlDescription.isNullOrEmpty()) {
                series?.htmlDescription.toString()
            } else {
                series?.description.toString()
            }

            binding.txtEpisodDesc.setText(
                description
            )

            binding.txtEpisodDesc.setListener(object : ReadMoreTextView.Listener {
                override fun onExpandClicked(isSeeMore: Boolean) {
                    if (isSeeMore) {
                        val containsURL =
                            if (description.contains("https://") || description.contains("http://")) {
                                "yes"
                            } else {
                                "no"
                            }

                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", CHANNEL_AUDIOSERIES_PAGE)
                        bundle.putAnalyticsKey("catalogId", series?.id)
                        bundle.putAnalyticsKey("catalogTitle", series?.title)
                        bundle.putAnalyticsKey("entitySubType", series?.type)
                        bundle.putAnalyticsKey("containsURL", containsURL)
                        AnalyticsUtil.sendEvent("expand_catalog_description", bundle)
                    }
                }

                override fun onLinkClicked(uri: Uri?) {
                    val url = uri?.toString() ?: return
                    if (url.isNotEmpty()) {
                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", CHANNEL_AUDIOSERIES_PAGE)
                        bundle.putAnalyticsKey("catalogId", series?.id)
                        bundle.putAnalyticsKey("catalogTitle", series?.title)
                        bundle.putAnalyticsKey("entitySubType", series?.type)
                        bundle.putAnalyticsKey("linkDetail", url)
                        AnalyticsUtil.sendEvent("description_link_clicked", bundle)
                    }
                }
            })

            binding.txtEpisodDesc.visibility = VISIBLE
        } else {
            binding.txtEpisodDesc.visibility = GONE
        }

        ImageUtils.loadImageByUrl(binding.imgPodcast, series?.imageMeta?.size300, placeHolder = R.drawable.ic_audiobook_default, originalUrl = series?.imageMeta?.size300)
        ImageUtils.showBlurImage(series?.imageMeta?.size300, binding.imgCover)

        title = series?.title.toString()
        binding.txtToolbarTitle.text = title

        series?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            if(isFollow.actionValue != null) {
                isFollowChannel = isFollow.actionValue!!
                updateFollow()
            }
        }

        if (series?.genres.isNullOrEmpty()) {
            binding.rvGenre.visibility = GONE
        } else {
            val genreAdapter = GenreAdapter(ctx, series?.genres ?: ArrayList(),"Audioseries_Catalog_Page")
            if (binding.rvGenre.itemDecorationCount > 0) {
                binding.rvGenre.removeItemDecorationAt(0)
            }
            binding.rvGenre.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(
                        R.dimen.dp8
                    )))
            binding.rvGenre.adapter = genreAdapter
            binding.rvGenre.visibility = VISIBLE
        }

        if (playContentDirectly) {
            playContentDirectly = false
            binding.mediaButton.performClick()
        }

        setRssContent()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayerAction(event: EventMessage) {
        if (event.eventCode == Constants.MARK_AS_PLAYED){
            markAsPlayed(event.data as Content)
            adapter?.notifyDataSetChanged()
        } else if(event.eventCode == Constants.MARK_FROM_EPISODE){
            adapter?.notifyDataSetChanged()
        } else if (event.eventCode == USER_ACTIVITY_FOLLOW){
            if (isSamePage){
                isSamePage = false
            } else {
                val data = event.data as MediaAction
                val followAction =  series?.meta?.userActions?.find { mediaAction ->
                    mediaAction.action.equals(UserAction.FOLLOW.action, true)
                }
                isFollowChannel = data.actionValue?:0.0
                if (followAction != null){
                    followAction.actionValue = data.actionValue
                   val index =  series?.meta?.userActions?.indexOf(followAction)
                    series?.meta?.userActions?.set(index?:0,followAction)
                }else{
                    series?.meta?.userActions?.add(data)
                }
                isSamePage = false
                updateFollow()
            }
        }
    }

    private fun markAsPlayed(content: Content) {
        val analyticsAction: String
        val action: String
        if (content.meta?.markedAsPlayed == true){
                action = EventConstant.MARK_PLAYED
                analyticsAction = "content_marked_as_played"
            }else{
                action = EventConstant.MARK_UN_PLAYED
                analyticsAction = "content_marked_as_unplayed"
            }

        val payload = Payload(content.meta?.timeElapsed?.toInt(),content.id,"content",content.catalog?.type,AnalyticsUtil.audioseries_channel,PrefUtils.uniqueId,content.duration?.toInt(),
            catalogId = content.catalog?.id ?: "")

        val event = EventPost(action,payload,contentId = content.id?:"")
        AnalyticsUtil.sendEvent(
            content.catalog?.type,
            content.id,
            seriesId,
            analyticsAction,
            AnalyticsUtil.audioseries_channel,
            content.meta?.timeElapsed.toString(),
            content.duration.toString(),
            catalogTitle = content.catalog?.title?:"",
            contentTitle =  content.title?:""
        )

        if (NetworkUtils.isNetworkConnected(ctx)){
            podcastViewModel.postEvent(event)
        }else{
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
    }

    private fun setRssContent() {
        val source = if (!series?.source.isNullOrEmpty()) {
            series?.source ?: ""
        } else if (seriesList.isNotEmpty() && !seriesList[0].catalog?.source.isNullOrEmpty()) {
            seriesList[0].catalog?.source ?: ""
        } else {
            ""
        }
        if (series?.type != Constants.ENTITY_TYPE_LIVE_STREAM && (source.equals(SOURCE_EXCLUSIVE, true) || source.equals(SOURCE_ORIGINAL, true) || source.equals(SOURCE_SPECIAL, true))) {
            binding.origExc.text = source.uppercase()
            binding.origExc.visibility = VISIBLE
        } else {
            binding.origExc.visibility = GONE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {
        Handler(Looper.getMainLooper()).postDelayed({
            seriesList.find { it.isPremium == true }?.let {
                if (it.catalogId == seriesId) {
                    getDataFromCache(true)
                }
            }
        },500)
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.channel_loader)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    override fun onOffsetChanged(appBarLayout: AppBarLayout?, offset: Int) {
        val maxScroll = appBarLayout?.totalScrollRange
        val percentage = abs(offset).toFloat() / (maxScroll?.toFloat() ?: 1f)

        handleAlphaOnTitle(percentage)
        handleToolbarTitleVisibility(percentage)
    }

    private fun handleToolbarTitleVisibility(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR) {
            if (!mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    VISIBLE
                )
                mIsTheTitleVisible = true
            }
        } else {
            if (mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    View.INVISIBLE
                )
                mIsTheTitleVisible = false
            }
        }
    }

    private fun handleAlphaOnTitle(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_HIDE_TITLE_DETAILS) {
            if (mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, View.INVISIBLE)
                mIsTheTitleContainerVisible = false
            }
        } else {
            if (!mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, VISIBLE)
                mIsTheTitleContainerVisible = true
            }
        }
    }

    private fun startAlphaAnimation(v: View, duration: Int, visibility: Int) {
        val alphaAnimation =
            if (visibility == VISIBLE) AlphaAnimation(0f, 1f) else AlphaAnimation(1f, 0f)
        alphaAnimation.duration = duration.toLong()
        alphaAnimation.fillAfter = true
        v.startAnimation(alphaAnimation)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    /* Display ads are visible when it's active/enabled at both Catalog and Ads api level. */
    private fun getDisplayAds() {
        podcastViewModel.getAds(
            Constants.Ads.AdsEntityType.PAGE,
            Constants.Ads.AdsEntityValue.CATALOG_DETAIL
        ).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (it?.data?.data?.isActive == true) {
                    adsResponse = it.data?.data
                    setAd()
                }
            }
        }
    }

    private fun setAd() {
        val adView = CustomAdView(ctx)
        adView.setData(
            size = adsResponse?.config?.adSize,
            adUnitId = adsResponse?.config?.adId,
            viewGroup = binding.frameAdView,
            genres = getGenresAsString(),
            vertical = ENTITY_TYPE_AUDIO_SERIES,
            isPlayedPreview = mediaContent?.isPurchaseNeeded
        )
        binding.frameAdView.visibility = VISIBLE
        binding.frameAdView.addView(adView)
    }

    private fun getGenresAsString(): String {
        val genresString = series?.genres?.joinToString(",") { genre ->
            genre.name ?: ""
        }
        return genresString?.trim() ?: ""
    }
}