package noice.app.modules.dashboard.home.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.modules.coins.repository.SubscriptionRepository
import noice.app.modules.profile.repository.NotificationApiRepository
import javax.inject.Inject

@HiltViewModel
class HomeFragmentViewModel @Inject constructor(
    private val notificationApiRepository: NotificationApiRepository,
    private val subscriptionRepository: SubscriptionRepository
): ViewModel() {
    fun getUserSubscription(map: HashMap<String, String>) = subscriptionRepository.getUserSubscription(map)

    fun getNotifications(map: HashMap<String, String>, cachingConstant : String?) = notificationApiRepository.getNotifications(map, cachingConstant)
}