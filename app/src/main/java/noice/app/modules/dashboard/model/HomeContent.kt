package noice.app.modules.dashboard.model

import android.os.Parcelable
import com.moengage.cards.core.model.Card
import kotlinx.parcelize.Parcelize
import noice.app.model.ImageMeta
import noice.app.model.TagData
import noice.app.modules.live.model.RoomData
import noice.app.modules.live.model.RoomParticipant
import noice.app.modules.media.model.Community
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.utils.ImageUtils

@Parcelize
data class HomeContent(
    var id: String?,
    val category: String?,
    val contentType : String?,
    var title: String?,
    var subTitle: String?,
    var url: String?,
    var image: String?,
    var imageMeta: ImageMeta?,
    val duration: Long? = 0L,
    var meta: Community?,
    val scheduledTime: String?,
    val contentSchedule: Schedule?,
    var catalog: Channel? = null,
    val publishedAt: String?,
    val seasonName: String?,
    val status: String?,
    val roomParticipants: List<RoomParticipant>? = ArrayList(),
    var source: String?,
    val content: Content?,
    var color: String?,
    val value: String?, // used for Search screen Jelajah segment
    var entitySubType: String?, // for nav-search, type = collection -> content, for handling future scaling
    val icon: String?,
    val tag: String?,
    val vertical: String?,
    var displayAds : Boolean?,
    var type: String?,
    val isVerified: Boolean?,
    val contentNumber: Int?,
    var tags : List<TagData>? = null,
    var isPremium: Boolean? = false,
    val text: String?,
    val textType: String?,
    val videoId: String?,
    val videoLink: String?,
    var earlyAccessBenefits: String?,
    var earlyAccessDescription: String?,
    var earlyAccessFinishDate: String?,
    val videoUrl: String?,
    val viewType: String?, //used for ThemePageView deep-linking
    val isVideoPremium: Boolean?,
    val showVideo: Boolean?,
    val hasVideoAccess: Boolean?,
    val hasPurchased : Boolean?,
    val prerollEnabled: Boolean?,
    val midrollEnabled: Boolean?,
    val totalCuePoints: Int?,
    val subEntityId: String?,
    val isVideo: Boolean?,
    val displayAdsEnabled: Boolean?,
    var route: Route?,
    var previewTime: Long?=0,

    //local
    var segmentTitle: String?,
    var entityType: String?,
    val userId: String?,
    var isPlaying: Boolean? = null,
    var isLive: Boolean = false,
    var showLoader: Boolean = false,
    val genres: ArrayList<Genre>? = null,
    var showRecording: Boolean = false,
    var subtype: String? = null,
    val data: RoomData? = null,
    val badge: String? = null,
    var moEngageImageWidgetId: Int? = null,

    // added field
    var orientation: String? = "landscape"
) : Parcelable {
    constructor(id: String) : this(
        id,
        "",
        null,
        "",
        "",
        "",
        null,
        null,
        0L,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "",
        "",
        "",
        "",
        "",
        "",
        null,
        "",
        false,
        null,
        null,
        false,
        "",
        "",
        null,
        null,
        null,
        null,
        "",
        "",
        "",
        null,
        null,
        null,
        null,
        false,
        false,
        null,
        "",
        false,
        false,
        null,
        0,
        "",
        "",
        "",
        null,
        false,
        false,
        null
    )

    override fun equals(other: Any?): Boolean {
        if (other is HomeContent) {
            return other.id == id
        }
        return false
    }

    override fun hashCode(): Int {
        return id?.hashCode() ?: 0
    }

   /* fun getSmallImage(url: String? = ""): String? {
        return if (!url.isNullOrEmpty()) {
            ImageUtils.getSmallImage(url, "300x300")
        } else {
            ImageUtils.getSmallImage(image ?: "", "300x300")
        }
    }*/

    /*fun getCompressedImage(url: String? = ""): String? {
        return if (!url.isNullOrEmpty()) {
            ImageUtils.getSmallImage(url, "compressed")
        } else {
            ImageUtils.getSmallImage(image ?: "", "compressed")
        }
    }*/

    val isPurchaseNeeded : Boolean
        get() = (isPremium == true && hasPurchased == false)
}