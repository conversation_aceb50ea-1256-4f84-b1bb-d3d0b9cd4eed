package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentCollectionBinding
import noice.app.listner.CustomClickListener
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.adapter.ContinueListeningAdapter
import noice.app.modules.dashboard.collection.adapter.PlayListGridAdapter
import noice.app.modules.dashboard.collection.viewmodel.CollectionViewModel
import noice.app.modules.dashboard.home.activity.CreatePlaylistActivity
import noice.app.modules.media.model.Playlist
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.podcast.model.Content
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_SERIES
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PODCAST
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.utils.Utils.getPlayerActivity
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class CollectionFragment : Fragment() {

    private val viewModel: CollectionViewModel by viewModels()

    private lateinit var binding : FragmentCollectionBinding
    private lateinit var ctx : Context
    private lateinit var continueListeningAdapter : ContinueListeningAdapter
    private lateinit var playListGridAdapter : PlayListGridAdapter
    private var continueListening = ArrayList<Content>()
    private var playList = ArrayList<Playlist>()

    companion object {
        fun newInstance() = CollectionFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCollectionBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        lifecycleScope.launchWhenResumed {
            initViews()
        }
    }

    override fun onResume() {
        super.onResume()

        getData()
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Collection_Page")
            putString("previousScreen", "start")
        })
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handlePlayStates(it)
            }

        binding.continueListening.getRecyclerView().layoutManager = LinearLayoutManager(
            ctx,
            RecyclerView.HORIZONTAL,
            false
        )
        continueListeningAdapter = ContinueListeningAdapter(continueListening, "content",getString(R.string.continue_listening))
        binding.continueListening.getRecyclerView().adapter = continueListeningAdapter
        binding.continueListening.getRecyclerView().addItemDecoration(
            RecyclerViewMargin(
                ctx.resources.getDimensionPixelSize(
                    R.dimen.dp8
                )
            )
        )

        playListGridAdapter = PlayListGridAdapter(playList)
        binding.playListRv.adapter = playListGridAdapter
        binding.playListRv.addItemDecoration(
            RecyclerViewMargin(
                ctx.resources.getDimensionPixelSize(R.dimen.dp16),
                bottomSpacing = ctx.resources.getDimensionPixelSize(R.dimen.dp64)
            )
        )

        binding.downloads.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                ctx.getPlayerActivity()?.handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }
            openFragment(DownloadFragment(), DownloadFragment::class.java.simpleName)
        }

        binding.nestedScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
            if (scrollY > oldScrollY + playList.size && binding.playlistBtn.isExtended) {
                binding.playlistBtn.shrink()
            }

            if (scrollY < oldScrollY - playList.size && !binding.playlistBtn.isExtended) {
                binding.playlistBtn.extend()
            }

            if (scrollY == 0) {
                binding.playlistBtn.extend()
            }
        })

        binding.likedContent.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }
            openFragment(LikedContentFragment(), LikedContentFragment::class.java.simpleName)
            AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                putString(FirebaseAnalytics.Param.SCREEN_NAME,"Collection_Page")
                putString("previousScreen", "Collection_Liked")
            })
        }

        binding.history.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            AnalyticsUtil.sendEventForOpenScreen("", "history_index_opened")
            openFragment(HistoryContentFragment(), HistoryContentFragment::class.java.simpleName)
        }

        binding.yourPodCast.setOnClickListener{
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_PODCAST,"your_podcast_opened")
            openFragment(PodCastCollectionFragment(), PodCastCollectionFragment::class.java.simpleName)
        }

        binding.yourRadio.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_RADIO,"your_radio_opened")
            openFragment(RadioCollectionFragment(), RadioCollectionFragment::class.java.simpleName)
        }

        binding.playlistBtn.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }
            startActivity(Intent(ctx, CreatePlaylistActivity::class.java))
        }

        binding.yourNoiceBook.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_BOOK,"your_audiobook_opened")
            openFragment(AudioBookCollectionFragment(), AudioBookCollectionFragment::class.java.simpleName)
        }

        binding.yourOriginalSeries.setOnClickListener {
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-collection-page")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "collection page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            AnalyticsUtil.sendEventForOpenScreen(ENTITY_TYPE_AUDIO_SERIES,"your_audioseries_opened")
            openFragment(AudioSeriesCollectionFragment(), AudioSeriesCollectionFragment::class.java.simpleName)
        }
    }

    private fun handlePlayStates(playerEvent: PlayerEvent) {
        continueListening.filter { content ->
            (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content.isPlaying = false
            content.showLoader = false

            val index = continueListening.indexOf(content)
            if (index != -1) {
                continueListeningAdapter.notifyItemChanged(index)
            }
        }

        val content = continueListening.find {
            it.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = continueListening.indexOf(content)
            if (index != -1) {
                continueListeningAdapter.notifyItemChanged(index)
            }
        }
    }

    private fun getData() {
        if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
            (ctx as HomeActivity).handleUserNotLoggedIn()
            return
        }

        binding.errorView.showLoading()

        getListeningHistory()

        getUserPlaylist()
    }

    private fun getListeningHistory() {
        val map = HashMap<String, String>()
        map["page"] = "0"
        map["limit"] = "10"

        viewModel.getListeningHistory(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                continueListening.clear()
                continueListening.addAll(it.data)
                val playingContent = continueListening.find { content ->
                    content.id == DataController.playerContentId
                }
                if (playingContent != null && ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                    val index = continueListening.indexOf(playingContent)
                    playingContent.isPlaying = true
                    continueListening[index] = playingContent
                }

                continueListeningAdapter.notifyDataSetChanged()

                binding.continueListening.visibility = View.VISIBLE
            } else {
                binding.continueListening.visibility = View.GONE
            }
        }
    }

    private fun getUserPlaylist() {

        val map = HashMap<String, String>()
        map["page"] = "1"
        map["limit"] = "100"
        map["entityType"] = "playlist"
        map["entitySubType"] = "playlist"

        viewModel.getUserPlaylist(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                playList.clear()
                playList.addAll(it.data)
                playListGridAdapter.notifyDataSetChanged()
                hideLoading()
            } else if (playList.isEmpty()) {
                showEmptyView()
            } else {
                hideLoading()
            }
        }
    }

    private fun hideLoading() {
        binding.emptyView.visibility = View.GONE
        binding.errorView.hide()
    }

    private fun showEmptyView() {
        binding.errorView.hide()
        binding.emptyView.visibility = View.VISIBLE
    }

    private fun openFragment(fragment: Fragment?, clazz : String? = null, tag: String? = null) {
        if(fragment != null) {
            childFragmentManager.beginTransaction()
                .add(R.id.homeContainer, fragment, tag).apply {
                    if(clazz != null) {
                        addToBackStack(clazz)
                    }
                    commitAllowingStateLoss()
                }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        if(event.eventCode == Constants.PLAYLIST_DELETED) {
            if(event.data is Playlist) {
                playList.remove(event.data)
                DataController.playlists?.remove(event.data)
                playListGridAdapter.notifyDataSetChanged()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {
        getData()
    }

    override fun onSaveInstanceState(outState: Bundle) {}

    fun handleBackPress() {
        when {
            childFragmentManager.backStackEntryCount > 0 -> {
                childFragmentManager.popBackStack()
            }
            else -> {
                (ctx as HomeActivity).openHome()
            }
        }
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }
}