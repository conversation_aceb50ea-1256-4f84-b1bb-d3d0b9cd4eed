package noice.app.modules.dashboard.collection.adapter

import android.os.Bundle
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.DataController
import noice.app.listner.CustomClickListener
import noice.app.listner.OnClickInterface
import noice.app.modules.coins.fragments.VipSubscriptionDialog
import noice.app.model.ExtraData
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.player.managers.QueueManager
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.*
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.getBaseActivity
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.unwrap
import noice.app.views.EpisodeView

class EpisodeLoadMoreAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Content?>,
    listener: LoadMoreAdapterListener?,
    private val onClickInterface: OnClickInterface<Pair<Content, String>>?,
    private var entityType : String,
    private val type:String,
    private var queueTitle:String,
    private var catalog:Channel?,
    private var extraData: ExtraData? = null
) : LoadMoreAdapter<Content>(recyclerView, dataSet, listener) {

    private lateinit var episodeView: EpisodeView
    
    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        episodeView = EpisodeView(parent.context, onClickInterface)
        episodeView.setExtraData(extraData)
        return episodeView.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {

        episodeView.viewHolder = holder as EpisodeView.ViewHolder

        if (!dataSet[position]?.catalogType.isNullOrEmpty()){
            dataSet[position]?.entitySubType = dataSet[position]?.catalogType
        } else {
            dataSet[position]?.entitySubType = entityType
        }

        dataSet[position]?.let { content ->
            when (type) {
                DataController.DOWN_LOAD_PAGE -> {
                    episodeView.setDownloadedData(content,catalog)
                }
            }
        }

        episodeView.viewHolder.priceLayout.setOnClickListener {
            val content = dataSet[position] ?: return@setOnClickListener
            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-vip-content-catalog")
                it.context?.getBaseActivity()?.handleUserNotLoggedIn(loginDialogData = loginDialogData)
            } else {
                it.context.fetchActivity()?.supportFragmentManager?.let { fm ->
                    ClickHandler.openPurchaseDialog(fm, content, type)
                }
            }
        }

        episodeView.viewHolder.playButtonLayout.setOnClickListener {
            val ctx = it.context?.getPlayerActivity()?.activity() ?: return@setOnClickListener

            if (DataController.isAdPlaying) {
                Utils.showSnackBar(ctx, ctx.getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            if (dataSet.size <= position)
                return@setOnClickListener

            if (DataController.playerContentId == dataSet[position]?.id) {
                if (ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, false)
                    return@setOnClickListener
                } else if (DataController.playingFromWhere == type) {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, true)
                    return@setOnClickListener
                }
            }

            val list = Utils.trimList(ctx, ArrayList(dataSet.subList(position, dataSet.size))).filterIsInstance<Content>()

            if (list.isEmpty()) {
                return@setOnClickListener
            }

            when (type) {
                DataController.DOWN_LOAD_PAGE -> {
                    ContentPlayRequest.Builder()
                        .clearQueue(true)
                        .contents(list)
                        .extraData(extraData)
                        .queueTitle(queueTitle.lowercase())
                        .pageSource(queueTitle.lowercase())
                        .play(ctx)
                }
            }

            val content = list[0]

            AnalyticsUtil.sendEvent(
                content.catalog?.type ?:content.catalogType?: "",
                content.id ?: "",
                content.id,
                Constants.EVENT_CONTENT_QUEUED_AUTO,
                type,
                (content.meta?.timeElapsed ?: 0).toString(),
                (content.duration ?: 0).toString(),
                "",
                content.catalog?.title ?: "",
                content.title ?: ""
            )

            DataController.setCurrentlyPlaying(type, catalog?.id ?: "", DataController.CONTENT_LIST)

            dataSet[position]?.let { con ->
                onClickInterface?.dataClicked(Pair(con, ""), Constants.EPISODE_VIEW_PLAY_CLICK)
            }

            dataSet[position]?.isPlaying = true
            notifyItemChanged(position)
        }

        episodeView.viewHolder.imgQue.setOnClickListener(CustomClickListener ({
            val ctx = episodeView.context?.unwrap()

            if (dataSet.size <= position || ctx == null)
                return@CustomClickListener

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(ctx, ctx.getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            val content = dataSet[position]

            if (content != null) {

                ContentPlayRequest.Builder()
                    .contents(listOf(content))
                    .pageSource(AnalyticsUtil.episode_page)
                    .addToManualQueue(ctx)

                AnalyticsUtil.sendEvent(
                    content.catalog?.type ?: "",
                    content.id ?: "",
                    content.id,
                    Constants.EVENT_CONTENT_QUEUED_AUTO,
                    type,
                    (content.meta?.timeElapsed ?: 0).toString(),
                    (content.duration ?: 0).toString(),
                    "",
                    content.catalog?.title ?: "",
                    content.title ?: ""
                )

            }
        },dataSet[position]?.catalog?.type + " page"))
    }
}