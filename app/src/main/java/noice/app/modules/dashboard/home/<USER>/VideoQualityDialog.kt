package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.FragmentVideoQualityDialogBinding
import noice.app.modules.media.adapter.VideoQualityAdapter
import noice.app.player.models.PlayerEvent

@AndroidEntryPoint
class VideoQualityDialog : BottomSheetDialogFragment() {

    companion object {
        private const val CONTENT_ID = "CONTENT_ID"
        fun newInstance(contentId: String) = VideoQualityDialog().apply {
            arguments = Bundle().apply {
                putString(CONTENT_ID, contentId)
            }
        }
    }

    private lateinit var ctx: Context
    private lateinit var binding: FragmentVideoQualityDialogBinding
    private var contentId = ""
    private var videoQualityAdapter: VideoQualityAdapter? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        contentId = arguments?.getString(CONTENT_ID) ?: ""
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentVideoQualityDialogBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    private fun initViews() {
        videoQualityAdapter = VideoQualityAdapter(DataController.videoQualityList) {
            PlayerEvent(PlayerEvent.VIDEO_QUALITY_CHANGED).sendPlayerEvent()
            dismiss()
        }
        binding.videoQualityRecyclerView.adapter = videoQualityAdapter
    }
}