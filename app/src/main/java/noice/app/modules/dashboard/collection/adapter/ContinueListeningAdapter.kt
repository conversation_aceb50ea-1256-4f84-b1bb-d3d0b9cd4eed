package noice.app.modules.dashboard.collection.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.data.DataController
import noice.app.modules.podcast.model.Content
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.unwrap
import noice.app.views.homesegmentviews.RecentlyPlayedSegment

class ContinueListeningAdapter(val data : ArrayList<Content>, private val entityTye:String, val queueTitle:String) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var content : RecentlyPlayedSegment

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        content = RecentlyPlayedSegment(parent.context,entityTye,"","Collection")
        return content.viewHolder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        content.viewHolder = holder as RecentlyPlayedSegment.ViewHolder
        content.setData(data[position])
        content.viewHolder.itemView.setOnClickListener {

            val ctx = it.context.unwrap()

            if (DataController.isAdPlaying) {
                Utils.showSnackBar(it.context, it.context.getString(R.string.playback_will_resume_after_ad))
                return@setOnClickListener
            }

            if (DataController.playingFromWhere == DataController.RECENTLY_PLAYED && DataController.playerContentId == data[position].id) {
                if (ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, false)
                } else {
                    ContentPlayRequest.Builder()
                        .playWhenReady(ctx, true)
                }
                return@setOnClickListener
            }

            ContentPlayRequest.Builder()
                .contents(listOf(data[position]))
                .pageSource(AnalyticsUtil.index_continue_listening)
                .queueTitle(queueTitle)
                .contentFetchLimit(20)
                .addCatalogContents(true)
                .play(ctx)

            DataController.setCurrentlyPlaying(DataController.RECENTLY_PLAYED, DataController.RECENTLY_PLAYED)
        }
    }

    override fun getItemCount(): Int {
        return data.size
    }
}