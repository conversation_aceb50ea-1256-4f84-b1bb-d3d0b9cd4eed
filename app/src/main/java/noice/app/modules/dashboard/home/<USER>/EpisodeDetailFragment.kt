package noice.app.modules.dashboard.home.fragment

import android.Manifest
import android.content.Context
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.*
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.view.animation.AlphaAnimation
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.media3.exoplayer.offline.Download
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnItemTouchListener
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.firebase.analytics.FirebaseAnalytics
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.data.DataController
import noice.app.data.DataController.CONTENT_PAGE
import noice.app.data.DataController.PRIMARY_PLAY_BUTTON
import noice.app.databinding.FragmentEpisodeDetailBinding
import noice.app.enums.BadgeType
import noice.app.enums.Priority
import noice.app.enums.UserAction
import noice.app.exoplayer.ExoPlayerDownloadTracker
import noice.app.exoplayer.ExoplayerUtils
import noice.app.listner.CustomClickListener
import noice.app.listner.OnClickInterface
import noice.app.model.ExoNotificationData
import noice.app.model.ExtraData
import noice.app.model.ads.AdsResponse
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.model.user.User
import noice.app.modules.ads.GaManager
import noice.app.modules.chat.block.BlockUserRequest
import noice.app.modules.chat.model.PinComment
import noice.app.modules.coins.model.SubscriptionPackage
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.dialog.CommentDialog
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.podcast.fragment.EpisodeListFragment
import noice.app.modules.podcast.fragment.PodCastPagerFragment
import noice.app.modules.podcast.adapter.ArtistAdapter
import noice.app.modules.podcast.adapter.ArtistAdapter.Companion.CREATOR_VIEW
import noice.app.modules.podcast.adapter.CommentAdapter
import noice.app.modules.podcast.adapter.EpisodeGenreAdapter
import noice.app.modules.podcast.model.*
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.modules.profile.fragment.userprofile.UserProfileViewModel
import noice.app.observers.GlobalObservers
import noice.app.player.managers.QueueManager
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.room.Downloads
import noice.app.utils.*
import noice.app.utils.Constants.Companion.COMMENT_DELETED
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.EVENT_CONTENT_DOWNLOAD_CLICKED
import noice.app.utils.Constants.Companion.EVENT_PODCAST_PAGE_OPENED
import noice.app.utils.Constants.Companion.EVENT_QUEUED_MANUAL
import noice.app.utils.Constants.Companion.PIN_COMMENT
import noice.app.utils.Constants.Companion.POST_COMMENT
import noice.app.utils.Constants.Companion.SOURCE_EXCLUSIVE
import noice.app.utils.Constants.Companion.SOURCE_ORIGINAL
import noice.app.utils.Constants.Companion.SOURCE_SPECIAL
import noice.app.utils.Constants.Companion.UNPIN_COMMENT
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.unwrap
import noice.app.views.CustomAdView
import noice.app.views.PopupFilterMenu
import noice.app.views.ReadMoreTextView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

@AndroidEntryPoint
class EpisodeDetailFragment : Fragment(), AppBarLayout.OnOffsetChangedListener {

    companion object {

        private const val PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR = 0.6f
        private const val PERCENTAGE_TO_HIDE_TITLE_DETAILS = 0.5f
        private const val ALPHA_ANIMATIONS_DURATION = 600
        private const val EPISODE_ID = "EPISODE_ID"
        private const val ADD_COMMENT = "ADD_COMMENT"
        private const val SUB_COMMENT = "SUB_COMMENT"
        private const val SOURCE = "SOURCE"
        private const val PAGE_SOURCE = "PAGE_SOURCE"
        private const val PLAY_CONTENT_DIRECTLY = "PLAY_CONTENT_DIRECTLY"
        private const val EXTRA_DATA = "EXTRA_DATA"
        private const val PODCAST_EPISODE_PAGE = "Podcast_Episode_Page"

        fun newInstance(episodeId: String, source: String, pageSource : String, playContentDirectly: Boolean = false, extraData: ExtraData? = null) = EpisodeDetailFragment().apply {
            val bundle = Bundle()
            bundle.putString(EPISODE_ID, episodeId)
            bundle.putString(SOURCE, source)
            bundle.putString(PAGE_SOURCE, pageSource)
            bundle.putBoolean(PLAY_CONTENT_DIRECTLY, playContentDirectly)
            bundle.putParcelable(EXTRA_DATA, extraData)
            arguments = bundle
        }
    }

    private val viewModel: ChannelPodcastViewModel by viewModels()
    private val profileViewModel: UserProfileViewModel by viewModels()

    private lateinit var binding: FragmentEpisodeDetailBinding
    private lateinit var ctx: Context
    private var comments = ArrayList<Comment?>()
    private var artists = ArrayList<User>()
    private var episode: Content? = null
    private var artistAdapter: ArtistAdapter? = null
    private var genreAdapter: EpisodeGenreAdapter? = null
    private var commentAdapter: CommentAdapter? = null
    private var episodeId = ""
    private var mIsTheTitleVisible = false
    private var mIsTheTitleContainerVisible = true
    private var commentDialog: CommentDialog? = null
    private var isFollowedChannel = 0.0
    private var downloadListener : ExoPlayerDownloadTracker.DownloadProgressListener? = null
    private var downloadState = -1
    private var source : String ?= null
    private var pageSource : String = ""
    private var isPostedBySelf = false
    private var playContentDirectly = false
    private var extraData: ExtraData? = null
    private lateinit var pinUnpinComment : PinComment
    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null
    private var adsResponse: AdsResponse? = null
    private var purchaseDialog: BottomSheetDialogFragment? = null
    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        episodeId = arguments?.getString(EPISODE_ID, "") ?: ""
        source = arguments?.getString(SOURCE, "") ?: ""
        pageSource = arguments?.getString(PAGE_SOURCE, "") ?: ""
        playContentDirectly = arguments?.getBoolean(PLAY_CONTENT_DIRECTLY) ?: false
        extraData = arguments?.parcelable(EXTRA_DATA)
        extraData?.t?.let {
            val timeStamp = extraData?.t?.toInt()?.takeIf { it > 0 } ?: 0
            extraData?.t = timeStamp.toLong()
        }

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentEpisodeDetailBinding.inflate(inflater, container, false)
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setToolbar()

        initViews()

        getData()
    }

    override fun onStart() {
        super.onStart()

        if (episode?.catalog?.type == "audiobook") {
            MoEngageAnalytics.setOrResetInAppContext(setOf("content audiobook"))
        } else {
            MoEngageAnalytics.setOrResetInAppContext(setOf("content podcast"))
        }
    }

    override fun onResume() {
        super.onResume()

        MoEInAppHelper.getInstance().showInApp(ctx)
        MoEInAppHelper.getInstance().showNudge(ctx)
    }

    private fun setToolbar() {
        binding.threeDotMenu.setOnClickListener {
            val bundle = Bundle()
            bundle.putParcelable(NoiceContentMenu.EPISODE, episode)
            bundle.putBoolean(NoiceContentMenu.IS_FROM_EPISODE, true)
            bundle.putString(NoiceContentMenu.ENTITY_SUB_TYPE, episode?.catalog?.type.toString())

            bundle.putString(NoiceContentMenu.PAGE_SOURCE, AnalyticsUtil.episode_page)
            val noiceMenu =
                NoiceContentMenu()
            noiceMenu.arguments = bundle
            noiceMenu.show(
                childFragmentManager,
                "noiceMenu"
            )
        }

        binding.toolbar.setNavigationOnClickListener {
            Utils.hideKeyboard(binding.toolbar)
            (ctx as AppCompatActivity).onBackPressed()
        }
    }

    private fun initViews() {

        binding.rvUser.addOnItemTouchListener(object : OnItemTouchListener {
            override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                when (e.action) {
                    MotionEvent.ACTION_MOVE -> rv.parent.requestDisallowInterceptTouchEvent(false)
                    MotionEvent.ACTION_UP -> rv.parent.requestDisallowInterceptTouchEvent(true)
                }
                return false
            }

            override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {

            }
            override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
                Log.d("disable",disallowIntercept.toString())
            }
        })

        genreAdapter = EpisodeGenreAdapter(episode?.catalog?.type + "_Episode_Page")
        binding.genreRecyclerView.adapter = genreAdapter
        binding.genreRecyclerView.addItemDecoration(
            RecyclerViewMargin(
                ctx.resources.getDimensionPixelSize(
                    R.dimen.dp8
                )
            )
        )

        binding.priceLayout.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                (ctx.unwrap() as HomeActivity).handleUserNotLoggedIn(source = "episode_page")
                return@setOnClickListener
            }
            if (purchaseDialog?.isAdded != true) {
                purchaseDialog = ClickHandler.openPurchaseDialog(childFragmentManager, episode, "episode_page")
            }
            return@setOnClickListener
        }

        binding.downloadLayout.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-download")
                (ctx as HomeActivity).handleUserNotLoggedIn(source = "episode_page", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            when (downloadState) {
                Download.STATE_COMPLETED -> {
                    NoiceAlertDialog.Builder()
                        .setTitle(ctx.getString(R.string.delete_download))
                        .setMessage(ctx.getString(R.string.delete_dowload_msg))
                        .setListener(object : OnClickInterface<Boolean> {
                            override fun dataClicked(data: Boolean, eventId: Int) {
                                if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                                    ExoplayerUtils.removeDownload(episodeId)
                                    downloadState = -1
                                    manageState()
                                }
                            }
                        })
                        .show(childFragmentManager)
                }
                -1, Download.STATE_STOPPED, Download.STATE_FAILED -> {
                    episode?.let { episodeData ->
                        downloadState = Download.STATE_QUEUED
                        manageState()
                        startDownload(episodeData)

                        AnalyticsUtil.sendEvent(
                            episode?.catalog?.type ?: "",
                            episode?.id ?: "",
                            episode?.id,
                            EVENT_CONTENT_DOWNLOAD_CLICKED,
                            episode?.catalog?.type + "_page",
                            (episode?.meta?.timeElapsed ?: 0).toString(),
                            (episode?.duration ?: 0).toString(),
                            "",
                            episode?.catalog?.title ?: "",
                            episode?.title ?: ""
                        )
                    }
                }
                else -> {
                    ExoplayerUtils.stopDownload(episodeId)
                }
            }
        }

        binding.addToQueLayout.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (ctx as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            episode?.let { it1 ->

                ContentPlayRequest.Builder()
                    .contents(listOf(it1))
                    .pageSource(AnalyticsUtil.episode_page)
                    .addToManualQueue(ctx)

                AnalyticsUtil.sendEvent(
                    it1.catalog?.type ?: "",
                    it1.id ?: "",
                    it1.id,
                    EVENT_QUEUED_MANUAL,
                    it1.catalog?.type + "_page",
                    (it1.meta?.timeElapsed ?: 0).toString(),
                    (it1.duration ?: 0).toString(),
                    "",
                    it1.catalog?.title ?: "",
                    it1.title ?: ""
                )

                MoEngageAnalytics.sendEvent(ctx, "content queued", Bundle().apply {
                    putString("vertical", it1.catalog?.type?: it1.entitySubType?:"")
                    putString("catalog title", it1.catalog?.title?:"")
                    putString("content title", it1.title?:"")
                    putString("catalog id", it1.catalog?.id?:"")
                    putString("content id", it1.id?:"")
                    putString("content duration", (it1.duration?:0).toString())
                    putString("source", it1.catalog?.type + "_page")
                    putStringArrayList("genre", ArrayList(it1.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", it1.source?:"")
                })
            }
        }

        binding.followLayout.setOnClickListener {
            val event = when (episode?.catalog?.type) {
                Constants.ENTITY_TYPE_AUDIO_BOOK -> {
                    OpenIndexEvent(
                        OpenIndexEvent.OPEN_AUDIO_BOOK_PAGE,
                        episode?.catalog?.id.toString(), targetPageId = episode?.catalog?.type+"_Page",
                        extraData = extraData
                    )
                }
                Constants.ENTITY_TYPE_AUDIO_SERIES -> {
                    OpenIndexEvent(
                        OpenIndexEvent.OPEN_AUDIO_SERIES_PAGE,
                        episode?.catalog?.id.toString(), targetPageId = episode?.catalog?.type+"_Page",
                        extraData = extraData
                    )
                }
                else -> {
                    OpenIndexEvent(
                        OpenIndexEvent.OPEN_CATALOG_PAGE,
                        episode?.catalog?.id.toString(), targetPageId = episode?.catalog?.type+"_Page",
                        extraData = extraData
                    )
                }
            }
            EventBus.getDefault().post(event)
        }

        binding.artistRecyclerView.getRecyclerView().layoutManager = LinearLayoutManager(
            ctx,
            RecyclerView.HORIZONTAL,
            false
        )
        binding.artistRecyclerView.getRecyclerView().addItemDecoration(
            RecyclerViewMargin(
                ctx.resources.getDimensionPixelSize(
                    R.dimen.dp8
                )
            )
        )

        artistAdapter = ArtistAdapter(artists, CREATOR_VIEW, object : OnClickInterface<User> {
            override fun dataClicked(data: User) {

            }
        }, "Podcast_Episode_Page")
        binding.artistRecyclerView.getRecyclerView().adapter = artistAdapter

        commentAdapter = CommentAdapter(
            binding.commentRecyclerView,
            comments,
            null,
            episode,
            object : OnClickInterface<Comment> {
                override fun dataClicked(data: Comment, eventId: Int) {
                    when (eventId) {
                        Constants.DELETE_COMMENT -> {
                            data.id?.let { id ->
                                if(!NetworkUtils.isNetworkConnected(ctx)) {
                                    Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                                    return@let
                                }
                                deleteComment(id)
                            }
                        }
                        Constants.COMMENT_LIKE -> {
                            performAction(UserAction.LIKE.action, data, true)

                        }
                        Constants.COMMENT_DISLIKE -> {
                            performAction(UserAction.DISLIKE.action, data, true)
                        }
                        Constants.BLOCK_USER -> {
                            data.user?.let {
                                blockUser(it.id ?: "")
                            }
                        }
                        PIN_COMMENT, UNPIN_COMMENT -> {
                            pinUnpinComment(data, eventId)
                        }
                    }
                }
            }, ctx)

        binding.commentRecyclerView.adapter = commentAdapter
        binding.commentRecyclerView.itemAnimator = null

        binding.imgShare.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            val dialog = ShareDialog.newInstance(episode, false, null)
            dialog.show(childFragmentManager, "share")
        }
        binding.appBar.addOnOffsetChangedListener(this)
        startAlphaAnimation(binding.txtToolbarTitle, 0, View.INVISIBLE)
        binding.collapsingToolbar.collapsedTitleGravity = Gravity.START

        binding.errorView.setOnReturnClick {
            getData()
        }

        ImageUtils.loadImageByUrl(
            binding.userProfileImage,
            PrefUtils.userDetails?.smallImage,
            true,
            R.drawable.ic_user_profile, originalUrl =  PrefUtils.userDetails?.originalImage
        )

        binding.commentView.setOnClickListener(CustomClickListener ({
            Utils.hideKeyboard(ctx)

            openCommentDialog()
        },"episode_page"))

        binding.lihatSemua.setOnClickListener {
            openCommentDialog()
        }

        binding.followBtn.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, ctx.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-subscribe-catalog")
                ctx.getPlayerActivity()?.handleUserNotLoggedIn(loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            val value = if (isFollowedChannel > 0) {
                0.0
            } else {
                1.0
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (value == 1.0) { //catalog followed
                    /* notification permission for Android 13 */
                    if (permissionUtils?.isAllPermissionGranted() == false) {
                        permissionUtils?.requestPermissions()
                        return@setOnClickListener
                    }
                }
            }

            isFollowedChannel = value

            val eventName = if (value == 0.0) "catalog unfollowed" else "catalog followed"

            MoEngageAnalytics.sendEvent(ctx, eventName, Bundle().apply {
                putString("vertical", episode?.catalog?.type ?: "")
                putString("catalog title", episode?.catalog?.title ?: "")
                putString("catalog id", episode?.catalogId ?: "")
                putStringArrayList(
                    "genre",
                    ArrayList(episode?.genres?.map { it.name ?: "" } ?: ArrayList()))
                putString("catalog source", episode?.source ?: "")
                putString("image url", episode?.catalog?.imageMeta?.size300 ?: "")
                putString("content id", episode?.id ?: "")
            })

            updateFollowTextCount()

            handleFollowBtn()

            val mediaAction = if (PrefUtils.isLoggedIn) {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value,
                    episode?.catalog?.id,
                    "catalog",
                    PrefUtils.userDetails?.id.toString(),
                    episode?.catalog?.type,
                    null
                )
            } else {
                MediaAction(
                    UserAction.FOLLOW.action,
                    value,
                    episode?.catalog?.id,
                    "catalog",
                    PrefUtils.guestId,
                    episode?.catalog?.type,
                    null
                )
            }

            val eventNameGoogle = if (value == 0.0) {
                "catalog_unfollowed"
            } else {
                "catalog_followed"
            }

            AnalyticsUtil.sendEventForCatalogFollow(
                "catalog",
                eventNameGoogle,
                episode?.catalog?.id,
                episode?.catalog?.title ?: "",
                "podcast_page"
            )

            viewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
                if (it?.status == ResponseStatus.SUCCESS) {
                    it.data?.userActions?.find { mediaAction ->
                        mediaAction.action.equals(UserAction.FOLLOW.action, true)
                    }?.let { isFollow ->
                        if (isFollow.actionValue != null) {
                            isFollowedChannel = isFollow.actionValue!!

                            handleFollowBtn()

                            if (episode?.catalogMeta?.userActions.isNullOrEmpty()) {
                                episode?.catalogMeta?.userActions = ArrayList()
                                episode?.catalogMeta?.userActions?.add(isFollow)
                            }
                        }
                    }
                }
            }
        }

        binding.showAllEpisodes.setOnClickListener {

            val tag : String

            val fragment = if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK) {
                tag = EpisodeListFragment::class.java.simpleName
                EpisodeListFragment.newInstance(DataController.AUDIO_BOOK_PAGE, PopupFilterMenu.FILTER_ASC, episode?.catalog, 0, extraData = extraData)
            } else {
                episode?.catalog?.selectedFilter = if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_SERIES) {
                    PopupFilterMenu.FILTER_ASC
                } else {
                    PopupFilterMenu.FILTER_DESC
                }
                tag = PodCastPagerFragment::class.java.simpleName
                PodCastPagerFragment.newInstance(
                    episode?.catalog?.title.toString(),
                    episode?.catalog?.id.toString(),
                    true,
                    episode?.catalog,
                    extraData
                )
            }

            parentFragment?.childFragmentManager?.beginTransaction()
                ?.add(R.id.homeContainer, fragment)
                ?.addToBackStack(tag)
                ?.commit()
        }

        binding.mediaButton.clickWithDebounce {
            episode?.let { content ->
                if (DataController.isAdPlaying) {
                    Utils.showSnackBar(ctx, ctx.getString(R.string.playback_will_resume_after_ad))
                    return@clickWithDebounce
                }

                // add validation if timestamp is not null then will reset the queue
                if (QueueManager.getPlayingData()?.id == episodeId && extraData?.t == null) {
                    if (ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                        ContentPlayRequest.Builder()
                            .playWhenReady(ctx, false)
                    } else {
                        handlePlayButton(getPlayerEvent(PlayerEvent.PLAY))
                        ContentPlayRequest.Builder()
                            .setPlaybackTimestamp(extraData?.t)
                            .playWhenReady(ctx, true)
                    }
                } else {
                    handlePlayButton(getPlayerEvent(PlayerEvent.PLAY))

                    DataController.setCurrentlyPlaying(CONTENT_PAGE, episodeId, PRIMARY_PLAY_BUTTON)

                    ContentPlayRequest.Builder()
                        .contents(listOf(content))
                        .pageSource(AnalyticsUtil.episode_page)
                        .extraData(extraData)
                        .addCatalogContents(true)
                        .contentFetchLimit(20)
                        .queueTitle(episode?.catalog?.title ?: "")
                        .setPlaybackTimestamp(extraData?.t)
                        .play(ctx)

                    // reset to null timestamp after playing
                    extraData?.t = null
                }
            }
        }

        binding.notLoggedIn.setOnClickListener {
            val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-comment")
            (ctx as HomeActivity).handleUserNotLoggedIn(loginDialogData = loginDialogData)
        }

        FacebookAnalytics.trackContentViewed(
            episode?.id ?: "",
            episode?.catalog?.title ?: ""
        )
        AnalyticsUtil.sendEventForOpenScreen(
            episode?.entitySubType,
            "content_page_opened",
            episode?.id ?: "",
            episode?.title ?: "",
            source = episode?.catalog?.source ?: "",
            catalogId = episode?.catalog?.id ?: "",
            catalogTitle = episode?.catalog?.title ?: "",
            contentId = episode?.id ?: "",
            extraData = extraData
        )

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) { event ->
                handlePlayButton(event)

                if (event.event == PlayerEvent.PAUSE_END_LOADER && event.currentContentPositionMs != null && event.exoData?.id == episodeId) {
                    episode?.meta?.timeElapsed = event.currentContentPositionMs.msToSeconds().toLong()
                    handleElapsedTime()
                }
            }

        GlobalObservers.contentPurchaseObserver
            .subscribe(viewLifecycleOwner) { response ->
                if (response is Content && response.id == episodeId) {
                    Utils.showSnackBarWithDrawable(
                        rootView = binding.root.rootView,
                        viewToAnchor = null,
                        messageText = getString(R.string.open_content2),
                        drawableResId = R.drawable.ic_tick_green_success
                    )
                    getData(ignoreCache = true)
                } else if (response is SubscriptionPackage) {
                    getData(ignoreCache = true)
                }
            }
    }

    private fun getPlayerEvent(event: String) = PlayerEvent(event, exoData)

    private fun handlePlayButton(playerEvent: PlayerEvent) {
        if (playerEvent.exoData?.id == episodeId) {
            binding.mediaButton.handleMusicButton(playerEvent)
        } else {
            binding.mediaButton.handleMusicButton(PlayerEvent(PlayerEvent.PAUSE_END_LOADER))
        }
    }

    private fun managePreButtonState() {
        val buttonState = ctx.getPlayerActivity()?.getContentPlayState(episodeId)

        if (buttonState?.playWhenReady == true) {
            handlePlayButton(getPlayerEvent(PlayerEvent.PLAY))
        } else {
            handlePlayButton(getPlayerEvent(PlayerEvent.PAUSE_END_LOADER))
        }

        if (buttonState?.waiting == true) {
            handlePlayButton(getPlayerEvent(PlayerEvent.SHOW_LOADER))
        }
    }

    private fun getData(ignoreCache: Boolean = false) {

        showLoading()

        getEpisodeDetails(ignoreCache)

        getComments()
    }

    private fun handleLogin() {
        if (!PrefUtils.isLoggedIn) {
            binding.commentView.visibility = GONE
            binding.notLoggedIn.visibility = VISIBLE

            val text = getString(R.string.login_to_comment)
            val span = SpannableString(text)
            span.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(ctx, R.color.dull_yellow)),
                0,
                5,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.notLoggedIn.text = span
        } else {
            binding.notLoggedIn.visibility = GONE
            binding.commentView.visibility = VISIBLE
            commentAdapter?.notifyDataSetChanged()
        }
    }

    private fun openCommentDialog() {
        episode?.let { content ->
            commentDialog = CommentDialog.newInstance(episodeId, content, source = "Podcast_Episode_Page")
            commentDialog?.show(childFragmentManager, "comment")
        }
    }

    private fun startDownload(content: Content) {
        BaseApplication.application.getDownloadTracker().download(
            null,
            content,
            "Podcast_Episode_Screen"
        )

        setDownloadListener()
    }

    private fun setDownloadListener() {
        if (downloadListener != null)
            return

        downloadListener = object : ExoPlayerDownloadTracker.DownloadProgressListener(episodeId) {
            override fun onDownloadsChanged(download: Download) {
                manageState(download)
            }

            override fun onProgressChanged(download: Download) {
                manageState(download)
            }
        }
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().addListener(it)
        }
    }

    private fun manageState(download: Download? = null) {
        download?.state?.let { state ->
            downloadState = state
        }

        when (downloadState) {
            Download.STATE_COMPLETED -> {
                releaseDownloadListener()

                binding.txtDownload.text = getString(R.string.downloaded)
                binding.txtDownload.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                binding.downloadIcon.setImageResource(R.drawable.ic_tick_yellow)
                binding.downloadProgress.progress = 0
                binding.downloadProgress.visibility = GONE
            }
            Download.STATE_QUEUED -> {
                binding.txtDownload.text = getString(R.string.in_queue)
                binding.txtDownload.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                binding.downloadIcon.setImageResource(R.drawable.ic_download_stop)
                binding.downloadProgress.progress = 0
                binding.downloadProgress.visibility = VISIBLE
            }
            Download.STATE_DOWNLOADING -> {
                //binding.txtDownload.text = getString(R.string.downloading)
                binding.txtDownload.text = ""
                binding.txtDownload.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                binding.downloadIcon.setImageResource(R.drawable.ic_download_stop)
                binding.downloadProgress.progress = download?.percentDownloaded?.toInt() ?: 0
                binding.downloadProgress.visibility = VISIBLE
            }
            Download.STATE_STOPPED -> {
                releaseDownloadListener()

                binding.txtDownload.text = getString(R.string.download)
                binding.txtDownload.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                binding.downloadIcon.setImageResource(R.drawable.ic_download)
                binding.downloadProgress.progress = 0
                binding.downloadProgress.visibility = GONE
            }
            else -> {
                releaseDownloadListener()

                binding.txtDownload.text = getString(R.string.download)
                binding.txtDownload.setTextColor(ContextCompat.getColor(ctx, R.color.dull_yellow))
                binding.downloadIcon.setImageResource(R.drawable.ic_download)
                binding.downloadProgress.progress = 0
                binding.downloadProgress.visibility = GONE
            }
        }
    }

    private fun updateFollowTextCount() {
        if (isFollowedChannel > 0) {
            if (episode?.catalogMeta?.aggregations?.followers != null) {
                val count = (episode?.catalogMeta?.aggregations?.followers ?: 0) + 1
                episode?.catalogMeta?.aggregations?.followers = count
                if (episode?.catalog?.type != Constants.ENTITY_TYPE_AUDIO_BOOK) {
                    binding.noOfFollowers.text = getString(
                        R.string.s_subscribers,
                        Utils.abbrNumberFormat(count)
                    )
                }
            }
        } else if (episode?.catalogMeta?.aggregations?.followers != null && (episode?.catalogMeta?.aggregations?.followers?:0) > 0) {
            val count = (episode?.catalogMeta?.aggregations?.followers?:0) - 1
            episode?.catalogMeta?.aggregations?.followers = count
            if (episode?.catalog?.type != Constants.ENTITY_TYPE_AUDIO_BOOK) {
                binding.noOfFollowers.text = getString(R.string.s_subscribers, Utils.abbrNumberFormat(count))
            }
        }
    }

    private fun getEpisodeDetails(ignoreCache: Boolean = false) {
        viewModel.getEpisodeDetails(episodeId, ignoreCache).observe(viewLifecycleOwner) {

            when(it?.status) {
                ResponseStatus.LOADING -> {
                    if (it.data?.data != null) {
                        handleGetResponse(it, true)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (it.data?.data != null) {
                        handleGetResponse(it)
                        //displayGamAd()
                    } else {
                        binding.errorView.showError(it.message)
                    }
                }
                else -> {
                    binding.errorView.showError(it?.message)
                }
            }
        }
    }

    private fun displayGamAd() {
        if ((!PrefUtils.appCDNConfig?.nativeAdsConfig?.nativeAdUnitId.isNullOrEmpty() ||
                    !PrefUtils.appCDNConfig?.nativeAdsConfig?.customNativeAdUnitId.isNullOrEmpty()) &&
            PrefUtils.userDetails?.isSubscribed == false
        ) {
            GaManager.refreshAd(episode?.catalogId, episode?.id, getGenresAsString())
        }
    }

    private fun handleGetResponse(
        resource: Resource<BaseModel<Content>>,
        isCache: Boolean = false
    ) {

        episode = resource.data?.data

        // if timestamp is bigger than episode duration, then reset to 0
        extraData?.t?.let { timestamp ->
            if (timestamp > (episode?.duration ?: 0)) {
                extraData?.t = 0
            }
        }

        if (episode?.imageMeta?.size300.isNullOrEmpty()) {
            episode?.imageMeta?.size300 = episode?.catalog?.imageMeta?.size300
        }

        setData()
        commentAdapter?.setContent(episode)
        commentAdapter?.notifyDataSetChanged()

        if (episode?.displayAdsEnabled == true && !isCache && PrefUtils.userDetails?.isSubscribed == false) {
            getDisplayAds()
        }

        updateContentInDb()

        if (resource.wasCacheAvailable == false) {
            sendEvents()
        }

        binding.mediaButton.visibility = if (episode?.showPlayButton == true)
            View.VISIBLE
        else View.GONE

        hideLoading()
    }

    private fun sendEvents() {
        AnalyticsUtil.sendEventForOpenScreen(
            episode?.entitySubType,
            EVENT_PODCAST_PAGE_OPENED,
            episode?.id ?: "",
            episode?.title ?: "",
            source = episode?.catalog?.source ?: "",
            catalogId = episode?.catalog?.id ?: "",
            catalogTitle = episode?.catalog?.title ?: "",
            contentId = episode?.id ?: "",
            extraData = extraData
        )

        if (episode?.catalog?.type == "podcast") {
            AnalyticsUtil.sendEvent(
                FirebaseAnalytics.Event.SCREEN_VIEW,
                Bundle().apply {
                    putString(
                        FirebaseAnalytics.Param.SCREEN_NAME,
                        "Podcast_Episode_Page"
                    )
                    putString("previousScreen", source)
                    putString("catalogTitle", episode?.catalog?.title)
                    putString("catalogId", episode?.catalog?.id)
                    putString("contentTitle", episode?.title)
                    putString("contentId", episode?.id)
                    putString("entitySubtype", episode?.catalog?.type)
                })

        } else {
            AnalyticsUtil.sendEvent(
                FirebaseAnalytics.Event.SCREEN_VIEW,
                Bundle().apply {
                    putString(
                        FirebaseAnalytics.Param.SCREEN_NAME,
                        "Audiobook_Chapter_Page"
                    )
                    putString("previousScreen", source)
                    putString("catalogTitle", episode?.catalog?.title)
                    putString("catalogId", episode?.catalog?.id)
                    putString("contentTitle", episode?.title)
                    putString("contentId", episode?.id)
                    putString("entitySubtype", episode?.catalog?.type)
                })
        }

        extraData?.apply {
            contentLink = "${Constants.WEB_BASE_URL}content/${episode?.id}"
            catalogLink = "${Constants.WEB_BASE_URL}catalog/${episode?.catalog?.id}"
            isContentPremium = episode?.isPremium == true
        }

        AnalyticsUtil.sendEventForOpenScreen(
            episode?.entitySubType,
            "content_page_opened",
            episode?.id ?: "",
            episode?.title ?: "",
            source = episode?.catalog?.source ?: "",
            catalogId = episode?.catalog?.id ?: "",
            catalogTitle = episode?.catalog?.title ?: "",
            contentId = episode?.id ?: "",
            extraData = extraData
        )

        MoEngageAnalytics.sendEventForCatalogDetail(
            ctx,
            "detail page viewed",
            source,
            pageSource,
            episode,
            extraData
        )
    }

    private fun updateContentInDb() {
        PrefUtils.userDetails?.id?.let { userId ->
            if (episode != null) {
                CoroutineScope(Dispatchers.IO).launch {
                    if (BaseApplication.application.getDownloadTracker().isDownloaded(episodeId, episode?.url)) {
                        episode?.catalogType = episode?.catalog?.type
                        episode?.catalogTitle = episode?.catalog?.title
                        episode?.catalogId = episode?.catalog?.id
                        episode?.source = episode?.catalog?.source
                        BaseApplication.application.getAppDb().downloadsDao().addDownload(
                            Downloads(userId, episodeId, episode!!)
                        )
                    }
                }
            }
        }
    }

    private fun getComments() {

        val map = HashMap<String, String>()
        map["page"] = "1"
        map["limit"] = "3"

        viewModel.getEntityComments(episodeId, map).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                commentAdapter?.addNewList(it.data)
            }
        }
    }

    private fun handleCommentCount(action: String) {

        getComments()

        episode?.meta?.aggregations?.comments = when {
            action == ADD_COMMENT -> {
                (episode?.meta?.aggregations?.comments ?: 0) + 1
            }
            (episode?.meta?.aggregations?.comments ?: 0) <= 0 -> {
                0
            }
            else -> {
                (episode?.meta?.aggregations?.comments ?: 0) - 1
            }
        }

        setCommentCount()
    }

    private fun handleReplyCount(parentComment :Comment) {
        val index  = comments.indexOf(parentComment)
        if (index >-1){
            comments[index] = parentComment
            commentAdapter?.notifyItemChanged(index)
        }
    }

    private fun setCommentCount() {
        if (episode?.meta?.aggregations?.comments != null) {
            val dotCount = Utils.formatNumber(episode?.meta?.aggregations?.comments.toString())
            binding.noOfComments.text = dotCount

            if (episode?.meta?.aggregations?.comments == 0) {
                binding.lihatSemua.text = getString(R.string.see_more)
            } else {
                binding.lihatSemua.text = getString(R.string.see_more_count, dotCount)
            }
        }
    }

    private fun setData() {
        if (episode?.isPurchaseNeeded == true) {
            binding.addToQueLayout.visibility = GONE
        } else {
            binding.addToQueLayout.visibility = VISIBLE
            binding.panel.visibility = VISIBLE
        }

        if (episode?.isDownloadable == false || (episode?.isPurchaseNeeded == true)) {
            binding.downloadLayout.visibility = GONE
        } else {
            binding.downloadLayout.visibility = VISIBLE
            binding.panel.visibility = VISIBLE
        }

        setContentTags()

        checkForDownloadState()

        if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK) {
            binding.imgLayout.layoutParams.width = ctx.resources.getDimensionPixelSize(R.dimen.dp30)
            binding.followBtn.text = getString(R.string.save)
            binding.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_bookmark_white_14dp,
                0,
                0,
                0
            )
            binding.noOfFollowers.text = Utils.getAuthorString(episode?.catalog)
            binding.showAllEpisodes.text = getString(R.string.show_all_chapter)

            binding.coverLayout.layoutParams.width = ctx.resources.getDimensionPixelSize(R.dimen.dp156)
            binding.coverLayout.layoutParams.height = WRAP_CONTENT
        } else if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_SERIES) {
            binding.imgLayout.layoutParams.width = ctx.resources.getDimensionPixelSize(R.dimen.dp30)
            binding.noOfFollowers.text = getString(
                R.string.s_subscribers,
                Utils.abbrNumberFormat(episode?.catalogMeta?.aggregations?.followers ?: 0)
            )

            binding.coverLayout.layoutParams.width = ctx.resources.getDimensionPixelSize(R.dimen.dp156)
            binding.coverLayout.layoutParams.height = WRAP_CONTENT
        } else if (episode?.catalogMeta?.aggregations?.followers != null) {
            binding.noOfFollowers.text = getString(
                R.string.s_subscribers,
                Utils.abbrNumberFormat(episode?.catalogMeta?.aggregations?.followers ?: 0)
            )
        }

        if (episode?.isPurchaseNeeded == true) {
           // binding.mediaButton.lockMediaButton(true)
            binding.premiumEpisodePrice.text = getString(R.string.unlock_content_with_coins, episode?.price ?: 0)
            binding.priceLayout.visibility = VISIBLE
        } else {
            //binding.mediaButton.lockMediaButton(false)
            managePreButtonState()
            binding.priceLayout.visibility = GONE
        }

        if (!episode?.genres.isNullOrEmpty()) {
            binding.genreRecyclerView.visibility = VISIBLE
            genreAdapter?.addAll(episode?.genres!!)
        } else {
            binding.genreRecyclerView.visibility = GONE
        }
        if (!episode?.users.isNullOrEmpty()) {
            binding.artistRecyclerView.visibility = VISIBLE
            artistAdapter?.setData(ArrayList(episode?.users!!))
        } else {
            binding.artistRecyclerView.visibility = GONE
        }


        /*binding.episodeName.setTitleWithVideoSpan(
            episode?.title,
            episode?.showVideo,
            R.drawable.video_filled_15dp
        )*/
        binding.episodeName.text = episode?.title ?: ""
        binding.txtToolbarTitle.text = episode?.title ?: ""

        handleElapsedTime()

        if (!episode?.description.isNullOrEmpty() || !episode?.htmlDescription.isNullOrEmpty()) {
            val description = if (!episode?.htmlDescription.isNullOrEmpty()) {
                episode?.htmlDescription.toString()
            } else {
                episode?.description.toString()
            }

            binding.description.setText(description)
            binding.description.setListener(object : ReadMoreTextView.Listener {
                override fun onExpandClicked(isSeeMore: Boolean) {
                    if (isSeeMore) {
                        val containsURL =
                            if (description.contains("https://") || description.contains("http://")) {
                                "yes"
                            } else {
                                "no"
                            }

                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", PODCAST_EPISODE_PAGE)
                        bundle.putAnalyticsKey("catalogId", episode?.catalog?.id)
                        bundle.putAnalyticsKey("catalogTitle", episode?.catalog?.title)
                        bundle.putAnalyticsKey("contentId", episode?.id)
                        bundle.putAnalyticsKey("contentTitle", episode?.title)
                        bundle.putAnalyticsKey("entitySubType", episode?.type)
                        bundle.putAnalyticsKey("containsURL", containsURL)
                        AnalyticsUtil.sendEvent("expand_content_description", bundle)
                    }
                }

                override fun onLinkClicked(uri: Uri?) {
                    val url = uri?.toString() ?: return
                    if (url.isNotEmpty()) {
                        val bundle = Bundle()
                        bundle.putAnalyticsKey("source", PODCAST_EPISODE_PAGE)
                        bundle.putAnalyticsKey("catalogId", episode?.catalog?.id)
                        bundle.putAnalyticsKey("catalogTitle", episode?.catalog?.title)
                        bundle.putAnalyticsKey("contentId", episode?.id)
                        bundle.putAnalyticsKey("contentTitle", episode?.title)
                        bundle.putAnalyticsKey("entitySubType", episode?.type)
                        bundle.putAnalyticsKey("linkDetail", url)
                        AnalyticsUtil.sendEvent("description_link_clicked", bundle)
                    }
                }
            })
            binding.description.visibility = VISIBLE
        } else {
            binding.description.visibility = GONE
        }

        if (!episode?.publishedAt.isNullOrEmpty()) {
            binding.date.text = DateUtils.formatDateToLocalMonthName(episode?.publishedAt!!)
        }

        /*if (episode?.meta?.aggregations?.listens != null) {
            binding.noOfPlays.text =
                Utils.formatNumber(episode?.meta?.aggregations?.listens.toString())
        }*/

        if (!episode?.catalog?.title.isNullOrEmpty()) {
            binding.channelName.text = episode?.catalog?.title
        }
        if (!episode?.catalog?.imageMeta?.size300.isNullOrEmpty()) {
            val placeholder : Int
            val imageUrl : String?
            placeholder = if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK || episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_SERIES) {
                imageUrl = episode?.catalog?.imageMeta?.size300
                R.drawable.ic_audiobook_default
            } else {
                imageUrl = episode?.catalog?.imageMeta?.size300
                R.drawable.ic_thumb_square
            }
            ImageUtils.loadImageByUrl(binding.coverImageSmall, imageUrl, placeHolder = placeholder, originalUrl = episode?.catalog?.image)
        }

        episode?.catalogMeta?.userActions?.find {
            it.action.equals(UserAction.FOLLOW.action, true)
        }?.let {
            isFollowedChannel = it.actionValue ?: 0.0
            handleFollowBtn()
        }

        setCommentCount()

        if (episode?.commentsDisabled == true){
            binding.disableView.commentDisable.visibility = VISIBLE
            binding.commentView.visibility = GONE
            binding.commentRecyclerView.visibility = GONE
            binding.lihatSemua.visibility = GONE
            binding.notLoggedIn.visibility = GONE
        } else {
            binding.disableView.commentDisable.visibility = GONE
            binding.commentRecyclerView.visibility = VISIBLE

            if (episode?.isPurchaseNeeded == true) {
                if (episode?.meta?.aggregations?.comments == 0) {
                    binding.lihatSemua.visibility = GONE
                    binding.commentTitle.visibility = GONE
                } else {
                    binding.lihatSemua.visibility = VISIBLE
                    binding.commentTitle.visibility = VISIBLE
                }
                binding.imgPurchased.visibility = GONE
                binding.panel.visibility = GONE
                binding.line.visibility = GONE
                binding.commentView.visibility = GONE
            } else {
                binding.imgPurchased.visible(episode?.isPremium.isTrue())
                binding.panel.visibility = VISIBLE
                binding.line.visibility = VISIBLE
                handleLogin()
            }
        }

        if (playContentDirectly || extraData?.t != null) {
            if (episode?.showPlayButton == true) {
                // prevent dialog open twice
                if (extraData?.t != null && episode?.isPurchaseNeeded == true && purchaseDialog?.isAdded != true) {
                    purchaseDialog = ClickHandler.openPurchaseDialog(childFragmentManager, episode, "episode_page")
                    return
                }

                // To prevent performing click twice at the same time
                ClickHandler.performClickSafely(view = binding.mediaButton)
                playContentDirectly = false
            }
        }

        if (!episode?.imageMeta?.size300.isNullOrEmpty()) {
            val placeholder : Int
            var imageUrl : String?
            placeholder = if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK || episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_SERIES) {
                imageUrl = episode?.imageMeta?.size300
                R.drawable.ic_audiobook_default
            } else {
                imageUrl = episode?.imageMeta?.size300
                R.drawable.ic_thumb_square
            }
            ImageUtils.loadImageByUrl(
                binding.coverImage,
                imageUrl,
                placeHolder = placeholder, originalUrl = episode?.image
            )

            binding.parentLayout.post {
                binding.imgCover.layoutParams.height = binding.parentLayout.height
                if (imageUrl.isNullOrEmpty()) imageUrl = episode?.image
                ImageUtils.showBlurImage(imageUrl, binding.imgCover, placeholder)
            }
        }
    }

    private fun handleElapsedTime() {
        if (episode?.duration != null) {
            binding.txtDuration.text = DateUtils.getMinuteStrFromSeconds(episode?.duration)
        }
        if (episode?.meta?.aggregations?.listens != null && !episode?.meta?.aggregations?.listens.isZero() ){
            binding.txtCount.text = Utils.abbrNumberFormat(episode?.meta?.aggregations?.listens?:0,true)
            binding.txtCount.visible(true)
            binding.txtCountUnit.visible(true)
        } else {
            binding.txtCount.visible(false)
            binding.txtCountUnit.visible(false)
        }

        if (episode?.meta?.markedAsPlayed == true) {
            binding.progressBar.progress = 100
            binding.progressBar.visibility = VISIBLE
            binding.timeLeft.text = getString(R.string.done)
        } else {
            val progress = Utils.remainingPercent(episode?.meta?.timeElapsed, episode?.duration)
            updateTimeLeft(episode?.meta?.timeElapsed ?: 0, progress)
        }
    }

    private fun updateTimeLeft(timeElapsed: Long, progress: Int) {
        if (timeElapsed != 0L) {
            binding.timeLeft.text = if (progress == 100) {
                getString(R.string.done)
            } else {
                var timeLeft = (episode?.duration ?: 0) - (episode?.meta?.timeElapsed ?: 0)
                if (timeLeft < 0) {
                    timeLeft = 0
                }
                getString(
                    R.string.int_minutes_left_text, DateUtils.getMinuteStrFromSeconds(timeLeft)
                )
            }
            binding.progressBar.progress = progress
            binding.progressBar.visibility = VISIBLE
            binding.timeLeft.visibility = VISIBLE
        } else {
            binding.progressBar.visibility = GONE
            binding.timeLeft.visibility = GONE
            /*if (episode?.duration != null) {
                binding.timeLeft.text =
                    getString(
                        R.string.int_minutes,
                        DateUtils.getMinuteStrFromSeconds(episode?.duration)
                    )
            }*/
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {
        Handler(Looper.getMainLooper()).postDelayed({
            if (episode?.isPremium == true) {
                getData(true)
            } else {
                handleLogin()
            }
        },500)
    }

    private fun handleFollowBtn() {
        if (isFollowedChannel > 0) {
            if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK) {
                binding.followBtn.text = getString(R.string.saved)
                binding.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_bookmark_grey_filled_14dp,
                    0,
                    0,
                    0
                )
            } else {
                binding.followBtn.text = getString(R.string.subscribed)
                binding.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_following,
                    0,
                    0,
                    0
                )
            }
            binding.followBtn.setTextColor(ContextCompat.getColor(ctx, R.color.white50))
            binding.followBtn.isSelected = true
        } else {
            if (episode?.catalog?.type == Constants.ENTITY_TYPE_AUDIO_BOOK) {
                binding.followBtn.text = getString(R.string.save)
                binding.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_bookmark_white_14dp,
                    0,
                    0,
                    0
                )
            } else {
                binding.followBtn.text = getString(R.string.subscribe)
                binding.followBtn.setCompoundDrawablesWithIntrinsicBounds(
                    R.drawable.ic_follower_plus,
                    0,
                    0,
                    0
                )
            }
            binding.followBtn.setTextColor(ContextCompat.getColor(ctx, R.color.white))
            binding.followBtn.isSelected = false
        }

        episode?.catalogMeta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.FOLLOW.action, true)
        }?.let { isFollow ->
            isFollow.actionValue = isFollowedChannel
        }
    }

    override fun onOffsetChanged(appBarLayout: AppBarLayout?, offset: Int) {
        val maxScroll = appBarLayout?.totalScrollRange ?: 0
        val percentage = abs(offset).toFloat() / maxScroll.toFloat()

        handleAlphaOnTitle(percentage)
        handleToolbarTitleVisibility(percentage)
    }

    private fun handleToolbarTitleVisibility(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_SHOW_TITLE_AT_TOOLBAR) {
            if (!mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    VISIBLE
                )
                mIsTheTitleVisible = true
            }
        } else {
            if (mIsTheTitleVisible) {
                startAlphaAnimation(
                    binding.txtToolbarTitle,
                    ALPHA_ANIMATIONS_DURATION,
                    View.INVISIBLE
                )
                mIsTheTitleVisible = false
            }
        }
    }

    private fun handleAlphaOnTitle(percentage: Float) {
        if (percentage >= PERCENTAGE_TO_HIDE_TITLE_DETAILS) {
            if (mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, View.INVISIBLE)
                mIsTheTitleContainerVisible = false
            }
        } else {
            if (!mIsTheTitleContainerVisible) {
                startAlphaAnimation(binding.headerLayout, ALPHA_ANIMATIONS_DURATION, VISIBLE)
                mIsTheTitleContainerVisible = true
            }
        }
    }

    private fun startAlphaAnimation(v: View, duration: Int, visibility: Int) {
        val alphaAnimation =
            if (visibility == VISIBLE) AlphaAnimation(0f, 1f) else AlphaAnimation(1f, 0f)
        alphaAnimation.duration = duration.toLong()
        alphaAnimation.fillAfter = true
        v.startAnimation(alphaAnimation)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayerAction(event: EventMessage) {
        if (event.eventCode == COMMENT_DELETED && event.data as String == episodeId) {
            if (episode?.meta?.aggregations?.comments != null && episode?.meta?.aggregations?.comments!! > 0) {
                episode?.meta?.aggregations?.comments =
                    episode?.meta?.aggregations?.comments!! - 1
                val dotCount =
                    Utils.formatNumber(episode?.meta?.aggregations?.comments.toString())
                binding.noOfComments.text = dotCount

                if (episode?.meta?.aggregations?.comments == 0) {
                    binding.lihatSemua.text = getString(R.string.see_more)
                } else {
                    binding.lihatSemua.text = getString(R.string.see_more_count, dotCount)
                }
            } else {
                binding.lihatSemua.text = getString(R.string.see_more)
                val dotCount =
                    Utils.formatNumber(episode?.meta?.aggregations?.comments.toString())
                binding.noOfComments.text = dotCount
            }
        } else if (event.eventCode == POST_COMMENT && event.data as String == episodeId) {
            if (!isPostedBySelf) {
                handleCommentCount(ADD_COMMENT)
            } else {
                isPostedBySelf = false
            }
        } else if (event.eventCode == Constants.COMMENT_COUNT_DEC && event.data as String == episodeId) {
            val commentId = event.data
            val comment = Comment(commentId)
            val index = comments.indexOf(comment)
            comments.remove(comment)
            commentAdapter?.notifyItemRemoved(index)
            handleCommentCount(SUB_COMMENT)
        } else if (event.eventCode == Constants.DOWNLOAD_CHECK) {
            checkForDownloadState()
        } else if (event.eventCode == Constants.COMMENT_LIKED){
            val data = event.data as Comment
            performAction(UserAction.LIKE.action, data, false)
        } else if (event.eventCode == Constants.COMMENT_DISLIKED){
            val data = event.data as Comment
            performAction(UserAction.DISLIKE.action, data, false)
        } else if (event.eventCode == Constants.HIDE_SHOW_USER){
            val data = event.data as Boolean
            if (data){
                binding.rvUser.visibility = VISIBLE
                binding.userEmptyView.visibility = GONE
            }else{
                binding.rvUser.visibility = GONE
                binding.userEmptyView.visibility = VISIBLE
            }
        } else if (event.eventCode == Constants.HIDE_SHOW_USER_EMPTY_VIEW){
            val data = event.data as Boolean
            if (data){
                binding.rvUser.visibility = GONE
                binding.userEmptyView.visibility = VISIBLE
            }else{
                binding.rvUser.visibility = VISIBLE
                binding.userEmptyView.visibility = GONE
            }
        } else if (event.eventCode == Constants.REPORT_SUCCESS){
            val id  = event.data as String
            val entityType  = event.secondData as String
            if (entityType == "post"){
                val index = commentAdapter?.dataSet?.indexOf(Comment(id)) ?: -1
                if (index != -1) {
                    val comment = commentAdapter?.dataSet?.get(index)
                    comment?.meta?.isReported = true
                    commentAdapter?.dataSet?.set(index, comment)
                    commentAdapter?.notifyItemChanged(index)
                }
            }
        } else if (event.eventCode == Constants.REPLY_COUNT_DEC || event.eventCode == Constants.REPLY_COUNT_INC){
            val data = event.data as Comment
            handleReplyCount(data)
        }
    }

    private fun pinUnpinComment(comment: Comment, action: Int) {
        if (comment.type == "comment") {
            pinUnpinComment = PinComment(
                comment.message.toString(),
                action == PIN_COMMENT
            )
        }

        viewModel.pinUnpinComment(comment.id.toString(), pinUnpinComment).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                comment.pinned = true
                val index = comments.indexOf(comment)
                if (index != -1) {
                    comment.pinned = action == PIN_COMMENT
                    commentAdapter?.notifyItemChanged(index)
                }
                val message = if (action == PIN_COMMENT) {
                    ctx.getString(R.string.pin_comment_success)
                } else {
                    ctx.getString(R.string.unpin_comment_success)
                }
                Utils.showSnackBar(ctx, message)
            } else {
                comment.pinned = false
                val message = if (!it?.message.isNullOrEmpty()) {
                    it.message
                } else if (action == PIN_COMMENT) {
                    ctx.getString(R.string.pin_comment_failed)
                } else {
                    ctx.getString(R.string.unpin_comment_failed)
                }
                Utils.showSnackBar(binding, message.toString())
            }
        }
    }

    private fun performAction(action: String, comment: Comment, isApiCall: Boolean = false) {
        var mediaAction:MediaAction?  = null
        if (action == "like"){
            mediaAction = comment.meta?.userActions?.find { it.action == UserAction.LIKE.action }
        }
        if (action == "dislike"){
            mediaAction = comment.meta?.userActions?.find { it.action == UserAction.DISLIKE.action }
        }
        val index = comments.indexOf(comment)
        comments[index] = comment
        commentAdapter?.notifyItemChanged(index)
        if (isApiCall){
            AnalyticsUtil.sendEvent(
                "comment",
                comment.id,
                episodeId,
                action,
                AnalyticsUtil.podcast
            )
            viewModel.performAction(mediaAction!!).observe(this) {

            }
        }
    }

    private fun deleteComment(id: String) {
        viewModel.deleteComment(id).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {
                commentAdapter?.deleteComment(id)
                handleCommentCount(SUB_COMMENT)
            }
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.episode_loader)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun releaseDownloadListener() {
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().removeListener(it)
            downloadListener = null
        }
    }

    private fun checkForDownloadState() {
        if(episode != null) {
            downloadState = BaseApplication.application.getDownloadTracker().getStateFor(episode)
            manageState()
            if (downloadState == Download.STATE_DOWNLOADING ||
                downloadState == Download.STATE_QUEUED ||
                downloadState == Download.STATE_RESTARTING) {
                setDownloadListener()
            }
        }
    }

    private fun setContentTags() {
        if (episode?.catalog?.type != ENTITY_TYPE_LIVE_STREAM && (episode?.catalog?.source.equals(SOURCE_EXCLUSIVE) || episode?.catalog?.source.equals(SOURCE_ORIGINAL)|| episode?.catalog?.source.equals(SOURCE_SPECIAL, true))) {
            binding.origExc.text = episode?.catalog?.source?.uppercase()
            binding.origExc.visibility = VISIBLE
        } else {
            binding.origExc.visibility = GONE
        }

        when (episode?.badge) {
            BadgeType.EARLY_ACCESS.badge -> {
                binding.noiceVip.visibility = VISIBLE
                binding.noiceVip.setImageResource(R.drawable.early_access)
            }
            BadgeType.PREMIUM.badge -> {
                binding.noiceVip.visibility = VISIBLE
                binding.noiceVip.setImageResource(R.drawable.premium)
            }
            BadgeType.VIP.badge -> {
                binding.noiceVip.visibility = VISIBLE
                binding.noiceVip.setImageResource(R.drawable.vip)
            }
            else -> {
                binding.noiceVip.visibility = GONE
            }
        }
        binding.imgVideo.visible(episode?.showVideo.isTrue())
    }

    override fun onDestroyView() {
        releaseDownloadListener()
        EventBus.getDefault().unregister(this)
        if (commentDialog?.isAdded == true) {
            commentDialog?.dismissAllowingStateLoss()
            commentDialog = null
        }
        super.onDestroyView()
    }

    private fun blockUser(userId: String) {
        val blockUserRequests = BlockUserRequest(userId, true)
        profileViewModel.profileUseCase.blockUser(blockUserRequests).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {
                Utils.showSnackBar(binding, getString(R.string.msg_user_block))
            } else {
                Utils.showSnackBar(binding, it.message ?: getString(R.string.something_is_wrong))
            }
        }
    }

    private var exoData: ExoNotificationData? = null
        get() {
            if (field == null && episode != null) {
                episode?.let {
                    return MediaUtil.getExoNotificationData(it, Priority.PLAYING.type, "content", pageSource)
                }
            }
            return null
        }

    /* Display ads are visible when it's active/enabled at both Catalog and Ads api level. */
    private fun getDisplayAds() {
        viewModel.getAds(
            Constants.Ads.AdsEntityType.PAGE,
            Constants.Ads.AdsEntityValue.CONTENT_DETAIL
        ).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (it?.data?.data?.isActive == true) {
                    adsResponse = it.data?.data
                    setAd()
                }
            }
        }
    }

    private fun setAd() {
        val adView = CustomAdView(ctx)
        adView.setData(
            size = adsResponse?.config?.adSize,
            adUnitId = adsResponse?.config?.adId,
            viewGroup = binding.frameAdView,
            genres = getGenresAsString(),
            vertical = episode?.catalog?.type ?: "",
            isPlayedPreview = episode?.isPurchaseNeeded,
            page = "content_page"
        )
        binding.frameAdView.visibility = VISIBLE
        binding.frameAdView.addView(adView)
    }

    private fun getGenresAsString(): String {
        val genresString = episode?.genres?.joinToString(",") { genre ->
            genre.name ?: ""
        }
        return genresString?.trim() ?: ""
    }
}