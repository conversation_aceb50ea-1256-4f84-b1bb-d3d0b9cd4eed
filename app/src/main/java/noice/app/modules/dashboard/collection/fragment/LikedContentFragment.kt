package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentLikedContentBinding
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.adapter.CollectionRecentAdapter
import noice.app.modules.dashboard.collection.viewmodel.CollectionViewModel
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.podcast.model.Content
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.parcelable
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class LikedContentFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    private val viewModel : CollectionViewModel by viewModels()

    private lateinit var binding : FragmentLikedContentBinding
    private lateinit var adapter : CollectionRecentAdapter
    private lateinit var ctx : Context
    private var likedContent = ArrayList<Content?>()
    private var page = 1
    private var extraData: ExtraData? = null

    companion object {
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun newInstance(extraData: ExtraData?) = LikedContentFragment().apply {
            arguments = Bundle().apply {
                putParcelable(EXTRA_DATA, extraData)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        extraData = arguments?.parcelable(EXTRA_DATA)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentLikedContentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        binding.errorView.showLoading()

        getData(false)
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handlePlayStates(it)
            }

        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getData(false)
        }

        adapter = CollectionRecentAdapter(
            binding.likedContentRecycler,
            likedContent,
            this,
            "content",
            object : OnClickInterface<Content> {
                override fun dataClicked(data: Content) {
                    data.id?.let { id ->
                        EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CONTENT_PAGE, id,"Collection_Liked_Page"))
                    }
                }
            },
            "Liked Content",
            "Kontent disukai",
            ctx,
            extraData
        )

        binding.likedContentRecycler.adapter = adapter
        binding.likedContentRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp16)))

        binding.homeBtn.setOnClickListener {
            val activity = ctx.fetchActivity()
            if (activity is HomeActivity) {
                activity.openHome()
            } else {
                activity?.onBackPressed()
            }
        }

        AnalyticsUtil.sendEvent("like_index_opened", Bundle().apply {
            putString("segmentName", extraData?.segmentName ?: "")
            putInt("segmentPosition", extraData?.segmentPosition ?: 0)
            putInt("entityPosition", extraData?.entityPosition ?: 0)
        })

        MoEngageAnalytics.sendEvent(ctx, "liked page opened", Bundle().apply {
            putString("segment name", extraData?.segmentName ?: "")
            putInt("segment position", extraData?.segmentPosition ?: 0)
            putInt("entity position", extraData?.entityPosition ?: 0)
        })
    }

    private fun handlePlayStates(playerEvent: PlayerEvent) {
        likedContent.filter { content ->
            content != null && (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content?.isPlaying = false
            content?.showLoader = false

            val index = likedContent.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }

        val content = likedContent.find {
            it?.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = likedContent.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun getData(loadMore : Boolean) {

        if (!loadMore) {
            page = 1
        }

        val map = HashMap<String, String>()
        map["page"] = page.toString()
        map["limit"] = "10"
        map["entityType"] = "content"
        map["entitySubType"] = "podcast|audiobook|music"

        viewModel.getCollection(map).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (!it.data.isNullOrEmpty()) {
                    if (loadMore)
                        adapter.addMore(ArrayList(it.data))
                    else
                        adapter.addNewList(ArrayList(it.data))

                    hideLoading()
                } else if (likedContent.isEmpty() || !loadMore) {
                    showEmptyView()
                } else {
                    adapter.isLoadMoreEnabled(false)
                    hideLoading()
                }
            } else {
                showError(it?.message)
            }
        }
    }

    private fun hideLoading() {
        binding.emptyView.visibility = View.GONE
        binding.errorView.hide()
    }

    private fun showEmptyView() {
        binding.errorView.hide()
        binding.emptyView.visibility = View.VISIBLE
    }

    private fun showError(message : String?) {
        adapter.isLoadMoreEnabled(false)
        binding.emptyView.visibility = View.GONE
        binding.errorView.showError(message, type = "Collection_Liked_Page", errorName = message)
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > 9) {
            getData(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }
}