package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentAudioSeriesHomeNewBinding
import noice.app.layoutmanagers.CustomLLManager
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.adapter.HomeSegmentAdapter
import noice.app.modules.dashboard.model.HomeBanner
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.CacheUtils
import noice.app.utils.Constants
import kotlin.math.ceil

@AndroidEntryPoint
class AudioSeriesHomeNewFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener, SwipeRefreshLayout.OnRefreshListener {

    companion object {
        const val ANALYTICS_TAG = "Audioseries_New"
        fun newInstance() = AudioSeriesHomeNewFragment()
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding: FragmentAudioSeriesHomeNewBinding
    private lateinit var ctx: Context
    private lateinit var adapter: HomeSegmentAdapter
    private val dataList = ArrayList<HomeSegment?>()
    private val bannerList = ArrayList<HomeBanner>()
    private var offset = 1
    private val limit = 4
    private var totalCount = -1
    private var isLoginChanged = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAudioSeriesHomeNewBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
    }

    override fun onResume() {
        super.onResume()

        CoroutineScope(Dispatchers.Main).launch {
            getData(
                forced = isLoginChanged || offset == 1,
                showLoader = isLoginChanged || dataList.isEmpty()
            )
        }

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, "Home_Audioseries")
            putString("previousScreen", "Home")
        })
    }

    private fun initViews() {

        binding.toolbar.title = getString(R.string.audio_series)

        binding.errorView.setBackToHomeVisibility(View.GONE)
        binding.pullToRefresh.setOnRefreshListener(this)

        binding.audioSeriesRecycler.layoutManager = CustomLLManager(ctx, RecyclerView.VERTICAL, false)

        if (!::adapter.isInitialized) {
            adapter = HomeSegmentAdapter(binding.audioSeriesRecycler, dataList, this, ANALYTICS_TAG)
        } else {
            adapter.bindListenerToRecyclerView(binding.audioSeriesRecycler)
        }

        binding.audioSeriesRecycler.adapter = adapter

        binding.errorView.setOnReturnClick {
            getData()
        }

        binding.toolbar.setNavigationOnClickListener {
            (ctx as AppCompatActivity).onBackPressed()
        }
    }

    private fun getData(forced: Boolean = false, showLoader: Boolean = true) {

        isLoginChanged = false

        if (!forced && dataList.isNotEmpty()) {
            offset++
            adapter.notifyDataSetChanged()
            hideLoading()
        } else {
            if (showLoader) {
                showLoading()
            }
            getAudioSeriesSegment()
        }

        if (!forced && bannerList.isNotEmpty()) {
            setBannerData()
        } else {
            getAudioSeriesBanner()
        }
    }

    private fun getAudioSeriesSegment(loadMore : Boolean = false) {

        if (!loadMore) {
            offset = 1
        }

        val map = HashMap<String, String>()
        map["page"] = "audioseries"
        map["offset"] = offset.toString()
        map["limit"] = limit.toString()

        val page = offset

        if (isDetached || isRemoving || !isAdded)
            return

        viewModel.getHomeSegment(map, Constants.HOME_AUDIO_SERIES).observe(viewLifecycleOwner) {

            it?.data?.data?.filter { segment ->
                !segment.content.isNullOrEmpty()
            }.let { segments ->
                it?.data?.data = segments
            }

            when(it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(loadMore, page, it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(loadMore, page, it)
                    } else if (dataList.isNotEmpty()) {
                        hideLoading()
                        adapter.isLoadMoreEnabled(false)
                    } else {
                        showError(it.message)
                    }
                    binding.pullToRefresh.isRefreshing = false
                }
                else -> {
                    handleError(it?.message)
                    binding.pullToRefresh.isRefreshing = false
                }
            }
        }
    }

    private fun handleSegmentResponse(
        loadMore: Boolean,
        page: Int,
        resource: Resource<BaseModel<List<HomeSegment>>>
    ) {
        totalCount = resource.data?.meta?.totalCount ?: -1

        resource.data?.data?.let { segments ->

            if (loadMore) {
                segments.forEach { seg ->
                    seg.segmentPage = page
                }
                adapter.addMore(segments)
            } else {
                val seg = ArrayList(segments)
                if (bannerList.isNotEmpty()) {
                    seg.add(0, HomeSegment("banners", bannerList))
                }
                seg.forEach { segment ->
                    segment.segmentPage = page
                }
                if (resource.wasCacheAvailable == true) {
                    dataList.filter { segment ->
                        segment?.segmentPage == page
                    }.forEach { segment ->
                        dataList.remove(segment)
                    }
                    seg.addAll(dataList)
                    dataList.clear()
                    dataList.addAll(seg)
                    adapter.notifyDataSetChanged()
                } else {
                    adapter.addNewList(seg)
                }
            }
            hideLoading()
        }
    }

    private fun getAudioSeriesBanner() {
        viewModel.getHomeBanner("audioseries", Constants.HOME_AUDIO_SERIES_BANNER).observe(viewLifecycleOwner) {

            when (it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleBannerResponse(it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleBannerResponse(it)
                    }
                }
                else -> {

                }
            }
        }
    }

    private fun handleBannerResponse(
        resource: Resource<BaseModel<List<HomeBanner>>>
    ) {
        if (!resource.data?.data.isNullOrEmpty()) {
            bannerList.clear()
            bannerList.addAll(resource.data?.data!!)
            setBannerData()
        } else {
            CacheUtils.deleteCachedDBData(Constants.HOME_AUDIO_SERIES_BANNER)
        }
    }

    private fun handleError(message: String?) {
        if (dataList.isEmpty()) {
            showError(message)
        } else {
            hideLoading()
        }
    }

    private fun setBannerData() {
        if (isDetached || isRemoving)
            return
        if (dataList.isNotEmpty()) {
            dataList.find { segment ->
                segment?.id == "banners"
            }?.let { segment ->
                dataList.remove(segment)
            }
            dataList.add(0, HomeSegment("banners", bannerList).apply {
                segmentPage = 1
            })
            adapter.notifyDataSetChanged()
        }
    }

    fun onLoginChangedEvent(isCurrentFragment: Boolean) {
        isLoginChanged = true
        if (isCurrentFragment) {
            getData(forced = true, showLoader = true)
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.for_you_loader)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun showError(message: String?) {
        adapter.isLoadMoreEnabled(false)
        binding.errorView.showError(message, type = "Home_Audioseries", errorName = message)
        binding.appBar.setExpanded(false)
    }

    override fun onDestroyView() {
        adapter.notifyBusUnregister()
        super.onDestroyView()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        offset = page + 1

        if (totalCount == -1 ||
            ceil((totalCount.toDouble() / limit.toDouble())).toInt() >= offset
        ) {
            getAudioSeriesSegment(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }

    override fun onRefresh() {
        getData(true, dataList.isEmpty() && bannerList.isEmpty())
    }
}