package noice.app.modules.dashboard.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import noice.app.modules.dashboard.home.fragment.*
import noice.app.modules.dashboard.home.HomeFragment.HomeTabs.*
import noice.app.modules.live.fragment.LiveTabFragment
import noice.app.utils.PrefUtils

class HomePagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    private val forYouFragment = ForYouFragment()
    private val podCastFragment = PodCastFragment()
    private val audioSeriesFragment = AudioSeriesHomeFragment()
    private val radioFragment = RadioHomeFragment()
    private val audioBookFragment = AudioBookHomeFragment()
    private val liveHomeFragment = LiveTabFragment()

    private val tabList = values().toMutableList().apply {
        if (PrefUtils.appConfig?.enableLiveTab == null || PrefUtils.appConfig?.enableLiveTab == false) {
            val index = indexOf(TAB_LIVE)

            if (index != -1) {
                removeAt(index)
            }
        }
    }
    var pageIds = tabList.map { it.hashCode().toLong() }.toMutableList()

    override fun getItemCount(): Int {
        return tabList.size
    }

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            TAB_FOR_YOU.position -> {
                forYouFragment
            }
            TAB_PODCAST.position -> {
                podCastFragment
            }
            TAB_AUDIO_SERIES.position -> {
                audioSeriesFragment
            }
            TAB_RADIO.position -> {
                radioFragment
            }
            TAB_AUDIO_BOOK.position -> {
                audioBookFragment
            }
            else -> {
                liveHomeFragment
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return pageIds[position]
    }

    override fun containsItem(itemId: Long): Boolean = pageIds.find { it.hashCode().toLong() == itemId } != null

}
