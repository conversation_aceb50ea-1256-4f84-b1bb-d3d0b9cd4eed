package noice.app.modules.dashboard.home.fragment

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.DrawableCompat
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.offline.Download
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BaseApplication
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.ActivityEpisodeMenuBinding
import noice.app.enums.BadgeType
import noice.app.enums.UserAction
import noice.app.exoplayer.BasePlayerViewModel
import noice.app.exoplayer.ExoPlayerDownloadTracker
import noice.app.exoplayer.ExoplayerUtils
import noice.app.listner.CustomClickListener
import noice.app.listner.OnClickInterface
import noice.app.model.eventbus.EventMessage
import noice.app.modules.chat.report.ReportActivity
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.dashboard.home.model.UserActionEvent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.dialog.RecordingDeleteDialog
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.model.CreateRoomRequest
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.media.fragment.QueuePlayerFragment
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.model.Payload
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CONTENT
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.EVENT_CONTENT_DISLIKED
import noice.app.utils.Constants.Companion.EVENT_CONTENT_DOWNLOAD_CLICKED
import noice.app.utils.Constants.Companion.EVENT_CONTENT_LIKED
import noice.app.utils.Constants.LiveRoom.ClientRole.ROLE_HOST
import noice.app.utils.DateUtils
import noice.app.utils.EventConstant
import noice.app.utils.ExperimentUtils
import noice.app.utils.ImageUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.ReadMoreOption
import noice.app.utils.Utils
import noice.app.utils.Utils.fetchActivity
import noice.app.utils.Utils.getBaseActivity
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.parcelable
import noice.app.utils.Utils.unwrap
import org.greenrobot.eventbus.EventBus

@OptIn(UnstableApi::class)
@AndroidEntryPoint
class NoiceContentMenu : BottomSheetDialogFragment() {

    companion object {
        const val LIVE_ROOM = "LIVE_ROOM"
        const val EPISODE = "EPISODE"
        const val IS_FROM_EPISODE = "IS_FROM_EPISODE"
        const val ENTITY_SUB_TYPE = "ENTITY_SUB_TYPE"
        const val PAGE_SOURCE = "PAGE_SOURCE"

        const val CATALOG_FOLLOWED = 1.0
    }

    private val podcastViewModel : ChannelPodcastViewModel by viewModels()
    private val basePlayerViewModel: BasePlayerViewModel by activityViewModels()

    private lateinit var binding : ActivityEpisodeMenuBinding
    private lateinit var mContext: Context
    private var episode : Content? = null
    private var playlistDialog : PlayListDialog? = null
    private var isFromEpisode = false
    private var pageSource =""
    private var entitySubType =""
    private var liveRoom:LiveRoom?=null
    private var likedStatus = 0
    private var downloadState = -1
    private var downloadListener : ExoPlayerDownloadTracker.DownloadProgressListener? = null
    private var deleteDialog: RecordingDeleteDialog? = null
    private var videoQualityDialog : VideoQualityDialog? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getDataFromIntent()

        val previousScreen = if (isFromEpisode){
           "Podcast_Podcast_Episode_PagePage"
        }else{
            "Podcast_Media_Player"
        }
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME, "Podcast_Episode_ActionMenu")
            putString("previousScreen", previousScreen)
            putString("catalogTitle",episode?.catalog?.title?:"")
            putString("catalogId",episode?.catalog?.id?:"")
            putString("contentTitle",episode?.title?:"")
            putString("contentId",episode?.id?:"")
            putString("entitySubtype", episode?.catalog?.type?:"")
        })
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ActivityEpisodeMenuBinding.inflate(inflater, container, false)

        intiViews()

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setData()

        initBottomSheetDialog()
    }

    private fun getDataFromIntent() {
        if(arguments?.parcelable<Content>(EPISODE) !=null) {
            episode = arguments?.parcelable(EPISODE)
        }
        if(arguments?.getString(ENTITY_SUB_TYPE)!=null) {
            entitySubType = arguments?.getString(ENTITY_SUB_TYPE).toString()
        }
        if(arguments?.getBoolean(IS_FROM_EPISODE) !=null) {
            isFromEpisode = arguments?.getBoolean(IS_FROM_EPISODE,false)?:false
        }
        if(arguments?.getString(PAGE_SOURCE)!=null) {
            pageSource = arguments?.getString(PAGE_SOURCE).toString()
        }

        if(arguments?.parcelable<LiveRoom>(LIVE_ROOM)!=null) {
            liveRoom = arguments?.parcelable(LIVE_ROOM)
        }
    }

    private fun intiViews() {

        if (episode?.catalog?.type == ENTITY_TYPE_AUDIO_BOOK) {
            binding.seePodCasts.text = getString(R.string.lihat_audio_book)
            binding.seeEpisodeDetails.text = getString(R.string.lihat_chapter)
            binding.coverImageLayout.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }

        downloadState = BaseApplication.application.getDownloadTracker().getStateFor(episode)

        manageState()

        if (downloadState == Download.STATE_DOWNLOADING ||
            downloadState == Download.STATE_QUEUED ||
            downloadState == Download.STATE_RESTARTING) {
            setDownloadListener()
        }

        if (isFromEpisode || episode?.entitySubType == ENTITY_TYPE_LIVE_STREAM ||
            episode?.catalogType == ENTITY_TYPE_LIVE_STREAM || episode?.catalog?.type == ENTITY_TYPE_LIVE_STREAM){
            binding.seeEpisodeDetails.visibility = GONE
        } else {
            binding.seeEpisodeDetails.visibility = VISIBLE
        }
        if (episode?.entitySubType == ENTITY_TYPE_LIVE_STREAM ||
            episode?.catalogType == ENTITY_TYPE_LIVE_STREAM || episode?.catalog?.type == ENTITY_TYPE_LIVE_STREAM){
            binding.seePodCasts.visibility = GONE
            if (PrefUtils.isLoggedIn && !episode?.users.isNullOrEmpty() && episode?.users?.get(0)?.id == PrefUtils.userDetails?.id && !liveRoom?.id.isNullOrEmpty()){
                binding.deleteRecording.visibility = VISIBLE
            } else {
                binding.deleteRecording.visibility = GONE
            }
        }else{
            binding.seePodCasts.visibility = VISIBLE
            binding.deleteRecording.visibility = GONE
            binding.hostLayout.visibility = GONE
        }
        binding.toolbar.setBackClick { dismiss() }

        binding.deleteRecording.setOnClickListener {

            deleteDialog = RecordingDeleteDialog.newInstance(liveRoom?.id?:"",object :OnClickInterface<Any>{
                override fun dataClicked(data: Any) {
                    if (!NetworkUtils.isNetworkConnected(mContext)) {
                        Utils.showSnackBar(mContext, getString(R.string.this_action_requires_internet))
                        return
                    }
                    val request = CreateRoomRequest(Constants.STATUS_ENDED)
                    request.type = liveRoom?.type
                    request.subtype = liveRoom?.subtype
                    binding.progressBar.visibility = VISIBLE
                    podcastViewModel.deleteRecording(liveRoom?.id ?: "", request).observe(viewLifecycleOwner) {
                        binding.progressBar.visibility = GONE
                        if (it?.status == ResponseStatus.SUCCESS) {
                            LiveTrigger.onRecordingDeleted(liveRoom?.id)
                            dismiss()
                        }
                    }
                }
            })
            deleteDialog?.show(childFragmentManager,RecordingDeleteDialog::class.java.name)
        }

        if (episode?.isDownloadable == false || (episode?.isPurchaseNeeded == true)) {
            binding.downloadLayout.visibility = GONE
        } else {
            binding.downloadLayout.visibility = VISIBLE
        }

        if (episode?.isPurchaseNeeded == true) {
            binding.markAsPlayed.visibility = GONE
            binding.addToQueue.visibility = GONE
            binding.like.visibility = GONE
            binding.disLike.visibility = GONE
            binding.reportContent.visibility = GONE
        } else {
            binding.markAsPlayed.visibility = VISIBLE
            binding.addToQueue.visibility = VISIBLE
            binding.like.visibility = VISIBLE
            binding.disLike.visibility = VISIBLE
            binding.reportContent.visibility = VISIBLE
        }

        binding.videoQuality.visibility = if (DataController.isPlayingVideo) VISIBLE else GONE

        binding.downloadLayout.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-download")
                (mContext as HomeActivity).handleUserNotLoggedIn(source = "content menu", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            if(downloadState == Download.STATE_COMPLETED) {
                NoiceAlertDialog.Builder()
                    .setTitle(mContext.getString(R.string.delete_download))
                    .setMessage(mContext.getString(R.string.delete_dowload_msg))
                    .setListener(object : OnClickInterface<Boolean> {
                        override fun dataClicked(data: Boolean, eventId: Int) {
                            if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                                ExoplayerUtils.removeDownload(episode?.id ?: "")
                                downloadState = -1
                                manageState()
                            }
                        }
                    }).show(childFragmentManager)
            } else if (downloadState == -1 || downloadState == Download.STATE_STOPPED || downloadState == Download.STATE_FAILED) {
                downloadState = Download.STATE_QUEUED
                episode?.let { episodeData ->
                    BaseApplication.application.getDownloadTracker().download(
                        null,
                        episodeData,
                        pageSource
                    )

                    AnalyticsUtil.sendEvent(
                        episode?.catalog?.type ?: "",
                        episode?.id ?: "",
                        episode?.id,
                        EVENT_CONTENT_DOWNLOAD_CLICKED,
                        "Menu",
                        (episode?.meta?.timeElapsed ?: 0).toString(),
                        (episode?.duration ?: 0).toString(),
                        "",
                        episode?.catalog?.title ?: "",
                        episode?.title ?: ""
                    )

                    setDownloadListener()
                }
            } else if (!episode?.id.isNullOrEmpty()) {
                ExoplayerUtils.stopDownload(episode?.id ?: "")
            }
        }

        binding.videoQuality.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            mContext.fetchActivity()?.supportFragmentManager?.let { manager ->
                videoQualityDialog = VideoQualityDialog.newInstance(episode?.id.toString())
                videoQualityDialog?.show(manager, "VideoQuality")
            }
            dismiss()
        }

        binding.addToPlayList.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-playlist")
                (mContext as HomeActivity).handleUserNotLoggedIn(source = "content menu", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            episode?.id?.let {
                episode?.entitySubType = entitySubType
                playlistDialog = PlayListDialog.newInstance(episode!!,pageSource)
                playlistDialog?.show(childFragmentManager, "pl_d")
            }
        }

        binding.markAsPlayed.setOnClickListener(CustomClickListener({
            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            if (episode?.meta?.markedAsPlayed == true){
                episode?.meta?.markedAsPlayed = false
                binding.markAsPlayed.text = getString(R.string.mark_as_played)
                binding.markAsPlayed.setTextColor(ContextCompat.getColor(mContext, R.color.dull_white))
                binding.markAsPlayed.compoundDrawables[Utils.DRAWABLE_START]?.setTint(
                    ContextCompat.getColor(
                        mContext,
                        R.color.dull_white
                    )
                )
            }else{
                episode?.meta?.markedAsPlayed = true
                binding.markAsPlayed.text = getString(R.string.marked_as_played)
                binding.markAsPlayed.setTextColor(ContextCompat.getColor(mContext, R.color.white50))
                binding.markAsPlayed.compoundDrawables[Utils.DRAWABLE_START]?.setTint(
                    ContextCompat.getColor(
                        mContext,
                        R.color.white50
                    )
                )
            }

            episode?.let {
                markAsPlayed(it)
                EventBus.getDefault().post(EventMessage(episode, Constants.MARK_AS_PLAYED))
            }
        },
        skipLogin = true
        ))

        binding.addToQueue.setOnClickListener {
            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (context?.unwrap() as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            episode?.let { it1 ->

                ContentPlayRequest.Builder()
                    .contents(listOf(it1))
                    .pageSource(pageSource)
                    .addToManualQueue(mContext)
                MoEngageAnalytics.sendEvent(mContext, "content queued", Bundle().apply {
                    putString("vertical", it1.catalog?.type?: it1.entitySubType?:"")
                    putString("catalog title", it1.catalog?.title?:"")
                    putString("content title", it1.title?:"")
                    putString("catalog id", it1.catalog?.id?:"")
                    putString("content id", it1.id?:"")
                    putString("content duration", (it1.duration?:0).toString())
                    putString("source", it1.catalog?.type + "_page")
                    putStringArrayList("genre", ArrayList(it1.genres?.map { it.name ?: "" } ?: ArrayList()))
                    putString("catalog source", it1.source?:"")
                })
            }
        }

        binding.seeQueue.setOnClickListener(CustomClickListener ({
            val bundle = Bundle()
            bundle.putString("title","")
            bundle.putString("source", "Podcast_Episode_ActionMenu")
            QueuePlayerFragment.showAllowingStateLoss(childFragmentManager, bundle)
        },"content menu"))

        binding.share.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            val dialog = ShareDialog.newInstance(episode,false,null)
            dialog.show(childFragmentManager, "share_menu")
        }

        binding.like.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-content-like-dislike")
                mContext.getBaseActivity()?.handleUserNotLoggedIn(source = "content menu", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            likedStatus = if(likedStatus == 1) {
                basePlayerViewModel.isContentLiked(false)
                0
            } else {
                basePlayerViewModel.isContentLiked(true)
                1
            }

            val isCatalogFollowed = episode?.catalogMeta?.userActions?.find { action ->
               action.action == UserAction.FOLLOW.action
            }?.actionValue == CATALOG_FOLLOWED

            performAction(UserAction.LIKE.action)

            AnalyticsUtil.sendEvent(
                entitySubType,
                episode?.id ?: "",
                episode?.catalog?.id ?: "",
                EVENT_CONTENT_LIKED,
                "queue",
                "",
                (episode?.duration ?: 0).toString(),
                "",
                episode?.catalog?.title ?: "",
                episode?.title ?: "",
                isCatalogFollowed = if (isCatalogFollowed) "yes" else "no"
            )

            val contentLink = "${Constants.WEB_BASE_URL}content/${episode?.id}"
            val catalogLink = "${Constants.WEB_BASE_URL}catalog/${episode?.catalogId ?: episode?.catalog?.id ?: ""}"

            MoEngageAnalytics.sendEvent(mContext, "content liked", Bundle().apply {
                putString("entity type", episode?.catalogType?:episode?.catalog?.type?:"")
                putString("catalog title", episode?.catalogTitle?:episode?.catalog?.title?:"")
                putString("content title", episode?.title?:"")
                putString("catalog id", episode?.catalogId?:episode?.catalog?.id?:"")
                putString("content id", episode?.id?:"")
                putString("catalog source", episode?.source?:episode?.catalog?.source?:"")
                putString("catalog follow", if (isCatalogFollowed) "yes" else "no")
                putString("content link", contentLink)
                putString("catalog link", catalogLink)
            })
        }

        binding.disLike.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-content-like-dislike")
                mContext.getBaseActivity()?.handleUserNotLoggedIn(source = "content menu", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            likedStatus = if(likedStatus == -1) {
                0
            } else {
                -1
            }
            basePlayerViewModel.isContentLiked(false)

            performAction(UserAction.DISLIKE.action)

            AnalyticsUtil.sendEvent(
                entitySubType,
                episode?.id ?: "",
                episode?.catalog?.id ?: "",
                EVENT_CONTENT_DISLIKED,
                "queue",
                "",
                (episode?.duration ?: 0).toString(),
                "",
                episode?.catalog?.title ?: "",
                episode?.title ?: ""
            )

            val contentLink = "${Constants.WEB_BASE_URL}content/${episode?.id}"
            val catalogLink = "${Constants.WEB_BASE_URL}catalog/${episode?.catalogId ?: episode?.catalog?.id ?: ""}"

            MoEngageAnalytics.sendEvent(mContext, "content disliked", Bundle().apply {
                putString("entity type", episode?.catalogType?:episode?.catalog?.type?:"")
                putString("catalog title", episode?.catalogTitle?:episode?.catalog?.title?:"")
                putString("content title", episode?.title?:"")
                putString("catalog id", episode?.catalogId?:episode?.catalog?.id?:"")
                putString("content id", episode?.id?:"")
                putString("catalog source", episode?.source?:episode?.catalog?.source?:"")
                putString("content link", contentLink)
                putString("catalog link", catalogLink)
            })
        }

        binding.seePodCasts.setOnClickListener {
            val source = if (isFromEpisode){
                episode?.catalog?.type+"_Page"
            }else{
                "Podcast_Media_Player"
            }
            when (episode?.catalog?.type) {
                ENTITY_TYPE_AUDIO_BOOK -> {
                    EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_AUDIO_BOOK_PAGE, episode?.catalogId,source))
                }
                Constants.ENTITY_TYPE_AUDIO_SERIES -> {
                    EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_AUDIO_SERIES_PAGE, episode?.catalogId,source))
                }
                else -> {
                    EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CATALOG_PAGE, episode?.catalogId,source))
                }
            }
            dismiss()
        }

        binding.seeEpisodeDetails.setOnClickListener {
            if (episode?.catalog?.type == ENTITY_TYPE_AUDIO_BOOK){
                EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CONTENT_PAGE, episode?.id,"AudioBook_ActionMenu"))
            }else{
                EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CONTENT_PAGE, episode?.id,"Podcast_Episode_ActionMenu"))
            }

            dismiss()
        }

        binding.reportContent.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            if (!PrefUtils.isLoggedIn) {
                val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-report-content")
                mContext.getPlayerActivity()?.handleUserNotLoggedIn(source = "content menu", loginDialogData = loginDialogData)
                return@setOnClickListener
            }

            Intent(mContext, ReportActivity::class.java).apply {
                putExtra(ReportActivity.ENTITY_ID, episode?.id)
                putExtra(ReportActivity.ENTITY_TYPE, "content")
                mContext.startActivity(this)
            }
        }
    }

    private fun getLiveDetail() {
        val map  = HashMap<String, String>()
        map["includeEntities"] = "[\"roomParticipants\"]"
        podcastViewModel.getRoomDetailsByContentId(episode?.id?:"",map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null){
                liveRoom = it.data.data
                setHostData()
            }
        }
    }
    /** setting host profile picture in top card */
    private fun setHostData() {
        binding.hostLayout.visibility = VISIBLE
        binding.details.visibility = GONE
        binding.episodeName.text = liveRoom?.title
        val readMoreOption = ReadMoreOption.Builder(mContext)
            .textLength(2, ReadMoreOption.TYPE_LINE)
            .expandAnimation(true)
            .build()
        readMoreOption.addReadMoreTo(binding.artistName, liveRoom?.description ?: "")

        liveRoom?.roomParticipants?.find { it.type == ROLE_HOST }.let { host ->
            if (host != null) {
                ImageUtils.loadImageByUrl(binding.coverImage, host.user?.smallImage, circleCrop = true, placeHolder = R.drawable.ic_user_profile, originalUrl = host.user?.originalImage)
                if (host.user?.id == PrefUtils.userDetails?.id){
                    binding.textUserName.text = getString(R.string.you)
                }else{
                    binding.textUserName.text = "@".plus(host.user?.userName ?: "host")
                }
                binding.textUserName.visibility = VISIBLE
                if (host.user?.isVerified == true){
                    binding.textUserName.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_shield, 0,R.drawable.ic_verified_tag,0)
                }else{
                    binding.textUserName.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_shield, 0, 0, 0)
                }
            }
        }
    }

    private fun setDownloadListener() {
        if (downloadListener != null)
            return

        downloadListener = object : ExoPlayerDownloadTracker.DownloadProgressListener(episode?.id ?: "") {
            override fun onDownloadsChanged(download: Download) {
                manageState(download)
            }

            override fun onProgressChanged(download: Download) {
                manageState(download)
            }
        }
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().addListener(it)
        }
    }

    private fun markAsPlayed(content: Content) {
        val action : String = if (content.meta?.markedAsPlayed == true){
            EventConstant.MARK_PLAYED
        }else{
            EventConstant.MARK_UN_PLAYED
        }

        val payload = Payload(
                content.meta?.timeElapsed?.toInt(),
                content.id,
                ENTITY_TYPE_CONTENT,
                entitySubType,
                pageSource,
                PrefUtils.uniqueId,
                content.duration?.toInt(),
                catalogId = content.catalog?.id ?: ""
        )

        val event = EventPost(action, payload, contentId = content.id?:"")
        AnalyticsUtil.sendEvent(
            entitySubType,
            content.id,
            "",
            action,
            pageSource,
            "",
            ""
        )

        podcastViewModel.postEvent(event)
    }

    private fun setData() {
       val placeholder =  if (liveRoom!=null){
           R.drawable.ic_user_profile
        }else{
           R.drawable.ic_thumb_square
       }

        if(episode?.duration != null) {
            binding.details.text = getString(
                R.string.int_minutes_date, DateUtils.getMinuteStrFromSeconds(
                    episode?.duration
                ), DateUtils.formatDateToLocalMonthName(episode?.publishedAt)
            )
        }

        if (episode?.entitySubType == ENTITY_TYPE_LIVE_STREAM ||
            episode?.catalogType == ENTITY_TYPE_LIVE_STREAM || episode?.catalog?.type == ENTITY_TYPE_LIVE_STREAM){
            binding.coverImageLayout.setBackgroundColor(Color.TRANSPARENT)
            binding.details.visibility = GONE
            if (liveRoom == null){
                getLiveDetail()
            }else{
                setHostData()
            }
        }else{
            binding.details.visibility = VISIBLE
            ImageUtils.loadImageByUrl(binding.coverImage, episode?.imageMeta?.size300, placeHolder = placeholder, originalUrl =  ImageUtils.getOriginalImage(episode))
            if(!episode?.title.isNullOrEmpty()) {
                binding.episodeName.text = episode?.title
            }
            if(!episode?.catalog?.title.isNullOrEmpty()) {
                binding.artistName.text = "Oleh "+episode?.catalog?.title
            }else if(!episode?.catalogTitle.isNullOrEmpty()) {
                binding.artistName.text = "Oleh "+episode?.catalogTitle
            }
        }

        if(episode?.meta?.markedAsPlayed == true) {
            binding.markAsPlayed.text = getString(R.string.marked_as_played)
            binding.markAsPlayed.setTextColor(ContextCompat.getColor(mContext, R.color.white50))
            binding.markAsPlayed.compoundDrawables[Utils.DRAWABLE_START]?.setTint(
                ContextCompat.getColor(
                    mContext,
                    R.color.white50
                )
            )
        } else {
            binding.markAsPlayed.text = getString(R.string.mark_as_played)
            binding.markAsPlayed.setTextColor(ContextCompat.getColor(mContext, R.color.dull_white))
            binding.markAsPlayed.compoundDrawables[Utils.DRAWABLE_START]?.setTint(
                ContextCompat.getColor(
                    mContext,
                    R.color.dull_white
                )
            )
        }

        val likeAction = episode?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.LIKE.action, true)
        }

        val dislikeAction = episode?.meta?.userActions?.find { mediaAction ->
            mediaAction.action.equals(UserAction.DISLIKE.action, true)
        }

        likedStatus = when {
            likeAction?.actionValue == 1.0 -> {
                1
            }
            dislikeAction?.actionValue == 1.0 -> {
                -1
            }
            else -> {
                0
            }
        }

        updateStatus()
    }

    private fun performAction(action: String) {

        val value = if(likedStatus == 1 || likedStatus == -1) {
            1.0
        } else {
            0.0
        }


        updateObject(action,value)

        updateStatus()

        EventBus.getDefault().post(EventMessage(UserActionEvent(action, episode, episode?.meta), Constants.USER_ACTION_EVENT))

        val mediaAction = MediaAction(
                action,
                value,
                episode?.id,
                ENTITY_TYPE_CONTENT,
                PrefUtils.userDetails?.id.toString(),
                entitySubType,
                null
        )

        AnalyticsUtil.sendEvent(entitySubType, episode?.id, episode?.catalogId, action, pageSource)

        podcastViewModel.performAction(mediaAction).observe(this) {

        }
    }

    private fun updateObject(action: String, value: Double) {
        if (action == UserAction.LIKE.action){
            if (value == 1.0) {
                episode?.meta?.aggregations?.likes = episode?.meta?.aggregations?.likes?.plus(1)
                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.LIKE.action, true)
                }?.let {
                    if (it.actionValue == 0.0) {
                        it.actionValue = 1.0
                    }
                }

                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.DISLIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                        episode?.meta?.aggregations?.dislikes =
                                episode?.meta?.aggregations?.dislikes?.minus(
                                        1
                                )
                    }
                }
            } else if (value == 0.0) {
                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.LIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                    }
                }
                episode?.meta?.aggregations?.likes = episode?.meta?.aggregations?.likes?.minus(1)
            }
        }else if (action == UserAction.DISLIKE.action){
            if (value == 1.0) {
                episode?.meta?.aggregations?.dislikes = episode?.meta?.aggregations?.dislikes?.plus(1)
                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.DISLIKE.action, true)
                }?.let {
                    if (it.actionValue == 0.0) {
                        it.actionValue = 1.0
                    }
                }
                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.LIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                        episode?.meta?.aggregations?.likes = episode?.meta?.aggregations?.likes?.minus(1)
                    }
                }
            } else if (value == 0.0) {
                episode?.meta?.userActions?.find {
                    it.action.equals(UserAction.DISLIKE.action, true)
                }?.let {
                    if (it.actionValue == 1.0) {
                        it.actionValue = 0.0
                    }
                }
                episode?.meta?.aggregations?.dislikes = episode?.meta?.aggregations?.dislikes?.minus(1)
            }
        }

    }

    private fun updateStatus() {

        val d1 = ContextCompat.getDrawable(mContext, R.drawable.ic_thumb_up)
        val d2 = ContextCompat.getDrawable(mContext, R.drawable.ic_thumb_down)
        val likeDrawable = DrawableCompat.wrap(d1!!)
        val unLikeDrawable = DrawableCompat.wrap(d2!!)

        if(likedStatus == 1) {
            binding.like.text = getString(R.string.liked)
            binding.like.setTextColor(ContextCompat.getColor(mContext, R.color.orange_yellow))
            DrawableCompat.setTint(likeDrawable, ContextCompat.getColor(mContext, R.color.orange_yellow))
        } else {
            binding.like.text = getString(R.string.like)
            binding.like.setTextColor(ContextCompat.getColor(mContext, R.color.dull_white))
            DrawableCompat.setTint(likeDrawable, ContextCompat.getColor(mContext, R.color.dull_white))
        }
        binding.like.setCompoundDrawablesWithIntrinsicBounds(likeDrawable,null,null,null)

        if(likedStatus == -1) {
            binding.disLike.text = getString(R.string.disliked)
            binding.disLike.setTextColor(ContextCompat.getColor(mContext, R.color.orange_yellow))
            DrawableCompat.setTint(unLikeDrawable, ContextCompat.getColor(mContext, R.color.orange_yellow))
        } else {
            binding.disLike.text = getString(R.string.dislike)
            binding.disLike.setTextColor(ContextCompat.getColor(mContext, R.color.dull_white))
            DrawableCompat.setTint(unLikeDrawable, ContextCompat.getColor(mContext, R.color.dull_white))
        }
        binding.disLike.setCompoundDrawablesWithIntrinsicBounds(unLikeDrawable,null,null,null)
    }

    private fun setupFullHeight(bottomSheetDialog: BottomSheetDialog) {
        val bottomSheet =
            bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val behavior = BottomSheetBehavior.from(bottomSheet)
        val layoutParams = bottomSheet.layoutParams
        val windowHeight = Utils.getDisplayMetrics(mContext).heightPixels
        layoutParams?.height = windowHeight
        behavior.isHideable = false
        bottomSheet.layoutParams = layoutParams
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.isHideable = false
        behavior.peekHeight = windowHeight
        bottomSheet.parent.requestLayout()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog: Dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            setupFullHeight(bottomSheetDialog)
        }
        return dialog
    }

    private fun manageState(download: Download? = null) {
        download?.state?.let { state ->
            downloadState = state
        }

        when (downloadState) {
            Download.STATE_COMPLETED -> {
                releaseDownloadListener()

                binding.download.text = getString(R.string.downloaded)
                binding.download.setTextColor(ContextCompat.getColor(mContext, R.color.white50))
                binding.downloadIcon.setImageResource(R.drawable.ic_download_grey)
                binding.downloadProgress.visibility = GONE
            }
            Download.STATE_QUEUED -> {
                binding.download.text = getString(R.string.in_queue)
                binding.download.setTextColor(ContextCompat.getColor(mContext, R.color.white))
                binding.downloadIcon.setImageResource(R.drawable.ic_download_stop)
                binding.downloadProgress.progress = 0
                binding.downloadProgress.visibility = VISIBLE
            }
            Download.STATE_DOWNLOADING -> {
                binding.download.text = getString(R.string.downloading)
                binding.download.setTextColor(ContextCompat.getColor(mContext, R.color.white))
                binding.downloadIcon.setImageResource(R.drawable.ic_download_stop)
                binding.downloadProgress.progress = download?.percentDownloaded?.toInt() ?: 0
                binding.downloadProgress.visibility = VISIBLE
            }
            Download.STATE_STOPPED -> {
                releaseDownloadListener()

                binding.download.text = getString(R.string.download)
                binding.download.setTextColor(ContextCompat.getColor(mContext, R.color.white))
                binding.downloadIcon.setImageResource(R.drawable.ic_download)
                binding.downloadProgress.visibility = GONE
            }
            else -> {
                releaseDownloadListener()

                binding.download.text = getString(R.string.download)
                binding.download.setTextColor(ContextCompat.getColor(mContext, R.color.white))
                binding.downloadIcon.setImageResource(R.drawable.ic_download)
                binding.downloadProgress.visibility = GONE
            }
        }
    }

    private fun releaseDownloadListener() {
        downloadListener?.let {
            BaseApplication.application.getDownloadTracker().removeListener(it)
            downloadListener = null
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        EventBus.getDefault().post(EventMessage(null, Constants.DOWNLOAD_CHECK))
        super.onDismiss(dialog)
    }

    override fun onDestroyView() {
        releaseDownloadListener()
        playlistDialog?.dismiss()
        super.onDestroyView()
    }

    private fun initBottomSheetDialog() {
        val d = dialog as BottomSheetDialog
        val bottomSheet = d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)


        bottomSheetBehavior.isHideable = false
        bottomSheetBehavior.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {}
            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })
    }
}