package noice.app.modules.dashboard.home.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.listner.OnClickInterface
import noice.app.modules.media.model.Playlist
import noice.app.views.PlaylistView

class PlaylistDialogAdapter(private val listener: OnClickInterface<Playlist>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var playlists = ArrayList<Playlist>()
    private lateinit var content : PlaylistView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        content = PlaylistView(parent.context)
        return content.viewHolder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        content.viewHolder = holder as PlaylistView.ViewHolder
        content.setData(listener, playlists[position])
    }

    override fun getItemCount(): Int {
        return playlists.size
    }

    fun addAll(playlists : ArrayList<Playlist>) {
        this.playlists = playlists
        notifyDataSetChanged()
    }
}