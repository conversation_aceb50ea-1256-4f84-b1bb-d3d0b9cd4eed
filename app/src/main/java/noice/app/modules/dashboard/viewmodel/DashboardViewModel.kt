package noice.app.modules.dashboard.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.model.user.preference.PreferenceData
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.live.repository.LiveApiRepository
import noice.app.modules.media.model.MediaAction
import noice.app.modules.media.model.PlaylistContentRequest
import noice.app.modules.notification.model.NotificationRequest
import noice.app.modules.onboarding.models.ShareRequest
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.repository.ChannelPodcastApiRepository
import noice.app.modules.profile.repository.NotificationApiRepository
import noice.app.modules.profile.repository.ProfileRepository
import okhttp3.MultipartBody
import okhttp3.RequestBody
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val onBoardingRepository: OnBoardingApiRepository,
    private val repository: DashboardApiRepository,
    private val profileRepository: ProfileRepository,
) : ViewModel() {

    private val catalogRepository = ChannelPodcastApiRepository()

    private val notificationRepo = NotificationApiRepository()

    private val liveApiRepository = LiveApiRepository()

    fun getHomeSegment(map: HashMap<String, String>,type:String, isRefreshing : Boolean = false) = repository.getHomeSegment(map, type, isRefreshing)

    fun getHomeBanner(target: String, cachingType : String = "") = repository.getHomeBanner(target, cachingType)

    fun getCommunityDetail(map: HashMap<String,String>) = repository.getCommunityDetail(map)

    fun performAction(mediaAction: MediaAction) = repository.performAction(mediaAction)

    fun getUserDetails(map: HashMap<String, String>) = onBoardingRepository.getUserDetails(map)

    fun getAdConfig(map: HashMap<String, String>) = catalogRepository.getAdConfig(map)

    fun createPlayList(filePart: MultipartBody.Part?, map: HashMap<String, RequestBody>) = repository.createPlayList(filePart, map)

    fun editPlayList(filePart: MultipartBody.Part?, map: HashMap<String, RequestBody>, id : String) = repository.editPlayList(filePart, map, id)

    fun getPlaylists() = repository.getPlaylists()

    fun addContentInPlayList(request : PlaylistContentRequest, playlistId : String) = repository.addContentInPlayList(request, playlistId)

    fun shareContent(shareRequest: ShareRequest,isRadioLive:Boolean,endTime:String?) = repository.shareContent(shareRequest,isRadioLive,endTime)

    fun getLihatSemuaHome(segmentId: String, page:Int, limit:Int, fetchCacheWhenNoInternet : Boolean = false) = repository.getLihatSemuaHome(segmentId,page,limit, fetchCacheWhenNoInternet)

    fun getLiveRadio() = repository.getLiveRadio()

    fun shouldReviewApp() = repository.shouldReviewApp()

    fun getPlaylistContent(playlistId : String,page:Int,limit:Int) = catalogRepository.getPlaylistContent(playlistId,page,limit)

    fun getEpisodeDetails(episodeId : String) = catalogRepository.getEpisodeDetails(episodeId)

    fun markAllRead(request : NotificationRequest) = notificationRepo.markAllRead(request)

    fun getRoomDetails(id : String, map : HashMap<String, String>) = liveApiRepository.getRoomDetails(id, map)

    fun getAgoraToken(id : String) = liveApiRepository.getAgoraToken(id)

    fun setUserPreference(prefData : PreferenceData) = onBoardingRepository.setUserPreference(prefData)

    fun postEvent(eventPost: EventPost) = catalogRepository.updateEvents(listOf(eventPost))

    fun getUserDetailsByUserId(
        userId: String,
        map: HashMap<String, Any> = HashMap(),
        cachingConstant: String?
    ) = profileRepository.getUserDetailsByUserId(userId, map, cachingConstant)

    fun getFirebaseToken() = onBoardingRepository.getFirebaseToken()

    fun getPageName(pageValue: String) = repository.getPageName(pageValue)
}