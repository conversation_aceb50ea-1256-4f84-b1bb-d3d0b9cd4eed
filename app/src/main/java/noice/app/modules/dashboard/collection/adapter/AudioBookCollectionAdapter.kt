package noice.app.modules.dashboard.collection.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.modules.podcast.model.Channel
import noice.app.views.homesegmentviews.DefaultSegmentView

class AudioBookCollectionAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Channel?>,
    listener: LoadMoreAdapterListener?,
    private val pageSource : String
) : LoadMoreAdapter<Channel>(recyclerView, dataSet, listener) {

    private lateinit var defaultSegmentView: DefaultSegmentView

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        defaultSegmentView = DefaultSegmentView(parent.context, "catalog", "","Collection")
        defaultSegmentView.setWidth()
        defaultSegmentView.shouldHandleAudioBook(false)
        defaultSegmentView.makeImageSquare(false)
        return defaultSegmentView.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        if(dataSet[position] != null) {
            defaultSegmentView.viewHolder = holder as DefaultSegmentView.ViewHolder
            defaultSegmentView.setData(dataSet[position]!!, pageSource, true)
        }
    }
}