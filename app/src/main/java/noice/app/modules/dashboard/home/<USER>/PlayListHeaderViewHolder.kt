package noice.app.modules.dashboard.home.viewholder

import com.thesurix.gesturerecycler.GestureViewHolder
import noice.app.databinding.QueueHeaderItemBinding
import noice.app.modules.media.model.PlayListDataItem

class PlayListHeaderViewHolder(binding: QueueHeaderItemBinding) : GestureViewHolder<PlayListDataItem>(binding.root) {

    override fun canDrag() = false

    override fun canSwipe() = false

    override fun bind(item: PlayListDataItem) {

    }
}
