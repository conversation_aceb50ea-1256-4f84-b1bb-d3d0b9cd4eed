package noice.app.modules.dashboard.home.adapter

import android.content.Context
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.*
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.DataController
import noice.app.exoplayer.BasePlayerActivity
import noice.app.model.ExtraData
import noice.app.modules.dashboard.adapter.HomeInnerAdapter
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.podcast.model.Content
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.PermissionUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.unwrap
import noice.app.views.LiveSegment
import noice.app.views.PodcastHorizontalView
import noice.app.views.homesegmentviews.*
import org.greenrobot.eventbus.EventBus

class HomeLihatSemuaAdapter(
    recyclerView: RecyclerView,
    listener: LoadMoreAdapterListener?,
    private val type: String,
    dataSet: ArrayList<*>,
    private val entityType: String? = "", val mContext: Context,
    private val extraData: ExtraData?,
    private val permissionUtils: PermissionUtils? = null
) : LoadMoreAdapter<Any>(recyclerView, dataSet as ArrayList<Any?>, listener) {

    companion object {
        const val TYPE_EPISODE_BARU = "episode_baru"
        const val TYPE_DEFAULT = "default"
        const val TYPE_ARTIST = "artist"
        const val TYPE_RADIO = "radio"
        const val TYPE_RADIO_PODCAST = "radioPodcast"
        const val TYPE_RECENTLY_PLAYED = "recently_played"
        const val TYPE_CONTENT_HORIZONTAL = "content_horizontal"
        const val TYPE_AUDIO_BOOK = "audiobook"
        const val TYPE_LIVE = "livestream"
    }

    private lateinit var artistSegmentView: ArtistSegmentView
    private lateinit var episodeBaruSegment: RecentlyPlayedSegment
    private lateinit var defaultSegmentView: DefaultSegmentView
    private lateinit var radioSegmentView: RadioSegmentView
    private lateinit var recentlyPlayedView: RecentPlaysVertical
    private lateinit var contentHorizontalSegment: PodcastHorizontalView
    private lateinit var radioPodcast: PodcastHorizontalView
    private lateinit var liveSegment: LiveSegment
    private var liveEventsJob : Job? = null

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        return when (type) {
            TYPE_EPISODE_BARU -> {
                episodeBaruSegment =
                    RecentlyPlayedSegment(parent.context, entityType, "", "Lihat Semua")
                episodeBaruSegment.setWidth()
                episodeBaruSegment.setExtraData(extraData)
                episodeBaruSegment.viewHolder
            }
            TYPE_ARTIST -> {
                artistSegmentView =
                    ArtistSegmentView(parent.context, entityType, "", "Home_For_You_Artist")
                artistSegmentView.viewHolder
            }
            TYPE_RADIO -> {
                radioSegmentView = RadioSegmentView(parent.context, entityType, "", "Lihat Semua")
                radioSegmentView.setWidth()
                radioSegmentView.setExtraData(extraData)
                radioSegmentView.viewHolder
            }

            TYPE_RECENTLY_PLAYED -> {
                recentlyPlayedView =
                    RecentPlaysVertical(parent.context, entityType, "Lihat Semua", "Lihat_Semua")
                recentlyPlayedView.setExtraData(extraData)
                recentlyPlayedView.viewHolder
            }
            TYPE_CONTENT_HORIZONTAL -> {
                contentHorizontalSegment =
                    PodcastHorizontalView(parent.context, entityType, "", "Lihat Semua")
                contentHorizontalSegment.setWidth()
                contentHorizontalSegment.setExtraData(extraData)
                contentHorizontalSegment.viewHolder
            }
            TYPE_RADIO_PODCAST -> {
                radioPodcast = PodcastHorizontalView(parent.context, entityType, "", "Lihat Semua")
                radioPodcast.setWidth()
                radioPodcast.setExtraData(extraData)
                radioPodcast.viewHolder
            }
            TYPE_AUDIO_BOOK -> {
                defaultSegmentView = DefaultSegmentView(parent.context, entityType, "", "Lihat Semua")
                defaultSegmentView.setWidth()
                defaultSegmentView.shouldHandleAudioBook(false)
                defaultSegmentView.makeImageSquare(false)
                defaultSegmentView.setExtraData(extraData)
                defaultSegmentView.viewHolder
            }
            TYPE_LIVE -> {
                liveSegment = LiveSegment(parent.context, entityType, "", "LiveLihatSemua")
                liveSegment.setWidth()
                liveSegment.setExtraData(extraData)
                liveSegment.viewHolder
            }
            else -> {
                defaultSegmentView =
                    DefaultSegmentView(parent.context, entityType, "", "Lihat Semua")
                defaultSegmentView.setWidth()
                defaultSegmentView.shouldHandleAudioBook(true)
                defaultSegmentView.setExtraData(extraData)
                defaultSegmentView.viewHolder
            }
        }
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        when (type) {
            TYPE_EPISODE_BARU -> {
                episodeBaruSegment.viewHolder = holder as RecentlyPlayedSegment.ViewHolder
                episodeBaruSegment.setData(dataSet[position] as HomeContent)
                episodeBaruSegment.viewHolder.playButton.setOnClickListener {

                    EventBus.getDefault().post(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_CONTENT_PAGE,
                            (dataSet[position] as HomeContent).id.toString(),
                            "Podcast_Lihat Semua"
                        )
                    )
                }
            }
            TYPE_ARTIST -> {
                artistSegmentView.viewHolder = holder as ArtistSegmentView.ViewHolder
                artistSegmentView.setData(dataSet[position] as HomeContent)
            }
            TYPE_RADIO -> {
                radioSegmentView.viewHolder = holder as RadioSegmentView.ViewHolder
                radioSegmentView.setData(dataSet[position] as HomeContent)
            }
            TYPE_RECENTLY_PLAYED -> {
                recentlyPlayedView.viewHolder = holder as RecentPlaysVertical.ViewHolder
                recentlyPlayedView.setData(dataSet[position] as HomeContent)
                recentlyPlayedView.viewHolder.actionButton.setOnClickListener {
                    val ctx = it.context.unwrap()
                    if (DataController.isAdPlaying) {
                        Utils.showSnackBar(ctx, ctx.getString(R.string.playback_will_resume_after_ad))
                        return@setOnClickListener
                    }

                    if (DataController.playerContentId == (dataSet[position] as HomeContent).id) {
                        if (ctx is BasePlayerActivity) {
                            if (ctx.isPlayWhenReady()) {
                                ContentPlayRequest.Builder()
                                    .playWhenReady(ctx, false)
                            } else {
                                ContentPlayRequest.Builder()
                                    .playWhenReady(ctx, true)
                            }
                        }
                        return@setOnClickListener
                    }

                    val data = dataSet[position] as HomeContent

                    if (data.entityType == Constants.ENTITY_TYPE_CATALOG) {
                        val entitySubType = data.catalog?.type ?: data.entitySubType
                        ClickHandler.redirect(data.id.toString(), data.entityType.toString(), entitySubType.toString(), AnalyticsUtil.index_terakhir_deputar, extraData)
                    } else {
                        ContentPlayRequest.Builder()
                            .homeContents(listOf(data))
                            .pageSource(AnalyticsUtil.index_terakhir_deputar)
                            .queueTitle("Recent played")
                            .extraData(extraData)
                            .contentFetchLimit(20)
                            .addCatalogContents(true)
                            .play(it.context)

                        (dataSet[position] as? HomeContent)?.isPlaying = true
                        notifyItemChanged(position)
                    }
                }
            }
            TYPE_CONTENT_HORIZONTAL -> {
                contentHorizontalSegment.viewHolder = holder as PodcastHorizontalView.ViewHolder
                contentHorizontalSegment.setData(dataSet[position] as HomeContent)
            }
            TYPE_RADIO_PODCAST -> {
                radioPodcast.viewHolder = holder as PodcastHorizontalView.ViewHolder
                radioPodcast.setData(dataSet[position] as Content)
            }
            TYPE_AUDIO_BOOK -> {
                defaultSegmentView.viewHolder = holder as DefaultSegmentView.ViewHolder
                defaultSegmentView.setData(dataSet[position] as HomeContent, "lihat_semua")
            }
            TYPE_LIVE -> {
                liveSegment.viewHolder = holder as LiveSegment.ViewHolder
                liveSegment.setData(dataSet[position] as HomeContent, "lihat_semua", permissionUtils)
            }
            TYPE_DEFAULT -> {
                defaultSegmentView.viewHolder = holder as DefaultSegmentView.ViewHolder
                defaultSegmentView.setData(dataSet[position] as HomeContent, "lihat_semua")
            }
        }
    }

    private fun observeLiveTrigger() {
        if (liveEventsJob?.isActive == true) {
            return
        }
        liveEventsJob = CoroutineScope(Dispatchers.Main).launch {
            LiveTrigger.event.collect {
                when (it) {
                    is LiveTrigger.Event.RecordingDeleted -> {
                        val roomId = it.roomId.orEmpty()
                        val content = HomeContent(roomId)
                        val index = dataSet.indexOf(content)
                        if (index > -1){
                            dataSet.removeAt(index)
                            notifyItemRemoved(index)
                        }
                    }
                    else -> {}
                }
            }
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)

        if (shouldRegisterEventBus()) {
            observeLiveTrigger()
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)

        liveEventsJob?.cancel()
    }

    private fun shouldRegisterEventBus() = type == HomeInnerAdapter.TYPE_NOICE_LIVE
}