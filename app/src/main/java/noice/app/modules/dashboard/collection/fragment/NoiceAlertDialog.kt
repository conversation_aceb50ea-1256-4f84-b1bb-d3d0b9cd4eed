package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.InsetDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import noice.app.R
import noice.app.databinding.FragmentNoiceAlertDialogBinding
import noice.app.listner.OnClickInterface

class NoiceAlertDialog : DialogFragment() {

    companion object {
        const val POSITIVE_BTN_CLICK = 88
        const val NEGATIVE_BTN_CLICK = 99
        const val BACK_PRESS = 77
    }

    private lateinit var binding : FragmentNoiceAlertDialogBinding
    private lateinit var ctx : Context
    private var listener : OnClickInterface<Boolean>? = null
    private var useNewDesign = false
    private var title = ""
    private var message = ""
    private var positiveButtonText = ""
    private var negativeButtonText = ""
    private var negativeButtonColor = R.color.dull_yellow
    private var showLoaderOnPositiveClick = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        useNewDesign = arguments?.getBoolean(Builder.USE_NEW_DESIGN) ?: false
        title = arguments?.getString(Builder.TITLE, "") ?: ""
        message = arguments?.getString(Builder.MESSAGE, "") ?: ""
        positiveButtonText = arguments?.getString(Builder.POSITIVE_BUTTON_TEXT, ctx.getString(R.string.yes_delete)) ?: ""
        negativeButtonText = arguments?.getString(Builder.NEGATIVE_BUTTON_TEXT, ctx.getString(R.string.not)) ?: ""
        negativeButtonColor = arguments?.getInt(Builder.NEGATIVE_BUTTON_COLOR, R.color.dull_yellow) ?: R.color.dull_yellow
        showLoaderOnPositiveClick = arguments?.getBoolean(Builder.SHOW_LOADER_ON_POSITIVE_CLICK, false) ?: false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentNoiceAlertDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onStart() {
        super.onStart()

        dialog?.window?.setBackgroundDrawable(
            InsetDrawable(
                ColorDrawable(Color.TRANSPARENT), ctx.resources.getDimensionPixelSize(
                    R.dimen.dp16
                )
            )
        )
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        intiViews()

        setData()
    }

    private fun intiViews() {

        if (useNewDesign) {
            binding.newDesign.visibility = View.VISIBLE
            binding.oldDesign.visibility = View.GONE
        } else {
            binding.newDesign.visibility = View.GONE
            binding.oldDesign.visibility = View.VISIBLE
        }

        binding.positiveBtn.setOnClickListener {
            if (showLoaderOnPositiveClick) {
                binding.positiveBtn.alpha = 0.5f
                binding.positiveBtnNew.alpha = 0.5f
                binding.loader.visibility = View.VISIBLE
                binding.loaderNew.visibility = View.VISIBLE
            } else {
                dismiss()
            }
            listener?.dataClicked(true, POSITIVE_BTN_CLICK)
            listener?.dataClicked(true)
        }

        binding.negativeBtn.setOnClickListener {
            dismiss()
            listener?.dataClicked(true, NEGATIVE_BTN_CLICK)
            listener?.dataClicked(true)
        }

        binding.positiveBtnNew.setOnClickListener {
            binding.positiveBtn.performClick()
        }

        binding.negativeBtnNew.setOnClickListener {
            binding.negativeBtn.performClick()
        }
    }

    fun setData() {
        binding.title.text = title
        binding.description.text = message

        binding.positiveBtn.text = positiveButtonText
        binding.positiveBtnNew.text = positiveButtonText

        binding.negativeBtn.text = negativeButtonText
        binding.negativeBtnNew.text = negativeButtonText

        binding.negativeBtn.setTextColor(ContextCompat.getColor(ctx, negativeButtonColor))
    }

    fun setListener(listener: OnClickInterface<Boolean>?) {
        this.listener = listener
    }

    class Builder {

        companion object {
            const val USE_NEW_DESIGN = "USE_NEW_DESIGN"
            const val TITLE = "TITLE"
            const val MESSAGE = "MESSAGE"
            const val POSITIVE_BUTTON_TEXT = "POSITIVE_BUTTON_TEXT"
            const val SHOW_LOADER_ON_POSITIVE_CLICK = "SHOW_LOADER_ON_POSITIVE_CLICK"
            const val NEGATIVE_BUTTON_TEXT = "NEGATIVE_BUTTON_TEXT"
            const val NEGATIVE_BUTTON_COLOR = "NEGATIVE_BUTTON_COLOR"
        }

        private var bundle = Bundle()
        private var listener : OnClickInterface<Boolean>? = null

        fun useNewDesign(useNewDesign: Boolean): Builder {
            bundle.putBoolean(USE_NEW_DESIGN, useNewDesign)
            return this
        }

        fun setTitle(title: String): Builder {
            bundle.putString(TITLE, title)
            return this
        }

        fun setMessage(message: String): Builder {
            bundle.putString(MESSAGE, message)
            return this
        }

        fun setPositiveButtonText(text : String) : Builder {
            bundle.putString(POSITIVE_BUTTON_TEXT, text)
            return this
        }

        fun showLoaderOnPositiveClick(showLoaderOnPositiveClick : Boolean) : Builder {
            bundle.putBoolean(SHOW_LOADER_ON_POSITIVE_CLICK, showLoaderOnPositiveClick)
            return this
        }

        fun setNegativeButtonText(text : String) : Builder {
            bundle.putString(NEGATIVE_BUTTON_TEXT, text)
            return this
        }

        fun setNegativeButtonColor(@ColorRes color: Int) : Builder {
            bundle.putInt(NEGATIVE_BUTTON_COLOR, color)
            return this
        }

        fun setListener(listener: OnClickInterface<Boolean>): Builder {
            this.listener = listener
            return this
        }

        fun show(manager: FragmentManager) : NoiceAlertDialog {
            return NoiceAlertDialog().apply {
                arguments = bundle
                setListener(<EMAIL>)

                val ft = manager.beginTransaction()
                ft.add(this, NoiceAlertDialog::class.java.simpleName)
                ft.commitAllowingStateLoss()
            }
        }
    }

    override fun onDestroyView() {
        dismissAllowingStateLoss()
        super.onDestroyView()
    }
}