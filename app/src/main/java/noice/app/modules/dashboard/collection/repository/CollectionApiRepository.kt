package noice.app.modules.dashboard.collection.repository

import androidx.lifecycle.MutableLiveData
import noice.app.BaseApplication
import noice.app.model.generics.BaseModel
import noice.app.modules.media.model.Playlist
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.modules.radio.model.Radio
import noice.app.rest.*

class CollectionApiRepository : BaseRepository() {

    fun getCollection(map: HashMap<String, String>) : MutableLiveData<Resource<List<Content>>> {

        val mutableData = MutableLiveData<Resource<List<Content>>>()

        BaseApplication.doServerCall({NetworkRequests.getCollection(map)}, object : ServerInterface<BaseModel<List<Content>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<List<Content>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getCollectionCatalog(map: HashMap<String, String>) : MutableLiveData<Resource<List<Channel>>> {

        val mutableData = MutableLiveData<Resource<List<Channel>>>()

        BaseApplication.doServerCall({NetworkRequests.getCollectionCatalog(map)}, object : ServerInterface<BaseModel<List<Channel>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message, null)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, null)
            }

            override fun onSuccess(data: BaseModel<List<Channel>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getCollectionRadio(map: HashMap<String, String>) : MutableLiveData<Resource<List<Radio>>> {

        val mutableData = MutableLiveData<Resource<List<Radio>>>()

        BaseApplication.doServerCall({NetworkRequests.getCollectionRadio(map)}, object : ServerInterface<BaseModel<List<Radio>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<List<Radio>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getListeningHistory(map: HashMap<String, String>) : MutableLiveData<Resource<List<Content>>> {

        val mutableData = MutableLiveData<Resource<List<Content>>>()

        BaseApplication.doServerCall({NetworkRequests.getListeningHistory(map)}, object : ServerInterface<BaseModel<List<Content>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<List<Content>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getUserPlaylist(map: HashMap<String, String>) : MutableLiveData<Resource<List<Playlist>>> {

        val mutableData = MutableLiveData<Resource<List<Playlist>>>()

        BaseApplication.doServerCall({NetworkRequests.getUserPlaylist(map)}, object : ServerInterface<BaseModel<List<Playlist>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<List<Playlist>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getPlaylistDetails(id : String, map: HashMap<String, String>) = networkBoundResource(
        cachingConstant = id,
        apiPage = map["page"]?.toIntOrNull(),
        apiCall = {
            NetworkRequests.getPlaylistDetails(id, map)
        }
    )

    fun deleteUserHistory(searchedTerm: String) : MutableLiveData<Resource<Any>> {

        val mutableData = MutableLiveData<Resource<Any>>()

        BaseApplication.doServerCall({NetworkRequests.deleteUserHistory(searchedTerm)}, object : ServerInterface<BaseModel<Any>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<Any>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }

    fun getUserHistory(map: HashMap<String, String>,searchedTerm:String) : MutableLiveData<Resource<List<Content>>> {

        val mutableData = MutableLiveData<Resource<List<Content>>>()

        BaseApplication.doServerCall({NetworkRequests.getUserHistory(map,searchedTerm)}, object : ServerInterface<BaseModel<List<Content>>> {

            override fun onCustomError(e: ApiError) {
                mutableData.value = Resource.error(e.message)
            }

            override fun onError(e: Throwable) {
                mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
            }

            override fun onSuccess(data: BaseModel<List<Content>>, dataFromCache: Boolean) {
                mutableData.value = Resource.success(data.data, dataFromCache)
            }
        })

        return mutableData
    }
}