package noice.app.modules.dashboard.collection.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.modules.radio.model.Radio
import noice.app.views.homesegmentviews.RadioSegmentView

class RadioCollectionAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Radio?>,
    listener: LoadMoreAdapterListener?,
    val entityType:String?
) : LoadMoreAdapter<Radio>(recyclerView, dataSet, listener) {

    private lateinit var content: RadioSegmentView

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        content = RadioSegmentView(parent.context, entityType, "","Collection")
        content.setWidth()
        return content.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        dataSet[position]?.let {
            content.viewHolder = holder as RadioSegmentView.ViewHolder
            content.setData(it,"Collection_Radio_Page")
        }
    }
}