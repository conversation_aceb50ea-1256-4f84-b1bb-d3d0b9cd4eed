package noice.app.modules.dashboard.collection.adapter

import android.content.Context
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.DataController
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.modules.podcast.model.Content
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.unwrap
import noice.app.views.homesegmentviews.RecentPlaysVertical

class CollectionRecentAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Content?>,
    listener: LoadMoreAdapterListener?,
    private val entityType: String,
    private val clickListener : OnClickInterface<Content>,
    private var pageSource:String,val queueTitle:String = "",
    val mContext:Context,
    private var extraData: ExtraData? = null
) : LoadMoreAdapter<Content>(recyclerView, dataSet, listener) {

    private lateinit var content: RecentPlaysVertical

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        content = RecentPlaysVertical(parent.context,entityType,"Collection","Collection_Page")
        content.setExtraData(extraData)
        return content.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        if(dataSet[position] != null) {
            content.viewHolder = holder as RecentPlaysVertical.ViewHolder
            content.setData(dataSet[position] as Content, clickListener)
            content.viewHolder.actionButton.setOnClickListener {

                val ctx = it.context.unwrap()
                
                if (DataController.isAdPlaying) {
                    Utils.showSnackBar(ctx, ctx.getString(R.string.playback_will_resume_after_ad))
                    return@setOnClickListener
                }

                if (DataController.playerContentId == dataSet[position]?.id) {
                    if (it.context.getPlayerActivity()?.isPlayWhenReady() == true) {
                        ContentPlayRequest.Builder()
                            .playWhenReady(ctx, false)
                    } else {
                        ContentPlayRequest.Builder()
                            .playWhenReady(ctx, true)
                    }
                    return@setOnClickListener
                }

                dataSet[position]?.let { content ->
                    ContentPlayRequest.Builder()
                        .contents(listOf(content))
                        .extraData(extraData)
                        .pageSource(pageSource)
                        .queueTitle(queueTitle)
                        .addCatalogContents(true)
                        .contentFetchLimit(20)
                        .play(ctx)

                    dataSet[position]?.isPlaying = true
                    dataSet[position]?.showLoader = true
                    notifyItemChanged(position)
                }
            }
        }
    }
}