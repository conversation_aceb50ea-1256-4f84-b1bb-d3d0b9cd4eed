package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentHistoryContentBinding
import noice.app.listner.OnClickInterface
import noice.app.modules.dashboard.collection.adapter.CollectionRecentAdapter
import noice.app.modules.dashboard.collection.viewmodel.CollectionViewModel
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.podcast.model.Content
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.RecyclerViewMargin
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class HistoryContentFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    private val viewModel : CollectionViewModel by viewModels()

    private lateinit var binding : FragmentHistoryContentBinding
    private lateinit var adapter : CollectionRecentAdapter
    private lateinit var ctx : Context
    private var historyContent = ArrayList<Content?>()
    private var page = 1
    private var entityType = "content"

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHistoryContentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        binding.errorView.showLoading()

        getData(false)
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Collection_History")
            putString("previousScreen", "Collection_Page")
        })
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handlePlayStates(it)
            }

        adapter = CollectionRecentAdapter(binding.historyContentRecycler, historyContent, this, entityType,
        object : OnClickInterface<Content> {
            override fun dataClicked(data: Content) {
                data.id?.let { id ->
                    EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_CONTENT_PAGE, id,"Collection_History"))
                }
            }
        },"History","Histori",ctx)
        binding.historyContentRecycler.adapter = adapter
        binding.historyContentRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp16)))

        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getData(false)
        }

        binding.toolbar.setRightTextListener {
            NoiceAlertDialog.Builder()
                .setTitle(getString(R.string.delete_history))
                .setMessage(getString(R.string.delete_history_msg))
                .setListener(object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean, eventId: Int) {
                        if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                            binding.errorView.showLoading()
                            deleteUserHistory()
                        }
                    }
                }).show(childFragmentManager)
        }
    }

    private fun handlePlayStates(playerEvent: PlayerEvent) {
        historyContent.filter { content ->
            content != null && (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content?.isPlaying = false
            content?.showLoader = false

            val index = historyContent.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }

        val content = historyContent.find {
            it?.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = historyContent.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun deleteUserHistory() {
        viewModel.deleteUserHistory("contentPause").observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {
                adapter.addNewList(ArrayList())
                showEmptyView()
            } else {
                hideLoading()
            }
        }
    }

    private fun getData(loadMore : Boolean) {

        if (!loadMore) {
            page = 1
        }

        val map = HashMap<String, String>()
        map["page"] = page.toString()
        map["limit"] = "10"

        viewModel.getUserHistory(map,"contentPause").observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (!it.data.isNullOrEmpty()) {
                    if (loadMore)
                        adapter.addMore(ArrayList(it.data))
                    else
                        adapter.addNewList(ArrayList(it.data))

                    hideLoading()
                } else if (historyContent.isEmpty() || !loadMore) {
                    showEmptyView()
                } else {
                    adapter.isLoadMoreEnabled(false)
                    hideLoading()
                }
            } else {
                showError(it?.message)
            }
        }
    }

    private fun hideLoading() {
        binding.emptyView.visibility = View.GONE
        binding.errorView.hide()
    }

    private fun showEmptyView() {
        binding.errorView.hide()
        binding.emptyView.visibility = View.VISIBLE
        binding.toolbar.rightTextVisibility(View.GONE)
    }

    private fun showError(message : String?) {
        adapter.isLoadMoreEnabled(false)
        binding.emptyView.visibility = View.GONE
        binding.errorView.showError(message, type = "Collection_History", errorName = message)
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > 9) {
            getData(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }
}