package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.android.exoplayer2.offline.Download
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.data.DataController
import noice.app.databinding.FragmentDownloadBinding
import noice.app.exoplayer.EpisodeViewDownloadHelper
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.collection.adapter.EpisodeLoadMoreAdapter
import noice.app.modules.dashboard.home.fragment.EpisodeDetailFragment
import noice.app.modules.dashboard.model.Restore
import noice.app.modules.podcast.fragment.CatalogEpisodeListFragment
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.model.Payload
import noice.app.modules.podcast.viewmodel.EpisodeListViewModel
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.rest.ResponseStatus
import noice.app.room.Downloads
import noice.app.utils.*
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CONTENT
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PODCAST
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class DownloadFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        private const val LIMIT = 25
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun newInstance(extraData: ExtraData?) = DownloadFragment().apply {
            arguments = Bundle().apply {
                putParcelable(EXTRA_DATA, extraData)
            }
        }
    }

    private val viewModel: EpisodeListViewModel by viewModels()
    private lateinit var binding : FragmentDownloadBinding
    private lateinit var ctx : Context
    private lateinit var adapter : EpisodeLoadMoreAdapter
    private var downloads = ArrayList<Content?>()
    private lateinit var episodeViewDownloadHelper : EpisodeViewDownloadHelper
    private var downloadList:ArrayList<Restore>?=null
    private var contentIds = ArrayList<String>()
    private var page = 1
    private var extraData: ExtraData? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        extraData = arguments?.parcelable(EXTRA_DATA)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentDownloadBinding.inflate(inflater, container, false)
        episodeViewDownloadHelper = EpisodeViewDownloadHelper(
            viewLifecycleOwner,
            null,
            binding.downloadRecycler,
            childFragmentManager,
            "Download_Page"
        )
        EventBus.getDefault().register(this)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        initData()
    }

    private fun getRestoreDownload() {

        val map = HashMap<String, String>()
        map["actionValue"] = "1"
        map["actionType"] = "download"
        map["entityType"] = "content"

        viewModel.getDownload(map).observe(viewLifecycleOwner) {
            if (it.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                binding.restoreLayout.visibility = View.VISIBLE
                binding.txtHeading.text = ctx.getString(R.string.want_to_redownload, it.data.size)

                downloadList = ArrayList(it.data)
            } else {
                binding.restoreLayout.visibility = View.GONE
            }
        }
    }

    private fun initData() {

        binding.errorView.showLoading()

        val ids = BaseApplication.application.getDownloadTracker().getDownloadObjects(Download.STATE_COMPLETED).map {
            it.request.id
        }
        contentIds.clear()
        contentIds.addAll(ids)

        getData()
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) {
                handlePlayStates(it)
            }

        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        adapter = EpisodeLoadMoreAdapter(
            binding.downloadRecycler,
            downloads,
            this,
            episodeViewDownloadHelper,
            "content",
            DataController.DOWN_LOAD_PAGE,
            "Downloads",
            null,
            extraData
        )

        adapter.setInternetCheckEnabled(false)
        binding.downloadRecycler.adapter = adapter
        binding.downloadRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp1)))

        binding.txtMau.setOnClickListener {
            DialogUtil.showDownloadWifiDialog(
                ctx,
                "",
                "",
                "",
                "",
                object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean) {
                        downloadList?.map {
                            Content(it.entityId ?: "")
                        }?.let {
                            PrefUtils.showRestoreDownload = false
                            binding.restoreLayout.visibility = View.GONE
                            BaseApplication.application.getDownloadQueue().addDownloads(ArrayList(it),"Download_Screen")
                        }
                    }
                }
            )
        }

        binding.txtGak.setOnClickListener {
            DialogUtil.showDownloadCancelDialog(ctx,
                getString(R.string.yakin_gak_m),
                getString(R.string.pilihan_ini),
                "",
                getString(
                    R.string.yakin
                ),
                object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean) {
                        PrefUtils.showRestoreDownload = false
                        binding.restoreLayout.visibility = View.GONE
                    }
                }
            )
        }

        AnalyticsUtil.sendEvent("collection_download", Bundle().apply {
            putString("segmentName", extraData?.segmentName ?: "")
            putInt("segmentPosition", extraData?.segmentPosition ?: 0)
            putInt("entityPosition", extraData?.entityPosition ?: 0)
        })

        MoEngageAnalytics.sendEvent(ctx, "download page opened", Bundle().apply {
            putString("segment name", extraData?.segmentName ?: "")
            putInt("segment position", extraData?.segmentPosition ?: 0)
            putInt("entity position", extraData?.entityPosition ?: 0)
        })
    }

    private fun handlePlayStates(playerEvent: PlayerEvent) {
        downloads.filter { content ->
            content != null && (content.isPlaying || content.showLoader) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content?.isPlaying = false
            content?.showLoader = false

            val index = downloads.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }

        val content = downloads.find {
            it?.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER

                if (playerEvent.event == PlayerEvent.PAUSE_END_LOADER && playerEvent.currentContentPositionMs != null) {
                    content.meta?.timeElapsed = playerEvent.currentContentPositionMs.msToSeconds().toLong()
                }
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = downloads.indexOf(content)
            if (index != -1) {
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun getData(loadMore : Boolean = false) {

        if (!loadMore) {
            page = 1
        }

        val ids = getContentIds()

        viewModel.getDownloadContents(ids).observe(viewLifecycleOwner) { contents ->

            if (!contents.isNullOrEmpty()) {
                if (loadMore)
                    adapter.addMore(contents)
                else
                    adapter.addNewList(contents)

                binding.emptyView.visibility = View.GONE
                binding.restoreLayout.visibility = View.GONE
                hideLoading()
            } else if (downloads.isEmpty() || !loadMore) {
                handleEmptyDownloads()
            } else {
                adapter.isLoadMoreEnabled(false)
                hideLoading()
            }
        }
    }

    private fun handleEmptyDownloads() {
        binding.emptyView.visibility = View.VISIBLE
        if (PrefUtils.showRestoreDownload){
            getRestoreDownload()
        }
        hideLoading()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        if (event.eventCode == Constants.MARK_AS_PLAYED) {
            adapter.notifyDataSetChanged()
            markAsPlayed(event.data as Content)
        } else if (event.eventCode == Constants.REMOVE_DOWNLOAD) {
            val episode = event.data as Content
            val index = downloads.indexOf(episode)
            if (index != -1) {
                downloads.removeAt(index)
                adapter.notifyItemRemoved(index)

                if (downloads.isEmpty()) {
                    binding.emptyView.visibility = View.VISIBLE
                }
            }
        } else if (event.eventCode == Constants.SHOW_DOWNLOAD) {
            val episode = event.data as Content
            downloads.add(0, episode)
            adapter.notifyItemInserted(0)
        }
    }

    private fun markAsPlayed(content: Content) {
        val analyticsAction: String
        val action = if (content.meta?.markedAsPlayed == true) {
            analyticsAction = "content_marked_as_played"
            EventConstant.MARK_PLAYED
        } else {
            analyticsAction = "content_marked_as_unplayed"
            EventConstant.MARK_UN_PLAYED
        }

        val payload = Payload(
                content.meta?.timeElapsed?.toInt(),
                content.id,
                ENTITY_TYPE_CONTENT,
                content.catalog?.type,
                "download",
                PrefUtils.uniqueId,
                content.duration?.toInt(),
                catalogId = content.catalog?.id ?: ""
        )

        val event = EventPost(action, payload, contentId = content.id ?: "")
        AnalyticsUtil.sendEvent(
            content.catalog?.type ?: ENTITY_TYPE_PODCAST,
            content.id,
            content.catalog?.id,
            analyticsAction,
            "download",
            content.meta?.timeElapsed.toString(),
            content.duration.toString(),
            catalogTitle = content.catalog?.title ?: "",
            contentTitle = content.title ?: ""
        )
        if (NetworkUtils.isNetworkConnected(ctx)) {
            viewModel.postEvent(event).observe(viewLifecycleOwner) {
                if (it?.status == ResponseStatus.SUCCESS) {
                    CoroutineScope(Dispatchers.IO).launch {
                        content.catalogType = content.catalog?.type
                        content.catalogTitle = content.catalog?.title
                        content.catalogId = content.catalog?.id
                        content.source = content.catalog?.source
                        BaseApplication.application.getAppDb().downloadsDao().addDownload(
                            Downloads(PrefUtils.userDetails?.id ?: "", content.id ?: "", content)
                        )
                    }
                }
            }
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                BaseApplication.application.getAppDb().eventDao().addEvents(event)
            }
        }
    }

    private fun getContentIds() : MutableList<String> {
        var upperLimit = (page * LIMIT)
        if (upperLimit >= contentIds.size) {
            upperLimit = contentIds.size
        }

        val lowerLimit = (page * LIMIT) - LIMIT
        if (lowerLimit > upperLimit) {
            return ArrayList()
        }

        return contentIds.subList(lowerLimit, upperLimit)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > (LIMIT - 1)) {
            getData(loadMore = true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }
}