package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.viewModels
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.PlayListDialogBinding
import noice.app.listner.OnClickInterface
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.home.activity.CreatePlaylistActivity
import noice.app.modules.dashboard.home.adapter.PlaylistDialogAdapter
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.media.model.Playlist
import noice.app.modules.media.model.PlaylistContentRequest
import noice.app.modules.podcast.model.Content
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class PlayListDialog : BottomSheetDialogFragment() {

    companion object {
        private const val CONTENT = "CONTENT"
        private const val PAGE_SOURCE = "PAGE_SOURCE"

        fun newInstance(content : Content,pageSource:String) = PlayListDialog().apply {
            arguments = Bundle().apply {
                putParcelable(CONTENT, content)
                putString(PAGE_SOURCE, pageSource)
            }
        }
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding : PlayListDialogBinding
    private lateinit var ctx : Context
    private lateinit var adapter : PlaylistDialogAdapter
    private var content:Content?=null
    private var playlist:Playlist?=null
    private var pageSource:String?=null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
        content = arguments?.getParcelable(CONTENT)
        pageSource = arguments?.getString(PAGE_SOURCE)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PlayListDialogBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        getData()
    }

    private fun initViews() {

        binding.errorView.setLayoutBackground(R.color.home_search)

        binding.cancelBtn.setOnClickListener {
            dismiss()
        }

        binding.createNewPlaylist.setOnClickListener {
            startActivity(Intent( ctx, CreatePlaylistActivity::class.java))
        }

        adapter = PlaylistDialogAdapter(object : OnClickInterface<Playlist> {
            override fun dataClicked(data: Playlist) {
                playlist = data
                data.id?.let {
                    addToPlaylist(it, data.title)
                }
            }
        })
        binding.playListRv.adapter = adapter
        binding.playListRv.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp8)))
    }

    private fun getData() {
        if (!DataController.playlists.isNullOrEmpty()) {
            adapter.addAll(DataController.playlists!!)
        } else {
            binding.errorView.showLoading()
            DataController.playlists = ArrayList()
            getPlaylists()
        }
    }

    private fun getPlaylists() {
        viewModel.getPlaylists().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                DataController.playlists?.clear()
                DataController.playlists?.addAll(it.data)
                adapter.addAll(DataController.playlists!!)
            }

            binding.errorView.hide()
        }
    }

    private fun addToPlaylist(playListId: String, title: String?) {
        viewModel.addContentInPlayList(PlaylistContentRequest(content?.id?:""), playListId).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS) {

                AnalyticsUtil.sendEventForPlaylist(
                    content?.catalog?.type ?: "",
                    content?.id ?: "",
                    content?.catalog?.id ?: "",
                    "content_added_to_playlist",
                    pageSource,
                    "",
                    (content?.duration ?: 0).toString(),
                    content?.catalog?.title ?: "",
                    content?.title ?: "",
                    playListId, playlist?.user?.displayName ?: "", playlist?.title ?: ""
                )
                MoEngageAnalytics.sendEvent(ctx, "content added to playlist", Bundle().apply {
                    putString("entity type", content?.catalog?.type)
                    putString("catalog title", content?.catalogTitle ?: "")
                    putString("content title", content?.title)
                    putString("catalog id", content?.catalogId ?: "")
                    putString("content id", content?.id ?: "")
                    putString("content duration", (content?.duration ?: 0).toString())
                    putString("source", pageSource)
                    putStringArrayList(
                        "genre",
                        ArrayList(content?.catalog?.genres?.map { genre ->
                            genre.name ?: ""
                        } ?: ArrayList()))
                    putString("catalog source", content?.catalog?.source)
                })

                Toast.makeText(
                    ctx,
                    getString(R.string.content_added_to_playlist, title),
                    Toast.LENGTH_SHORT
                ).show()
                dismiss()
            } else {
                Toast.makeText(
                    ctx,
                    getString(R.string.content_not_added_to_playlist),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlaylistAdded(event : EventMessage) {
        if(event.eventCode == Constants.PLAYLIST_CREATED) {
            if (DataController.playlists != null) {
                DataController.playlists?.add(0, event.data as Playlist)
                adapter.addAll(DataController.playlists!!)
            }
            AnalyticsUtil.sendEventForPlaylist(
                content?.catalog?.type?:"",
                content?.id?:"",
                content?.catalog?.id?:"",
                "playlist_created",
                pageSource,
                "",
                (content?.duration?:0).toString(),
                content?.catalog?.title?:"",
                content?.title?:"",
                (event.data as Playlist).id?:"", event.data.user?.displayName?:"",
                event.data.title ?:""
            )
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}