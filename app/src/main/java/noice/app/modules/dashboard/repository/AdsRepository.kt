package noice.app.modules.dashboard.repository

import noice.app.modules.live.event.ServiceCallback.networkBoundResource
import noice.app.rest.apiinterfaces.AdsApiInterface
import noice.app.utils.PrefUtils
import javax.inject.Inject

class AdsRepository @Inject constructor(
    private val adsApiInterface: AdsApiInterface
) {
    fun getAds(adsEntityType: String, adsEntityValue: String) = networkBoundResource(
        cachingConstant = null,
        apiCall = {
            adsApiInterface.getAds(PrefUtils.token, adsEntityType, adsEntityValue)
        }
    )
}