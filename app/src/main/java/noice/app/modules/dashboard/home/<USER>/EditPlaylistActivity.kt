package noice.app.modules.dashboard.home.activity

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.ItemTouchHelper
import com.google.gson.Gson
import com.thesurix.gesturerecycler.GestureAdapter
import com.thesurix.gesturerecycler.GestureManager
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.ActivityEditPlaylistBinding
import noice.app.listner.OnClickInterface
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.dashboard.home.adapter.PlayListAdapter
import noice.app.modules.dashboard.home.model.PlayListWeight
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.media.model.*
import noice.app.modules.podcast.model.Content
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class EditPlaylistActivity : AppCompatActivity() {

    companion object {
        private const val PLAYLIST_DATA = "PLAYLIST_DATA"
        private const val SOURCE = "SOURCE"

        fun start(ctx: Context, playlist: Playlist,source:String) {
            Intent(ctx, EditPlaylistActivity::class.java).apply {
                putExtra(PLAYLIST_DATA, playlist)
                putExtra(SOURCE, source)
                ctx.startActivity(this)
            }
        }
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding : ActivityEditPlaylistBinding
    private var playlist : Playlist? = null
    private var playListBitmap: Bitmap? = null
    private var playListUri: Uri? = null
    private var adapter : PlayListAdapter?=null
    private var playlistData: MutableList<PlayListDataItem> = ArrayList()
    private var gestureManager: GestureManager? = null
    private var source:String = ""

    /** onActivityResult contract for selected image */
    private val startForImagePicker =
        registerForActivityResult(ActivityResultContracts.GetContent()) { imageUri ->
            ImageUtils.getBitmapFromUri(this, imageUri).observe(this) {
                if (it != null) {
                    playListUri = imageUri
                    playListBitmap = it
                    ImageUtils.loadImageByBitmap(binding.playlistImage, it)
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditPlaylistBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getDataFromIntent()

        initViews()

        setData()
    }

    private fun getDataFromIntent() {
        if(intent.hasExtra(SOURCE)){
            source = intent.getStringExtra(SOURCE)?:""
        }
        if(intent.hasExtra(PLAYLIST_DATA)) {
            playlist = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                intent.getParcelableExtra(PLAYLIST_DATA, Playlist::class.java)
            } else {
                intent.getParcelableExtra(PLAYLIST_DATA)
            }
            generateData()
        }
    }

    private fun generateData() {
        if (!playlist?.playlistContent.isNullOrEmpty()) {
            playlistData.clear()
            playlist?.playlistContent?.forEach { content ->
                playlistData.add(PlayListData(content))
            }
        }
    }

    private fun setData() {
        ImageUtils.getBitmapFromUrl(this, playlist?.imageMeta?.size300?:"", R.drawable.ic_thumb_default).observe(this) {
            if (!playlist?.imageMeta?.size300.isNullOrEmpty())
                playListBitmap = it
            ImageUtils.loadImageByBitmap(binding.playlistImage, it)
        }

        binding.playListName.setText(playlist?.title)

        binding.privacy.isChecked = !(playlist?.isPublic ?: false)
    }

    private fun initViews() {

        binding.toolbar.setBackClick {
            onBackPressed()
        }

        binding.uploadPicBtn.setOnClickListener {
            ImageUtils.showImagePicker(startForImagePicker)
        }

        binding.submitButton.setOnClickListener {
            Utils.hideKeyboard(binding.submitButton)

            if (binding.playListName.text.isNullOrEmpty()) {
                binding.inputLayout.error = getString(R.string.playlist_mandatory)
                return@setOnClickListener
            }

            proceedToUploadData()
        }

        adapter = PlayListAdapter(R.layout.playlist_item, object : OnClickInterface<Content> {
            override fun dataClicked(data: Content) {
                data.let { content ->
                    NoiceAlertDialog.Builder()
                        .setTitle(getString(R.string.delete_episode))
                        .setMessage(getString(R.string.delete_episode_msg))
                        .setListener(object : OnClickInterface<Boolean> {
                            override fun dataClicked(data: Boolean, eventId: Int) {
                                if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                                    deleteContent(content)
                                }
                            }
                        }).show(supportFragmentManager)
                }
            }
        })
        adapter?.data = playlistData
        binding.playListRv.adapter = adapter

        adapter?.setDataChangeListener(object :
            GestureAdapter.OnDataChangeListener<PlayListDataItem> {

            override fun onItemRemoved(item: PlayListDataItem, position: Int, direction: Int) {

            }

            override fun onItemReorder(item: PlayListDataItem, fromPos: Int, toPos: Int) {
                move(fromPos, toPos)

                playlistData.map {
                    it.content
                }.let {
                    playlist?.playlistContent = it
                }

                adapter?.notifyDataSetChanged()
            }
        })

        gestureManager = GestureManager.Builder(binding.playListRv)
            .setSwipeEnabled(false)
            //.setLongPressDragEnabled(true)
            .setManualDragEnabled(true)
            .setSwipeFlags(ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT)
            .setDragFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN)
            .build()
    }

    private fun deleteContent(content: Content) {
        playlistData.remove(PlayListData(content))
        playlistData.map {
            it.content
        }.let {
            playlist?.playlistContent = it
        }

        adapter?.data = playlistData
        adapter?.notifyDataSetChanged()
    }

    private fun proceedToUploadData() {

        binding.errorView.showLoading()

        val map = HashMap<String, RequestBody>()

        binding.playListName.text.toString().let {
            playlist?.title = it
            map["title"] = it.toRequestBody("multipart/form-data".toMediaTypeOrNull())
        }

        map["isPublic"] = (!binding.privacy.isChecked).toString().toRequestBody("multipart/form-data".toMediaTypeOrNull())
        playlist?.isPublic = !binding.privacy.isChecked

        /* 'weightArray' should be empty array when emptying playlist */
        val weightMap = ArrayList<PlayListWeight>()
        playlist?.playlistContent?.forEachIndexed { index, content ->
            weightMap.add(PlayListWeight(content.id ?: "", index + 1))
        }
        map["weightArray"] = Gson().toJson(weightMap).toRequestBody("multipart/form-data".toMediaTypeOrNull())

        if (playListBitmap != null) {
            ImageUtils.getFileFromBitmap(this, playListBitmap).observe(this) {
                if (it != null) {
                    val filePart = MultipartBody.Part.createFormData(
                        "image", it.name, it.asRequestBody(
                            "image/*".toMediaTypeOrNull()
                        )
                    )
                    editPlayList(filePart, map)
                } else {
                    editPlayList(null, map)
                }
            }
        } else {
            editPlayList(null, map)
        }
    }

    fun move(fromPosition: Int, toPosition: Int) {
        if (fromPosition < toPosition) {
            playlistData.add(toPosition, playlistData[fromPosition])
            playlistData.removeAt(fromPosition)
        } else {
            playlistData.add(toPosition, playlistData[fromPosition])
            playlistData.removeAt(fromPosition + 1)
        }
    }

    private fun editPlayList(
        filePart: MultipartBody.Part?,
        map: HashMap<String, RequestBody>
    ) {
        viewModel.editPlayList(filePart, map, playlist?.id ?: "").observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                playlist?.localImageUri = playListUri
                EventBus.getDefault().post(EventMessage(playlist, Constants.PLAYLIST_EDITED))
                finish()
            } else {
                Toast.makeText(this, it.message, Toast.LENGTH_LONG).show()
            }

            binding.errorView.hide()
        }
    }
 }