package noice.app.modules.dashboard.home.viewholder

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.media.model.MediaAction
import noice.app.modules.profile.repository.NotificationApiRepository
import java.lang.reflect.Type
import javax.inject.Inject

@HiltViewModel
class HomeFragmentNewViewModel @Inject constructor(
    private val dashboardApiRepository: DashboardApiRepository
): ViewModel() {
    private val notificationRepository = NotificationApiRepository()

    fun shouldReviewApp() = dashboardApiRepository.shouldReviewApp()

    fun getHomeSegment(map: HashMap<String, String>,type:String, isRefreshing : Boolean = false) = dashboardApiRepository.getHomeSegment(map, type, isRefreshing)

    fun getNotifications(map: HashMap<String, String>, cachingConstant : String?) = notificationRepository.getNotifications(map, cachingConstant)

    fun performAction(mediaAction: MediaAction) = dashboardApiRepository.performAction(mediaAction)
}