package noice.app.modules.dashboard.model

data class HomeSegment(
    val id: String?,
    var title: String?,
    var hasMore: Boolean?,
    var entityType: String?,
    var viewOptions: HomeViewOptions?,
    var content: ArrayList<HomeContent>?,
    var segmentPage : Int?,
    var banners : List<HomeBanner>? = null,
    val dynamicSegmentType: String?, //added to differentiate Genre Kamu and All Genres on search screen to show empty view
    val description: String?, //for displaying text on top ranking and trending screens on search screen
    val image: String?
) {
    constructor(id: String, banners: List<HomeBanner>) : this(
        id,
        null,
        null,
        null,
        null,
        null,
        null,
        banners,
        null,
        null,
        null
    )

    override fun equals(other: Any?): Boolean {
        if(other is HomeSegment) {
            return id == other.id
        }
        return false
    }

    override fun hashCode(): Int {
        return id?.hashCode() ?: 0
    }
}