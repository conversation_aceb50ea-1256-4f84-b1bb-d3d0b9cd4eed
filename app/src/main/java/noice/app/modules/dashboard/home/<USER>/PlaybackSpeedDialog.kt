package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import noice.app.R
import noice.app.databinding.FragmentPlaybackSpeedDialogBinding
import noice.app.model.eventbus.EventMessage
import noice.app.player.models.PlayerEvent
import noice.app.utils.Constants
import org.greenrobot.eventbus.EventBus

class PlaybackSpeedDialog() : BottomSheetDialogFragment() {

    companion object {
        const val PLAYBACK_SPEED_1x = 1f
        private const val PLAYBACK_SPEED_1_25x = 1.25f
        private const val PLAYBACK_SPEED_1_5x = 1.5f
        private const val PLAYBACK_SPEED_2x = 2f

        private const val CURRENT_SPEED = "CURRENT_SPEED"

        fun newInstance(currentSpeed: Float) = PlaybackSpeedDialog().apply {
            arguments = Bundle().apply {
                putFloat(CURRENT_SPEED, currentSpeed)
            }
        }
    }

    private lateinit var binding : FragmentPlaybackSpeedDialogBinding
    private lateinit var ctx : Context
    private var currentSpeed = PLAYBACK_SPEED_1x

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        currentSpeed = arguments?.getFloat(CURRENT_SPEED) ?: PLAYBACK_SPEED_1x
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPlaybackSpeedDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        setSelected()
    }

    private fun initViews() {
        binding.cancelBtn.setOnClickListener {
            dismiss()
        }

        binding.speed1.setOnClickListener {
            postEvent(PLAYBACK_SPEED_1x)
        }

        binding.speed125.setOnClickListener {
            postEvent(PLAYBACK_SPEED_1_25x)
        }

        binding.speed15.setOnClickListener {
            postEvent(PLAYBACK_SPEED_1_5x)
        }

        binding.speed2.setOnClickListener {
            postEvent(PLAYBACK_SPEED_2x)
        }
    }

    private fun postEvent(speed : Float) {
        PlayerEvent(PlayerEvent.UPDATE_PLAYER_SPEED, speed).sendPlayerEvent()
        dismiss()
    }

    private fun setSelected() {
        val item = getSelectedItem()
        item.setTextColor(ContextCompat.getColor(ctx, R.color.orange_yellow))
        item.setCompoundDrawablesWithIntrinsicBounds(0,0,R.drawable.ic_tick_yellow,0)
    }

    private fun getSelectedItem(): TextView {
        return when (currentSpeed) {
            PLAYBACK_SPEED_1_25x -> {
                binding.speed125
            }
            PLAYBACK_SPEED_1_5x -> {
                binding.speed15
            }
            PLAYBACK_SPEED_2x -> {
                binding.speed2
            }
            else -> {
                binding.speed1
            }
        }
    }
}