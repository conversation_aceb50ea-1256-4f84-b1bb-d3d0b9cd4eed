package noice.app.modules.dashboard.home.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.listner.OnClickInterface
import noice.app.modules.dashboard.home.model.ShareOption
import noice.app.views.ShareOptionView

class ShareOptionAdapter(
    private val shareOptions: ArrayList<ShareOption>,
    private val eventListener: OnClickInterface<ShareOption>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var shareOptionView: ShareOptionView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        shareOptionView = ShareOptionView(parent.context)
        return shareOptionView.viewHolder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        shareOptionView.viewHolder = holder as ShareOptionView.ViewHolder
        shareOptionView.setData(shareOptions[position], eventListener)
    }

    override fun getItemCount(): Int {
        return shareOptions.size
    }
}