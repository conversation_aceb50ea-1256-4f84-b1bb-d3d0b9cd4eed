package noice.app.modules.dashboard.home.fragment

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import noice.app.R
import noice.app.databinding.CatalogMenuBinding
import noice.app.listner.CustomClickListener
import noice.app.modules.chat.report.ReportActivity
import noice.app.modules.podcast.model.Channel
import noice.app.utils.*

class CatalogMenu : BottomSheetDialogFragment() {

    companion object {
        const val CHANNEL = "CHANNEL"
        const val ENTITY_SUB_TYPE = "ENTITY_SUB_TYPE"
        const val PAGE_SOURCE = "PAGE_SOURCE"
    }

    private lateinit var binding : CatalogMenuBinding
    private lateinit var mContext: Context
    private var channel : Channel? = null
    private var pageSource =""
    private var entitySubType =""

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getDataFromIntent()
    }

    private fun getDataFromIntent() {
        if(arguments?.getParcelable<Channel>(CHANNEL) !=null) {
            channel = arguments?.getParcelable(CHANNEL)
        }
        if(arguments?.getString(ENTITY_SUB_TYPE)!=null) {
            entitySubType = arguments?.getString(ENTITY_SUB_TYPE).toString()
        }
        if(arguments?.getString(PAGE_SOURCE)!=null) {
            pageSource = arguments?.getString(PAGE_SOURCE).toString()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = CatalogMenuBinding.inflate(inflater, container, false)

        initViews()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setData()

        initializeBottomSheetDialog()
    }

    private fun initViews() {
        binding.toolbar.setBackClick { dismiss() }

        binding.share.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            val dialog = ShareDialog.newInstance(channel,false,null)
            dialog.show(childFragmentManager, "share_menu")
        }

        binding.reportContent.setOnClickListener(CustomClickListener ({

            if(!NetworkUtils.isNetworkConnected(mContext)) {
                Utils.showSnackBar(binding, mContext.getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            Intent(mContext, ReportActivity::class.java).apply {
                putExtra(ReportActivity.ENTITY_ID, channel?.id)
                putExtra(ReportActivity.ENTITY_TYPE, "catalog")
                mContext.startActivity(this)
            }
        },channel?.type+" menu"))
    }

    private fun setData() {

        if(channel?.imageMeta?.size300?.isNotEmpty() == true) {
            ImageUtils.loadImageByUrl(binding.coverImage, channel?.imageMeta?.size300, originalUrl = channel?.imageMeta?.size500)
        }

        if(channel?.title?.isNotEmpty() == true) {
            binding.catalogName.text = channel?.title
            binding.artistName.text = mContext.getString(R.string.by_channel, channel?.title)
        }

        DateUtils.getDate(channel?.data).let {
            binding.details.text =
                (channel?.meta?.contentCount ?: 0).toString().plus(" EPISODE · ")
                    .plus(Utils.abbrNumberFormat(channel?.meta?.aggregations?.followers ?: 0))
                    .plus(" ").plus(getString(R.string.subscribers))
        }
    }

    private fun setupFullHeight(bottomSheetDialog: BottomSheetDialog) {
        val bottomSheet =
            bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val behavior = BottomSheetBehavior.from(bottomSheet)
        val layoutParams = bottomSheet.layoutParams
        val windowHeight = Utils.getDisplayMetrics(mContext).heightPixels
        layoutParams?.height = windowHeight
        behavior.isHideable = false
        bottomSheet.layoutParams = layoutParams
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.isHideable = false
        behavior.peekHeight = windowHeight
        bottomSheet.parent.requestLayout()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog: Dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            setupFullHeight(bottomSheetDialog)
        }
        return dialog
    }

    private fun initializeBottomSheetDialog() {
        val d = dialog as BottomSheetDialog
        val bottomSheet = d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)

        bottomSheetBehavior.isHideable = false
        bottomSheetBehavior.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {}
            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })
    }
}