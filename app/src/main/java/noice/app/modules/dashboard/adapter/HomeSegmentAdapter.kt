package noice.app.modules.dashboard.adapter

import android.os.Parcelable
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.google.android.gms.ads.admanager.AdManagerAdView
import noice.app.adapter.LoadMoreAdapter
import noice.app.model.ExtraData
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.model.InnerRecyclerConfig
import noice.app.utils.PermissionUtils
import noice.app.utils.PrefUtils
import noice.app.views.BannerView
import noice.app.views.CustomAdView
import noice.app.views.NoGenreSelectedView
import noice.app.views.NoLiveView
import noice.app.views.homesegmentviews.HomeSegmentView
import noice.app.views.homesegmentviews.TopHighlightView

class HomeSegmentAdapter(
        recyclerView: RecyclerView,
        dataSet: ArrayList<HomeSegment?>,
        listener: LoadMoreAdapterListener?,
        private val previousScreen : String = "",
        private val permissionUtils: PermissionUtils? = null
) : LoadMoreAdapter<HomeSegment>(recyclerView, dataSet, listener) {

    companion object {
        private const val TYPE_BANNER = 888
        private const val TYPE_NO_LIVE = 889
        private const val TYPE_NO_GENRE_SELECTED = 890
        private const val TYPE_TOP_HIGHLIGHT = 891
        private const val TYPE_AD = 892

        const val VIEW_TYPE_TOP_HIGHLIGHT = "top_highlight"
        const val VIEW_TYPE_ADS = "ads"
    }

    private lateinit var homeSegmentView: HomeSegmentView
    private lateinit var bannerView: BannerView
    private lateinit var noLiveView: NoLiveView
    private lateinit var noGenreSelected: NoGenreSelectedView
    private lateinit var topHighlightView: TopHighlightView
    private lateinit var customAdView: CustomAdView
    private var innerPosition = -1
    private var unRegisterEventBus = false
    private val hashMap = HashMap<String, InnerRecyclerConfig>()
    private val scrollStates: MutableMap<String, Parcelable?> = mutableMapOf()
    private val adsMap by lazy { HashMap<Int, AdManagerAdView>() }
    private var screenTitle: String? = null //used for theme pages in case of deeplink

    override fun onCreateViewHolderCustom(
            parent: ViewGroup,
            viewType: Int
    ): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_BANNER -> {
                bannerView = BannerView(parent.context)
                bannerView.setPageSource(previousScreen)
                bannerView.viewHolder
            }
            TYPE_NO_LIVE -> {
                noLiveView = NoLiveView(parent.context)
                noLiveView.viewHolder
            }
            TYPE_NO_GENRE_SELECTED -> {
                noGenreSelected = NoGenreSelectedView(parent.context)
                noGenreSelected.viewHolder
            }
            TYPE_TOP_HIGHLIGHT -> {
                topHighlightView = TopHighlightView(parent.context)
                topHighlightView.viewHolder
            }
            TYPE_AD -> {
                customAdView = CustomAdView(parent.context, adsMap)
                customAdView.viewHolder
            }
            else -> {
                homeSegmentView = HomeSegmentView(parent.context, previousScreen, permissionUtils)
                homeSegmentView.viewHolder
            }
        }
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is BannerView.ViewHolder -> {
                bannerView.viewHolder = holder

                val extraData = ExtraData(segmentId = dataSet[position]?.id, segmentName = dataSet[position]?.title, segmentPosition = position + 1)
                bannerView.setExtraData(extraData)

                bannerView.setData(dataSet[position]?.banners ?: ArrayList())

                restoreStates(
                    getSectionID(holder.layoutPosition),
                    holder.bannerRecyclerView
                )
            }
            is TopHighlightView.ViewHolder -> {
                topHighlightView.viewHolder = holder

                val extraData = ExtraData(segmentId = dataSet[position]?.id, segmentName = dataSet[position]?.title, segmentPosition = position + 1)
                topHighlightView.setExtraData(extraData)

                topHighlightView.setData(dataSet[position])
            }
            is HomeSegmentView.ViewHolder -> {
                homeSegmentView.viewHolder = holder
                if (unRegisterEventBus) {
                    homeSegmentView.notifyBusUnregister()
                } else {
                    dataSet[position]?.let { segment ->
                        homeSegmentView.setData(segment, innerPosition, hashMap, position, screenTitle)
                    }
                }
                innerPosition = -1
                restoreStates(
                    getSectionID(holder.layoutPosition),
                    holder.recyclerView
                )
            }
            is CustomAdView.ViewHolder -> {
                customAdView.viewHolder = holder
                customAdView.setData(dataSet[position], position, previousScreen)
            }
        }
    }

    private fun getSectionID(position: Int): String? {
        return if (position >= 0 && position < dataSet.size) {
            dataSet[position]?.id.toString()
        } else {
            null
        }
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)

        val key = getSectionID(holder.layoutPosition)
        if (key.isNullOrEmpty()) {
            return
        }

        if (holder is HomeSegmentView.ViewHolder) {
            scrollStates[key] = holder.recyclerView.layoutManager?.onSaveInstanceState()
        } else if (holder is BannerView.ViewHolder) {
            scrollStates[key] = holder.bannerRecyclerView?.layoutManager?.onSaveInstanceState()
        }
    }

    private fun restoreStates(key : String?, recyclerView: RecyclerView?) {
        if (key == null || recyclerView == null) {
            return
        }

        val state = scrollStates[key]
        if (state != null) {
            recyclerView.layoutManager?.onRestoreInstanceState(state)
        } else {
            recyclerView.layoutManager?.scrollToPosition(0)
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        unRegisterEventBus = false
    }

    fun notifyBusUnregister() {
        unRegisterEventBus = true
        notifyDataSetChanged()
    }

    fun setScreenTitle(screenTitle: String?) {
        this.screenTitle = screenTitle
    }

    override fun getItemViewTypeCustom(position: Int): Int {
        return if(!dataSet[position]?.banners.isNullOrEmpty()) {
            TYPE_BANNER
        } else if (dataSet[position]?.viewOptions?.viewType == "livestream" && dataSet[position]?.content.isNullOrEmpty() && PrefUtils.isLoggedIn && PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true){
            TYPE_NO_LIVE
        } else if (dataSet[position]?.viewOptions?.viewType == "genre" && dataSet[position]?.dynamicSegmentType == "USER_SELECTED_GENRES" && dataSet[position]?.content.isNullOrEmpty()) {
            TYPE_NO_GENRE_SELECTED
        } else if (dataSet[position]?.viewOptions?.viewType == VIEW_TYPE_TOP_HIGHLIGHT) {
            TYPE_TOP_HIGHLIGHT
        } else if (dataSet[position]?.viewOptions?.viewType == VIEW_TYPE_ADS) {
            TYPE_AD
        } else {
            super.getItemViewTypeCustom(position)
        }
    }
}