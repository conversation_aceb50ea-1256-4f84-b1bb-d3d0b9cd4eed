package noice.app.modules.dashboard.home.viewholder

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.thesurix.gesturerecycler.GestureViewHolder
import noice.app.R
import noice.app.modules.media.model.PlayListData
import noice.app.modules.media.model.PlayListDataItem

abstract class BasePlayListViewHolder(rootView: View) : GestureViewHolder<PlayListDataItem>(rootView) {
    protected abstract val txtTitle: TextView
    protected abstract val subTitle: TextView
    protected abstract val itemDrag: ImageView
    protected abstract val imgCheck: AppCompatImageView
    protected abstract val foreground: View?

    override val draggableView: View?
        get() = itemDrag

    override val foregroundView: View
        get() = foreground ?: super.foregroundView

    override fun bind(item: PlayListDataItem) {
        if (item is PlayListData) {
            val exoData = item.content
            txtTitle.text = exoData.title
            subTitle.text = if(!exoData.catalog?.title.isNullOrEmpty()) {
                exoData.catalog?.title
            } else {
                exoData.catalogTitle
            }
            itemDrag.visibility = View.VISIBLE

            //imgCheck.isSelected = item.isSelected
           /* Glide.with(itemView.context)
                    .load(item.drawableId)
                    .apply(RequestOptions.centerCropTransform())
                    .into(monthPicture)*/
        }
    }

    override fun onItemSelect() {
        val textColorFrom = ContextCompat.getColor(itemView.context, android.R.color.white)
        val textColorTo = ContextCompat.getColor(itemView.context, R.color.white)
        ValueAnimator.ofObject(ArgbEvaluator(), textColorFrom, textColorTo).apply {
            duration = itemView.context.resources.getInteger(R.integer.animation_speed_ms).toLong()
            addUpdateListener(getTextAnimatorListener(txtTitle, this))
            start()
        }


        val backgroundColorFrom = ContextCompat.getColor(itemView.context, R.color.black)
        val backgroundColorTo = ContextCompat.getColor(itemView.context, android.R.color.black)
        ValueAnimator.ofObject(ArgbEvaluator(), backgroundColorFrom, backgroundColorTo).apply {
            duration = itemView.context.resources.getInteger(R.integer.animation_speed_ms).toLong()
            addUpdateListener(getBackgroundAnimatorListener(txtTitle, this))
            start()
        }
    }

    override fun onItemClear() {
        val textColorFrom = ContextCompat.getColor(itemView.context, R.color.white)
        val textColorTo = ContextCompat.getColor(itemView.context, android.R.color.white)
        ValueAnimator.ofObject(ArgbEvaluator(), textColorFrom, textColorTo).apply {
            duration = itemView.context.resources.getInteger(R.integer.animation_speed_ms).toLong()
            addUpdateListener(getTextAnimatorListener(txtTitle, this))
            start()
        }


        val backgroundColorFrom = ContextCompat.getColor(itemView.context, android.R.color.black)
        val backgroundColorTo = ContextCompat.getColor(itemView.context, R.color.black)
        ValueAnimator.ofObject(ArgbEvaluator(), backgroundColorFrom, backgroundColorTo).apply {
            duration = itemView.context.resources.getInteger(R.integer.animation_speed_ms).toLong()
            addUpdateListener(getBackgroundAnimatorListener(txtTitle, this))
            start()
        }
    }

    override fun canDrag() = true

    override fun canSwipe() = true

    private fun getBackgroundAnimatorListener(view: TextView, animator: ValueAnimator): ValueAnimator.AnimatorUpdateListener {
        return ValueAnimator.AnimatorUpdateListener { view.setBackgroundColor(animator.animatedValue as Int) }
    }

    private fun getTextAnimatorListener(view: TextView, animator: ValueAnimator): ValueAnimator.AnimatorUpdateListener {
        return ValueAnimator.AnimatorUpdateListener { view.setTextColor(animator.animatedValue as Int) }
    }
}