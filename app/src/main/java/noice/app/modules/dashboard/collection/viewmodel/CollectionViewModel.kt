package noice.app.modules.dashboard.collection.viewmodel

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import noice.app.modules.dashboard.collection.repository.CollectionApiRepository
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.media.model.MediaAction
import noice.app.modules.podcast.model.EventPost
import noice.app.modules.podcast.repository.ChannelPodcastApiRepository
import javax.inject.Inject

@HiltViewModel
class CollectionViewModel @Inject constructor(
    private val dashBoardRepository: DashboardApiRepository
) : ViewModel() {

    private val repository = CollectionApiRepository()
    private val channelRepository = ChannelPodcastApiRepository()

    fun postEvent(eventPost: EventPost) = channelRepository.updateEvents(listOf(eventPost))

    fun getCollection(map: HashMap<String, String>) = repository.getCollection(map)

    fun getUserHistory(map: HashMap<String, String>,searchedTerm:String) = repository.getUserHistory(map,searchedTerm)

    fun getCollectionCatalog(map: HashMap<String, String>) = repository.getCollectionCatalog(map)

    fun getCollectionRadio(map: HashMap<String, String>) = repository.getCollectionRadio(map)

    fun getListeningHistory(map: HashMap<String, String>) = repository.getListeningHistory(map)

    fun getUserPlaylist(map: HashMap<String, String>) = repository.getUserPlaylist(map)

    fun getPlaylistDetails(id : String, map: HashMap<String, String>) = repository.getPlaylistDetails(id, map)

    fun deleteUserHistory(searchedTerm: String) = repository.deleteUserHistory(searchedTerm)

    fun performAction(mediaAction: MediaAction) = dashBoardRepository.performAction(mediaAction)
}