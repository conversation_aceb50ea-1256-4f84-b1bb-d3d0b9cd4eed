package noice.app.modules.dashboard.model

import noice.app.model.ImageMeta
import noice.app.model.user.User
import noice.app.utils.ImageUtils

data class HomeBanner(
    val description: String?,
    val entityType: String?,
    val entitySubType: String?,
    val entityValue: String?,
    val id: String?,
    val image: String?,
    val page: String?,
    val title: String?,
    val type: String?,
    val isPremium: Boolean?,
    val imageMeta: ImageMeta?
){
    fun getCompImage():String?{
        return ImageUtils.getSmallImage(image?:"","compressed")
    }
}