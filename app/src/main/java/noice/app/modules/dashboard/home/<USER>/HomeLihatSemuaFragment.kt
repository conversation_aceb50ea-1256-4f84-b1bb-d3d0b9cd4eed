package noice.app.modules.dashboard.home.fragment

import android.Manifest
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentHomeLihatSemuaBinding
import noice.app.model.ExtraData
import noice.app.modules.dashboard.adapter.HomeInnerAdapter
import noice.app.modules.dashboard.home.adapter.HomeLihatSemuaAdapter
import noice.app.modules.dashboard.home.adapter.MultiVerticalsAdapter
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.indexpages.model.LihatSemuaEvent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.podcast.model.Content
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PermissionUtils
import noice.app.utils.RecyclerViewMargin
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.parcelable
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class HomeLihatSemuaFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    companion object {
        const val DATA = "DATA"
        const val SOURCE = "SOURCE"

        fun newInstance(data : LihatSemuaEvent, source : String, extraData: ExtraData? = null) =
            HomeLihatSemuaFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(DATA, data)
                    putString(SOURCE, source)
                    putParcelable(Constants.EXTRA_DATA, extraData)
                }
            }
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding: FragmentHomeLihatSemuaBinding
    private lateinit var mContext: Context
    private var semuaAdapter: HomeLihatSemuaAdapter? = null
    private var multipleVerticalsAdapter: MultiVerticalsAdapter? = null
    private var dataList = ArrayList<HomeContent?>()
    private var playListContent = ArrayList<Content?>()
    private val limit = 15
    private var page = 1
    private var segment:HomeSegment?=null
    private var data : LihatSemuaEvent? = null
    private var source : String? = ""
    private var extraData: ExtraData? = null
    private var isDeepLink = false
    private var isTitleSetWhenDeepLink = false

    private val permission = arrayListOf(Manifest.permission.POST_NOTIFICATIONS)
    private var permissionUtils: PermissionUtils? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        getDataFromIntent()

        isDeepLink = data?.title.isNullOrEmpty()

        if (!isDeepLink) {
            sendEventOnScreenOpen(data?.title ?: "")
        }

        permissionUtils = PermissionUtils(
            this,
            permission,
            textPermissionDeniedDialogTitle = getString(R.string.title_notification_permission),
            textPermissionDeniedDialogDescription = getString(R.string.description_notification_permission),
            textPermissionDeniedDialogPositiveButton = getString(R.string.notification_permission_positive_button),
            textPermissionDeniedDialogNegativeButton = getString(R.string.notification_permission_negative_button)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHomeLihatSemuaBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        observeLiveTrigger()

        showLoader()

        getData(false)
    }

    private fun getDataFromIntent() {
        arguments?.let {
            data = it.parcelable(DATA)
            source = it.getString(SOURCE, "") ?: ""
            extraData = it.parcelable(Constants.EXTRA_DATA)
        }
    }

    private fun initViews() {

        GlobalObservers.playerEventObserver
            .subscribeMusicButtonEvents(viewLifecycleOwner) { playerEvent ->
                handlePlayerEvents(playerEvent)
            }

        binding.toolBar.setToolbarTitle(data?.title ?: "")

        binding.toolBar.setBackClick {
            (mContext as AppCompatActivity).onBackPressed()
        }

        binding.toolBar.setRightIconListener {
            AnalyticsUtil.sendEventForOpenScreen("", "search_bar_clicked", data?.type)
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE, targetPageId = data?.screenName + "_Page"))
        }
    }

    private fun getData(isLoadMore: Boolean) {

        if (!isLoadMore) {
            page = 1
        }

        if (data?.isPlaylist == true) {
            viewModel.getPlaylistContent(data?.id ?: "", page, limit).observe(viewLifecycleOwner) {
                if (it.status == ResponseStatus.SUCCESS && !it.data.isNullOrEmpty()) {
                    if (it.data.isNotEmpty()) {

                        initializeRecyclerView()

                        if (isLoadMore) {
                            semuaAdapter?.addMore(it.data)
                        } else {
                            semuaAdapter?.addNewList(it.data)
                        }
                    } else {
                        semuaAdapter?.isLoadMoreEnabled(false)
                    }
                } else {
                    page -= 1
                }
                binding.errorView.hide()
            }
        } else {
            viewModel.getLihatSemuaHome(data?.id ?: "", page, limit).observe(viewLifecycleOwner) {
                if (it?.status == ResponseStatus.LOADING) {
                    if (!it.data?.data.isNullOrEmpty() && !it.data?.data?.get(0)?.content.isNullOrEmpty()) {
                        handleResponse(it.data?.data?.get(0), isLoadMore)
                    }
                } else if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                    if (!it.data?.data?.get(0)?.content.isNullOrEmpty()) {
                        handleResponse(it.data?.data?.get(0), isLoadMore)
                    } else {
                        if (data?.type == HomeInnerAdapter.TYPE_RECOMMENDATION || data?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION) {
                            multipleVerticalsAdapter?.isLoadMoreEnabled(false)
                        } else {
                            semuaAdapter?.isLoadMoreEnabled(false)
                        }
                    }
                    if (dataList.isEmpty()) {
                        binding.errorView.showError()
                    } else {
                        binding.errorView.hide()
                    }
                } else {
                    page -= 1
                    binding.errorView.hide()
                }
            }
        }
    }

    private fun handleResponse(data: HomeSegment?, isLoadMore: Boolean) {
        segment = data
        if (this.data?.type.isNullOrEmpty()) {
            this.data?.type = data?.viewOptions?.viewType
        }

        if (isDeepLink && !isTitleSetWhenDeepLink) {
            binding.toolBar.setToolbarTitle(segment?.title ?: "")
            sendEventOnScreenOpen(segment?.title ?: "")
            isTitleSetWhenDeepLink = true
        }

        initializeRecyclerView()

        if (isLoadMore) {
            if (<EMAIL>?.type == HomeInnerAdapter.TYPE_RECOMMENDATION ||
                <EMAIL>?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION
            ) {
                multipleVerticalsAdapter?.addMore(segment?.content ?: ArrayList())
            } else {
                semuaAdapter?.addMore(segment?.content ?: ArrayList())
            }
        } else {
            if (<EMAIL>?.type == HomeInnerAdapter.TYPE_RECOMMENDATION ||
                <EMAIL>?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION
            ) {
                multipleVerticalsAdapter?.addNewList(segment?.content ?: ArrayList())
            } else {
                semuaAdapter?.addNewList(segment?.content ?: ArrayList())
            }
        }
    }

    private fun handlePlayerEvents(playerEvent: PlayerEvent) {
        dataList.filter { content ->
            (content?.isPlaying == true || content?.showLoader == true) && content.id != playerEvent.exoData?.id
        }.forEach { content ->
            content?.isPlaying = false
            content?.showLoader = false

            val index = dataList.indexOf(content)
            if (index != -1) {
                semuaAdapter?.notifyItemChanged(index)
                multipleVerticalsAdapter?.notifyItemChanged(index)
            }
        }

        val content = dataList.find {
            it?.id == playerEvent.exoData?.id
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER

                if (playerEvent.event == PlayerEvent.PAUSE_END_LOADER && playerEvent.currentContentPositionMs != null) {
                    content.meta?.timeElapsed = playerEvent.currentContentPositionMs.msToSeconds().toLong()
                }
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = dataList.indexOf(content)
            if (index != -1) {
                semuaAdapter?.notifyItemChanged(index)
                multipleVerticalsAdapter?.notifyItemChanged(index)
            }
        }
    }

    private fun initializeRecyclerView() {

        if (semuaAdapter != null || multipleVerticalsAdapter != null)
            return

        binding.rvSegments.apply {
            if (data?.isPlaylist == true) {

                layoutManager = LinearLayoutManager(mContext, RecyclerView.VERTICAL, false)

                semuaAdapter = HomeLihatSemuaAdapter(
                    this,
                    this@HomeLihatSemuaFragment,
                    <EMAIL>?.type ?: "radioPodcast",
                    playListContent,
                    "content", mContext,
                    extraData
                )
            } else {
                layoutManager = if (segment?.viewOptions?.indexPage?.span != null && segment?.viewOptions?.indexPage?.span!! > 1) {
                    GridLayoutManager(
                        mContext,
                        segment?.viewOptions?.indexPage?.span!!,
                        RecyclerView.VERTICAL,
                        false
                    )
                } else {
                    LinearLayoutManager(mContext, RecyclerView.VERTICAL, false)
                }

                if (data?.type == HomeInnerAdapter.TYPE_RECOMMENDATION ||
                    data?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION
                ) {
                    multipleVerticalsAdapter = MultiVerticalsAdapter(
                        this,
                        this@HomeLihatSemuaFragment,
                        dataList,
                        segment?.entityType,
                        data?.type,
                        mContext,
                        extraData
                    )
                } else {
                    semuaAdapter = HomeLihatSemuaAdapter(
                        this,
                        this@HomeLihatSemuaFragment,
                        segment?.viewOptions?.viewType.toString(),
                        dataList,
                        segment?.entityType, mContext,
                        extraData,
                        permissionUtils = permissionUtils
                    )
                }
            }

            if (itemDecorationCount > 0) {
                removeItemDecorationAt(0)
            }

            addItemDecoration(RecyclerViewMargin(mContext.resources.getDimensionPixelSize(R.dimen.dp16)))

            adapter = if (data?.type == HomeInnerAdapter.TYPE_RECOMMENDATION ||
                data?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION
            ) {
                multipleVerticalsAdapter
            } else {
                semuaAdapter
            }
        }
    }

    private fun observeLiveTrigger() = viewLifecycleOwner.lifecycleScope.launchWhenStarted {
        LiveTrigger.event.collect{
            when(it){
                is LiveTrigger.Event.ReminderChanged -> {
                    val content = it.data as? HomeContent
                    val index = dataList.indexOf(content)
                    if (index != -1) {
                        if (data?.type == HomeInnerAdapter.TYPE_RECOMMENDATION ||
                            data?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION
                        ) {
                            multipleVerticalsAdapter?.notifyItemChanged(index)
                        }
                    }
                }
                else -> {}
            }
        }
    }

    private fun showLoader() {
        when (data?.type) {
            HomeLihatSemuaAdapter.TYPE_RECENTLY_PLAYED -> {
                binding.errorView.showLoading(R.layout.recently_played_sekelton)
            }
            HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION,
            HomeLihatSemuaAdapter.TYPE_DEFAULT,
            HomeInnerAdapter.TYPE_RECOMMENDATION -> {
                binding.errorView.showLoading(R.layout.default_view_loader)
            }
            HomeLihatSemuaAdapter.TYPE_AUDIO_BOOK -> {
                binding.errorView.showLoading(R.layout.audio_book_loader)
            }
            HomeLihatSemuaAdapter.TYPE_RADIO -> {
                binding.errorView.showLoading(R.layout.radio_loader)
            }
            HomeLihatSemuaAdapter.TYPE_CONTENT_HORIZONTAL -> {
                binding.errorView.showLoading(R.layout.horizontal_content_loader)
            }
            HomeLihatSemuaAdapter.TYPE_RADIO_PODCAST -> {
                binding.errorView.showLoading(R.layout.horizontal_content_loader)
            }
            HomeLihatSemuaAdapter.TYPE_ARTIST -> {
                binding.errorView.showLoading(R.layout.artist_loader)
            }
            HomeLihatSemuaAdapter.TYPE_LIVE -> {
                binding.errorView.showLoading(R.layout.lihat_live_view_loader)
            }
            else -> {
                binding.errorView.showLoading(R.layout.default_view_loader)
            }
        }
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {

        if (totalItemsCount > (limit-1)) {
            this.page = page + 1
            getData(true)
        } else {
            if (data?.type == HomeInnerAdapter.TYPE_RECOMMENDATION || data?.type == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION) {
                multipleVerticalsAdapter?.isLoadMoreEnabled(false)
            } else {
                semuaAdapter?.isLoadMoreEnabled(false)
            }
        }
    }

    private fun sendEventOnScreenOpen(title: String) {
        AnalyticsUtil.sendEvent(
            title.replace(" ","_").lowercase() + "_lihatSemua_page_opened",
            data?.screenName
        )

        AnalyticsUtil.sendEvent("segment_page_viewed", Bundle().apply {
            putString("segmentName", title.replace(" ","_").lowercase())
            putString("source", source)

            if (extraData != null) {
                if (!extraData?.source.isNullOrEmpty()) {
                    putString("source", extraData?.source ?: "")
                }
            }
        })
        MoEngageAnalytics.sendEvent(mContext, "segment page viewed", Bundle().apply {
            putString("segment name", title.replace(" ","_").lowercase())
            putString("source", source)
        })

        if (data?.type == HomeLihatSemuaAdapter.TYPE_RADIO_PODCAST) {
            AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                putString(FirebaseAnalytics.Param.SCREEN_NAME, "Radio_Podcast_Page")
                putString("previousScreen", data?.screenName)
                putString("radioTitle", title)
                putString("radioId", data?.parrentId ?: "")
                putString("entitySubtype", "radio")
            })
        }
    }
}