package noice.app.modules.dashboard.adapter

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import noice.app.model.Variant
import noice.app.modules.dashboard.collection.fragment.CollectionFragment
import noice.app.modules.dashboard.home.HomeFragment
import noice.app.modules.live.fragment.EmptyFragment
import noice.app.modules.live.fragment.LiveHomeFragment
import noice.app.modules.profile.self.ProfileSelfFragment
import noice.app.modules.search.fragment.NavSearchFragment
import noice.app.utils.ExperimentUtils

class DashBoardPagerAdapter(activity: AppCompatActivity) : FragmentStateAdapter(activity) {

    private val fragments = mutableListOf(
        HomeFragment.newInstance(),
        NavSearchFragment.newInstance(),
        CollectionFragment.newInstance(),
        ProfileSelfFragment.newInstance("Home")
    )

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun createFragment(position: Int): Fragment {
        return fragments[position]
    }

    fun updateFragment(index: Int, fragment: Fragment, isBottomNavConfigsAvailable: Boolean = false) {
        if (isBottomNavConfigsAvailable){
            fragments.clear()
            fragments.addAll( mutableListOf(
                HomeFragment.newInstance(),
                NavSearchFragment.newInstance(),
                EmptyFragment.newInstance(),
                CollectionFragment.newInstance(),
                ProfileSelfFragment.newInstance("Home")
            ))
            fragments[index] = fragment
        }
        notifyDataSetChanged()
    }
}