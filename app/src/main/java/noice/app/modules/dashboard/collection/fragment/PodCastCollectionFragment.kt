package noice.app.modules.dashboard.collection.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentPodcastCollectionBinding
import noice.app.modules.dashboard.collection.adapter.PodCastCollectionAdapter
import noice.app.modules.dashboard.collection.viewmodel.CollectionViewModel
import noice.app.modules.podcast.model.Channel
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.RecyclerViewMargin

@AndroidEntryPoint
class PodCastCollectionFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener {

    private val viewModel : CollectionViewModel by viewModels()

    private lateinit var binding : FragmentPodcastCollectionBinding
    private lateinit var adapter : PodCastCollectionAdapter
    private lateinit var ctx : Context
    private var podCasts = ArrayList<Channel?>()
    private var page = 1

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPodcastCollectionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        binding.errorView.showLoading()

        getData(false)
        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Collection_Podcast")
            putString("previousScreen", "Collection_Page")
        })
    }

    private fun initViews() {

        binding.toolbar.setBackClick {
            (ctx as AppCompatActivity).onBackPressed()
        }

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getData(false)
        }

        adapter = PodCastCollectionAdapter(binding.podCastRecycler, podCasts, this)
        binding.podCastRecycler.adapter = adapter
        binding.podCastRecycler.addItemDecoration(RecyclerViewMargin(ctx.resources.getDimensionPixelSize(R.dimen.dp16)))

        binding.errorView.setOnReturnClick {
            binding.errorView.showLoading()
            getData(false)
        }
    }

    private fun getData(loadMore : Boolean) {

        if (!loadMore) {
            page = 1
        }

        val map = HashMap<String, String>()
        map["page"] = page.toString()
        map["limit"] = "10"
        map["entityType"] = "catalog"
        map["entitySubType"] = "podcast"

        viewModel.getCollectionCatalog(map).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.SUCCESS) {
                if (!it.data.isNullOrEmpty()) {
                    if (loadMore)
                        adapter.addMore(ArrayList(it.data))
                    else
                        adapter.addNewList(ArrayList(it.data))

                    hideLoading()
                } else if (podCasts.isEmpty() || !loadMore) {
                    showEmptyView()
                } else {
                    adapter.isLoadMoreEnabled(false)
                    hideLoading()
                }
            } else {
                showError(it?.message)
            }
        }
    }

    private fun hideLoading() {
        binding.emptyView.visibility = View.GONE
        binding.errorView.hide()
    }

    private fun showEmptyView() {
        binding.errorView.hide()
        binding.emptyView.visibility = View.VISIBLE
    }

    private fun showError(message : String?) {
        adapter.isLoadMoreEnabled(false)
        binding.emptyView.visibility = View.GONE
        binding.errorView.showError(message, type = "Collection_Podcast", errorName = message)
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        this.page = page + 1
        if (totalItemsCount > 9) {
            getData(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }
}