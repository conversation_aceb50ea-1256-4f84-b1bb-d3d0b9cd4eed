package noice.app.modules.dashboard.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentAppReviewDialogBinding
import noice.app.enums.UserAction
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.media.model.MediaAction
import noice.app.utils.AnalyticsUtil
import noice.app.utils.PrefUtils
import noice.app.utils.ShareUtils

@AndroidEntryPoint
class AppReviewDialog : BottomSheetDialogFragment() {

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding : FragmentAppReviewDialogBinding
    private lateinit var ctx : Context
    private lateinit var reviewManager : ReviewManager

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun show(manager: FragmentManager, tag: String?) {
        manager.beginTransaction().add(this, tag).commitAllowingStateLoss()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAppReviewDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val bundle = Bundle()
        AnalyticsUtil.firebaseAnalytics.logEvent("rating_prompt_shown",bundle)
        initViews()
    }

    private fun initViews() {

        binding.later.setOnClickListener {
            PrefUtils.hasReviewedApp = true
            val bundle = Bundle()
            AnalyticsUtil.firebaseAnalytics.logEvent("rating_later_clicked",bundle)
            dismiss()
        }

        binding.rate.setOnClickListener {
            if (!::reviewManager.isInitialized) {
                reviewManager = ReviewManagerFactory.create(ctx)
            }

            AnalyticsUtil.firebaseAnalytics.logEvent("rating_give_clicked", Bundle())

            reviewManager.requestReviewFlow().addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    reviewManager.launchReviewFlow(ctx as AppCompatActivity, task.result)
                        .addOnCompleteListener {
                            PrefUtils.hasReviewedApp = true
                            if (it.isSuccessful) {
                                AnalyticsUtil.firebaseAnalytics.logEvent("rating_given", Bundle())

                                updateServer()
                            }else{
                                if (!isDetached || !isRemoving || isAdded) {
                                    dismiss()
                                }
                            }
                        }
                } else {
                    PrefUtils.hasReviewedApp = true
                    ShareUtils.redirectToPlayStore(ctx, ctx.packageName + "&reviewId=0")
                }
            }
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {}

    private fun updateServer() {
        if (isDetached || isRemoving || !isAdded)
            return
        val mediaAction = MediaAction(
            UserAction.REVIEW.action,
            null,
            PrefUtils.userDetails?.id.toString(),
            "app",
            PrefUtils.userDetails?.id.toString(),
            "app",
            null
        )

        viewModel.performAction(mediaAction)
        if ((!isDetached || !isRemoving) && isAdded && isVisible) {
            dismissAllowingStateLoss()
        }
    }
}