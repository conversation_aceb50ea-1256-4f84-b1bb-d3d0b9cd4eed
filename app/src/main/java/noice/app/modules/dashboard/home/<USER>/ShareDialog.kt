package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.appsflyer.share.LinkGenerator
import com.facebook.CallbackManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import noice.app.BaseApplication
import noice.app.R
import noice.app.databinding.FragmentShareEpisodeDialogBinding
import noice.app.listner.OnClickInterface
import noice.app.model.ExoNotificationData
import noice.app.modules.dashboard.home.adapter.ShareOptionAdapter
import noice.app.modules.dashboard.home.model.ShareOption
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.media.model.Playlist
import noice.app.modules.media.model.Share
import noice.app.modules.onboarding.models.ShareRequest
import noice.app.modules.podcast.model.Channel
import noice.app.modules.podcast.model.Content
import noice.app.modules.podcast.viewmodel.ChannelPodcastViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import noice.app.utils.Constants.Companion.CLIPBOARD
import noice.app.utils.Constants.Companion.COPY_LINK
import noice.app.utils.Constants.Companion.DEFAULT_URI_SCHEME
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CATALOG
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CONTENT
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PLAYLIST
import noice.app.utils.Constants.Companion.EVENT_CATALOG_SHARED
import noice.app.utils.Constants.Companion.EVENT_CONTENT_SHARED
import noice.app.utils.Constants.Companion.EVENT_PLAYLIST_SHARED
import noice.app.utils.Constants.Companion.EVENT_RADIO_SHARED
import noice.app.utils.Constants.Companion.FACEBOOK_FEED
import noice.app.utils.Constants.Companion.FACEBOOK_LITE_FEED
import noice.app.utils.Constants.Companion.FACEBOOK_STORIES
import noice.app.utils.Constants.Companion.INSTAGRAM_DIRECT
import noice.app.utils.Constants.Companion.INSTAGRAM_STORIES
import noice.app.utils.Constants.Companion.LINE
import noice.app.utils.Constants.Companion.MORE
import noice.app.utils.Constants.Companion.PACKAGE_NAME_INSTAGRAM
import noice.app.utils.Constants.Companion.TELEGRAM
import noice.app.utils.Constants.Companion.TWITTER
import noice.app.utils.Constants.Companion.WHATSAPP

@AndroidEntryPoint
class ShareDialog : BottomSheetDialogFragment() {

    companion object {
        const val AF_PARAM_CHANNEL = "mobile_share"

        private const val SHARE_DATA = "SHARE_DATA"
        private const val SHARE_TYPE = "SHARE_TYPE"
        private const val IS_RADIO = "IS_RADIO"
        const val TYPE_CONTENT = 1
        const val TYPE_PLAYLIST = 2
        const val TYPE_CHANNEL = 3
        const val TYPE_LIVE_ROOM = 4

        fun <T> newInstance(
            data: T,
            isRadio: Boolean = false,
            endTime: String?,
            screenShotUri: Uri? = null,
            isFromMediaPlayer: Boolean = false//to handle source value in Amplitude
        ) = ShareDialog().apply {
            val share = Share()
            share.isFromMediaPlayer = isFromMediaPlayer

            val type = when (data) {
                is Playlist -> {
                    share.playlist = data
                    TYPE_PLAYLIST
                }
                is Content -> {
                    share.content = data
                    TYPE_CONTENT
                }
                is Channel -> {
                    share.channel = data
                    TYPE_CHANNEL
                }
                is ExoNotificationData -> {
                    share.exoNotificationData = data
                    TYPE_CONTENT
                }
                is LiveRoom -> {
                    share.liveRoom = data
                    TYPE_LIVE_ROOM
                }
                else -> {
                    TYPE_CONTENT
                }
            }
            share.screenShotUri = screenShotUri
            arguments = Bundle().apply {
                putParcelable(SHARE_DATA, share)
                putInt(SHARE_TYPE, type)
                putBoolean(IS_RADIO, isRadio)
                putString("endTime", endTime)
            }
        }
    }

    override fun show(manager: FragmentManager, tag: String?) {
        val ft = manager.beginTransaction()
        ft.add(this, ShareDialog::class.java.simpleName)
        ft.commitAllowingStateLoss()
    }

    private val viewModel : ChannelPodcastViewModel by viewModels()

    private lateinit var binding : FragmentShareEpisodeDialogBinding
    private lateinit var mContext : Context
    private var share : Share? = null
    private var shareType = TYPE_CONTENT
    private var endTime:String ? = null

    private var isRadio = false

    private val shareOptions = ArrayList<ShareOption>()
    private val appsToCheck = arrayListOf(
        Constants.PACKAGE_NAME_WHATSAPP,
        PACKAGE_NAME_INSTAGRAM,
        Constants.PACKAGE_NAME_FACEBOOK,
        Constants.PACKAGE_NAME_FACEBOOK_LITE,
        Constants.PACKAGE_NAME_TELEGRAM,
        Constants.PACKAGE_NAME_LINE,
        Constants.PACKAGE_NAME_TWITTER
    )

    /* using it to add facebook feed share callback */
    private val callbackManager = CallbackManager.Factory.create()

    private var deferredDeepLink: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (arguments?.containsKey("endTime") == true){
            endTime = arguments?.getString("endTime")
        }
        share = arguments?.getParcelable(SHARE_DATA)
        shareType = arguments?.getInt(SHARE_TYPE) ?: TYPE_CONTENT
        isRadio = arguments?.getBoolean(IS_RADIO,false) ?: false
        setStyle(STYLE_NORMAL, R.style.AppBottomSheetDialogTheme)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentShareEpisodeDialogBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews()
        share?.isRadio = isRadio //for handling radio three dot menu share to story incorrect description
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        callbackManager.onActivityResult(requestCode, resultCode, data)
    }

    private fun initViews() {
        binding.cancelBtn.setOnClickListener {
            dismiss()
        }
        setShareOptionAdapter()
    }

    private fun shareToSocialMedia(channel: String) {
        share?.socialMediaType = channel
        if(shareType == TYPE_CONTENT) {
            if (share?.exoNotificationData != null){
                ShareUtils.shareHomeContentTemplate(mContext, share)
            } else {
                ShareUtils.shareContentTemplate(mContext, share)
            }
        } else if(shareType == TYPE_PLAYLIST) {
            ShareUtils.sharePlayListTemplate(mContext, share)
        } else if(shareType == TYPE_CHANNEL) {
            ShareUtils.shareCatalogTemplate(mContext, share)
        } else if(shareType == TYPE_LIVE_ROOM) {
            ShareUtils.shareLiveTemplate(mContext, share)
        }
       // dismiss()
    }

    private fun getDeepLinkDetail(channel : String) {

        val id : String
        val entityType : String
        val entitySubType : String

        when (shareType) {
            TYPE_CONTENT -> {
                if(share?.content != null) {
                    id = share?.content?.id ?: ""
                    entityType = ENTITY_TYPE_CONTENT
                    entitySubType = share?.content?.catalog?.type ?: ""
                } else {
                    if (share?.exoNotificationData?.isRadio == true){
                        id = share?.exoNotificationData?.catalogId ?: ""
                        entityType = ENTITY_TYPE_CATALOG
                        entitySubType = share?.exoNotificationData?.entitySubType ?: ""
                    }else{
                        id = share?.exoNotificationData?.id ?: ""
                        entityType = ENTITY_TYPE_CONTENT
                        entitySubType = share?.exoNotificationData?.entitySubType ?: ""
                    }
                }
            }
            TYPE_CHANNEL -> {
                id = share?.channel?.id ?: ""
                entityType = ENTITY_TYPE_CATALOG
                entitySubType = share?.channel?.type ?: ""
            }
            TYPE_PLAYLIST -> {
                id = share?.playlist?.id ?: ""
                entityType = ENTITY_TYPE_PLAYLIST
                entitySubType = ENTITY_TYPE_PLAYLIST
            }
            TYPE_LIVE_ROOM -> {
                id = share?.liveRoom?.id ?: ""
                entityType = ENTITY_TYPE_LIVE_STREAM
                entitySubType = ENTITY_TYPE_LIVE_STREAM
            }
            else -> {
                id = ""
                entityType = ""
                entitySubType = ""
            }
        }

        share?.id = id
        share?.entityType = entityType
        share?.entitySubType = entitySubType

        if(id.isEmpty() || entityType.isEmpty() || entitySubType.isEmpty())
            return

        binding.errorView.showLoading()

        val shareRequest = ShareRequest(id, entityType, entitySubType, channel)

        viewModel.shareContent(shareRequest,isRadio,endTime).observe(this) {
            binding.errorView.hide()
            if (it.status == ResponseStatus.SUCCESS) {
                share?.deepLink = it.data?.deepLink
                share?.imageUrl = it.data?.imageUrl
                share?.text = it.data?.text

                when (channel) {
                    INSTAGRAM_STORIES -> {
                        shareToSocialMedia(INSTAGRAM_STORIES)
                        dismiss()
                    }
                    FACEBOOK_STORIES -> {
                        shareToSocialMedia(FACEBOOK_STORIES)
                        dismiss()
                    }
                    INSTAGRAM_DIRECT,
                    FACEBOOK_FEED,
                    FACEBOOK_LITE_FEED,
                    WHATSAPP,
                    TELEGRAM,
                    LINE,
                    TWITTER,
                    CLIPBOARD,
                    MORE -> {
                        if (PrefUtils.appConfig?.share_config?.enable_appsflyer_deferred_deep_link_android == true) {
                            generateCustomDeferredLink(channel)
                        } else {
                            shareDeferredOrDefaultUrl(channel)
                        }
                    }
                }
            } else {
                Utils.showSnackBar(binding, mContext.getString(R.string.unable_to_share))
            }
        }
    }

    private fun sendAnalytics(channel: String) {
        val linkType = channel.replace("_", " ")

        if (shareType == TYPE_CHANNEL){
            if (isRadio) {
                AnalyticsUtil.sendEventForCatalog(EVENT_RADIO_SHARED, share?.channel?.id, share?.channel?.title
                    ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType)
                MoEngageAnalytics.sendEventForRadioShare(mContext, "radio shared", linkType, share)
            } else {
                AnalyticsUtil.sendEventForCatalog(EVENT_CATALOG_SHARED, share?.channel?.id, share?.channel?.title
                    ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType)
                MoEngageAnalytics.sendEventForContentShare(
                    mContext,
                    "content shared",
                    share?.channel?.type?:"",
                    linkType,
                    share,
                    shareType
                )
            }
        } else if (shareType == TYPE_CONTENT) {
            if (share?.exoNotificationData != null) {
                if (share?.isRadio == true) {
                    AnalyticsUtil.sendEventForRadio(share?.exoNotificationData?.catalogId, EVENT_RADIO_SHARED, share?.exoNotificationData?.catalogTitle
                        ?: "", share?.exoNotificationData?.id
                        ?: "", share?.exoNotificationData?.title ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType)
                    MoEngageAnalytics.sendEventForRadioShare(mContext, "radio shared", linkType, share)
                } else {
                    AnalyticsUtil.sendEventForContent(share?.exoNotificationData?.entitySubType, EVENT_CONTENT_SHARED, share?.exoNotificationData?.catalogId
                        ?: "", share?.exoNotificationData?.catalogTitle
                        ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType, share?.exoNotificationData?.title ?: "")
                    MoEngageAnalytics.sendEventForContentShare(
                        mContext,
                        "content shared",
                        share?.content?.type?:"",
                        linkType,
                        share,
                        shareType
                    )
                }
            } else {
                AnalyticsUtil.sendEventForContent(share?.content?.entitySubType, EVENT_CONTENT_SHARED, share?.content?.catalog?.id
                    ?: "", share?.content?.catalog?.title
                    ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType, share?.content?.title ?: "")
                MoEngageAnalytics.sendEventForContentShare(
                    mContext,
                    "content shared",
                    share?.content?.type?:"",
                    linkType,
                    share,
                    shareType
                )
            }
        } else if (shareType == TYPE_PLAYLIST) {
            AnalyticsUtil.sendEventForPlaylist(ENTITY_TYPE_PLAYLIST, EVENT_PLAYLIST_SHARED, share?.playlist?.title
                ?: "", share?.playlist?.id ?: "", share?.playlist?.user?.displayName
                ?: "", if (channel == COPY_LINK) CLIPBOARD else linkType)
        } else if (shareType == TYPE_LIVE_ROOM) {
            AnalyticsUtil.sendEvent("live_room_shared", Bundle().apply {
                putString("roomName", share?.liveRoom?.title?:"")
                putString("roomId", share?.liveRoom?.id?:"")
                putString("hostUserId", share?.liveRoom?.roomHost?.user?.id?:"")
                putString("schedule", share?.liveRoom?.scheduledTime?:"")
                putString("channelSelected", if (channel == COPY_LINK) CLIPBOARD else linkType)
                putString("hostName", share?.liveRoom?.roomHost?.user?.userName?:"")
            })
            MoEngageAnalytics.sendEventForLiveRoom(mContext, "live room shared", linkType, share)
        }
    }

    private fun setShareOptionAdapter() {
        if (share?.screenShotUri == null) {
            shareOptions.add(ShareOption(R.drawable.ic_copy_link_round, getString(R.string.copy_link), CLIPBOARD))
        }
        appsToCheck.forEach { appPackage ->
            if (Utils.isAppInstalled(mContext, appPackage)) {
                when (appPackage) {
                    Constants.PACKAGE_NAME_WHATSAPP -> {
                        shareOptions.add(ShareOption(R.drawable.ic_whatsapp, getString(R.string.whatsapp), WHATSAPP))
                    }
                    PACKAGE_NAME_INSTAGRAM -> {
                        shareOptions.add(ShareOption(R.drawable.ic_share_circle, getString(R.string.stories), INSTAGRAM_STORIES))
                        shareOptions.add(ShareOption(R.drawable.ic_instagram, getString(R.string.instagram_direct), INSTAGRAM_DIRECT))
                    }
                    Constants.PACKAGE_NAME_FACEBOOK -> {
                        shareOptions.add(ShareOption(R.drawable.ic_facebook_story, getString(R.string.stories), FACEBOOK_STORIES))
                        shareOptions.add(ShareOption(R.drawable.ic_facebook, getString(R.string.news_feed), FACEBOOK_FEED))
                    }
                    Constants.PACKAGE_NAME_FACEBOOK_LITE -> {
                        shareOptions.add(ShareOption(R.drawable.ic_facebook_lite, getString(R.string.facebook_lite), FACEBOOK_LITE_FEED))
                    }
                    Constants.PACKAGE_NAME_TELEGRAM -> {
                        shareOptions.add(ShareOption(R.drawable.ic_telegram, getString(R.string.telegram), TELEGRAM))
                    }
                    Constants.PACKAGE_NAME_LINE -> {
                        shareOptions.add(ShareOption(R.drawable.ic_line, getString(R.string.line), LINE))
                    }
                    Constants.PACKAGE_NAME_TWITTER -> {
                        shareOptions.add(ShareOption(R.drawable.ic_twitter, getString(R.string.twitter), TWITTER))
                    }
                }
            }
        }
        shareOptions.add(ShareOption(R.drawable.ic_more_round, getString(R.string.more), MORE))

        val shareOptionAdapter = ShareOptionAdapter(shareOptions, object : OnClickInterface<ShareOption> {
            override fun dataClicked(data: ShareOption) {
                when (data.optionType) {
                    FACEBOOK_STORIES -> {
                        if (deferredDeepLink.isNullOrEmpty()) {
                            getDeepLinkDetail(FACEBOOK_STORIES)
                        } else {
                            shareToSocialMedia(FACEBOOK_STORIES)
                            dismiss()
                        }
                    }
                    INSTAGRAM_STORIES -> {
                        if (deferredDeepLink.isNullOrEmpty()) {
                            getDeepLinkDetail(INSTAGRAM_STORIES)
                        } else {
                            shareToSocialMedia(INSTAGRAM_STORIES)
                            dismiss()
                        }
                    }
                    FACEBOOK_FEED,
                    FACEBOOK_LITE_FEED,
                    WHATSAPP,
                    INSTAGRAM_DIRECT,
                    TELEGRAM,
                    LINE,
                    TWITTER,
                    CLIPBOARD,
                    MORE -> {
                        if (deferredDeepLink.isNullOrEmpty()) {
                            getDeepLinkDetail(data.optionType)
                        } else {
                            shareDeferredOrDefaultUrl(data.optionType, deferredDeepLink)
                        }
                    }
                }
            }
        })

        binding.recyclerShareOptions.adapter = shareOptionAdapter
        binding.recyclerShareOptions.layoutManager = GridLayoutManager(mContext, 4, GridLayoutManager.VERTICAL, false)
        binding.recyclerShareOptions.addItemDecoration(RecyclerViewMargin(mContext.resources.getDimensionPixelSize(R.dimen.dp16), RecyclerViewMargin.SHARE))
    }

    /* custom callback when content is shared on facebook feed (or cancelled, error) */
    private val fbCustomCallback = object : OnClickInterface<String> {
        override fun dataClicked(data: String) {
            if (data == Constants.SUCCESS) {
                sendAnalytics(FACEBOOK_FEED)
            }

            dismiss()
        }
    }

    /** AppsFlyer custom deferred deep-link and configs to generate links. */
    private fun generateCustomDeferredLink(channel: String) {
        val linkGenerator = BaseApplication.application.getLinkGen()
        linkGenerator?.apply {
            addParameter("deep_link_value", share?.id)
            addParameter("deep_link_sub1", share?.entityType)
            addParameter("deep_link_sub2", share?.entitySubType)
            addParameter("deep_link_sub3", Utils.getDeviceId())

            addParameter(
                "deep_link_sub4",
                if (PrefUtils.isLoggedIn) {
                    PrefUtils.userDetails?.id ?: ""
                } else {
                    PrefUtils.guestId ?: ""
                }
            )

            addParameter("is_retargeting", "true")
            addParameter("af_web_dp", share?.deepLink)
            addParameter("af_dp", DEFAULT_URI_SCHEME)
            addParameter("pid", AF_PARAM_CHANNEL)

            campaign = "${share?.entityType}_share"
            setChannel(AF_PARAM_CHANNEL)

            generateLink(BaseApplication.getBaseAppContext(), getDeferredDeeplinkListener(channel))
        }
    }

    /** AppsFlyer's custom deferred deep-link listener that receives the link or fails. */
    private fun getDeferredDeeplinkListener(channel: String) : LinkGenerator.ResponseListener {
        val listener = object: LinkGenerator.ResponseListener {
            override fun onResponse(p0: String?) {
                val deferredDeepLinkUrl = if (channel == FACEBOOK_FEED || channel == FACEBOOK_LITE_FEED) {
                    p0
                } else {
                    share?.text?.replace(share?.deepLink ?: "", p0 ?: "")
                }

                deferredDeepLink = deferredDeepLinkUrl
                shareDeferredOrDefaultUrl(channel, deferredDeepLinkUrl)
            }

            override fun onResponseError(p0: String?) {
                shareDeferredOrDefaultUrl(channel)
            }
        }

        return listener
    }

    /** A standard method that shares the generated AppsFlyer's deferred deep-links to its respective platform.
     * If link generation fails at AppsFlyer's end then we use our links (open.noice.id/<content|catalog>) as a fallback.
     **/
    private fun shareDeferredOrDefaultUrl(channel: String, deferredDeeplinkUrl: String? = null) {
        when (channel) {
            INSTAGRAM_DIRECT -> {
                sendAnalytics(INSTAGRAM_DIRECT)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareLinkToApp(mContext, PACKAGE_NAME_INSTAGRAM, deferredDeeplinkUrl ?: textToShare, share?.screenShotUri)
                dismiss()
            }
            FACEBOOK_FEED -> {
                ShareUtils.shareLinkToFacebookFeed(
                    mContext,
                    deferredDeeplinkUrl ?: share?.deepLink ?: "", //we can send only url for feed
                    share?.screenShotUri,
                    callbackManager,
                    fbCustomCallback
                )
            }
            FACEBOOK_LITE_FEED -> {
                sendAnalytics(FACEBOOK_LITE_FEED)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareLinkToApp(mContext, Constants.PACKAGE_NAME_FACEBOOK_LITE, deferredDeeplinkUrl ?: textToShare, share?.screenShotUri)
                dismiss()
            }
            WHATSAPP -> {
                sendAnalytics(WHATSAPP)
                ShareUtils.shareToWhatsApp(mContext, share, deferredDeeplinkUrl, share?.screenShotUri)
                dismiss()
            }
            TELEGRAM -> {
                sendAnalytics(TELEGRAM)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareLinkToApp(mContext, Constants.PACKAGE_NAME_TELEGRAM, deferredDeeplinkUrl ?: textToShare, share?.screenShotUri)
                dismiss()
            }
            LINE -> {
                sendAnalytics(LINE)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareLinkToApp(mContext, Constants.PACKAGE_NAME_LINE, deferredDeeplinkUrl ?: textToShare, share?.screenShotUri)
                dismiss()
            }
            TWITTER -> {
                sendAnalytics(TWITTER)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareLinkToApp(mContext, Constants.PACKAGE_NAME_TWITTER, deferredDeeplinkUrl ?: textToShare, share?.screenShotUri)
                dismiss()
            }
            CLIPBOARD -> {
                //for Firebase we are sending 'clipboard' & for Amplitude and other 'copy_link'
                sendAnalytics(COPY_LINK)
                ShareUtils.copyToClipBoard(mContext, share, deferredDeeplinkUrl)
                dismiss()
            }
            MORE -> {
                sendAnalytics(MORE)

                val textToShare = if (share?.text.isNullOrEmpty()) {
                    share?.deepLink ?: ""
                } else {
                    share?.text ?: ""
                }

                ShareUtils.shareWithNativeDialog(mContext, deferredDeeplinkUrl ?: textToShare, share, shareType, isRadio)
                dismiss()
            }
        }
    }
}