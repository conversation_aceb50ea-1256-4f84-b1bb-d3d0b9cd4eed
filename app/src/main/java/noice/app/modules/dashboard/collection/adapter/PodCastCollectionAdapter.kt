package noice.app.modules.dashboard.collection.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.modules.podcast.model.Channel
import noice.app.views.homesegmentviews.ContentHorizontalSegment

class PodCastCollectionAdapter(
    recyclerView: RecyclerView,
    dataSet: ArrayList<Channel?>,
    listener: LoadMoreAdapterListener?
) : LoadMoreAdapter<Channel>(recyclerView, dataSet, listener) {

    private lateinit var content: ContentHorizontalSegment

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        content = ContentHorizontalSegment(parent.context,"catalog","","Collection")
        content.setWidth()
        return content.viewHolder
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        if(dataSet[position] != null) {
            content.viewHolder = holder as ContentHorizontalSegment.ViewHolder
            content.setData(dataSet[position] as Channel)
        }
    }
}