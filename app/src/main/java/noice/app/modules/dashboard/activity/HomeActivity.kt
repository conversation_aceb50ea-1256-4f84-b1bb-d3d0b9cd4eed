package noice.app.modules.dashboard.activity

import android.content.Intent
import android.graphics.drawable.BitmapDrawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Base64.decode
import android.util.Log
import android.view.MenuItem
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.Nullable
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.PlayerView
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.ProductDetails
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.gson.Gson
import com.moengage.firebase.MoEFireBaseHelper
import com.moengage.inapp.MoEInAppHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.data.DataController
import noice.app.databinding.ActivityHomeBinding
import noice.app.exoplayer.BasePlayerActivity
import noice.app.exoplayer.ExoplayerUtils
import noice.app.exoplayer.NotificationBroadcast
import noice.app.exoplayer.NotificationBroadcast.Companion.CANCEL_DOWNLOAD
import noice.app.exoplayer.NotificationBroadcast.Companion.PAUSE_DOWNLOAD
import noice.app.exoplayer.NotificationBroadcast.Companion.PLAY_DOWNLOAD
import noice.app.exoplayer.NotificationBroadcast.Companion.RETRY_DOWNLOAD
import noice.app.exoplayer.cast.CastManager
import noice.app.listner.OnClickInterface
import noice.app.model.ExtraData
import noice.app.model.appconfig.AdConfig
import noice.app.model.appconfig.AppConfig
import noice.app.model.appconfig.BottomNavConfigsAos
import noice.app.model.appconfig.FirebaseVariant
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.model.user.User
import noice.app.modules.audiobook.fragment.AudioBookDetailFragment
import noice.app.modules.audioseries.fragment.AudioSeriesFragment
import noice.app.modules.browser.InAppBrowserActivity
import noice.app.modules.clips.activities.ClipsActivity
import noice.app.modules.coins.activity.CoinTopUpActivity
import noice.app.modules.coins.client.BillingClientLifecycle
import noice.app.modules.coins.client.CoinValidationHelper
import noice.app.modules.coins.fragments.CoinHistoryFragment
import noice.app.modules.coins.fragments.CoinTransactionDetails
import noice.app.modules.coins.fragments.CoinsUpdateDialog
import noice.app.modules.dashboard.adapter.DashBoardPagerAdapter
import noice.app.modules.dashboard.collection.fragment.CollectionFragment
import noice.app.modules.dashboard.collection.fragment.DownloadFragment
import noice.app.modules.dashboard.collection.fragment.LikedContentFragment
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.dashboard.home.HomeFragment
import noice.app.modules.dashboard.home.HomeFragmentNew
import noice.app.modules.dashboard.home.fragment.AudioBookHomeNewFragment
import noice.app.modules.dashboard.home.fragment.AudioSeriesHomeNewFragment
import noice.app.modules.dashboard.home.fragment.EpisodeDetailFragment
import noice.app.modules.dashboard.home.fragment.HomeLihatSemuaFragment
import noice.app.modules.dashboard.home.fragment.PodCastFragmentNew
import noice.app.modules.dashboard.home.fragment.RadioHomeFragmentNew
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.follow.FollowFragment
import noice.app.modules.indexpages.fragment.GenreDetailFragment
import noice.app.modules.indexpages.fragment.PlaylistDetailFragment
import noice.app.modules.indexpages.model.LihatSemuaEvent
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.indexpages.model.OpenIndexEvent.Companion.OPEN_PAGE
import noice.app.modules.indexpages.model.OpenIndexEvent.Companion.OPEN_POST_COMMENT
import noice.app.modules.indexpages.model.OpenIndexEvent.Companion.OPEN_POST_REPLY
import noice.app.modules.live.activity.EditRoomActivity
import noice.app.modules.live.analytics.LiveAnalytics
import noice.app.modules.live.core.ExConfigs
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.feature.createroom.CreateRoomPlusActivity
import noice.app.modules.live.feature.details.RoomDetailsFragment
import noice.app.modules.live.fragment.AllParticipantsFragmentNew
import noice.app.modules.live.fragment.AllSpeakersFragment
import noice.app.modules.live.fragment.EmptyFragment
import noice.app.modules.live.fragment.LiveBlockListFragment
import noice.app.modules.live.fragment.LiveHomeFragment
import noice.app.modules.live.fragment.LiveLihatSemuaFragment
import noice.app.modules.live.fragment.LiveLihatSemuaNewFragment
import noice.app.modules.live.fragment.SpeakerRequestFragmentNew
import noice.app.modules.live.fragment.UpdateDialog
import noice.app.modules.live.model.LiveRoom
import noice.app.modules.media.dialog.CommentDialog
import noice.app.modules.media.dialog.ReplyCommentDialog
import noice.app.modules.notification.fragment.NotificationFragment
import noice.app.modules.notification.model.NotificationRequest
import noice.app.modules.onboarding.activity.SplashActivity
import noice.app.modules.onboarding.fragments.MyGenreFragment
import noice.app.modules.onboarding.models.Genre
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.modules.onboarding.viewmodel.OnBoardingViewModel
import noice.app.modules.podcast.fragment.ChannelPodcastFragment
import noice.app.modules.podcast.fragment.EpisodeListFragment
import noice.app.modules.podcast.fragment.PodCastPagerFragment
import noice.app.modules.podcast.model.Channel
import noice.app.modules.profile.fragment.ProfileFragment
import noice.app.modules.profile.fragment.UserSettingsFragment
import noice.app.modules.profile.fragment.karya.UserKaryaFragment
import noice.app.modules.profile.fragment.userprofile.UserProfileFragment
import noice.app.modules.profile.self.ProfileSelfFragment
import noice.app.modules.radio.fragment.RadioDetailFragment
import noice.app.modules.radio.fragment.SchedulePagerFragment
import noice.app.modules.search.fragment.NavSearchFragment
import noice.app.modules.search.fragment.SearchFragment
import noice.app.modules.themedpages.fragment.ThemePageFragment
import noice.app.modules.trending.fragment.TopRankingFragment
import noice.app.modules.trending.fragment.TrendingEpisodeFragment
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.update.InAppUpdateManager
import noice.app.update.InAppUpdateStatus
import noice.app.utils.AnalyticsBuilder
import noice.app.utils.AnalyticsUtil
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.CONFIG_BOTTOM_NAV_CONFIGS
import noice.app.utils.Constants.Companion.ENTITY_SUBTYPE_SUBSCRIPTION
import noice.app.utils.Constants.Companion.ENTITY_SUBTYPE_THEME_PAGE
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_SERIES
import noice.app.utils.Constants.Companion.ENTITY_TYPE_COLLECTION
import noice.app.utils.Constants.Companion.ENTITY_TYPE_CREATE_LIVE_ROOM
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_TAB
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PAGE
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PODCAST
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.utils.Constants.Companion.ENTITY_TYPE_TOP_RANKING
import noice.app.utils.Constants.Companion.ENTITY_TYPE_TRENDING_EPISODE
import noice.app.utils.Constants.Companion.PAGE_COIN_HISTORY
import noice.app.utils.Constants.Companion.PAGE_COIN_PACKAGE_LIST
import noice.app.utils.Constants.Companion.PAGE_DOWNLOAD
import noice.app.utils.Constants.Companion.PAGE_LIKED_CONTENT
import noice.app.utils.Constants.Companion.PAGE_PROFILE_TAB
import noice.app.utils.Constants.Companion.PAGE_USER_SETTINGS
import noice.app.utils.Constants.Companion.RESULT
import noice.app.utils.DownloadUtils
import noice.app.utils.ExperimentUtils
import noice.app.utils.ImageUtils
import noice.app.utils.ImageUtils.downloadAndCacheImage
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import noice.app.utils.ShareUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.isTrue
import noice.app.utils.millisToMinutes
import noice.app.utils.resetIconViews
import noice.app.utils.showAtomically
import noice.app.utils.toggleByIndex
import noice.app.utils.toggleByMenuItem
import noice.app.views.PopupFilterMenu
import noice.app.views.SnackBarCustom
import noice.app.views.tooltip.NoiceSpotlightDialog
import noice.app.views.tooltip.TooltipBuilder
import noice.app.views.tooltip.TooltipObject
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.util.Base64
import java.util.Date
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@OptIn(UnstableApi::class)
@AndroidEntryPoint
class HomeActivity : BasePlayerActivity(), InAppUpdateManager.InAppUpdateListener,
    OnClickInterface<Any> {

    private lateinit var adapter: DashBoardPagerAdapter
    private lateinit var binding: ActivityHomeBinding
    private var notificationReceiver = NotificationBroadcast()
    private var tooltipDialog: NoiceSpotlightDialog? = null
    private var inAppUpdateManager: InAppUpdateManager? = null
    private val tooltips: ArrayList<TooltipObject> = ArrayList()
    private var dialog: UpdateDialog? = null
    private var isLoginEvent = false
    private var authCount = 0
    private var isForceUpdate = false
    private var forcedLogout = false
    private lateinit var coinValidationHelper: CoinValidationHelper

    private val viewModel: OnBoardingViewModel by viewModels()

    @Inject
    lateinit var billingClientLifecycle: BillingClientLifecycle

    /* bottom nav dynamic tab selected and unselected image icon */
    private var dynamicNavItemSelectedImage: BitmapDrawable? = null
    private var dynamicNavItemUnselectedImage: BitmapDrawable? = null
    private var liveItem: MenuItem? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHomeBinding.inflate(layoutInflater)

        setContentView(binding.root)
        EventBus.getDefault().register(this)

        checkFirebaseToken()
        initBindings(binding.bottomSheetContent, binding.miniView)
        observeLiveEvents()

        coinValidationHelper = CoinValidationHelper(this)

        lifecycleScope.launch {
            withResumed {
                ContextCompat.registerReceiver(
                    this@HomeActivity,
                    notificationReceiver,
                    NotificationBroadcast.INTENT_FILTER,
                    ContextCompat.RECEIVER_NOT_EXPORTED
                )

                initViews()

                getTritonAdConfig()

                fetchRemoteConfig()

                getAppConfig()
                CastManager.registerForDeviceCallback()
                CoroutineScope(Dispatchers.Main).launch {
                    setUp()

                    if (PrefUtils.isLoggedIn) {
                        getUserDetails()
                    } else {
                        MoEngageAnalytics.setAttributes(this@HomeActivity, isLoggedIn = false)
                    }

                    /* Making sure that MoEngage receives FCM token apart from NoiceFirebaseMessagingService class */
                    if (!PrefUtils.fcmToken.isNullOrEmpty()) {
                        MoEFireBaseHelper.getInstance()
                            .passPushToken(applicationContext, PrefUtils.fcmToken!!)

                        BaseApplication.firebaseAnalytics.setUserProperty(
                            "fcmToken",
                            PrefUtils.fcmToken ?: ""
                        )
                    }

                    checkLiveStreamProcessDeath()
                }
            }
        }
    }

    private fun observeLiveEvents() = lifecycleScope.launch {
        LiveTrigger.event.collect {
            when (it) {
                is LiveTrigger.Event.JoinLiveRoom -> {
                    getRoomDetail(roomId = it.roomId)
                }

                is LiveTrigger.Event.OpenSpeakerRequest -> {
                    val fragment = SpeakerRequestFragmentNew.newInstance(
                        it.room.id.orEmpty(),
                        it.room.title.orEmpty()
                    )
                    val clazz = SpeakerRequestFragmentNew::class.java.simpleName
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenAllParticipants -> {
                    val dataForListenersScreen = it.quadruple
                    val roomId = dataForListenersScreen.first
                    val roomTitle = dataForListenersScreen.second
                    val clientRole = dataForListenersScreen.third
                    val guestParticipantCount = dataForListenersScreen.fourth
                    if (roomId.isNullOrEmpty()) {
                        return@collect
                    }
                    val fragment = AllParticipantsFragmentNew.newInstance(
                        roomId,
                        roomTitle.toString(),
                        clientRole,
                        guestParticipantCount,
                        it.roomContentType
                    )
                    val clazz = AllParticipantsFragmentNew::class.java.simpleName
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenLiveBlockList -> {
                    val roomId = it.roomId as String
                    val fragment = LiveBlockListFragment.newInstance(roomId)
                    val clazz = LiveBlockListFragment::class.java.simpleName
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenManageParticipantsPage -> {
                    /*val room = it.room
                    val clientRole = it.clientRole*/

                    val fragment = AllSpeakersFragment()
                    val clazz = AllSpeakersFragment::class.java.simpleName
                    AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                        putString(FirebaseAnalytics.Param.SCREEN_NAME, "Manage_Participants_Page")
                        putString("previousScreen", it.targetPageId)
                    })
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenUserPage -> {
                    val fragment: Fragment
                    val clazz: String?
                    if (it.userId == PrefUtils.userDetails?.id) {
                        fragment = ProfileSelfFragment.newInstance(
                            source = it.targetPageId,
                            extraData = it.extraData
                        )
                        clazz = ProfileFragment::class.java.simpleName
                    } else {
                        fragment = UserProfileFragment.newInstance(
                            userId = it.userId.orEmpty(),
                            source = it.targetPageId,
                            extraData = it.extraData
                        )
                        clazz = UserProfileFragment::class.java.simpleName
                    }

                    AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                        putString(FirebaseAnalytics.Param.SCREEN_NAME, "Profile_Page")
                        putString("previousScreen", it.targetPageId)
                    })
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenPlayer -> openPlayer()

                is LiveTrigger.Event.ForceUpdate -> {
                    dialog = UpdateDialog.newInstance()
                    dialog?.showAtomically(supportFragmentManager, "UpdateDialog")
                }

                is LiveTrigger.Event.OpenLiveDetailPage -> {

                    LiveAnalytics().onRoomDetailsNavigation(it.targetPageId)

                    val roomId = it.roomId as String
                    val roomStatus = it.roomStatus

                    val fragment = RoomDetailsFragment.newInstance(
                        id = roomId,
                        roomStatus = roomStatus,
                        playContentDirectly = it.playContentDirectly,
                        extraData = it.extraData
                    )

                    val clazz = fragment::class.java.simpleName
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.OpenViewAll -> {
                    val fragment: Fragment?
                    val clazz: String?
                    if (it.pageSource == HomeFragmentNew.VERTICALS_SEGMENT) {
                        fragment =
                            LiveLihatSemuaNewFragment.newInstance(it.targetPageId, it.extraData)
                        clazz = LiveLihatSemuaNewFragment::class.java.simpleName
                    } else {
                        fragment = LiveLihatSemuaFragment.newInstance(it.targetPageId, it.extraData)
                        clazz = LiveLihatSemuaFragment::class.java.simpleName
                    }

                    AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                        putString(FirebaseAnalytics.Param.SCREEN_NAME, "Live_AllRoom")
                        putString("previousScreen", it.targetPageId)
                    })
                    onAddFragmentStack(fragment, clazz)
                }

                is LiveTrigger.Event.SoFileDownloadSucceeded -> {
                    DownloadUtils.showDownLoadDialog(supportFragmentManager)
                }

                else -> {}
            }
        }
    }

    override fun onResume() {
        super.onResume()

        isAppInBackground = false

        MoEngageAnalytics.setOrResetInAppContext(null)
        MoEInAppHelper.getInstance().showInApp(this)
        MoEInAppHelper.getInstance().showNudge(this)
        /*
        * When user clicks the AppsFlyer One-Link and the deferred deep-link data is received before HomeActivity onCreate() or
        * app is in the background and deferred deep-link data is but onNewIntent() is called for HomeActivity, in both
        * of these cases onResume() is called and if deeplink is handled with this method.
        */
        handleAppsFlyerDeeplink()

        handleNoFragmentOnTopOfEmptyFragment()
    }

    // this is a special case, when the redirection is an activity from the bottom nav configs,
    // closing the activity e.g. InAppBrowser shows an empty screen on live/middle tab. here we
    // are redirecting the user to home screen after closing the opened activity to prevent empty screen.
    private fun handleNoFragmentOnTopOfEmptyFragment() {
        if (!isAppInBackground) {
            if (binding.bottomNavigationView.selectedItemId == R.id.navigation_live) {
                getFragment()?.let { fragment ->
                    if (fragment is EmptyFragment) {
                        if (fragment.childFragmentManager.backStackEntryCount == 0) {
                            openHome()
                        }
                    }
                }
            }
        }
    }

    private fun checkFirebaseToken() {
        if (!PrefUtils.firebaseToken.isNullOrEmpty()) {
            val split = PrefUtils.firebaseToken.toString().split(".")

            if (split.size > 1) {
                val firebaseToken = PrefUtils.firebaseToken.toString().split(".")[1]

                val decodedBytes = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    Base64.getDecoder().decode(firebaseToken)
                } else {
                    decode(firebaseToken, android.util.Base64.DEFAULT)
                }
                val decodedString = String(decodedBytes)
                var exp = 0L
                if (JSONObject(decodedString).has("exp")) {
                    if (JSONObject(decodedString).get("exp") is Int) {
                        exp = (JSONObject(decodedString).get("exp") as Int).toLong() * 1000
                    } else if (JSONObject(decodedString).get("exp") is Long) {
                        exp = (JSONObject(decodedString).get("exp") as Long).toLong() * 1000
                    }
                    val date = Date(exp)
                    val currentDate = Date(System.currentTimeMillis())
                    val dif = currentDate.time - date.time
                    if (dif > 20 * 60000) {
                        getFirebaseToken()
                    } else {
                        loginWithCustomToken(PrefUtils.firebaseToken ?: "")
                    }
                } else {
                    getFirebaseToken()
                }
            } else {
                getFirebaseToken()
            }
        } else {
            getFirebaseToken()
        }
    }

    private fun loginWithCustomToken(token: String) {
        val mAuth = Firebase.auth
        mAuth.signInWithCustomToken(token).addOnCompleteListener(this) { task ->
            if (task.isSuccessful) {
                Log.w("TAG", "signInWithCustomToken:success")
            } else {
                if (authCount < 2) {
                    authCount += 1
                    loginWithCustomToken(PrefUtils.firebaseToken ?: "")
                }
                // If sign in fails, display a message to the user.
                Log.w("TAG", "signInWithCustomToken:failure", task.exception)
            }
        }
    }

    private fun initViews() {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            NetworkUtils.registerNetworkCallback(this)
        }

        setBottomNavigation()

        tooltipDialog = TooltipBuilder()
            .setListener(object : OnClickInterface<Boolean> {
                override fun dataClicked(data: Boolean, eventId: Int) {
                    if (getFragment() is NavSearchFragment) {
                        binding.bottomNavigationView.selectedItemId = R.id.navigation_home
                        tooltipDialog?.dismiss()
                        PrefUtils.isTooltipShown = true
                        return
                    }

                    if (eventId == NoiceAlertDialog.NEGATIVE_BTN_CLICK) {
                        AnalyticsUtil.sendEvent("onboarding_tooltip_skipped", Bundle().apply {
                            putString("source", "Home_Page")
                        })
                        tooltipDialog?.dismiss()
                        PrefUtils.isTooltipShown = true

                        return
                    } else if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                        AnalyticsUtil.sendEvent(
                            "onboarding_tooltip_continue_clicked",
                            Bundle().apply {
                                putString("source", "Home_Page")
                            })
                    }

                    binding.bottomNavigationView.selectedItemId = R.id.navigation_search
                }
            })
            .build()

        /* this is added when app comes screens like IntroActivity */
        //handleAppsFlyerDeepLinkData()
        Handler(Looper.getMainLooper()).postDelayed({
            if (DataController.redirectIntent != null) {
                intent = DataController.redirectIntent
                DataController.redirectIntent = null
            }
            handleIntent()
        }, 1000)

        tooltips.add(
            TooltipObject(
                findViewById(binding.bottomNavigationView.selectedItemId),
                getString(R.string.tooltip_title_home)
            )
        )

        tooltips.add(
            TooltipObject(
                findViewById(R.id.navigation_search),
                getString(R.string.tooltip_title_search)
            )
        )

        if (PrefUtils.isPartOfAbTest && !PrefUtils.isTooltipShown) {
            tooltipDialog?.show(this@HomeActivity, supportFragmentManager, tooltips)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)

        /*** Version 3.51 change note
         * This will finish current activity and start a new one (recreate).
         * It has similar behaviour with previous version (3.50) where we clear current Task and
         * create a new one, but we can't use the old way because we change HomeActivity launchMode
         * from singleTask to singleTop.
         */
        if (PrefUtils.shouldRecreateActivity) {
            PrefUtils.shouldRecreateActivity = false
            finish()
            startActivity(Intent(this, HomeActivity::class.java).apply {
                Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            })
        }

        if (DataController.redirectIntent != null) {
            this.intent = DataController.redirectIntent
            DataController.redirectIntent = null
        } else {
            this.intent = intent
        }

        handleIntent()
    }

    private fun handleIntent(appsFlyerIntent: Intent? = null) {
        val intent = appsFlyerIntent ?: this.intent

        val extraData = if (!intent?.getStringExtra(Constants.SOURCE).isNullOrEmpty()) {
            ExtraData(source = intent?.getStringExtra(Constants.SOURCE))
        } else null

        when {
            intent?.action == CANCEL_DOWNLOAD -> {
                val contentId = intent.getStringExtra(ENTITY_ID) ?: ""
                ExoplayerUtils.stopDownload(contentId)
            }

            intent?.action == PAUSE_DOWNLOAD -> {
                ExoplayerUtils.pauseDownload()
            }

            intent?.action == PLAY_DOWNLOAD -> {
                val contentId = intent.getStringExtra(ENTITY_ID) ?: ""
                Utils.runOnIOThread({
                    BaseApplication.application.getAppDb().downloadsDao()
                        .getDownloadedContent(contentId)
                }) {
                    if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                        ContentPlayRequest.Builder()
                            .contents(listOf(it.data))
                            .replaceAutomaticQueue(false)
                            .pageSource("download")
                            .play(this)
                    }
                }
            }

            intent?.action == RETRY_DOWNLOAD -> {
                val contentId = intent.getStringExtra(ENTITY_ID) ?: ""
                CoroutineScope(Dispatchers.IO).launch {
                    val content = BaseApplication.application.getAppDb().downloadsDao()
                        .getDownloadedContent(contentId)
                    if (content != null)
                        BaseApplication.application.getDownloadTracker()
                            .download(null, content, "Notification_Retry")
                }
            }

            intent?.action == OPEN_PLAYER -> {
                openPlayer()
            }

            intent?.action == OPEN_DOWNLOADS -> {
                onIndexEvent(OpenIndexEvent(OpenIndexEvent.OPEN_DOWNLOADS))
            }

            intent?.action == OpenIndexEvent.OPEN_CONTENT_PAGE -> {
                val contentId = intent.getStringExtra(ENTITY_ID) ?: ""
                if (contentId.isNotEmpty()) {
                    if (getFragment()?.childFragmentManager?.findFragmentById(R.id.homeContainer) is EpisodeDetailFragment) {
                        onBackPressed()
                    }
                    onIndexEvent(
                        OpenIndexEvent(
                            OpenIndexEvent.OPEN_CONTENT_PAGE,
                            contentId,
                            extraData = extraData
                        )
                    )
                } else {
                    onIndexEvent(OpenIndexEvent(OpenIndexEvent.OPEN_DOWNLOADS))
                }
            }

            intent?.action == OPEN_INDEX_ACTION -> {
                if (intent.hasExtra(NOTIFICATION_ID)) {
                    markNotificationRead(intent.getStringExtra(NOTIFICATION_ID))
                }

                val eventId = intent.getStringExtra(EVENT_ID)

                val entityId = if (eventId == OPEN_POST_REPLY || eventId == OPEN_POST_COMMENT) {
                    intent.getStringExtra(PARENT_ID)
                } else
                    intent.getStringExtra(ENTITY_ID)

                /* entityType, entitySubType is used for Index Screens, Vertical screen deeplinks */
                val entityType = intent.getStringExtra(ENTITY_TYPE)
                val entitySubType = intent.getStringExtra(ENTITY_SUB_TYPE)

                if (eventId == OpenIndexEvent.OPEN_IN_APP_BROWSER) {
                    val linkType = intent.getStringExtra(Constants.LINK_TYPE)

                    if (linkType == Constants.EXTERNAL) {
                        ShareUtils.redirectTo(this, entityId ?: "")
                    } else {
                        if (entitySubType == ENTITY_SUBTYPE_SUBSCRIPTION) {
                            AnalyticsBuilder.newBuilder().apply {
                                putAnalyticsKey("page name", "subscription page")
                            }.also {
                                it.send("subscription page viewed")
                            }
                        }
                        InAppBrowserActivity.startBrowser(this, entityId ?: "", "")
                    }
                } else if (eventId == OPEN_POST_COMMENT) {
                    onIndexEvent(
                        OpenIndexEvent(
                            eventId,
                            Pair(entityId, intent.getStringExtra(ENTITY_ID)),
                            extraData = extraData
                        )
                    )
                } else if (eventId == OpenIndexEvent.OPEN_LIVE_DETAIL_PAGE) {
                    LiveTrigger.onOpenLiveDetailPage(
                        roomId = entityId,
                        roomStatus = null,
                        extraData = extraData
                    )
                } else if (eventId == OPEN_PAGE) {
                    onIndexEvent(
                        OpenIndexEvent(
                            eventId,
                            Triple(entityId, entityType, entitySubType),
                            extraData = extraData
                        )
                    )
                } else if (eventId == OpenIndexEvent.OPEN_GENRE_PAGE) {
                    onIndexEvent(
                        OpenIndexEvent(
                            eventId,
                            Genre(id = entityId),
                            extraData = extraData
                        )
                    )
                } else if (!eventId.isNullOrEmpty()) {
                    onIndexEvent(OpenIndexEvent(eventId, entityId, extraData = extraData))
                }
            }

            intent != null -> {
                setDeepLink(intent, extraData)
            }
        }
    }

    private fun getRoomToken(liveRoom: LiveRoom) {
        viewModelBase.getAgoraToken(liveRoom.id ?: "").observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                liveRoom.agoraToken = it.data.data
                joinLiveRoom(liveRoom)
            } else {
                binding.errorView.hide()
            }
        }
    }

    private fun getRoomDetail(roomId: String) {
        val map = HashMap<String, String>()
        map["includeEntities"] = "[\"roomParticipants\"]"

        viewModelBase.getRoomDetails(roomId, map).observe(this) {

            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }

            if (it?.status == ResponseStatus.SUCCESS && it.data?.data != null) {
                handleLiveRoomResponse(it)
            }
            binding.errorView.hide()
        }
    }

    private fun handleLiveRoomResponse(
        resource: Resource<BaseModel<LiveRoom>>
    ) {
        if (resource.data?.data?.status == "live") {
            getRoomToken(resource.data.data!!)
        } else {
            Utils.showSnackBar(this, "Room is not live")
        }
    }

    private fun markNotificationRead(notificationId: String?) {
        if (notificationId.isNullOrEmpty())
            return

        viewModelBase.markAllRead(NotificationRequest(arrayListOf(notificationId), true))
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, @Nullable data: Intent?) {
        if (requestCode == SplashActivity.REQ_CODE_VERSION_UPDATE) {
            if (resultCode == RESULT_CANCELED) {
                if (isForceUpdate) {
                    // If the update is cancelled by the user,
                    // you can request to start the update again.
                    finish()
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun setBottomNavigation() {
        val isBottomNavConfigsAvailable = PrefUtils.bottomNavConfigsAos != null &&
                PrefUtils.bottomNavConfigsAos?.bottomNavConfigs != null &&
                PrefUtils.bottomNavConfigsAos?.bottomNavConfigs?.route != null

        setAdapter(isBottomNavConfigsAvailable)

        binding.bottomNavigationView.itemIconTintList = null

        binding.pager.isUserInputEnabled = false

        if (isBottomNavConfigsAvailable) {
            binding.bottomNavigationView.menu.clear() //clear old inflated items.
            binding.bottomNavigationView.inflateMenu(R.menu.live_navigation)

            liveItem = binding.bottomNavigationView.menu.findItem(R.id.navigation_live)
            liveItem?.title = PrefUtils.bottomNavConfigsAos?.bottomNavConfigs?.title ?: ""
            loadNavIconFromUrl(
                liveItem,
                PrefUtils.bottomNavConfigsAos?.bottomNavConfigs?.highlightedIcon ?: "",
                PrefUtils.bottomNavConfigsAos?.bottomNavConfigs?.icon ?: ""
            )
        } else {
            binding.bottomNavigationView.menu.clear() //clear old inflated items.
            binding.bottomNavigationView.inflateMenu(R.menu.navigation)
        }

        binding.bottomNavigationView.apply {
            resetIconViews()
            toggleByIndex()
        }
        binding.bottomNavigationView.setOnItemSelectedListener { menuItem ->
            binding.bottomNavigationView.toggleByMenuItem(menuItem)
            val index = getCurrentIndex(menuItem)
            when (menuItem.itemId) {
                R.id.navigation_home -> {
                    if (isLoginEvent) {
                        binding.pager.adapter = adapter
                        isLoginEvent = false
                    }

                    if (binding.pager.currentItem != index) {
                        binding.pager.setCurrentItem(index, false)
                        clearBottomNavigationStack()
                        AnalyticsUtil.sendEvent("home_clicked", "home")
                    } else {
                        getFragment().let { fragment ->
                            if (fragment is HomeFragment) {
                                fragment.handleSecondTabClick()
                            } else if (fragment is HomeFragmentNew) {
                                fragment.handleSecondTabClick()
                            }
                        }
                    }

                    updateDynamicBottomNavIcon(false)
                    true
                }

                R.id.navigation_live -> {
                    if (binding.pager.currentItem != index) {
                        binding.pager.setCurrentItem(index, false)

                        // on app launch if user hasn't reached this tab its fragment is not yet created.
                        // due to that redirection was not working as getFragment() was returning null.
                        // here first we click this tab, fragment is created and getFragment() returns it.
                        Handler(Looper.getMainLooper()).postDelayed({
                            getFragment().let { fragment ->
                                if (fragment is EmptyFragment) {
                                    fragment.navigate(PrefUtils.bottomNavConfigsAos?.bottomNavConfigs?.route)
                                }
                            }
                        }, 50)

                        clearBottomNavigationStack()
                        AnalyticsUtil.sendEventForOpenScreen(
                            ENTITY_TYPE_LIVE_STREAM,
                            "live_clicked"
                        )
                        MoEngageAnalytics.sendEvent(this@HomeActivity, "live page viewed", "", null)
                    }
                    updateDynamicBottomNavIcon(true)
                    true
                }

                R.id.navigation_search -> {
                    binding.pager.setCurrentItem(index, false)
                    clearBottomNavigationStack()
                    updateDynamicBottomNavIcon(false)
                    true
                }

                R.id.navigation_collection -> {
                    if (binding.pager.currentItem != index) {
                        binding.pager.setCurrentItem(index, false)
                        clearBottomNavigationStack()
                        AnalyticsUtil.sendEventForOpenScreen(
                            ENTITY_TYPE_COLLECTION,
                            "collection_clicked"
                        )
                        MoEngageAnalytics.sendEvent(
                            this@HomeActivity,
                            "collection page viewed",
                            "",
                            null
                        )
                    }
                    updateDynamicBottomNavIcon(false)
                    true
                }

                R.id.navigation_profile -> {
                    if (binding.pager.currentItem != index) {
                        binding.pager.setCurrentItem(index, false)
                        clearBottomNavigationStack()
                        AnalyticsUtil.sendEventForOpenScreen("", "profile_clicked")
                        if (!PrefUtils.isLoggedIn) {
                            AnalyticsUtil.sendEvent("login_page_viewed", Bundle().apply {
                                putString("source", "Profile page")
                            })
                            MoEngageAnalytics.sendEvent(
                                this@HomeActivity,
                                "login page viewed",
                                "source",
                                "Profile page"
                            )
                        }
                    }
                    updateDynamicBottomNavIcon(false)
                    true
                }

                else -> false
            }
        }
    }

    private fun getCurrentIndex(menuItem: MenuItem): Int {
        var currentPosition = 0
        for (i in 0 until binding.bottomNavigationView.menu.size()) {
            if (menuItem === binding.bottomNavigationView.menu.getItem(i)) {
                currentPosition = i
                break
            }
        }
        return currentPosition
    }

    private fun getUserDetails() {

        val map = HashMap<String, String>()
        map["listeningTime"] = "false"
        map["includeEntities"] = "artist|live"

        viewModelBase.getUserDetails(map).observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                PrefUtils.userDetails = it.data.data
                CoroutineScope(Dispatchers.IO).launch {
                    val eventAction = BaseApplication.application.getAppDb().activationDao()
                        .getEvent(PrefUtils.userDetails?.id.toString())
                    if (eventAction != null) {
                        eventAction.tslMin =
                            (PrefUtils.userDetails?.meta?.aggregations?.listeningTime?.toLong()
                                ?: 0) / 60
                        BaseApplication.application.getAppDb().activationDao().addEvent(eventAction)
                    }
                }
            }

            if (!PrefUtils.appCDNConfig?.premium_package_sku.isNullOrEmpty()) {
                var promotionType = ""

                lifecycleScope.launch {
                    billingClientLifecycle.queryProducts(
                        listOf(PrefUtils.appCDNConfig?.premium_package_sku ?: ""),
                        BillingClient.ProductType.SUBS
                    ).collect { productDetailsResult ->
                        productDetailsResult.productDetailsList?.forEach { productDetails ->
                            if (productDetails.subscriptionOfferDetails != null && productDetails.subscriptionOfferDetails?.isNotEmpty() == true) {
                                val storePriceList =
                                    productDetails.subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList

                                if (!storePriceList.isNullOrEmpty()) {
                                    val price1 = storePriceList[0] // index 0 | offer/original price
                                    var price2: ProductDetails.PricingPhase? =
                                        null // index 1 | used in case of offer available

                                    if (storePriceList.size >= 2) {
                                        price2 = storePriceList[1]
                                    }

                                    promotionType = if (price1.priceAmountMicros == 0L) {
                                        Constants.PromotionType.FREE_TRIAL
                                    } else if (price1.priceAmountMicros < (price2?.priceAmountMicros
                                            ?: 0L)
                                    ) {
                                        Constants.PromotionType.INTRODUCTORY
                                    } else {
                                        Constants.PromotionType.RECURRING
                                    }

                                    MoEngageAnalytics.setAttributes(
                                        this@HomeActivity,
                                        "promotion type",
                                        promotionType
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun getTritonAdConfig() {

        /* top episode segment experiment on home screen */
        val adsConfigVariant = ExperimentUtils.getExperiment(
            ExperimentUtils.EXPERIMENT_ADS_CONFIG,
            ExperimentUtils.VARIANT_A
        )

        val map = HashMap<String, String>()

        /* ads-config experiment */
        val adsConfigExperimentObject =
            mapOf(
                "experimentName" to ExperimentUtils.EXPERIMENT_ADS_CONFIG,
                "variant" to "${adsConfigVariant.value}"
            )

        val experimentParamArray = ArrayList<Map<String, String>>()
        experimentParamArray.add(adsConfigExperimentObject)

        map["experiment"] = Utils.stringify(experimentParamArray)

        viewModelBase.getAdConfig(map).observe(this) { res ->
            if (res?.status == ResponseStatus.SUCCESS && !res.data.isNullOrEmpty()) {
                PrefUtils.adConfig = res.data.toMutableList() as ArrayList<AdConfig>
            }
        }
    }

    private fun getFirebaseToken() {
        viewModelBase.getFirebaseToken().observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                val json = JSONObject(it.data.toString())
                if (json.has("firebaseNoiceToken")) {
                    val firebaseNoiceToken = json.get("firebaseNoiceToken").toString()
                    PrefUtils.firebaseToken = firebaseNoiceToken
                    loginWithCustomToken(firebaseNoiceToken)
                }
            }
        }
    }

    override fun onBackPressed() {
        if (bottomSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
            bottomSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        } else if (radioSheetBehavior?.state == BottomSheetBehavior.STATE_EXPANDED) {
            radioSheetBehavior?.state = BottomSheetBehavior.STATE_HIDDEN
        } else {
            getFragment()?.childFragmentManager?.fragments?.let { fragments ->
                val lastIndex = fragments.size - 1
                if (lastIndex >= 0) {
                    val currentFrag = fragments[lastIndex]
                    if (liveStreamManager.isSessionActive() && (
                                currentFrag is SpeakerRequestFragmentNew ||
                                        currentFrag is AllSpeakersFragment ||
                                        currentFrag is AllParticipantsFragmentNew)
                    ) {
                        openPlayer()
                    }
                }
            }
            val page = getFragment()
            if (page != null) {
                when (page) {
                    is HomeFragment -> {
                        page.handelBackPress()
                        updateDynamicBottomNavIcon(false)
                    }

                    is NavSearchFragment -> {
                        page.handleBackPress()
                        updateDynamicBottomNavIcon(false)
                    }

                    is LiveHomeFragment -> {
                        page.handleBackPress()
                    }

                    is EmptyFragment -> {
                        page.handleBackPress()
                    }

                    is HomeFragmentNew -> {
                        if (isLoginEvent) {
                            binding.pager.adapter = adapter
                            isLoginEvent = false
                        } else {
                            page.handelBackPress()
                        }
                    }

                    is CollectionFragment -> {
                        page.handleBackPress()
                        updateDynamicBottomNavIcon(false)
                    }

                    is ProfileFragment -> {
                        page.handleBackPress()
                    }

                    is UserProfileFragment -> {
                        page.handleBackPress()
                    }

                    is ProfileSelfFragment -> {
                        page.handleBackPress()
                        updateDynamicBottomNavIcon(false)
                    }
                }
            } else {
                super.onBackPressed()
            }
        }
    }

    fun openHome(
        homeTab: HomeFragment.HomeTabs = HomeFragment.HomeTabs.TAB_FOR_YOU,
        extraData: noice.app.model.ExtraData? = null
    ) {
        binding.bottomNavigationView.selectedItemId = R.id.navigation_home
        val frag = getFragment()
        if (frag is HomeFragment) {
            frag.selectTab(homeTab, extraData)
        }
    }

    private fun setDeepLink(intent: Intent, extraData: noice.app.model.ExtraData? = null) {
        if (intent.data != null) {
            if (intent.data.toString().contains("onelink"))
                return

            val uri = Utils.removeQueryParamsFromUrl(intent.data.toString())
            val segments = uri?.pathSegments

            val intentData = intent.data
            val utmMedium = intentData?.getQueryParameter(UTM_MEDIUM)

            if (uri?.path?.contains("noice.app") == true) {
                Utils.openActivity(this, uri.path.toString())
            } else if (!segments.isNullOrEmpty()) {
                sendUtmEvent(intent.data)

                if (segments.isNotEmpty()) {
                    if (segments.size > 1) {
                        when {
                            segments[0].equals(SEGMENT_LIVESTREAM, true) -> {
                                if (ExConfigs.ForceUpdate.LiveModule) {
                                    EventBus.getDefault()
                                        .post(EventMessage(null, Constants.FORCE_UPDATE))
                                } else {
                                    LiveTrigger.onOpenLiveDetailPage(
                                        roomId = segments[1].toString(),
                                        roomStatus = null,
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                }
                            }

                            segments[0].equals(SEGMENT_CONTENT, true) -> {
                                val t = intent.data?.getQueryParameter("t")
                                extraData?.t = try {
                                    t?.toLong()
                                } catch (e: NumberFormatException) {
                                    0
                                }

                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_CONTENT_PAGE,
                                        segments[1].toString(), targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }

                            segments[0].equals(SEGMENT_CATALOG, true) -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_CATALOG_PAGE,
                                        segments[1].toString(),
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData,
                                        playContentDirectly = utmMedium?.equals(
                                            UTM_TYPE_RADIO,
                                            true
                                        ) ?: false
                                    )
                                )
                            }

                            segments[0].equals(SEGMENT_PLAYLIST, true) -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_PLAY_LIST_PAGE,
                                        segments[1].toString(), targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }

                            segments[0].equals(SEGMENT_THEMEPAGE, true) -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_THEME_PAGE,
                                        Pair(segments[1].toString(), ""),
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }

                            segments[0].equals(SEGMENT_SEGMENT, true) -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_LIHAT_SEMUA,
                                        LihatSemuaEvent(
                                            segments[1].toString(),
                                            null,
                                            "", //view type
                                            null,
                                            source = FROM_DEEPLINK
                                        ),
                                        extraData = extraData
                                    )
                                )
                            }

                            segments[0].equals(SEGMENT_VOUCHER, true) -> {
                                //It relates to Live-Campaigns
                                if (segments.size > 1 && !segments[1].isNullOrEmpty() && segments[1] == REDEEM) {
                                    InAppBrowserActivity.startBrowser(
                                        ctx = this,
                                        url = intent.data.toString(),
                                        browserTitle = getString(R.string.tukur_voucher)
                                    )
                                }
                            }
                            segments[0].equals(SEGMENT_USER) -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_USER_PAGE_WITH_USER_NAME,
                                        segments[1].toString(),
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }
                            else -> {
                                InAppBrowserActivity.startBrowser(
                                    this,
                                    intent.data.toString(),
                                    ""
                                )
                            }
                        }
                    } else if (!segments[0].isNullOrEmpty()) {
                        when (segments[0]) {
                            SEGMENT_LIVESTREAM -> {
                                LiveTrigger.onOpenViewAll(extraData = extraData)
                            }

                            SEGMENT_COIN -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OPEN_PAGE,
                                        Triple(PAGE_COIN_PACKAGE_LIST, ENTITY_TYPE_PAGE, ""),
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }

                            SEGMENT_SUBSCRIPTION -> {
                                intent.action = OPEN_INDEX_ACTION
                                intent.putExtra(EVENT_ID, OpenIndexEvent.OPEN_IN_APP_BROWSER)
                                intent.putExtra(ENTITY_ID, intent.data.toString())
                                intent.putExtra(ENTITY_SUB_TYPE, ENTITY_SUBTYPE_SUBSCRIPTION)

                                if (checkLoginState(
                                        OPEN_INDEX_ACTION,
                                        intent.data.toString(),
                                        "deeplink"
                                    )
                                ) {
                                    handleIntent(intent)
                                }
                            }

                            SEGMENT_USER -> {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_USER_PAGE_WITH_USER_NAME,
                                        segments[1].toString(),
                                        targetPageId = FROM_DEEPLINK,
                                        extraData = extraData
                                    )
                                )
                            }
                            else -> {
                                InAppBrowserActivity.startBrowser(
                                    this,
                                    intent.data.toString(),
                                    ""
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    private fun clearBottomNavigationStack() {
        supportFragmentManager.fragments.forEachIndexed { _, fragment ->
            if (fragment is HomeFragment || fragment is HomeFragmentNew || fragment is CollectionFragment
                || fragment is ProfileFragment || fragment is NavSearchFragment || fragment is LiveHomeFragment || fragment is ProfileSelfFragment
            ) {
                clearFragmentStack(fragment)
            }
        }
    }

    private fun clearFragmentStack(fragment: Fragment?) {
        if (fragment != null && fragment.childFragmentManager.backStackEntryCount > 0) {
            fragment.childFragmentManager.popBackStackImmediate(
                null,
                FragmentManager.POP_BACK_STACK_INCLUSIVE
            )
        }
    }

    private fun getFragment(index: Int = binding.pager.currentItem) =
        supportFragmentManager.findFragmentByTag("f$index")

    override fun dataClicked(data: Any) {
        isForceUpdate = true
        dialog?.dismissAllowingStateLoss()
        checkForUpdate()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: EventMessage) {
        when (event.eventCode) {
            Constants.FORCE_UPDATE -> {
                dialog = UpdateDialog.newInstance()
                dialog?.show(supportFragmentManager, "UpdateDialog")
            }

            Constants.ERROR_RETURN -> {
                val fragment = getFragment()
                clearFragmentStack(fragment)

                if (fragment != null && fragment is NavSearchFragment) {
                    openHome()
                }
            }

            Constants.OPEN_PLAYER -> {
                openPlayer()
            }

            Constants.APPSFLYER_DEEPLINK_RECEIVED -> {
                /* this block triggers when appsflyer callback runs after home screen is visible */

                if (!isAppInBackground) {
                    handleAppsFlyerDeepLinkData()
                }
            }

            Constants.MO_IN_APP_DEEPLINK -> {
                val i = event.data as Intent
                setDeepLink(i)
            }

            Constants.HOME_REFRESH_CALLED -> {
                fetchRemoteConfig()
                getAppConfig()
            }

            Constants.STOP_AUDIO_SERVICE -> {
                stopMediaService()
            }

            else -> {
                onMessageEventBase(event)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun setPlayer(styledPlayerView: PlayerView) {
        setPlayerVideo(styledPlayerView)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onIndexEvent(event: OpenIndexEvent) {

        var fragment: Fragment? = null
        var clazz: String? = null

        when (event.eventId) {
            OpenIndexEvent.OPEN_PLAY_LIST_PAGE -> {
                fragment = PlaylistDetailFragment.newInstance(
                    event.data as String,
                    event.targetPageId,
                    event.extraData
                )
                clazz = PlaylistDetailFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_USER_PAGE -> {
                if (event.data == PrefUtils.userDetails?.id) {
                    fragment = ProfileSelfFragment.newInstance(event.targetPageId, event.extraData)
                    clazz = ProfileFragment::class.java.simpleName
                } else {
                    fragment = UserProfileFragment.newInstance(
                        event.data as String,
                        event.targetPageId,
                        event.extraData
                    )
                    clazz = UserProfileFragment::class.java.simpleName
                }

                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Profile_Page")
                    putString("previousScreen", event.targetPageId)
                })
            }

            OpenIndexEvent.OPEN_USER_PAGE_WITH_USER_NAME -> {


                if (event.data == PrefUtils.userDetails?.userName) {
                    fragment = ProfileSelfFragment.newInstance(event.targetPageId)
                    clazz = ProfileFragment::class.java.simpleName
                } else {
                    fragment = UserProfileFragment.newInstanceWithUserName(
                        event.data as String,
                        event.targetPageId
                    )
                    clazz = UserProfileFragment::class.java.simpleName
                }

                AnalyticsUtil.sendEvent("Profile_Page", Bundle().apply {
                    putString("previousScreen", event.targetPageId)
                })
            }

            OpenIndexEvent.OPEN_GENRE_PAGE -> {
                fragment = GenreDetailFragment.newInstance(
                    event.data as Genre,
                    event.targetPageId,
                    event.extraData
                )
                clazz = GenreDetailFragment::class.java.simpleName
            }

            OpenIndexEvent.MY_GENRE_PAGE -> {
                fragment = MyGenreFragment()
                clazz = MyGenreFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_SEARCH_PAGE -> {
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Search_Page")
                    putString("previousScreen", event.targetPageId)
                })
                MoEngageAnalytics.sendEvent(this, "search bar opened", "", null)

                fragment = SearchFragment()
                clazz = SearchFragment::class.java.simpleName

                if (event.targetPageId == NavSearchFragment.TARGET_PAGE_NAV_SEARCH) {
                    clazz = NavSearchFragment::class.java.simpleName
                }
            }

            OpenIndexEvent.OPEN_KARYA -> {
                val triple = event.data as Triple<*, *, *>
                if (triple.second is List<*>) {
                    val list = (triple.second as List<*>).filterIsInstance<Channel>()
                    fragment = UserKaryaFragment.newInstance(
                        userDetails = triple.first as User?,
                        list, triple.third as Boolean, extraData = event.extraData
                    )
                    clazz = UserKaryaFragment::class.java.simpleName
                }
            }

            OpenIndexEvent.OPEN_RADIO_PAGE -> {
                fragment = RadioDetailFragment.newInstance(
                    (event.data as String),
                    sourcePage = event.targetPageId,
                    playContentDirectly = event.playContentDirectly,
                    extraData = event.extraData
                )
                clazz = RadioDetailFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_RADIO_SCHEDULE_PAGE -> {
                fragment =
                    SchedulePagerFragment.newInstance(null, (event.data as String), false, null)
                clazz = SchedulePagerFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_CATALOG_PAGE -> {

                fragment = ChannelPodcastFragment.newInstance(
                    event.data as String,
                    event.targetPageId,
                    pageSource = event.pageSource,
                    playContentDirectly = event.playContentDirectly,
                    event.extraData
                )
                clazz = ChannelPodcastFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_AUDIO_SERIES_PAGE -> {
                fragment = AudioSeriesFragment.newInstance(
                    event.data as String,
                    event.targetPageId,
                    pageSource = event.pageSource,
                    playContentDirectly = event.playContentDirectly,
                    event.extraData
                )
                clazz = AudioSeriesFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_AUDIO_BOOK_PAGE -> {
                fragment = AudioBookDetailFragment.newInstance(
                    event.data as String,
                    event.targetPageId,
                    pageSource = event.pageSource,
                    playContentDirectly = event.playContentDirectly,
                    event.extraData
                )
                clazz = AudioBookDetailFragment::class.java.simpleName
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, "Audiobook_Catalog_Page")
                    putString("previousScreen", event.targetPageId)
                })
            }

            OpenIndexEvent.OPEN_CONTENT_PAGE -> {
                fragment = EpisodeDetailFragment.newInstance(
                    (event.data as String),
                    event.targetPageId,
                    pageSource = event.pageSource,
                    playContentDirectly = event.playContentDirectly,
                    event.extraData
                )
                clazz = EpisodeDetailFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_LIHAT_SEMUA -> {
                fragment = HomeLihatSemuaFragment.newInstance(
                    (event.data as LihatSemuaEvent),
                    event.targetPageId,
                    event.extraData
                )
                clazz = HomeLihatSemuaFragment::class.java.simpleName
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME, event.data.screenName ?: "")
                    putString("previousScreen", event.data.source ?: "")
                })
            }

            OpenIndexEvent.OPEN_DOWNLOADS -> {
                fragment = DownloadFragment.newInstance(event.extraData)
                clazz = DownloadFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_NOTIFICATION_CENTER -> {
                fragment = NotificationFragment.newInstance()
                clazz = NotificationFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_FOLLOWER -> {
                fragment = FollowFragment.newInstance(event.data as String, true)
                clazz = FollowFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_FOLLOWEE -> {
                fragment = FollowFragment.newInstance(event.data as String, false)
                clazz = FollowFragment::class.java.simpleName
            }

            OPEN_POST_REPLY -> {
                val commentId = event.data as String

                val replyCommentDialog = ReplyCommentDialog.newInstance(
                    commentId,
                    comment = null,
                    null,
                    source = "Comment_notification",
                    false
                )
                replyCommentDialog.show(supportFragmentManager, "Comment")
            }

            OPEN_POST_COMMENT -> {
                val episodeId = (event.data as Pair<*, *>).first as String
                val updatedCommentId = event.data.second as String

                val commentDialog = CommentDialog.newInstance(
                    episodeId,
                    null,
                    source = "Comment_notification",
                    alteredCommentId = updatedCommentId
                )
                commentDialog.show(supportFragmentManager, "Comment")
            }

            OpenIndexEvent.OPEN_CLIPS_SCREEN -> {
                val segmentId = (event.data as Pair<*, *>).first as String
                val clipId = (event.data).second as String
                ClipsActivity.start(this, segmentId, clipId, startForClipsActivity)

                return
            }
            /*OpenIndexEvent.OPEN_BLOCK_LIST_SCREEN -> {
                val dataForListenersScreen = event.data as Triple< *, *, *>
                val roomId = dataForListenersScreen.first as String

                fragment = LiveBlockListFragment.newInstance(roomId)
                clazz = LiveBlockListFragment::class.java.simpleName
            }*/
            OpenIndexEvent.OPEN_VERTICALS_SCREEN -> {
                val firstFragment = getFragment(0)
                if (firstFragment is HomeFragmentNew) {
                    when (event.data) {
                        ENTITY_TYPE_PODCAST -> {
                            fragment = PodCastFragmentNew.newInstance()
                            clazz = PodCastFragmentNew::class.java.simpleName
                        }

                        ENTITY_TYPE_RADIO -> {
                            fragment = RadioHomeFragmentNew.newInstance()
                            clazz = RadioHomeFragmentNew::class.java.simpleName
                        }

                        ENTITY_TYPE_AUDIO_BOOK -> {
                            fragment = AudioBookHomeNewFragment.newInstance()
                            clazz = AudioBookHomeNewFragment::class.java.simpleName
                        }

                        ENTITY_TYPE_AUDIO_SERIES -> {
                            fragment = AudioSeriesHomeNewFragment.newInstance()
                            clazz = AudioSeriesHomeNewFragment::class.java.simpleName
                        }

                        ENTITY_TYPE_LIVE_STREAM -> {
                            fragment = LiveLihatSemuaNewFragment.newInstance(event.pageSource)
                            clazz = LiveLihatSemuaNewFragment::class.java.simpleName
                        }
                    }
                } else {
                    //binding.pager.currentItem = 0
                    clearFragmentStack(firstFragment)
                    when (event.data) {
                        ENTITY_TYPE_PODCAST -> {
                            openHome(HomeFragment.HomeTabs.TAB_PODCAST, event.extraData)
                        }

                        ENTITY_TYPE_RADIO -> {
                            openHome(HomeFragment.HomeTabs.TAB_RADIO, event.extraData)
                        }

                        ENTITY_TYPE_AUDIO_BOOK -> {
                            openHome(HomeFragment.HomeTabs.TAB_AUDIO_BOOK, event.extraData)
                        }

                        ENTITY_TYPE_AUDIO_SERIES -> {
                            openHome(HomeFragment.HomeTabs.TAB_AUDIO_SERIES, event.extraData)
                        }

                        else -> {
                            openHome(HomeFragment.HomeTabs.TAB_PODCAST, event.extraData)
                        }
                    }
                    return
                }
            }

            OpenIndexEvent.OPEN_TOP_RANKING -> {
                fragment = TopRankingFragment.newInstance(event.data as String?, event.extraData)
                clazz = TopRankingFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_TRENDING_EPISODE -> {
                fragment = TrendingEpisodeFragment.newInstance(event.extraData)
                clazz = TrendingEpisodeFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_COIN_HISTORY -> {
                val fragments = getFragment()?.childFragmentManager?.fragments
                val lastFragment = if (fragments?.isNotEmpty().isTrue()) fragments?.last() else null
                if (lastFragment is CoinHistoryFragment) {
                    return
                }
                fragment = CoinHistoryFragment.newInstance(event.data as Boolean)
                clazz = CoinHistoryFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_TRANSACTION_DETAIL_PAGE -> {
                val pair = event.data as Pair<*, *>
                val transactionId = pair.first as String
                val transactionType = pair.second as String

                if (transactionType in Utils.handledTxnTypes) {
                    CoinTransactionDetails.show(supportFragmentManager, transactionId)
                } else {
                    CoinsUpdateDialog.show(supportFragmentManager, transactionId)
                }
                return
            }

            OpenIndexEvent.OPEN_USER_SETTINGS -> {
                PrefUtils.userDetails?.let { user ->
                    fragment = UserSettingsFragment.newInstance(user)
                    clazz = UserSettingsFragment::class.java.simpleName
                }
            }

            OPEN_PAGE -> {
                val triplet = event.data as Triple<*, *, *>
                val entityId = triplet.first as String?
                val entityType = triplet.second as String?
                val entitySubType = triplet.third as String?

                if (entityId == null || entityType == null) return

                if (entityType == Constants.ENTITY_TYPE_SEGMENT) {
                    /* This handling is for deep-linking Index/view more screen. In order to open
                     * index screen, we need two values 1) entityId and 2) viewType (for showing loader).
                     * Here in this case:
                     * entityId = some segment id (id same as on home screen segments)
                     * viewType = entitySubType (reusing this param name to handle viewType)
                     */
                    if (entitySubType == ENTITY_TYPE_LIVE_STREAM) {
                        LiveTrigger.onOpenViewAll(extraData = event.extraData)
                    } else {
                        onIndexEvent(
                            OpenIndexEvent(
                                OpenIndexEvent.OPEN_LIHAT_SEMUA,
                                LihatSemuaEvent(
                                    entityId,
                                    null,
                                    entitySubType, //view type
                                    null,
                                    source = "home"
                                ),
                                extraData = event.extraData
                            )
                        )
                    }
                } else if (entityType == ENTITY_TYPE_PAGE) {
                    /* here entityId = foryou, podcast, radio, audiobook, livestream, audioseries, trendingepisode, topranking
                     * entitySubType = (any segment id) used only in case of tabs in top ranking screen
                     *
                     * This code block is also used to handle any other page/screen to make it easy
                     * to understand that all special screen related deeplinks are handled here.
                     */
                    when (entityId) {
                        ENTITY_TYPE_PODCAST,
                        ENTITY_TYPE_RADIO,
                        ENTITY_TYPE_AUDIO_BOOK,
                        ENTITY_TYPE_AUDIO_SERIES -> {
                            // for vertical screens
                            onIndexEvent(
                                OpenIndexEvent(
                                    OpenIndexEvent.OPEN_VERTICALS_SCREEN,
                                    entityId, //it contains the entity type for deeplinking vertical screens
                                    extraData = event.extraData
                                )
                            )
                        }

                        ENTITY_TYPE_LIVE_STREAM -> {
                            LiveTrigger.onOpenViewAll(extraData = event.extraData)
                        }

                        ENTITY_TYPE_CREATE_LIVE_ROOM -> {
                            closePlayer()
                            binding.pager.setCurrentItem(2, false)
                            clearBottomNavigationStack()
                            binding.bottomNavigationView.selectedItemId = R.id.navigation_live
                            performCreateLiveRoom()
                        }

                        ENTITY_TYPE_LIVE_TAB -> {
                            closePlayer()
                            binding.pager.setCurrentItem(2, false)
                            clearBottomNavigationStack()
                            binding.bottomNavigationView.selectedItemId = R.id.navigation_live
                        }

                        ENTITY_TYPE_TOP_RANKING -> {
                            onIndexEvent(
                                OpenIndexEvent(
                                    OpenIndexEvent.OPEN_TOP_RANKING,
                                    entitySubType, //tab segment id in top charts
                                    extraData = event.extraData
                                )
                            )
                        }

                        ENTITY_TYPE_TRENDING_EPISODE -> {
                            onIndexEvent(
                                OpenIndexEvent(
                                    OpenIndexEvent.OPEN_TRENDING_EPISODE,
                                    extraData = event.extraData
                                )
                            )
                        }

                        PAGE_COIN_PACKAGE_LIST -> {
                            if (ExperimentUtils.isCoinFeatureEnabled()) {
                                val obj = JSONObject()
                                obj.put("action", OPEN_PAGE)
                                obj.put("entityType", entityType)
                                val action = Gson().toJson(obj)
                                if (checkLoginState(action, entityId, "deeplink")) {
                                    CoinTopUpActivity.start(this, "")
                                }
                            }
                        }

                        PAGE_PROFILE_TAB -> {
                            binding.bottomNavigationView.selectedItemId = R.id.navigation_profile
                        }

                        PAGE_USER_SETTINGS -> {
                            binding.bottomNavigationView.selectedItemId = R.id.navigation_profile
                            binding.pager.post {
                                onIndexEvent(OpenIndexEvent(OpenIndexEvent.OPEN_USER_SETTINGS))
                            }
                        }

                        PAGE_COIN_HISTORY, Constants.PAGE_TRANSACTION_HISTORY -> {
                            if (ExperimentUtils.isCoinFeatureEnabled()) {
                                val obj = JSONObject()
                                obj.put("action", OPEN_PAGE)
                                obj.put("entityType", entityType)
                                val action = Gson().toJson(obj)
                                if (checkLoginState(action, entityId, "deeplink")) {
                                    onIndexEvent(
                                        OpenIndexEvent(
                                            OpenIndexEvent.OPEN_COIN_HISTORY,
                                            false
                                        )
                                    )
                                }
                            }
                        }

                        PAGE_DOWNLOAD -> {
                            onIndexEvent(
                                OpenIndexEvent(
                                    OpenIndexEvent.OPEN_DOWNLOADS,
                                    extraData = event.extraData
                                )
                            )
                        }

                        PAGE_LIKED_CONTENT -> {
                            onIndexEvent(
                                OpenIndexEvent(
                                    OpenIndexEvent.OPEN_LIKED_CONTENT,
                                    extraData = event.extraData
                                )
                            )
                        }

                        else -> {
                            if (entitySubType == ENTITY_SUBTYPE_THEME_PAGE) {
                                onIndexEvent(
                                    OpenIndexEvent(
                                        OpenIndexEvent.OPEN_THEME_PAGE,
                                        Pair(entityId, ""),
                                        "",
                                        extraData = event.extraData
                                    )
                                )
                            }
                        }
                    }
                } else if (entityType == Constants.PAGE_TRANSACTION) {
                    val pair = Pair(entityId, entitySubType)
                    onIndexEvent(OpenIndexEvent(OpenIndexEvent.OPEN_TRANSACTION_DETAIL_PAGE, pair))
                } else if (entityType == ENTITY_TYPE_COLLECTION) {
                    if (entitySubType == ENTITY_SUBTYPE_THEME_PAGE) {
                        onIndexEvent(
                            OpenIndexEvent(
                                OpenIndexEvent.OPEN_THEME_PAGE,
                                Pair(entityId, ""),
                                "",
                                extraData = event.extraData
                            )
                        )
                    }
                }
            }

            OpenIndexEvent.OPEN_THEME_PAGE -> {
                val themePageData = (event.data as Pair<*, *>)
                val themPageValue = themePageData.first as String
                val title = themePageData.second as String
                val extraData = event.extraData

                fragment = ThemePageFragment.newInstance(themPageValue, title, extraData)
                clazz = ThemePageFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_SUB_DETAIL_PAGE -> {
                val ledgerId = event.data as? String
                ledgerId ?: return
                CoinTransactionDetails.show(supportFragmentManager, ledgerId)
                return
            }

            OpenIndexEvent.OPEN_LIKED_CONTENT -> {
                fragment = LikedContentFragment.newInstance(event.extraData)
                clazz = LikedContentFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_ALL_EPISODE_PAGE -> {
                val catalog = event.data as Channel

                fragment = PodCastPagerFragment.newInstance(
                    catalog.title.toString(),
                    catalog.id.toString(),
                    channelData = catalog,
                    extraData = event.extraData
                )
                clazz = PodCastPagerFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_EPISODE_LIST_PAGE -> {
                val catalog = event.data as Channel

                fragment = EpisodeListFragment.newInstance(
                    DataController.AUDIO_BOOK_PAGE,
                    PopupFilterMenu.FILTER_ASC,
                    catalog,
                    0,
                    extraData = event.extraData
                )
                clazz = EpisodeListFragment::class.java.simpleName
            }

            OpenIndexEvent.OPEN_NATIVE_DEEPLINK -> {
                val intent = event.data as Intent
                setDeepLink(intent, event.extraData)
            }
        }

        if (onAddFragmentStack(fragment, clazz)) return
    }

    private fun performCreateLiveRoom() {
        if (ExConfigs.ForceUpdate.LiveModule) {
            EventBus.getDefault().post(EventMessage(null, Constants.FORCE_UPDATE))
        } else {
            if (PrefUtils.isLiveSoFileLoaded) {
                if (!PrefUtils.isLoggedIn) {
                    val loginDialogData = ExperimentUtils.getLoginDialogData("login-on-live-chat")
                    getPlayerActivity()?.handleUserNotLoggedIn(
                        loginDialogData = loginDialogData,
                        actionName = ENTITY_TYPE_CREATE_LIVE_ROOM
                    )
                    return
                }
                if (PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true) {
                    AnalyticsUtil.sendEvent(Constants.EVENT_CREATE_ROOM_CLICKED)
                    val intent = Intent(this, CreateRoomPlusActivity::class.java)
                    intent.putExtra(EditRoomActivity.SOURCE_SCREEN, "DeepLink")
                    startActivity(intent)
                    AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                        putString(FirebaseAnalytics.Param.SCREEN_NAME, "Live_CreateRoom_Page")
                        putString("previousScreen", "DeepLink")
                    })
                } else {
                    SnackBarCustom.Builder()
                        .parentView(binding.root)
                        .text(getString(R.string.no_access_for_create))
                        .show()
                }
            } else {
                DownloadUtils.showDownLoadDialog(supportFragmentManager)
            }
        }
    }

    private fun onAddFragmentStack(
        fragment: Fragment?,
        clazz: String?
    ): Boolean {
        if (fragment == null || clazz == null)
            return true

        closePlayer()

        getFragment()?.childFragmentManager?.beginTransaction()
            ?.add(R.id.homeContainer, fragment)
            ?.addToBackStack(clazz)
            ?.commitAllowingStateLoss()
        return false
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        unregisterReceiver(notificationReceiver)
        if (BaseApplication.application.getDownloadQueue().getDownloadList().isNotEmpty()) {
            PrefUtils.showRestoreDownload = false
        }
        CastManager.unRegisterDeviceCallback()
        BaseApplication.application.getDownloadQueue().release()
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event: LoginChangeEvent) {
        isLoginEvent = true

        val action = Utils.getObjectFromJson<JSONObject>(event.actionName)
        if (action?.get("action") == OPEN_PAGE) {
            val triple = Triple(event.id, action.get("entityType"), null)
            onIndexEvent(OpenIndexEvent(OPEN_PAGE, triple))
        } else if (event.actionName == ENTITY_TYPE_CREATE_LIVE_ROOM) {
            performCreateLiveRoom()
        }
    }

    private val startForClipsActivity =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { activityResult ->
            if (activityResult.resultCode == RESULT_OK) {
                activityResult.data?.let { data ->
                    if (data.hasExtra(RESULT)) {
                        val allClips = data.getParcelableArrayListExtra<HomeContent>(RESULT)
                        EventBus.getDefault().post(
                            EventMessage(
                                allClips,
                                Constants.UPDATE_HOME_CLIPS
                            )
                        )
                    }
                }
            }
        }

    /** This is triggered when AppsFlyer callback runs after home screen is visible. */
    private fun handleAppsFlyerDeepLinkData() {
        val appsFlyerDeepLinkIntent = BaseApplication.application.appsFlyerDeepLinkIntent

        if (appsFlyerDeepLinkIntent != null) {
            handleIntent(appsFlyerDeepLinkIntent)
        }
    }

    private fun setAdapter(isBottomNavConfigsAvailable: Boolean = false) {
        adapter = DashBoardPagerAdapter(this)

        val homeFragment =
            if (PrefUtils.isPartOfAbTest) HomeFragmentNew.newInstance()
            else HomeFragment.newInstance()

        adapter.updateFragment(0, homeFragment, isBottomNavConfigsAvailable)
        binding.pager.adapter = adapter
    }

    override fun onInAppUpdateError(code: Int, error: Throwable?) {

    }

    override fun onInAppUpdateStatus(status: InAppUpdateStatus?) {
        if (status?.isDownloaded == true) {
            val rootView: View = window.decorView.findViewById(android.R.id.content)

            val snackBar = Snackbar.make(
                rootView,
                "An update has just been downloaded.",
                Snackbar.LENGTH_INDEFINITE
            )
            snackBar.setActionTextColor(ContextCompat.getColor(this, R.color.white))
            snackBar.setAction("RESTART") {
                // Triggers the completion of the update of the app for the flexible flow.
                if (isForceUpdate && forcedLogout) {
                    PrefUtils.logout(fullClear = true, redirect = false, baseContext = this)
                }
                inAppUpdateManager?.completeUpdate()
            }

            snackBar.show()
        } else if (status?.isUpdateAvailable == true && isForceUpdate) {
            inAppUpdateManager?.updateApp()
        }
    }

    /** When user comes from Deferred or Normal AppsFlyer Deeplink click and deep-link data
     * from apps-flyer was received before reaching this screen. */
    private fun handleAppsFlyerDeeplink() {
        /* this triggers when appsflyer callback is received before home screen is visible */
        val appsFlyerDeepLinkIntent = BaseApplication.application.appsFlyerDeepLinkIntent

        if (appsFlyerDeepLinkIntent != null) {
            binding.mainMotionLayout.post {
                handleIntent(appsFlyerDeepLinkIntent)
            }
        }
    }

    private fun getAppConfig() {
        val cacheInterval = if (BuildConfig.DEBUG) {
            5
        } else {
            30
        }
        val timeDiff = (System.currentTimeMillis() - (PrefUtils.appCDNConfig?.fetchTime
            ?: 0)).millisToMinutes()
        if (timeDiff < 0 || timeDiff > cacheInterval) {
            viewModel.getAppConfig().observe(this) {
                if (it?.status == ResponseStatus.SUCCESS) {
                    PrefUtils.appCDNConfig = it.data?.apply {
                        fetchTime = System.currentTimeMillis()
                    }
                    it.data?.splash_config?.imageUrl?.let {
                        downloadAndCacheImage(it, this@HomeActivity)
                    }
                }
            }
        }
    }

    private fun fetchRemoteConfig() {

        val remoteConfig = Firebase.remoteConfig
        remoteConfig.setDefaultsAsync(R.xml.remote_config)
        remoteConfig.setConfigSettingsAsync(
            FirebaseRemoteConfigSettings.Builder()
                .setFetchTimeoutInSeconds(10)
                .setMinimumFetchIntervalInSeconds(TimeUnit.MINUTES.toSeconds(5))
                .build()
        )

        remoteConfig.fetchAndActivate().addOnCompleteListener(this) {
            var currentVersionCode: Int

            if (it.isSuccessful) {
                remoteConfig.getString("app_config").let { configStr ->
                    if (configStr.isNotEmpty()) {
                        val config = Gson().fromJson(configStr, AppConfig::class.java)

                        PrefUtils.appConfig = config

                        forcedLogout = config.app_update_config?.android?.forcedLogout ?: false
                        val minVersionCode =
                            PrefUtils.appConfig?.app_update_config?.android?.minForceUpdateVersionCode
                                ?: 0
                        val maxVersionCode =
                            PrefUtils.appConfig?.app_update_config?.android?.maxForceUpdateVersionCode
                                ?: 0
                        currentVersionCode =
                            PrefUtils.appConfig?.app_update_config?.android?.current_app_version_code
                                ?: 0

                        if (minVersionCode != -1 && maxVersionCode != -1) {
                            isForceUpdate =
                                BuildConfig.VERSION_CODE in minVersionCode until maxVersionCode
                        }


                        if (isForceUpdate && BuildConfig.VERSION_CODE < currentVersionCode) {
                            checkForUpdate()
                        }
                    }
                }

                remoteConfig.getString(CONFIG_BOTTOM_NAV_CONFIGS).let { bottomNavConfigs ->
                    if (bottomNavConfigs.isNotEmpty()) {
                        val configs =
                            Gson().fromJson(bottomNavConfigs, BottomNavConfigsAos::class.java)
                        PrefUtils.bottomNavConfigsAos = configs
                    }
                }

                /* similar catalog experiment */
                remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_SIMILAR_CATALOG_AOS)
                    .let { experiment ->
                        if (experiment.isNotEmpty()) {
                            val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                            PrefUtils.firebaseVariantSimilarCatalog = variant
                        }
                    }

                /* quick-picks experiment */
                remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_QUICK_PICKS_AOS)
                    .let { experiment ->
                        if (experiment.isNotEmpty()) {
                            val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                            PrefUtils.firebaseVariantQuickPicks = variant
                        }
                    }

                /* genre based top podcast experiment */
                /* This block of code runs every time after app reopen. Fetching remote config
                *  again as on Splash Screen remote config doesn't give the expected value (variant - a/b). */
                remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_GENRE_BASED_TOP_PODCAST_AOS)
                    .let { experiment ->
                        if (experiment.isNotEmpty()) {
                            val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                            PrefUtils.firebaseVariantGenreBasedTopPodcast = variant
                        }
                    }

                /* catalog quick filters experiment */
                remoteConfig.getString(ExperimentUtils.FIREBASE_VARIANT_CATALOG_QUICK_FILTERS)
                    .let { experiment ->
                        if (experiment.isNotEmpty()) {
                            val variant = Gson().fromJson(experiment, FirebaseVariant::class.java)
                            PrefUtils.firebaseCatalogQuickFilters = variant
                        }
                    }
            }
        }
    }

    private fun checkForUpdate() {
        if (inAppUpdateManager == null) {
            inAppUpdateManager = InAppUpdateManager.Builder(this)
                .listener(this)
                .isForceUpdate(true)
                .requestCode(SplashActivity.REQ_CODE_VERSION_UPDATE)
                .build()

            inAppUpdateManager?.let { manager ->
                lifecycle.addObserver(manager)
                manager.snackBarMessage("An update has just been downloaded.")
                manager.snackBarAction("RESTART")
            }
        }
        inAppUpdateManager?.checkForUpdate()
    }

    private fun sendUtmEvent(uri: Uri?) {
        if (uri == null) {
            return
        }

        val utmMedium = uri.getQueryParameter(UTM_MEDIUM)
        val utmSource = uri.getQueryParameter(UTM_SOURCE)
        val utmCampaign = uri.getQueryParameter(UTM_CAMPAIGN)
        val referrerId = uri.getQueryParameter("referrer_id")
        val contentId =
            if (uri.pathSegments.getOrNull(0) == "content") uri.pathSegments.getOrNull(1)
            else uri.getQueryParameter("content_id")
        val catalogId =
            if (uri.pathSegments.getOrNull(0) == "catalog") uri.pathSegments.getOrNull(1)
            else uri.getQueryParameter("catalog_id")

        val pathSegment = uri.pathSegments.firstOrNull() ?: "" // e.g., "content" or "catalog"
        val contentOrCatalogId =
            uri.pathSegments.getOrNull(1) ?: "" // ID following "content" or "catalog"

        val filteredPath = Uri.Builder()
            .appendPath(pathSegment) // e.g., "content" or "catalog"
            .appendPath(contentOrCatalogId) // ID
            .apply {
                if (referrerId != null) appendQueryParameter("referrer_id", referrerId)
            }
            .build()

        AnalyticsBuilder.newBuilder().apply {
            putAnalyticsKey(UTM_MEDIUM, utmMedium ?: "")
            putAnalyticsKey(UTM_SOURCE, utmSource ?: "")
            putAnalyticsKey(UTM_CAMPAIGN, utmCampaign ?: "")
            putAnalyticsKey("referrer_id", referrerId ?: "")
            putAnalyticsKey("content_id", contentId ?: "")
            putAnalyticsKey("catalog_id", catalogId ?: "")
            putAnalyticsKey("url_link", filteredPath.toString())
        }.send("native_link_resolved")

        BaseApplication.firebaseAnalytics.setUserProperty("utmMedium", utmMedium ?: "")
        BaseApplication.firebaseAnalytics.setUserProperty("utmSource", utmSource ?: "")
        BaseApplication.firebaseAnalytics.setUserProperty("utmCampaign", utmCampaign ?: "")
        BaseApplication.firebaseAnalytics.setUserProperty("referrerId", referrerId ?: "")
    }

    private fun loadNavIconFromUrl(
        navItem: MenuItem?,
        urlSelectedImage: String,
        urlUnselectedImage: String
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            navItem?.setIconTintList(null)
            navItem?.setIconTintMode(null)
        }

        if (dynamicNavItemSelectedImage == null) {
            ImageUtils.getBitmapFromUrl(
                this,
                urlSelectedImage,
                placeHolder = R.drawable.ic_live_home
            ).observe(this) { bitmap ->
                dynamicNavItemSelectedImage = BitmapDrawable(resources, bitmap)
            }
        }

        if (dynamicNavItemUnselectedImage == null) {
            ImageUtils.getBitmapFromUrl(
                this,
                urlUnselectedImage,
                placeHolder = R.drawable.ic_live_home
            ).observe(this) { bitmap ->
                dynamicNavItemUnselectedImage = BitmapDrawable(resources, bitmap)
                navItem?.icon = dynamicNavItemUnselectedImage
            }
        }
    }

    private fun updateDynamicBottomNavIcon(isSelected: Boolean) {
        liveItem?.icon = if (isSelected) {
            dynamicNavItemSelectedImage
        } else {
            dynamicNavItemUnselectedImage
        }
    }

    companion object {
        private const val UTM_MEDIUM = "utm_medium"
        private const val UTM_SOURCE = "utm_source"
        private const val UTM_CAMPAIGN = "utm_campaign"
        private const val UTM_TYPE_RADIO = "radio"
        private const val FROM_DEEPLINK = "Deeplink"

        // Constant for segment[0] value
        private const val SEGMENT_CAR_LOGIN = "car-login"
        private const val SEGMENT_LIVESTREAM = "livestream"
        private const val SEGMENT_CONTENT = "content"
        private const val SEGMENT_CATALOG = "catalog"
        private const val SEGMENT_PLAYLIST = "playlist"
        private const val SEGMENT_THEMEPAGE = "themepage"
        private const val SEGMENT_SEGMENT = "segment" // Refers to "segment" path itself
        private const val SEGMENT_VOUCHER = "voucher"
        private const val SEGMENT_COIN = "coin"
        private const val SEGMENT_SUBSCRIPTION = "subscription"
        private const val SEGMENT_TV_LOGIN = "tv-login"
        private const val SEGMENT_USER = "u"

        // Constant for segment[1] value
        private const val REDEEM = "redeem"
    }
}