package noice.app.modules.dashboard.repository

import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.Job
import noice.app.BaseApplication
import noice.app.R
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.model.Restore
import noice.app.modules.dashboard.model.ReviewApp
import noice.app.modules.media.model.*
import noice.app.modules.onboarding.models.ShareRequest
import noice.app.modules.radio.model.Radio
import noice.app.rest.*
import noice.app.rest.apiinterfaces.CDNConfigApiInterface
import noice.app.rest.apiinterfaces.CatalogApiInterface
import noice.app.rest.apiinterfaces.PlayListApiInterface
import noice.app.utils.DateUtils
import noice.app.utils.NetworkUtils
import noice.app.utils.PrefUtils
import okhttp3.MultipartBody
import okhttp3.RequestBody
import javax.inject.Inject

class DashboardApiRepository @Inject constructor(
    private val playListApiInterface: PlayListApiInterface,
    private val catalogApiInterface: CatalogApiInterface,
    private var configApiInterface: CDNConfigApiInterface,
) : BaseRepository() {

    private var actionJob : Job? = null

    fun getHomeSegment(map: HashMap<String, String>, type: String, isRefreshing : Boolean = false) = networkBoundResource(
        cachingConstant = type,
        conditionsForFetchingCache = {
            !isRefreshing
        },
        apiPage = map["offset"]?.toInt(),
        apiCall = {
            NetworkRequests.getHomeSegment(map)
        }
    )

    fun getHomeBanner(target: String, type: String) = networkBoundResource(
        cachingConstant = type,
        apiCall = {
            NetworkRequests.getHomeBanner(target)
        }
    )

    fun getCommunityDetail(map: HashMap<String, String>): MutableLiveData<Resource<Community>> {

        val mutableData = MutableLiveData<Resource<Community>>()

        BaseApplication.doServerCall({ NetworkRequests.getCommunityDetail(map) },
            object : ServerInterface<BaseModel<Community>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Community>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getLiveRadio(): MutableLiveData<Resource<List<Radio>>> {

        val mutableData = MutableLiveData<Resource<List<Radio>>>()

        BaseApplication.doServerCall({ catalogApiInterface.getLiveRadio(PrefUtils.token) },
            object : ServerInterface<BaseModel<List<Radio>>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<List<Radio>>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun createPlayList(
        filePart: MultipartBody.Part?,
        map: HashMap<String, RequestBody>
    ): MutableLiveData<Resource<Playlist>> {

        val mutableData = MutableLiveData<Resource<Playlist>>()

        BaseApplication.doServerCall({ playListApiInterface.createPlayList(PrefUtils.token, filePart, map) },
            object : ServerInterface<BaseModel<Playlist>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Playlist>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun editPlayList(
        filePart: MultipartBody.Part?,
        map: HashMap<String, RequestBody>,
        id: String
    ): MutableLiveData<Resource<Playlist>> {

        val mutableData = MutableLiveData<Resource<Playlist>>()

        BaseApplication.doServerCall({ playListApiInterface.editPlayList(PrefUtils.token, filePart, map, id) },
            object : ServerInterface<BaseModel<Playlist>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Playlist>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getPlaylists(): MutableLiveData<Resource<List<Playlist>>> {

        val map = HashMap<String, String>()
        map["page"] = "1"
        map["limit"] = "20"

        val mutableData = MutableLiveData<Resource<List<Playlist>>>()

        BaseApplication.doServerCall({ playListApiInterface.getPlaylists(PrefUtils.token, map) },
            object : ServerInterface<BaseModel<List<Playlist>>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<List<Playlist>>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun addContentInPlayList(request: PlaylistContentRequest, playlistId: String): MutableLiveData<Resource<AddedContent>> {

        val mutableData = MutableLiveData<Resource<AddedContent>>()

        BaseApplication.doServerCall({ playListApiInterface.addContentInPlayList(PrefUtils.token, request, playlistId) },
            object : ServerInterface<BaseModel<AddedContent>> {
                override fun onCustomError(e: ApiError) {
                    val msg = if (e.code == 605) {
                        BaseApplication.getBaseAppContext()
                            .getString(R.string.song_already_exist_in_this_playlist)
                    } else {
                        e.message
                    }
                    mutableData.value = Resource.error(msg)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<AddedContent>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun performAction(mediaAction: MediaAction): MutableLiveData<Resource<Community>> {

        val mutableData = MutableLiveData<Resource<Community>>()
        actionJob?.cancel()
        actionJob = BaseApplication.doServerCall({ NetworkRequests.performAction(mediaAction) },
            object : ServerInterface<BaseModel<Community>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Community>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun shareContent(mediaAction: ShareRequest,isRadioLive:Boolean = false,endTime:String?): MutableLiveData<Resource<Share>> {

        val mutableData = MutableLiveData<Resource<Share>>()

        if (isRadioLive && !endTime.isNullOrEmpty()){
            val currentTime = DateUtils.getLocalTimeFromUTC(endTime)
            mediaAction.endTime = currentTime.uppercase()
        }
        BaseApplication.doServerCall({ NetworkRequests.shareContent(mediaAction) },
            object : ServerInterface<BaseModel<Share>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<Share>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getLihatSemuaHome(id: String, page: Int, limit: Int, fetchCacheWhenNoInternet : Boolean) = networkBoundResource(
        cachingConstant = "lihat_semua_$id",
        apiPage = page,
        conditionsForFetchingCache = {
            if (fetchCacheWhenNoInternet) {
                !NetworkUtils.isNetworkConnected(BaseApplication.getBaseAppContext())
            } else {
                true
            }
        },
        apiCall = {
            NetworkRequests.getLihatSemuaHome(id, page, limit)
        }
    )

    fun getPageFilters(page: String, isRefreshing : Boolean = false) = networkBoundFlowResource(
        cachingConstant = "page_filter_$page",
        conditionsForFetchingCache = {
            !isRefreshing
        },
        apiCall = {
            NetworkRequests.getPageFilters(page)
        }
    )

    fun shouldReviewApp(): MutableLiveData<Resource<ReviewApp>> {

        val mutableData = MutableLiveData<Resource<ReviewApp>>()

        BaseApplication.doServerCall({ NetworkRequests.shouldReviewApp() },
            object : ServerInterface<BaseModel<ReviewApp>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<ReviewApp>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getDownload(map: HashMap<String, String>): MutableLiveData<Resource<List<Restore>>> {

        val mutableData = MutableLiveData<Resource<List<Restore>>>()

        BaseApplication.doServerCall({ NetworkRequests.getDownload(map) },
            object : ServerInterface<BaseModel<List<Restore>>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                }

                override fun onSuccess(data: BaseModel<List<Restore>>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                }
            })

        return mutableData
    }

    fun getPageName(pageValue: String) = networkBoundResource(
        cachingConstant = null,
        apiCall = {
            NetworkRequests.getPageName(pageValue)
        }
    )

    fun getQuickFilters() = networkBoundResource(
        apiCall = {
            configApiInterface.getQuickFilters()
        }
    )
}