package noice.app.modules.dashboard.adapter

import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import noice.app.R
import noice.app.data.DataController
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.home.fragment.ForYouFragment
import noice.app.modules.dashboard.model.HomeContent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.viewmodel.RealtimeViewModel
import noice.app.modules.media.model.Community
import noice.app.observers.GlobalObservers
import noice.app.player.models.PlayerEvent
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.STATUS_LIVE
import noice.app.utils.Constants.Companion.TOP_RANKING_PODCAST
import noice.app.utils.Constants.Companion.UPDATE_HOME_CLIPS
import noice.app.utils.PermissionUtils
import noice.app.utils.Utils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.msToSeconds
import noice.app.utils.Utils.unwrap
import noice.app.utils.extensions.printLog
import noice.app.views.ContentVerticalsView
import noice.app.views.GenreView
import noice.app.views.HtmlTextView
import noice.app.views.ImageBannerView
import noice.app.views.ListSegmentView
import noice.app.views.LiveSegment
import noice.app.views.MoEngageView
import noice.app.views.QuickPicksView
import noice.app.views.ThemePageView
import noice.app.views.VideoView
import noice.app.views.homesegmentviews.ArtistSegmentView
import noice.app.views.homesegmentviews.ClipHorizontalSegment
import noice.app.views.homesegmentviews.ContentHighlightSegment
import noice.app.views.homesegmentviews.ContentHorizontalSegment
import noice.app.views.homesegmentviews.ContentVerticalSegment
import noice.app.views.homesegmentviews.DefaultSegmentView
import noice.app.views.homesegmentviews.RadioSegmentView
import noice.app.views.homesegmentviews.RecentlyPlayedSegment
import noice.app.views.homesegmentviews.RecommendationSegment
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class HomeInnerAdapter(
    private val segmentId: String,
    private val type: String,
    private val entityType: String? = "",
    private val pageSource: String = "",
    private val orientation: String,
    private val previousScreen: String,
    private val segmentPosition: Int,
    private val permissionUtils: PermissionUtils? = null,
    private val screenTitle: String? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val TYPE_RECENTLY_PLAYED = "recently_played"
        private const val TYPE_DEFAULT = "default"
        const val TYPE_CONTENT_HORIZONTAL = "content_horizontal"
        const val TYPE_CONTENT_HIGHLIGHT = "content_highlight"
        private const val TYPE_RADIO = "radio"
        private const val TYPE_ARTIST = "artist"
        private const val TYPE_AUDIO_BOOK = "audiobook"
        const val TYPE_CONTENT_VERTICAL = "content_vertical"
        const val TYPE_NOICE_LIVE = "livestream"
        const val TYPE_CLIP = "content_clip"
        const val TYPE_GENRE = "genre"
        const val TYPE_COLLECTION = "collection"
        const val TYPE_RECOMMENDATION = "content_recommendation"
        const val TYPE_PERSONAL_RECOMMENDATION = "personalised_recommendation"
        const val TYPE_BANNER_IMAGE = "bannerimage"
        const val TYPE_CUSTOM_TEXT = "custom_text_content"
        const val TYPE_CUSTOM_VIDEO = "custom_video_content"
        const val TYPE_THEME_PAGE = "themepage"
        const val TYPE_QUICK_PICKS = "quick_picks"
        const val TYPE_LIST_VIEW = "list_view"
        const val TYPE_MOENGAGE_CARDS = "moengage_cards"
    }

    private var realtimeViewModel: RealtimeViewModel? = null
    private val dataList: ArrayList<HomeContent> = ArrayList()
    private var playerUIEventJob: Job? = null
    private var liveEventsJob: Job? = null

    private fun startPlayerUIEventObserver() {
        if (isPlayableSegment()) {
            playerUIEventJob?.cancel()
            playerUIEventJob = CoroutineScope(Dispatchers.Main).launch {
                GlobalObservers.playerEventObserver
                    .subscribeMusicButtonEvents {
                        handleUIEvents(it)
                    }
            }
        }
    }

    private fun handleUIEvents(playerEvent: PlayerEvent) {
        dataList
            .asSequence()
            .filter { content ->
                (content.isPlaying == true || content.showLoader)
                        && ((content.entityType == Constants.ENTITY_TYPE_CONTENT && content.id != playerEvent.exoData?.id)
                        || (content.entityType == Constants.ENTITY_TYPE_CATALOG && content.id != playerEvent.exoData?.catalogId))
            }.forEach { content ->
                content.isPlaying = false
                content.showLoader = false

                val index = dataList.indexOf(content)
                if (index != -1) {
                    notifyItemChanged(index)
                }
            }

        val content = dataList.find { content ->
            content.id == playerEvent.exoData?.id || content.id == playerEvent.exoData?.catalogId
        }
        if (content != null) {
            if (playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER || playerEvent.event == PlayerEvent.PAUSE_END_LOADER) {
                content.isPlaying = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER
                content.showLoader = playerEvent.event == PlayerEvent.PLAY_SHOW_LOADER

                if (playerEvent.event == PlayerEvent.PAUSE_END_LOADER && playerEvent.currentContentPositionMs != null) {
                    content.meta?.timeElapsed = playerEvent.currentContentPositionMs.msToSeconds().toLong()
                }
            } else if (playerEvent.event == PlayerEvent.SHOW_LOADER || playerEvent.event == PlayerEvent.END_LOADER) {
                content.showLoader = playerEvent.event == PlayerEvent.SHOW_LOADER
            }
            val index = dataList.indexOf(content)
            if (index != -1) {
                notifyItemChanged(index)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val extraData = ExtraData(segmentId = segmentId, segmentName = pageSource, segmentPosition = segmentPosition + 1)
        return when (type) {
            TYPE_RECENTLY_PLAYED -> {
                RecentlyPlayedSegment(parent.context.unwrap(),entityType,pageSource,"Home_$previousScreen")
                .viewHolder
            }
            TYPE_DEFAULT -> {
                DefaultSegmentView(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    shouldHandleAudioBook(true)
                    setExtraData(extraData)
                    handleOrientation(orientation)
                }.viewHolder
            }
            TYPE_AUDIO_BOOK -> {
                DefaultSegmentView(parent.context, entityType, pageSource,"Home_$previousScreen").apply {
                    shouldHandleAudioBook(false)
                    makeImageSquare(false)
                    setExtraData(extraData)
                    handleOrientation(orientation)
                }.viewHolder
            }
            TYPE_CONTENT_HORIZONTAL -> {
                ContentHorizontalSegment(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    enlargeItemMedium()
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_CONTENT_HIGHLIGHT -> {
                ContentHighlightSegment(parent.context,entityType,pageSource,"Home_$previousScreen")
                .viewHolder
            }
            TYPE_RADIO -> {
                RadioSegmentView(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    if(orientation == "vertical") {
                        setWidth()
                        setExtraData(extraData)
                    }
                }.viewHolder
            }
            TYPE_ARTIST -> {
                ArtistSegmentView(parent.context,entityType,pageSource,"Home_$previousScreen")
                .viewHolder
            }
            TYPE_CONTENT_VERTICAL -> {
                ContentVerticalSegment(parent.context,entityType,pageSource,"Home_$previousScreen")
                .viewHolder
            }
            TYPE_CLIP -> {
                ClipHorizontalSegment(parent.context, entityType, pageSource, "Home_$previousScreen").apply {
                    handleOrientation(orientation)
                }.viewHolder
            }
            TYPE_NOICE_LIVE -> {
                LiveSegment(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    if (previousScreen == "LiveLihatSemua") {
                        setWidth()
                    }
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_GENRE -> {
                GenreView(parent.context, pageSource, previousScreen).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_COLLECTION -> {
                if (previousScreen.equals(ForYouFragment.ANALYTICS_TAG, true)) {
                    /* This condition is kept for handling home screen case only. */
                    // will deprecate it when max user base is moved to dedicated implementation.
                    ThemePageView(parent.context, previousScreen).apply {
                        setExtraData(extraData)
                    }.viewHolder
                } else {
                    GenreView(parent.context, pageSource, previousScreen).apply {
                        setExtraData(extraData)
                    }.viewHolder
                }
            }
            TYPE_RECOMMENDATION -> {
                RecommendationSegment(parent.context).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_BANNER_IMAGE -> {
                ImageBannerView(parent.context).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_CUSTOM_TEXT -> {
                HtmlTextView(parent.context).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_CUSTOM_VIDEO -> {
                VideoView(parent.context).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TOP_RANKING_PODCAST -> {
                DefaultSegmentView(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    shouldHandleAudioBook(true)
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_THEME_PAGE -> {
                ThemePageView(parent.context, previousScreen).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_QUICK_PICKS -> {
                QuickPicksView(parent.context, previousScreen).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_LIST_VIEW -> {
                ListSegmentView(parent.context, previousScreen).apply {
                    setExtraData(extraData)
                }.viewHolder
            }
            TYPE_MOENGAGE_CARDS -> {
                if (dataList.size > 1) {
                    MoEngageView(parent.context).apply {
                        setExtraData(extraData)
                    }.viewHolder
                } else {
                    ImageBannerView(parent.context).apply {
                        setExtraData(extraData)
                    }.viewHolder
                }
            }
            else -> {
                DefaultSegmentView(parent.context,entityType,pageSource,"Home_$previousScreen").apply {
                    shouldHandleAudioBook(true)
                    setExtraData(extraData)
                    handleOrientation(orientation)
                }.viewHolder
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return dataList[position].id.hashCode().toLong()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is RecentlyPlayedSegment.ViewHolder -> {
                (holder.itemView as RecentlyPlayedSegment).let { recentlyPlayedSegment ->
                    recentlyPlayedSegment.viewHolder = holder
                    recentlyPlayedSegment.setData(dataList[position])
                }

                holder.playButtonLayout.setOnClickListener {

                    if (DataController.isAdPlaying) {
                        Utils.showSnackBar(it.context, it.context.getString(R.string.playback_will_resume_after_ad))
                        return@setOnClickListener
                    }

                    val data = dataList[position]

                    if (DataController.playingFromWhere == DataController.RECENTLY_PLAYED && DataController.playerContentId == dataList[position].id) {
                        if (it.context.getPlayerActivity()?.isPlayWhenReady() == true) {
                            ContentPlayRequest.Builder()
                                .playWhenReady(it.context, false)
                        } else {
                            ContentPlayRequest.Builder()
                                .playWhenReady(it.context, true)
                        }

                        return@setOnClickListener
                    }

                    val extraData = ExtraData(segmentId = segmentId, segmentName = pageSource, segmentPosition = segmentPosition + 1)

                    if (dataList[position].entityType == Constants.ENTITY_TYPE_CATALOG) {
                        val entitySubType = data.catalog?.type ?: data.entitySubType
                        ClickHandler.redirect(data.id.toString(), data.entityType.toString(), entitySubType.toString(), "Home_$previousScreen", extraData)
                    } else {
                        ContentPlayRequest.Builder()
                            .homeContents(listOf(data))
                            .pageSource(pageSource)
                            .queueTitle("Lanjut dengerin")
                            .extraData(extraData)
                            .contentFetchLimit(20)
                            .addCatalogContents(true)
                            .play(it.context)

                        DataController.setCurrentlyPlaying(DataController.RECENTLY_PLAYED, DataController.RECENTLY_PLAYED)

                        dataList[position].isPlaying = true
                        dataList[position].showLoader = true
                        notifyItemChanged(position)
                    }
                }

                holder.relativeEqualizer.setOnClickListener {
                    if (DataController.isAdPlaying) {
                        Utils.showSnackBar(it.context, it.context.getString(R.string.playback_will_resume_after_ad))
                        return@setOnClickListener
                    }

                    if (DataController.playerContentId == dataList[position].id) {
                        if (it.context.getPlayerActivity()?.isPlayWhenReady() == true) {
                            ContentPlayRequest.Builder()
                                .playWhenReady(it.context, false)
                        }
                    }
                }
            }

            is DefaultSegmentView.ViewHolder -> {
                (holder.itemView as DefaultSegmentView).let { defaultSegmentView ->
                    defaultSegmentView.viewHolder = holder

                    if (type == TYPE_PERSONAL_RECOMMENDATION) {
                        defaultSegmentView.setEntityType(dataList[position].entityType)
                        defaultSegmentView.setViewType(TYPE_PERSONAL_RECOMMENDATION)
                    } else if (type == TOP_RANKING_PODCAST) {
                        defaultSegmentView.setViewType(TOP_RANKING_PODCAST)
                    }

                    defaultSegmentView.setData(dataList[position],pageSource, position)
                }
            }
            is ContentHorizontalSegment.ViewHolder -> {
                (holder.itemView as ContentHorizontalSegment).let { contentHorizontalSegment ->
                    contentHorizontalSegment.viewHolder = holder
                    contentHorizontalSegment.setData(dataList[position])
                }
            }
            is ContentHighlightSegment.ViewHolder -> {
                (holder.itemView as ContentHighlightSegment).let { contentHighlightSegment ->
                    contentHighlightSegment.viewHolder = holder
                    contentHighlightSegment.setData(dataList[position])
                }

                holder.playButton.setOnClickListener {
                    val ctx = it.context.unwrap()

                    if (DataController.isAdPlaying) {
                        Utils.showSnackBar(ctx, ctx.getString(R.string.playback_will_resume_after_ad))
                        return@setOnClickListener
                    }

                    if (DataController.playerContentId == dataList[position].id) {
                        if (ctx.getPlayerActivity()?.isPlayWhenReady() == true) {
                            ContentPlayRequest.Builder()
                                .playWhenReady(ctx, false)
                        } else {
                            ContentPlayRequest.Builder()
                                .playWhenReady(ctx, true)
                        }
                    }

                    val list = Utils.trimList(ctx, ArrayList(dataList.subList(position,dataList.size)))

                    if (list.isEmpty()) {
                        return@setOnClickListener
                    }

                    val extraData = ExtraData(segmentId = segmentId, segmentName = pageSource, segmentPosition = segmentPosition + 1)
                    val data = dataList[position]

                    if (data.entityType == Constants.ENTITY_TYPE_CATALOG) {
                        val entitySubType = data.catalog?.type ?: data.entitySubType
                        ClickHandler.redirect(data.id.toString(), data.entityType.toString(), entitySubType.toString(), "Home_$previousScreen", extraData)
                    } else {
                        ContentPlayRequest.Builder()
                            .homeContents(list)
                            .pageSource(pageSource)
                            .queueTitle("Highlighted Content")
                            .extraData(extraData)
                            .contentFetchLimit(20)
                            .addCatalogContents(true)
                            .play(ctx)

                        dataList[position].isPlaying = true
                        notifyItemChanged(position)
                    }
                }
            }
            is RadioSegmentView.ViewHolder -> {
                (holder.itemView as RadioSegmentView).let { radioSegmentView ->
                    radioSegmentView.viewHolder = holder
                    radioSegmentView.setData(dataList[position])
                }
            }
            is ArtistSegmentView.ViewHolder -> {
                (holder.itemView as ArtistSegmentView).let { artistSegmentView ->
                    artistSegmentView.viewHolder = holder
                    artistSegmentView.setData(dataList[position])
                }
            }
            is ContentVerticalSegment.ViewHolder -> {
                (holder.itemView as ContentVerticalSegment).let { contentVerticalSegment ->
                    contentVerticalSegment.viewHolder = holder
                    contentVerticalSegment.setData(dataList[position], "Home_$previousScreen")
                }
            }
            is ClipHorizontalSegment.ViewHolder -> {
                (holder.itemView as ClipHorizontalSegment).let { clipHorizontalSegment ->
                    clipHorizontalSegment.viewHolder = holder
                    clipHorizontalSegment.setData(dataList[position], segmentId)
                }
            }
            is LiveSegment.ViewHolder -> {
                (holder.itemView as LiveSegment).let { noiceLiveSegment ->
                    noiceLiveSegment.viewHolder = holder
                    noiceLiveSegment.setData(dataList[position], pageSource, permissionUtils)
                }
            }
            is GenreView.ViewHolder -> {
                (holder.itemView as GenreView).let { genreView ->
                    genreView.viewHolder = holder
                    genreView.setData(dataList[position], entityType)
                }
            }
            is ContentVerticalsView.ViewHolder -> {
                (holder.itemView as ContentVerticalsView).let { contentVerticalsView ->
                    contentVerticalsView.viewHolder = holder
                    contentVerticalsView.setData(dataList[position])
                }
            }
            is RecommendationSegment.ViewHolder -> {
                (holder.itemView as RecommendationSegment).let { recommendationSegment ->
                    recommendationSegment.viewHolder = holder
                    recommendationSegment.shouldHandleAudioBook(true)
                    recommendationSegment.setData(dataList[position], segmentPosition, previousScreen)
                }
            }
            is ThemePageView.ViewHolder -> {
                (holder.itemView as ThemePageView).let { themeTileView ->
                    themeTileView.viewHolder = holder
                    themeTileView.setData(dataList[position], position)
                }
            }
            is ImageBannerView.ViewHolder -> {
                (holder.itemView as ImageBannerView).let { bannerImageView ->
                    bannerImageView.viewHolder = holder
                    bannerImageView.setData(dataList[position], screenTitle, type)
                }
            }
            is HtmlTextView.ViewHolder -> {
                (holder.itemView as HtmlTextView).let { htmlTextView ->
                    htmlTextView.viewHolder = holder
                    htmlTextView.setData(dataList[position], screenTitle)
                }
            }
            is VideoView.ViewHolder -> {
                (holder.itemView as VideoView).let { videoView ->
                    videoView.viewHolder = holder
                    videoView.setData(dataList[position], screenTitle)
                }
            }
            is QuickPicksView.ViewHolder -> {
                (holder.itemView as QuickPicksView).let { quickPicksView ->
                    quickPicksView.viewHolder = holder
                    quickPicksView.setData(dataList[position], position)
                }
            }
            is ListSegmentView.ViewHolder -> {
                (holder.itemView as ListSegmentView).let { listSegmentView ->
                    listSegmentView.viewHolder = holder
                    listSegmentView.setData(dataList[position], previousScreen)
                }
            }
            is MoEngageView.ViewHolder -> {
                (holder.itemView as MoEngageView).let { moEngageView ->
                    moEngageView.viewHolder = holder
                    moEngageView.setData(dataList[position])
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: EventMessage){
        if (event.eventCode == UPDATE_HOME_CLIPS) {
            if (type == TYPE_CLIP) {
                val updatedClips = (event.data as ArrayList<*>).filterIsInstance<HomeContent>()
                dataList.forEach { content ->
                    val index = updatedClips.indexOf(content)
                    if (index != -1) {
                        content.meta?.markedAsPlayed = updatedClips[index].meta?.markedAsPlayed
                    }
                }
                dataList.sortWith(compareBy { it.meta?.markedAsPlayed == true})

                notifyDataSetChanged()
            }
        }
    }

    private fun observeLiveTrigger() {
        if (liveEventsJob?.isActive == true) {
            return
        }
        liveEventsJob  = CoroutineScope(Dispatchers.Main).launch {
            LiveTrigger.event.collect {
                when (it) {
                    is LiveTrigger.Event.RecordingDeleted -> {
                        val roomId = it.roomId.orEmpty()
                        val content = HomeContent(roomId)
                        val index = dataList.indexOf(content)
                        if (index > -1){
                            dataList.removeAt(index)
                            notifyItemRemoved(index)
                        }
                    }
                    else -> {}
                }
            }
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)

        startPlayerUIEventObserver()

        if (shouldRegisterEventBus()) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }

            observeLiveTrigger()
        }

        realtimeViewModel = ViewModelProvider(recyclerView.context.unwrap() as AppCompatActivity)[RealtimeViewModel::class.java]
        val isOngoingLiveSection = dataList.find { it.status == STATUS_LIVE } != null
        if (type == TYPE_NOICE_LIVE && isOngoingLiveSection && realtimeViewModel?.liveListenerCount == null) {
            realtimeViewModel?.onObserveAllRoomParticipantsCount { roomId, liveParticipantCount ->
                val index = dataList.indexOf(HomeContent(roomId))
                if (index != -1) {
                    if (dataList[index].meta == null) {
                        dataList[index].meta = Community()
                    }
                    if (dataList[index].meta?.participantCount != liveParticipantCount) {
                        dataList[index].meta?.participantCount = liveParticipantCount
                        notifyItemChanged(index)
                    }
                } else {
                    printLog("Key:$roomId"+ "index -1 ")
                }
            }
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)

        playerUIEventJob?.cancel()
        playerUIEventJob = null

        if(EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().unregister(this)
        val isOngoingLiveSection = dataList.find { it.status == STATUS_LIVE } != null
        if (isOngoingLiveSection) {
            realtimeViewModel?.liveListenerCount?.remove()
            realtimeViewModel?.liveListenerCount = null
        }

        liveEventsJob?.cancel()
        liveEventsJob = null
    }

    private fun shouldRegisterEventBus() = type == TYPE_RECOMMENDATION || type == TYPE_CLIP || type == TYPE_NOICE_LIVE

    /* this method ads a listener to update the ui when play/pause happens. */
    private fun isPlayableSegment() = type == TYPE_RECENTLY_PLAYED || type == TYPE_CONTENT_HIGHLIGHT || type == TYPE_RECOMMENDATION || type == TYPE_CONTENT_HORIZONTAL || type == TYPE_QUICK_PICKS

    override fun getItemCount(): Int {
        return dataList.size
    }

    fun setData(dataList: ArrayList<HomeContent>) {
        this.dataList.clear()
        this.dataList.addAll(dataList)
    }
}