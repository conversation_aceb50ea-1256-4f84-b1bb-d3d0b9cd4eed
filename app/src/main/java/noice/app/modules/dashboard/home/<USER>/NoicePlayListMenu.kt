package noice.app.modules.dashboard.home.fragment

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.google.android.exoplayer2.offline.Download
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import noice.app.R
import noice.app.databinding.FragmentNoicePlaylistMenuBinding
import noice.app.exoplayer.BasePlayerActivity
import noice.app.listner.CustomClickListener
import noice.app.listner.OnClickInterface
import noice.app.model.eventbus.EventMessage
import noice.app.modules.dashboard.activity.HomeActivity
import noice.app.modules.dashboard.collection.fragment.NoiceAlertDialog
import noice.app.modules.dashboard.home.activity.EditPlaylistActivity
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.modules.media.model.Playlist
import noice.app.player.playrequests.ContentPlayRequest
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

@AndroidEntryPoint
class NoicePlayListMenu : BottomSheetDialogFragment() {

    companion object {
        private const val PLAYLIST_DATA = "PLAYLIST_DATA"
        private const val PLAYLIST_DOWNLOAD_STATE = "PLAYLIST_DOWNLOAD_STATE"

        fun newInstance(playlist: Playlist, playlistDownloadState : Int) = NoicePlayListMenu().apply {
            arguments = Bundle().apply {
                putParcelable(PLAYLIST_DATA, playlist)
                putInt(PLAYLIST_DOWNLOAD_STATE, playlistDownloadState)
            }
        }
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding : FragmentNoicePlaylistMenuBinding
    private lateinit var ctx : Context
    private var playList : Playlist? = null
    private var shareDialog : ShareDialog? = null
    private var playlistDownloadState = -1

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun show(manager: FragmentManager, tag: String?) {
        val ft = manager.beginTransaction()
        ft.add(this, NoicePlayListMenu::class.java.simpleName)
        ft.commitAllowingStateLoss()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        playList = arguments?.getParcelable(PLAYLIST_DATA)
        playlistDownloadState = arguments?.getInt(PLAYLIST_DOWNLOAD_STATE) ?: -1
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentNoicePlaylistMenuBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Playlist_ActionMenu")
            putString("previousScreen", "Playlist_Page")
            putString("playlistTitle", playList?.title)
            putString("playlistId", playList?.id?:"")
            putString("playlistCreator", playList?.user?.userName?:"")
        })

        setData()

        initViews()
    }

    private fun setData() {
        val disabledContent = playList?.playlistContent?.find{
            it.isDownloadable == false
        }

        if (disabledContent != null) {
            binding.download.visibility = View.GONE
        } else {
            binding.download.visibility = View.VISIBLE
        }

        val content = playList?.playlistContent?.find { content ->
            content.isPurchaseNeeded
        }

        if (content != null) {
            binding.addPlayListQueue.visibility = View.GONE
        } else {
            binding.addPlayListQueue.visibility = View.VISIBLE
        }

        ImageUtils.loadImageByUrl(binding.coverImage,playList?.imageMeta?.size300, originalUrl = playList?.imageMeta?.size500)
        binding.playListName.text = playList?.title
        binding.stats.text = getString(
            R.string.content_duration,
            playList?.meta?.contentCount,
            DateUtils.convertSeconds(playList?.meta?.totalDuration)
        )
        binding.followers.text = getString(R.string.no_of_followers_str, Utils.abbrNumberFormat(playList?.meta?.aggregations?.followers ?: 0))
        binding.creator.text = getString(R.string.playlist_by, playList?.user?.displayName)

        if(playList?.isPublic == true) {
            binding.privacy.visibility = View.VISIBLE
        } else {
            binding.privacy.visibility = View.GONE
        }

        val text = getString(R.string.playlist_by, playList?.user?.displayName)
        val spannableString = SpannableString(text)
        spannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(ctx, R.color.dull_yellow)), text.length - (playList?.user?.displayName?.length ?: 0), text.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.creator.text = spannableString

        if(playList?.userId == PrefUtils.userDetails?.id) {
            binding.edit.visibility = View.VISIBLE
            binding.delete.visibility = View.VISIBLE
        } else {
            binding.edit.visibility = View.GONE
            binding.delete.visibility = View.GONE
        }

        binding.download.setOnClickListener {

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }

            EventBus.getDefault().post(EventMessage(null, Constants.PLAYLIST_DOWNLOAD))
        }

        manageState()
    }

    private fun initViews() {

        binding.edit.setOnClickListener(CustomClickListener ({
            playList?.let {
                EditPlaylistActivity.start(ctx, it,"Playlist_ActionMenu")
                AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
                    putString(FirebaseAnalytics.Param.SCREEN_NAME,"Playlist_Edit")
                    putString("previousScreen", "Playlist_ActionMenu")
                    putString("playlistTitle", it.title?:"")
                    putString("playlistId", it.id?:"")
                    putString("playlistCreator", it.user?.userName?:"")
                })
            }
        },"playlist menu"))

        binding.toolbar.setBackClick {
            dismiss()
        }

        binding.addPlayListQueue.setOnClickListener {

            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                (ctx as HomeActivity).handleUserNotLoggedIn()
                return@setOnClickListener
            }

            playList?.playlistContent?.let { contents ->
                if (contents.isNotEmpty()) {
                    ContentPlayRequest.Builder()
                        .contents(contents)
                        .pageSource(AnalyticsUtil.playlist)
                        .addToManualQueue(ctx)
                }
            }
        }

        binding.share.setOnClickListener {
            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@setOnClickListener
            }
            shareDialog = ShareDialog.newInstance(playList,false,null)
            shareDialog?.show(childFragmentManager, "share_dialog")
        }

        binding.delete.setOnClickListener(CustomClickListener ({

            if(!NetworkUtils.isNetworkConnected(ctx)) {
                Utils.showSnackBar(binding, (ctx as BasePlayerActivity).getString(R.string.this_action_requires_internet))
                return@CustomClickListener
            }

            NoiceAlertDialog.Builder()
                .setTitle(getString(R.string.delete_playlist))
                .setMessage(getString(R.string.delete_playlist_msg))
                .setListener(object : OnClickInterface<Boolean> {
                    override fun dataClicked(data: Boolean, eventId: Int) {
                        if (eventId == NoiceAlertDialog.POSITIVE_BTN_CLICK) {
                            binding.errorView.showLoading()
                            deletePlayList()
                        }
                    }
                }).show(childFragmentManager)
        },"playlist menu"))
    }

    private fun deletePlayList() {
        val map = HashMap<String, RequestBody>()

        map["isActive"] = false.toString().toRequestBody("multipart/form-data".toMediaTypeOrNull())

        viewModel.editPlayList(null, map, playList?.id ?: "").observe(this) {
            if (it?.status == ResponseStatus.SUCCESS && it.data != null) {
                AnalyticsUtil.sendEventForPlaylist(
                    "playlist", "playlist_deleted",
                    it.data.title ?: "", it.data.id ?: "", it.data.user?.displayName ?: ""
                )
                EventBus.getDefault().post(EventMessage(playList, Constants.PLAYLIST_DELETED))
                dismiss()
            }
            binding.errorView.hide()
        }
    }

    private fun setupFullHeight(bottomSheetDialog: BottomSheetDialog) {
        val bottomSheet =
            bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val behavior = BottomSheetBehavior.from(bottomSheet)
        val layoutParams = bottomSheet.layoutParams
        val windowHeight = Utils.getDeviceHeight(ctx)
        layoutParams?.height = windowHeight
        behavior.isHideable = false
        bottomSheet.layoutParams = layoutParams
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.isHideable = false
        behavior.peekHeight = windowHeight
        bottomSheet.parent.requestLayout()
    }

    fun manageState(playlistDownloadState : Int? = null) {
        if(playlistDownloadState != null) {
            this.playlistDownloadState = playlistDownloadState
        }

        when (playlistDownloadState) {
            Download.STATE_COMPLETED -> {
                binding.download.text = getString(R.string.downloaded)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white50))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_download_grey,0,0,0)
            }
            Download.STATE_DOWNLOADING -> {
                binding.download.text = getString(R.string.downloading)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_download,0,0,0)
            }
            else -> {
                binding.download.text = getString(R.string.download)
                binding.download.setTextColor(ContextCompat.getColor(ctx, R.color.white))
                binding.download.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_download,0,0,0)
            }
        }
    }

    override fun onResume() {
        super.onResume()

        val d = dialog as BottomSheetDialog
        val bottomSheet = d.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet) as FrameLayout
        val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet)

        bottomSheetBehavior.isHideable = false
        bottomSheetBehavior.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {}
            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            }
        })
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog: Dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            setupFullHeight(bottomSheetDialog)
        }
        return dialog
    }

    override fun onDestroy() {
        super.onDestroy()
        shareDialog?.dismiss()
    }
}