package noice.app.modules.dashboard.home

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import noice.app.BaseApplication
import noice.app.BuildConfig
import noice.app.R
import noice.app.databinding.FragmentHomeBinding
import noice.app.model.ExtraData
import noice.app.model.eventbus.EventMessage
import noice.app.modules.browser.InAppBrowserActivity
import noice.app.modules.dashboard.adapter.HomePagerAdapter
import noice.app.modules.dashboard.home.fragment.AudioBookHomeFragment
import noice.app.modules.dashboard.home.fragment.AudioSeriesHomeFragment
import noice.app.modules.dashboard.home.fragment.DebugDialog
import noice.app.modules.dashboard.home.fragment.ForYouFragment
import noice.app.modules.dashboard.home.fragment.PodCastFragment
import noice.app.modules.dashboard.home.fragment.RadioHomeFragment
import noice.app.modules.dashboard.home.viewmodel.HomeFragmentViewModel
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.live.event.LiveTrigger
import noice.app.modules.live.fragment.LiveTabFragment
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClassVariantProvider
import noice.app.utils.ClickHandler
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.BANNER_CAMPAIGN_LINK
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PrefUtils
import noice.app.utils.Utils.getPlayerActivity
import noice.app.utils.Utils.unwrap
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@AndroidEntryPoint
class HomeFragment : Fragment() {

    private lateinit var binding: FragmentHomeBinding
    private lateinit var ctx: Context
    private lateinit var pagerAdapter: HomePagerAdapter
    private val viewModel : HomeFragmentViewModel by viewModels()
    private var debugDialog : DebugDialog? = null
    private var extraData: ExtraData? = null //used to add analytics source in case of tabs deeplink
    private var isFirstLoad = true

    enum class HomeTabs(val position: Int, val tabName : String) {
        TAB_FOR_YOU(0, BaseApplication.getBaseAppContext().getString(R.string.for_you)),
        TAB_AUDIO_SERIES(1, BaseApplication.getBaseAppContext().getString(R.string.audio_series)),
        TAB_PODCAST(2, BaseApplication.getBaseAppContext().getString(R.string.podcast)),
        TAB_RADIO(3, BaseApplication.getBaseAppContext().getString(R.string.radio)),
        TAB_AUDIO_BOOK(4, BaseApplication.getBaseAppContext().getString(R.string.audio_book)),
        TAB_LIVE(5, BaseApplication.getBaseAppContext().getString(R.string.live))
    }

    companion object {
        fun newInstance() = HomeFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHomeBinding.inflate(LayoutInflater.from(ctx), container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()

        lifecycleScope.launch {
            withResumed {
                getNotifications()

                getUserSubscription()
            }
        }
    }

    override fun onResume() {
        super.onResume()

        if (!isFirstLoad) {
            sendImpressions()
        }

        /* using this flag to prevent any conflict when app opens from cold start and starts
        * sending the impressions automatically. It will start re-tracking impressions when user
        * comes back to home from navigation from any other screen by pressing. */
        isFirstLoad = false
    }

    private fun initViews() {
        pagerAdapter = HomePagerAdapter(this)
        binding.viewPager.isSaveEnabled = false // added for fix a crash
        binding.viewPager.adapter = pagerAdapter
        binding.viewPager.isUserInputEnabled = false

        val tabNames = HomeTabs.values().map { homeTab ->
            homeTab.tabName
        }

        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = tabNames[position]
        }.attach()

        if (!PrefUtils.isNewVerticalClicked && binding.tabLayout.tabCount > 2) {
            binding.tabLayout.getTabAt(2)?.orCreateBadge?.apply {
                backgroundColor = ContextCompat.getColor(ctx, R.color.red_full)
                badgeGravity = BadgeDrawable.TOP_START
            }
        } else {
            removeNewVerticalBade()
        }

        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                updateTabText(tab)
            }
        })

        for (i in 0 until binding.tabLayout.tabCount) {
            binding.tabLayout.getTabAt(i)?.view?.setBackgroundResource(R.drawable.custom_ripple_bg_4dp)
        }

        updateTabText(binding.tabLayout.getTabAt(0))

        binding.viewPager.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                sendImpressions()
            }
        })


        AnalyticsUtil.sendEvent("for_you_opened", null)

        binding.btnOkay.setOnClickListener {
            binding.extendSubLayout.visibility = View.GONE
        }

        binding.proceedToSub.setOnClickListener {
            binding.extendSubLayout.visibility = View.GONE

            /* this button will redirect a user to subscription page in in-app browser */
            val url = if (!PrefUtils.appCDNConfig?.subscription_page_url.isNullOrEmpty()) {
                PrefUtils.appCDNConfig?.subscription_page_url.toString()
            } else {
                BuildConfig.SUBSCRIPTION_PAGE_URL
            }

            val uri = Uri.parse(url)
            EventBus.getDefault().post(
                OpenIndexEvent(
                    OpenIndexEvent.OPEN_NATIVE_DEEPLINK,
                    Intent().apply {
                        data = uri
                    },
                    extraData = extraData
                )
            )
        }

        binding.searchBar.setOnClickListener {
            AnalyticsUtil.sendEvent("search_bar_clicked","home")
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_SEARCH_PAGE,targetPageId = "Home"))
        }
        if (BuildConfig.DEBUG && !ClassVariantProvider.isToyotaVariant){
            binding.imgLogo.setOnClickListener {
                if (debugDialog == null) {
                    debugDialog = DebugDialog()
                }
                debugDialog?.show(childFragmentManager)
            }
        }

        binding.icNotification.setOnClickListener {
            if ((PrefUtils.isLoggedInAsGuest || !PrefUtils.isLoggedIn) && PrefUtils.guestId.isNullOrEmpty()) {
                ctx.getPlayerActivity()?.handleUserNotLoggedIn()
                return@setOnClickListener
            }

            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_NOTIFICATION_CENTER))
        }
    }

    private fun updateTabText(tab: TabLayout.Tab?) {
        val views = arrayListOf<View>()
        tab?.view?.findViewsWithText(views, tab.text, View.FIND_VIEWS_WITH_TEXT)
        views.forEach { view ->
            if (view is TextView) {
                if (tab?.isSelected == true) {
                    view.typeface = Typeface.create(view.typeface, Typeface.BOLD)

                    AnalyticsUtil.sendEvent("home_page_viewed", Bundle().apply {
                        putString("tabName", tab.text.toString())

                        if (extraData != null) {
                            if (!extraData?.source.isNullOrEmpty()) {
                                putString("source", extraData?.source ?: "")
                            }
                        }
                    })
                    MoEngageAnalytics.sendEvent(ctx, "home page viewed", Bundle().apply {
                        putString("tab name", tab.text.toString())

                        if (extraData != null) {
                            if (!extraData?.source.isNullOrEmpty()) {
                                putString("source", extraData?.source ?: "")
                            }
                        }
                    })

                    when (tab.text) {
                        getString(R.string.for_you) -> {
                            AnalyticsUtil.sendEvent("for_you_opened", null)
                        }
                        getString(R.string.podcast) -> {
                            AnalyticsUtil.sendEvent("podcast_home_opened", null)
                        }
                        getString(R.string.radio) -> {
                            AnalyticsUtil.sendEvent("radio_home_opened", null)
                        }
                        getString(R.string.audio_series) -> {
                            PrefUtils.isNewVerticalClicked = true
                            removeNewVerticalBade()
                            AnalyticsUtil.sendEvent("audioseries_home_opened", null)
                        }
                        getString(R.string.live) -> {
                            AnalyticsUtil.sendEvent("live_home_opened", null)
                            if (BuildConfig.VERSION_CODE < (PrefUtils.appConfig?.app_update_config?.android?.liveStreamUpdateVersionCode ?: 0)) {
                                LiveTrigger.onForceUpdate()
                            }
                        }
                        else -> {
                            AnalyticsUtil.sendEvent("audiobook_home_opened", null)
                        }
                    }

                    extraData = null //to prevent received data to be used while exploring app after the deeplink
                } else {
                    view.typeface = Typeface.create(view.typeface, Typeface.NORMAL)
                }
            }
        }
    }

    fun selectTab(homeTab: HomeTabs, extraData: ExtraData? = null) {
        /* this logic is used to fix a deeplink -> when last tab is selected and deeplink is to select the
        same already selected (last tab) tab. Issue was, tab was updating correctly but the fragment instead was incorrect.
        e.g. Deeplink is for 'Audiobook' and 'Audiobook' tab is selected. Here after clicking the deeplink,
        'Audiobook' Tab was highlighted but content was of Home Screen (FYP).
        Solution: https://issuetracker.google.com/issues/183847283
                  https://stackoverflow.com/a/70736137
        */
        binding.viewPager.post {
            binding.viewPager.currentItem = homeTab.position
        }

        if (extraData != null) {
            this.extraData = extraData
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event : LoginChangeEvent) {

        val currentFragment = getCurrentFragment()

        childFragmentManager.fragments.forEach { fragment ->
            when (fragment) {
                is ForYouFragment -> {
                    fragment.onLoginChangedEvent()
                }
                is PodCastFragment -> {
                    fragment.onLoginChangedEvent(currentFragment is PodCastFragment)
                }
                is RadioHomeFragment -> {
                    fragment.onLoginChangedEvent(currentFragment is RadioHomeFragment)
                }
                is AudioBookHomeFragment -> {
                    fragment.onLoginChangedEvent(currentFragment is AudioBookHomeFragment)
                }
            }
        }
        if (event.actionName == BANNER_CAMPAIGN_LINK){
            InAppBrowserActivity.startBrowser(
                ctx.unwrap(),
                event.id ?: "",
                ""
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event : EventMessage) {
        if (event.eventCode == Constants.NOTIFICATION_READ) {
            binding.newNotifications.visibility = View.GONE
        } else if (event.eventCode == Constants.NOTIFICATION_RECEIVED) {
            binding.newNotifications.visibility = View.VISIBLE
        }
    }

    private fun getUserSubscription() {
        if (!PrefUtils.isLoggedIn) {
            return
        }

        val map = hashMapOf(
            "userIds" to "[\"${PrefUtils.userDetails?.id}\"]",
            "status" to "[\"active\"]",
            "orderBy" to "created_at",
            "sort" to "ASC",
            "page" to "1",
            "limit" to "5"
        )

        viewModel.getUserSubscription(map).observe(viewLifecycleOwner) { res->
            if (res?.status == ResponseStatus.SUCCESS && !res.data?.data.isNullOrEmpty() && res.data?.data?.get(0)?.banner != null) {
                val banner = res.data.data?.get(0)?.banner
                binding.heading.text = banner?.title
                binding.desc.text = HtmlCompat.fromHtml(banner?.message.toString(), HtmlCompat.FROM_HTML_MODE_LEGACY)

                binding.proceedToSub.visibility = if (banner?.showPurchaseButton != true)
                    View.GONE
                else View.VISIBLE

                if (banner?.showPurchaseButton != true) {
                    binding.btnOkay.background =
                        ContextCompat.getDrawable(ctx, R.drawable.background_dull_yellow_20dp)
                    binding.btnOkay.apply {
                        setPadding(
                            ctx.resources.getDimensionPixelSize(R.dimen.dp16),
                            0,
                            ctx.resources.getDimensionPixelSize(R.dimen.dp16),
                            0)

                        (this.layoutParams as ConstraintLayout.LayoutParams).setMargins(0, ctx.resources.getDimensionPixelSize(R.dimen.dp8), 0, ctx.resources.getDimensionPixelSize(R.dimen.dp16))
                    }
                }

                binding.extendSubLayout.visibility = View.VISIBLE
            } else {
                binding.extendSubLayout.visibility = View.GONE
            }
        }
    }

    private fun getNotifications() {
        val map = HashMap<String, String>()
        map["page"] = "1"
        map["limit"] = "1"
        map["isRead"] = "false"

        viewModel.getNotifications(map, null).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                binding.newNotifications.visibility = View.VISIBLE
            } else {
                binding.newNotifications.visibility = View.GONE
            }
        }
    }

    override fun onPause() {
        super.onPause()
        debugDialog?.dismiss()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    private fun getCurrentFragment() =
        childFragmentManager.findFragmentByTag("f" + pagerAdapter.pageIds[binding.viewPager.currentItem])

    fun handleSecondTabClick() {
        val fragment = getCurrentFragment()
        if (fragment is ForYouFragment) {
            fragment.scrollUp()
            binding.appBarLayout.setExpanded(true)
            if (childFragmentManager.fragments.size > 1){
                childFragmentManager.popBackStackImmediate(
                    null,
                    FragmentManager.POP_BACK_STACK_INCLUSIVE
                )
            }
        } else {
            // fix: viewpager stuck b/w two screen when moved to another screen
            // in the bottom nav while view pager animation has not completed.
            binding.viewPager.setCurrentItem(0, false)
        }
    }

    fun handelBackPress() {
        when {
            childFragmentManager.backStackEntryCount > 0 -> {
                if (!childFragmentManager.isStateSaved) {
                    childFragmentManager.popBackStack()

                    /* childFragmentManager.popBackStack() is an async method and takes a bit
                    * of time to complete the transaction so had to add this delay to re-send
                    * home screen impression when navigating back to this screen from any other screen. */
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (childFragmentManager.backStackEntryCount == 0) {
                            /* handled to send impression when is navigation back to For You screen */
                            sendImpressions()
                        }
                    }, 100)
                }
            }
            getCurrentFragment() !is ForYouFragment -> {
                binding.viewPager.currentItem = 0
            }
            else -> {
                (ctx as AppCompatActivity).moveTaskToBack(true)
            }
        }
    }

    private fun removeNewVerticalBade() {
        binding.tabLayout.getTabAt(2)?.removeBadge()
    }

    fun sendImpressions() {
        val currentFragment = getCurrentFragment()

        when (currentFragment) {
            is ForYouFragment -> {
                currentFragment.sendImpressions()
            }

            is PodCastFragment -> {
                currentFragment.sendImpressions()
            }

            is AudioSeriesHomeFragment -> {
                currentFragment.sendImpressions()
            }

            is RadioHomeFragment -> {
                currentFragment.sendImpressions()
            }

            is AudioBookHomeFragment -> {
                currentFragment.sendImpressions()
            }

            is LiveTabFragment -> {
                currentFragment.sendImpressions()
            }
        }
    }
}