package noice.app.modules.dashboard.home

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.BuildConfig
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentHomeNewBinding
import noice.app.layoutmanagers.CustomLLManager
import noice.app.model.Variant
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.dashboard.adapter.HomeSegmentAdapter
import noice.app.modules.dashboard.fragment.AppReviewDialog
import noice.app.modules.dashboard.home.fragment.DebugDialog
import noice.app.modules.dashboard.home.viewholder.HomeFragmentNewViewModel
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.indexpages.model.OpenIndexEvent
import noice.app.modules.media.model.Community
import noice.app.modules.media.model.MediaAction
import noice.app.modules.onboarding.models.LoginChangeEvent
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.ClassVariantProvider
import noice.app.utils.Constants
import noice.app.utils.ExperimentUtils
import noice.app.utils.MoEngageAnalytics
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.ceil

@AndroidEntryPoint
class HomeFragmentNew : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener, SwipeRefreshLayout.OnRefreshListener {

    private val viewModel: HomeFragmentNewViewModel by viewModels()

    private var offset = 1
    private val limit = 4
    private var totalCount = -1
    private lateinit var ctx: Context
    private lateinit var binding: FragmentHomeNewBinding
    private lateinit var adapter: HomeSegmentAdapter
    private var segmentList = ArrayList<HomeSegment?>()
    private var debugDialog: DebugDialog? = null
    private var reviewDialog: AppReviewDialog? = null
    private var topEpisodeVariant: Variant? = null
    private var episodeBaruVariant: Variant? = null

    companion object {
        const val VERTICALS_SEGMENT = "Home_Jelajah"

        const val ANALYTICS_TAG = "HomeScreen_New"

        fun newInstance() = HomeFragmentNew()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onPause() {
        super.onPause()
        debugDialog?.dismiss()
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHomeNewBinding.inflate(LayoutInflater.from(ctx), container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        /* top episode segment experiment on home screen */
        topEpisodeVariant = ExperimentUtils.getExperiment(ExperimentUtils.EXPERIMENT_TOP_AND_TRENDING_EPISODE, ExperimentUtils.VARIANT_A)

        /* episode-baru segment experiment on home screen */
        episodeBaruVariant = ExperimentUtils.getExperiment(ExperimentUtils.EXPERIMENT_EPISODE_BARU, ExperimentUtils.VARIANT_A)

        initViews()

        lifecycleScope.launch {
            withResumed {
                getNotifications()
            }
        }

        getData(forced = offset == 1, segmentList.isEmpty())
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        offset = page + 1

        if (totalCount == -1 ||
            ceil((totalCount.toDouble() / limit.toDouble())).toInt() >= offset
        ) {
            getHomeSegment(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }

    override fun onRefresh() {
        getData(true, segmentList.isEmpty())
        EventBus.getDefault().post(EventMessage(null,Constants.HOME_REFRESH_CALLED))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: EventMessage) {
        when (event.eventCode) {
            Constants.NOTIFICATION_READ -> {
                binding.newNotifications.visibility = View.GONE
            }
            Constants.NOTIFICATION_RECEIVED -> {
                binding.newNotifications.visibility = View.VISIBLE
            }
            Constants.CATALOG_SAVE_ACTION -> {
                val mediaAction = event.data as MediaAction

                val homeContent = segmentList.find { segment ->
                    segment?.viewOptions?.viewType == HomeSegmentAdapter.VIEW_TYPE_TOP_HIGHLIGHT
                }?.let { segment ->
                    val content = segment.content?.find { homeContent ->
                        homeContent.id == mediaAction.entityId
                    }

                    if (content?.meta?.userActions.isNullOrEmpty()) {
                        content?.meta = Community()
                        content?.meta?.userActions = ArrayList()
                        content?.meta?.userActions?.add(mediaAction)
                    } else {
                        content?.meta?.userActions?.find { actionIt ->
                            actionIt.action == mediaAction.action
                        }.also { actionIt ->
                            if (actionIt == null) {
                                content?.meta?.userActions?.add(mediaAction)
                            } else {
                                val index = content?.meta?.userActions?.indexOf(actionIt) ?: -1
                                if (index != -1) {
                                    content?.meta?.userActions?.set(index, mediaAction)
                                }
                            }
                        }
                    }
                    adapter.notifyItemChange(segment)
                    content
                }

                viewModel.performAction(mediaAction).observe(viewLifecycleOwner) {
                    if(it.status == ResponseStatus.SUCCESS) {
                        AnalyticsUtil.sendEvent("spotlight_save_clicked", Bundle().apply {
                            putString("source", "Home_Page")
                            putString("entityTitle", homeContent?.title ?: "")
                            putString("entityId", homeContent?.id ?: "")
                            putString("entityType", homeContent?.entityType ?: "")
                            putString("entitySubType", homeContent?.category ?: "")
                            putString("segmentName", (event.secondData as? String) ?: "")
                            putString("recommendationSource", homeContent?.meta?.recommendationSource ?: "")
                        })
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginChangedEvent(event: LoginChangeEvent) {
        lifecycleScope.launch {
            withResumed {
                getData(true, showLoader = true)
            }
        }
    }

    private fun initViews() {

        binding.errorView.setBackToHomeVisibility(View.GONE)
        binding.swipeRefresh.setOnRefreshListener(this)

        binding.forYouRecycler.layoutManager = CustomLLManager(ctx, RecyclerView.VERTICAL, false)
        if (!::adapter.isInitialized) {
            adapter = HomeSegmentAdapter(
                binding.forYouRecycler, segmentList, this,
                ANALYTICS_TAG
            )
        } else {
            adapter.bindListenerToRecyclerView(binding.forYouRecycler)
        }
        binding.forYouRecycler.itemAnimator = null
        binding.forYouRecycler.setHasFixedSize(true)
        binding.forYouRecycler.adapter = adapter

        binding.errorView.setOnReturnClick {
            getData(true)
        }

        AnalyticsUtil.sendEvent("for_you_opened", null)

        if (BuildConfig.DEBUG && !ClassVariantProvider.isToyotaVariant) {
            binding.imgLogo.setOnClickListener {
                if (debugDialog == null) {
                    debugDialog = DebugDialog()
                }
                debugDialog?.show(childFragmentManager)
            }
        }

        binding.icNotification.setOnClickListener {
            EventBus.getDefault().post(OpenIndexEvent(OpenIndexEvent.OPEN_NOTIFICATION_CENTER))
        }

        AnalyticsUtil.sendEvent("home_page_viewed", Bundle().apply {
            putString("tabName", "FYP 2.0")
        })
        MoEngageAnalytics.sendEvent(ctx, "home page viewed", "tab name", "FYP 2.0")
    }

    private fun getData(forced: Boolean = false, showLoader: Boolean = true) {
        if (!forced && segmentList.isNotEmpty()) {
            offset++
            adapter.notifyDataSetChanged()
            hideLoading()
        } else {
            if (showLoader) {
                showLoading()
            }
            getHomeSegment()
        }

        if (PrefUtils.isLoggedIn && !PrefUtils.hasReviewedApp) {
            shouldReviewApp()
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.shimmer_home_new)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun showError(message: String?) {
        adapter.isLoadMoreEnabled(false)
        binding.errorView.showError(message, type = "Home_ForYou_New", errorName = message)
    }

    private fun getNotifications() {
        val map = HashMap<String, String>()
        map["page"] = "1"
        map["limit"] = "1"
        map["isRead"] = "false"

        viewModel.getNotifications(map, null).observe(viewLifecycleOwner) {
            if (it?.status == ResponseStatus.LOADING) {
                return@observe
            }
            if (it?.status == ResponseStatus.SUCCESS && !it.data?.data.isNullOrEmpty()) {
                binding.newNotifications.visibility = View.VISIBLE
            } else {
                binding.newNotifications.visibility = View.GONE
            }
        }
    }

    private fun getHomeSegment(loadMore : Boolean = false) {
        if(isDetached || isRemoving || !isAdded)
            return

        if (!loadMore) {
            offset = 1
        }

        val page = offset

        val map = HashMap<String, String>()
        map["page"] = "home"
        map["offset"] = offset.toString()
        map["limit"] = limit.toString()

        /* top-episode/trending-episode experiment */
        val topEpisodeExperimentObject =
            mapOf(
                "experimentName" to ExperimentUtils.EXPERIMENT_TOP_AND_TRENDING_EPISODE,
                "variant" to "${topEpisodeVariant?.value}"
            )

        /* episode-baru experiment */
        val episodeBaruExperimentObject =
            mapOf(
                "experimentName" to ExperimentUtils.EXPERIMENT_EPISODE_BARU,
                "variant" to "${episodeBaruVariant?.value}"
            )

        val experimentParamArray = ArrayList<Map<String, String>>()
        experimentParamArray.add(topEpisodeExperimentObject)
        experimentParamArray.add(episodeBaruExperimentObject)
        map["experiment"] = Utils.stringify(experimentParamArray)

        viewModel.getHomeSegment(
            map,
            Constants.NEW_HOME_SEGMENTS,
            binding.swipeRefresh.isRefreshing
        ).observe(viewLifecycleOwner) {

            it?.data?.data?.filter { segment ->
                !segment.content.isNullOrEmpty() ||
                        (segment.viewOptions?.viewType == "livestream" && PrefUtils.userDetails?.meta?.live?.canCreateLiveRoom == true)
            }?.let { segments ->
                it.data.data = segments
            }

            when (it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(loadMore, page, it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        totalCount = it.data?.meta?.totalCount ?: -1
                        handleSegmentResponse(loadMore, page, it)
                    } else if (segmentList.isNotEmpty()) {
                        hideLoading()
                        adapter.isLoadMoreEnabled(false)
                    } else {
                        showError(it.message)
                    }
                    binding.swipeRefresh.isRefreshing = false
                }
                else -> {
                    handleError(it?.message)
                    binding.swipeRefresh.isRefreshing = false
                }
            }
        }
    }

    private fun handleError(message: String?) {
        if (segmentList.isEmpty()) {
            showError(message)
        } else {
            hideLoading()
        }
    }

    private fun handleSegmentResponse(
        loadMore : Boolean,
        page: Int,
        resource: Resource<BaseModel<List<HomeSegment>>>
    ) {
        CoroutineScope(Dispatchers.Main).launch {
            resource.data?.data?.let { segments ->

                if (loadMore) {
                    segments.forEach { seg ->
                        seg.segmentPage = page
                    }
                    adapter.addMore(segments)
                } else {
                    val seg = ArrayList(segments)
                    seg.forEach { segment ->
                        segment.segmentPage = page
                    }
                    if (resource.wasCacheAvailable == true) {
                        segmentList.filter { segment ->
                            segment?.segmentPage == page
                        }.let { filteredSegments ->
                            segmentList.removeAll(filteredSegments.toSet())
                        }
                        seg.addAll(segmentList)
                        segmentList.clear()
                        segmentList.addAll(seg)
                        adapter.notifyDataSetChanged()
                    } else {
                        adapter.addNewList(seg)
                    }
                }
            }

            hideLoading()
        }
    }

    private fun shouldReviewApp() {
        if (isDetached || isRemoving || !isAdded || ClassVariantProvider.isToyotaVariant)
            return
        viewModel.shouldReviewApp().observe(viewLifecycleOwner) {
            if (it.status == ResponseStatus.SUCCESS) {
                PrefUtils.hasReviewedApp = it.data?.reviewed ?: false
                if (it.data?.reviewApp == true && it.data.reviewed == false) {
                    lifecycleScope.launch {
                        withResumed {
                            if (reviewDialog == null || reviewDialog?.isAdded == false) {
                                reviewDialog = AppReviewDialog()
                                reviewDialog?.isCancelable = false
                                reviewDialog?.show(childFragmentManager, "review_dialog")
                            }
                        }
                    }
                }
            }
        }
    }

    fun handelBackPress(isBottomTabClick: Boolean = false) {
        when {
            childFragmentManager.backStackEntryCount > 0 -> {
                if (!childFragmentManager.isStateSaved) {
                    childFragmentManager.popBackStack()
                }
            }
            else -> {
                if (!isBottomTabClick) {
                    (ctx as AppCompatActivity).moveTaskToBack(true)
                }
            }
        }
    }

    fun handleSecondTabClick() {
        if (childFragmentManager.fragments.size > 1){
            childFragmentManager.popBackStackImmediate(
                null,
                FragmentManager.POP_BACK_STACK_INCLUSIVE
            )
        } else binding.forYouRecycler.smoothScrollToPosition(0)
    }
}