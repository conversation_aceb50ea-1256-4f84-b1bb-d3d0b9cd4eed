package noice.app.modules.dashboard.collection.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.modules.media.model.Playlist
import noice.app.views.PlayListGridView

class PlayListGridAdapter(
    private val dataSet: ArrayList<Playlist>
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var content: PlayListGridView

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        content = PlayListGridView(parent.context)
        return content.viewHolder
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        content.viewHolder = holder as PlayListGridView.ViewHolder
        content.setData(dataSet[position])
    }

    override fun getItemCount(): Int {
        return dataSet.size
    }
}