package noice.app.modules.dashboard.home.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.firebase.analytics.FirebaseAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import noice.app.R
import noice.app.adapter.LoadMoreAdapter
import noice.app.databinding.FragmentAudioBookHomeBinding
import noice.app.layoutmanagers.CustomLLManager
import noice.app.model.eventbus.EventMessage
import noice.app.model.generics.BaseModel
import noice.app.modules.ads.GaManager
import noice.app.modules.dashboard.adapter.HomeInnerAdapter
import noice.app.modules.dashboard.adapter.HomeSegmentAdapter
import noice.app.modules.dashboard.model.HomeBanner
import noice.app.modules.dashboard.model.HomeSegment
import noice.app.modules.dashboard.viewmodel.DashboardViewModel
import noice.app.rest.Resource
import noice.app.rest.ResponseStatus
import noice.app.utils.AnalyticsUtil
import noice.app.utils.CacheUtils
import noice.app.utils.Constants
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import noice.app.utils.impressions.ImpressionTrackerHelper
import org.greenrobot.eventbus.EventBus
import kotlin.math.ceil

@AndroidEntryPoint
class AudioBookHomeFragment : Fragment(), LoadMoreAdapter.LoadMoreAdapterListener, SwipeRefreshLayout.OnRefreshListener  {

    companion object {
        const val ANALYTICS_TAG = "Audiobook"
        fun newInstance() = AudioBookHomeNewFragment()
    }

    private val viewModel: DashboardViewModel by viewModels()

    private lateinit var binding : FragmentAudioBookHomeBinding
    private lateinit var ctx : Context
    private lateinit var adapter: HomeSegmentAdapter
    private val dataList =  ArrayList<HomeSegment?>()
    private val bannerList =  ArrayList<HomeBanner>()
    private var offset = 1
    private val limit = 4
    private var totalCount = -1
    private var isLoginChanged = false

    private var impressionTrackerHelper: ImpressionTrackerHelper? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        ctx = context
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentAudioBookHomeBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews()
        displayGamAd()
    }

    override fun onResume() {
        super.onResume()

        CoroutineScope(Dispatchers.Main).launch {
            getData(forced = isLoginChanged || offset == 1, showLoader = isLoginChanged || dataList.isEmpty())
        }

        AnalyticsUtil.sendEvent(FirebaseAnalytics.Event.SCREEN_VIEW, Bundle().apply {
            putString(FirebaseAnalytics.Param.SCREEN_NAME,"Home_Audiobook")
            putString("previousScreen", "Home")
        })
    }

    private fun initViews() {

        binding.errorView.setBackToHomeVisibility(View.GONE)
        binding.pullToRefresh.setOnRefreshListener(this)
        binding.audioBookRecycler.layoutManager = CustomLLManager(ctx, RecyclerView.VERTICAL, false)
        if (!::adapter.isInitialized) {
            adapter = HomeSegmentAdapter(binding.audioBookRecycler, dataList, this, ANALYTICS_TAG)
        } else {
            adapter.bindListenerToRecyclerView(binding.audioBookRecycler)
        }
        binding.audioBookRecycler.adapter = adapter

        binding.errorView.setOnReturnClick {
            getData(true)
        }

        impressionTrackerHelper = ImpressionTrackerHelper(
            binding.audioBookRecycler,
            dataList,
            ANALYTICS_TAG
        )
        impressionTrackerHelper?.startTracking()
    }

    private fun displayGamAd() {
        if ((!PrefUtils.appCDNConfig?.nativeAdsConfig?.nativeAdUnitId.isNullOrEmpty() ||
                    !PrefUtils.appCDNConfig?.nativeAdsConfig?.customNativeAdUnitId.isNullOrEmpty()) &&
            (PrefUtils.userDetails?.isSubscribed ?: false) == false
        ) {
            GaManager.refreshAd(null, null, null)
        }
    }

    private fun getData(forced : Boolean = false, showLoader : Boolean = true) {

        isLoginChanged = false

        if(!forced && dataList.isNotEmpty()) {
            offset++
            adapter.notifyDataSetChanged()
            hideLoading()
        } else {
            if(showLoader) {
                showLoading()
            }
            getAudioBookSegment()
        }
        if (isDetached || isRemoving || !isAdded)
            return
        if(!forced && bannerList.isNotEmpty()) {
            setBannerData()
        } else {
            getAudioBookBanner()
        }
    }

    private fun getAudioBookSegment(loadMore : Boolean = false) {

        if (!loadMore) {
            offset = 1
        }

        val map = HashMap<String, String>()
        map["page"] = "audiobook"
        map["offset"] = offset.toString()
        map["limit"] = limit.toString()

        val page = offset

        if (isDetached || isRemoving || !isAdded)
            return

        viewModel.getHomeSegment(map, Constants.NOICE_BOOK).observe(viewLifecycleOwner) {

            it?.data?.data?.filter { segment ->
                if (segment.viewOptions?.viewType == HomeSegmentAdapter.VIEW_TYPE_ADS) {
                    !segment.content.isNullOrEmpty()
                            && (PrefUtils.userDetails?.isSubscribed == false
                            || PrefUtils.userDetails?.isSubscribed == null)
                } else {
                    !segment.content.isNullOrEmpty() || segment.viewOptions?.viewType == HomeInnerAdapter.TYPE_MOENGAGE_CARDS
                }
            }.let { segments ->
                it?.data?.data = segments
            }

            when(it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(loadMore, page, it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleSegmentResponse(loadMore, page, it)
                    } else if (dataList.isNotEmpty()) {
                        hideLoading()
                        adapter.isLoadMoreEnabled(false)
                    } else {
                        showError(it.message)
                    }
                    binding.pullToRefresh.isRefreshing = false
                }
                else -> {
                    handleError(it?.message)
                    binding.pullToRefresh.isRefreshing = false
                }
            }
        }
    }

    private fun handleSegmentResponse(
        loadMore: Boolean,
        page: Int,
        resource: Resource<BaseModel<List<HomeSegment>>>
    ) {
        totalCount = resource.data?.meta?.totalCount ?: -1

        resource.data?.data?.let { segments->
            if (loadMore) {
                segments.forEach { seg ->
                    seg.segmentPage = page
                }
                adapter.addMore(segments)
            } else {
                val seg = ArrayList(segments)
                if (bannerList.isNotEmpty()) {
                    seg.add(0, HomeSegment("banners", bannerList))
                }
                seg.forEach { segment ->
                    segment.segmentPage = page
                }
                if (resource.wasCacheAvailable == true) {
                    dataList.filter { segment ->
                        segment?.segmentPage == page
                    }.forEach { segment ->
                        dataList.remove(segment)
                    }
                    seg.addAll(dataList)
                    dataList.clear()
                    dataList.addAll(seg)
                    adapter.notifyDataSetChanged()
                } else {
                    adapter.addNewList(seg)
                }
            }

            getMoEngageCards()

            hideLoading()
        }
    }

    private fun getAudioBookBanner() {
        viewModel.getHomeBanner(ENTITY_TYPE_AUDIO_BOOK, Constants.NOICE_BOOK_BANNER).observe(viewLifecycleOwner) {

            when (it?.status) {
                ResponseStatus.LOADING -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleBannerResponse(it)
                    }
                }
                ResponseStatus.SUCCESS -> {
                    if (!it.data?.data.isNullOrEmpty()) {
                        handleBannerResponse(it)
                    }
                }
                else -> {

                }
            }
        }
    }

    private fun handleBannerResponse(
        resource: Resource<BaseModel<List<HomeBanner>>>
    ) {
        if (!resource.data?.data.isNullOrEmpty()) {bannerList.clear()
            bannerList.addAll(resource.data?.data!!)
            setBannerData()
        } else {
            CacheUtils.deleteCachedDBData(Constants.NOICE_BOOK_BANNER)
        }
    }

    private fun handleError(message: String?) {
        if (dataList.isEmpty()) {
            showError(message)
        } else {
            hideLoading()
        }
    }

    private fun setBannerData() {
        if (dataList.isNotEmpty()) {
            dataList.find { segment ->
                segment?.id == "banners"
            }?.let { segment ->
                dataList.remove(segment)
            }
            dataList.add(0, HomeSegment("banners", bannerList).apply {
                segmentPage = 1
            })
            adapter.notifyDataSetChanged()
        }
    }

    fun onLoginChangedEvent(isCurrentFragment: Boolean) {
        isLoginChanged = true
        if (isCurrentFragment) {
            getData(forced = true, showLoader = true)
        }
    }

    private fun showLoading() {
        binding.errorView.showLoading(R.layout.for_you_loader)
    }

    private fun hideLoading() {
        binding.errorView.hide()
    }

    private fun showError(message : String?) {
        adapter.isLoadMoreEnabled(false)
        binding.errorView.showError(message, type = "Home_Audiobook", errorName = message)
    }

    override fun onDestroyView() {
        adapter.notifyBusUnregister()
        super.onDestroyView()
    }

    override fun onLoadMore(page: Int, totalItemsCount: Int, view: RecyclerView?) {
        offset = page + 1

        if (totalCount == -1 ||
                    ceil((totalCount.toDouble()/limit.toDouble())).toInt() >= offset) {
            getAudioBookSegment(true)
        } else {
            adapter.isLoadMoreEnabled(false)
        }
    }

    override fun onRefresh() {
        getData(true, dataList.isEmpty() && bannerList.isEmpty())
        EventBus.getDefault().post(EventMessage(null,Constants.HOME_REFRESH_CALLED))
    }

    /** This method is used to manually invoke impression tracking. E.g. after moving away from
     * this screen and again coming back to this screen we need to trigger the impressions again
     * for all the visible Segments and Entities.
     *
     * ***scrollBy(1, 1)*** is a hack to invoke the impression tracking calculations.
     * */
    fun sendImpressions() {
        impressionTrackerHelper?.clearTrackedData()
        binding.audioBookRecycler.scrollBy(1, 1)
    }

    private fun getMoEngageCards() {
        lifecycleScope.launch(Dispatchers.Main) {
            val moEngageCardSegments = dataList.filter { homeSegment ->
                homeSegment?.viewOptions?.viewType == HomeInnerAdapter.TYPE_MOENGAGE_CARDS
            }

            moEngageCardSegments.forEach { homeSegment ->
                val index = dataList.indexOf(homeSegment)

                Utils.updateMoEngageCardSegment(
                    ctx,
                    homeSegment?.viewOptions?.moengageCategories?.get(0) ?: "",
                    index,
                    dataList,
                    adapter
                )
            }
        }
    }
}