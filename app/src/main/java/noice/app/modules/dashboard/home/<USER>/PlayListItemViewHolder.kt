package noice.app.modules.dashboard.home.viewholder

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import noice.app.databinding.PlaylistItemBinding

class PlayListItemViewHolder (private val binding: PlaylistItemBinding) : BasePlayListViewHolder(binding.root) {
    override val txtTitle: TextView
        get() = binding.txtTitle
    override val subTitle: TextView
        get() = binding.txtSubtitle
    override val itemDrag: ImageView
        get() = binding.imgMenu
    public override val imgCheck : AppCompatImageView
    get() = binding.imgCheck
    override val foreground: View?
        get() = null
}