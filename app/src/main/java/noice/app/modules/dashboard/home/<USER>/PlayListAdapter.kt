package noice.app.modules.dashboard.home.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import com.thesurix.gesturerecycler.GestureAdapter
import com.thesurix.gesturerecycler.GestureViewHolder
import noice.app.R
import noice.app.databinding.PlaylistItemBinding
import noice.app.databinding.QueueHeaderItemBinding
import noice.app.listner.OnClickInterface
import noice.app.modules.dashboard.home.viewholder.PlayListHeaderViewHolder
import noice.app.modules.dashboard.home.viewholder.PlayListItemViewHolder
import noice.app.modules.media.model.PlayListDataItem
import noice.app.modules.podcast.model.Content

class PlayListAdapter(
    @LayoutRes private val mItemResId: Int,
    private val clickListener: OnClickInterface<Content>
) : GestureAdapter<PlayListDataItem, GestureViewHolder<PlayListDataItem>>() {

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): GestureViewHolder<PlayListDataItem> {
        return when (viewType) {
            PlayListDataItem.ItemType.QUEUE.ordinal -> {
                when (mItemResId) {
                    R.layout.playlist_item -> PlayListItemViewHolder(
                        PlaylistItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                    )
                    else -> throw UnsupportedOperationException("Unsupported resource")
                }
            }
            else -> {
                PlayListHeaderViewHolder(
                    QueueHeaderItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                )
            }
        }
    }

    override fun onBindViewHolder(
        holder: GestureViewHolder<PlayListDataItem>,
        position: Int,
        payloads: MutableList<Any>
    ) {
        super.onBindViewHolder(holder, position, payloads)
        if (holder is PlayListItemViewHolder) {
            holder.imgCheck.setOnClickListener {
                clickListener.dataClicked(data[position].content)
            }
        }
    }

    override fun getItemViewType(viewPosition: Int): Int {
        val handledType = super.getItemViewType(viewPosition)
        if (handledType > 0) {
            return handledType
        }
        return getItemByViewPosition(viewPosition).type.ordinal
    }
}
