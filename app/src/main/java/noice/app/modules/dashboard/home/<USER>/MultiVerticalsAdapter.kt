package noice.app.modules.dashboard.home.adapter

import android.content.Context
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import noice.app.adapter.LoadMoreAdapter
import noice.app.model.ExtraData
import noice.app.modules.dashboard.adapter.HomeInnerAdapter
import noice.app.modules.dashboard.model.HomeContent
import noice.app.utils.Constants.Companion.ENTITY_TYPE_AUDIO_BOOK
import noice.app.utils.Constants.Companion.ENTITY_TYPE_LIVE_STREAM
import noice.app.utils.Constants.Companion.ENTITY_TYPE_RADIO
import noice.app.views.LiveSegment
import noice.app.views.homesegmentviews.DefaultSegmentView
import noice.app.views.homesegmentviews.RadioSegmentView

class MultiVerticalsAdapter(
    recyclerView: RecyclerView,
    listener: LoadMoreAdapterListener?,
    dataSet: ArrayList<*>,
    private val entityType: String? = "",
    private val viewType: String? = "",
    val mContext: Context,
    private val extraData: ExtraData?
) : LoadMoreAdapter<Any>(recyclerView, dataSet as ArrayList<Any?>, listener) {

    companion object {
        const val TYPE_RADIO = 1
        const val TYPE_AUDIO_BOOK = 2
        const val TYPE_LIVE = 3
        const val TYPE_DEFAULT = 4
    }

    private lateinit var defaultSegmentView: DefaultSegmentView
    private lateinit var radioSegmentView: RadioSegmentView
    private lateinit var liveSegment: LiveSegment

    override fun onCreateViewHolderCustom(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_RADIO -> {
                radioSegmentView = RadioSegmentView(parent.context, "", "", "Lihat Semua")
                radioSegmentView.setWidth()
                radioSegmentView.setExtraData(extraData)
                radioSegmentView.viewHolder
            }
            TYPE_AUDIO_BOOK -> {
                defaultSegmentView =
                    DefaultSegmentView(parent.context, "", "", "Lihat Semua")
                defaultSegmentView.setWidth()
                defaultSegmentView.shouldHandleAudioBook(true)
                defaultSegmentView.setExtraData(extraData)
                defaultSegmentView.viewHolder
            }
            TYPE_LIVE -> {
                liveSegment = LiveSegment(parent.context, "", "", "LiveLihatSemua")
                liveSegment.setWidth()
                liveSegment.setExtraData(extraData)
                liveSegment.viewHolder
            }
            else -> {
                defaultSegmentView =
                    DefaultSegmentView(parent.context, "", "", "Lihat Semua")
                defaultSegmentView.setWidth()
                defaultSegmentView.setExtraData(extraData)
                defaultSegmentView.viewHolder
            }
        }
    }

    override fun onBindViewHolderCustom(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is RadioSegmentView.ViewHolder -> {
                (holder.itemView as RadioSegmentView).let { radioSegmentView ->
                    radioSegmentView.viewHolder = holder
                    radioSegmentView.setData(dataSet[position] as HomeContent)
                    radioSegmentView.setEntityType((dataSet[position] as HomeContent).entityType)
                }
            }
            is LiveSegment.ViewHolder -> {
                (holder.itemView as LiveSegment).let { liveSegment ->
                    liveSegment.viewHolder = holder
                    liveSegment.setData(dataSet[position] as HomeContent, "lihat_semua")
                    liveSegment.setEntityType((dataSet[position] as HomeContent).entityType)
                }
            }
            is DefaultSegmentView.ViewHolder -> {
                (holder.itemView as DefaultSegmentView).let { defaultSegmentView ->
                    defaultSegmentView.viewHolder = holder
                    defaultSegmentView.setEntityType((dataSet[position] as HomeContent).entityType)

                    if (viewType == HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION) {
                        defaultSegmentView.setViewType(HomeInnerAdapter.TYPE_PERSONAL_RECOMMENDATION)
                    }

                    defaultSegmentView.setData(dataSet[position] as HomeContent, "lihat_semua")
                }
            }
        }
    }

    override fun getItemViewTypeCustom(position: Int): Int {
        return when ((dataSet[position] as HomeContent).category) {
            ENTITY_TYPE_RADIO -> {
                TYPE_RADIO
            }
            ENTITY_TYPE_AUDIO_BOOK -> {
                TYPE_AUDIO_BOOK
            }
            ENTITY_TYPE_LIVE_STREAM -> {
                TYPE_LIVE
            }
            else -> {
                TYPE_DEFAULT
            }
        }
    }
}