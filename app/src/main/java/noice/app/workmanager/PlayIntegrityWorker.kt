package noice.app.workmanager

import android.content.Context
import androidx.concurrent.futures.CallbackToFutureAdapter
import androidx.work.*
import com.google.android.play.core.integrity.IntegrityManagerFactory
import com.google.android.play.core.integrity.IntegrityTokenRequest
import com.google.common.util.concurrent.ListenableFuture
import com.google.gson.JsonObject
import noice.app.BaseApplication
import noice.app.model.generics.BaseModel
import noice.app.model.user.ChallengeResponse
import noice.app.model.user.IntegrityResponse
import noice.app.rest.ApiError
import noice.app.rest.ServerInterface
import noice.app.utils.PrefUtils
import noice.app.utils.Utils
import org.json.JSONObject

class PlayIntegrityWorker(
    context: Context,
    workerParams: WorkerParameters
) : ListenableWorker(context, workerParams) {

    private val userApiInterface = BaseApplication.commonEntryPoint.userApiInterface

    override fun startWork(): ListenableFuture<Result> = CallbackToFutureAdapter.getFuture {
        doWork(it)
    }

    private fun doWork(callback: CallbackToFutureAdapter.Completer<Result>) {

        val playServicesAvailable = Utils.checkPlayServices(applicationContext)
        if (playServicesAvailable) {
            fetchNonce(callback)
        } else {
            verifyToken(null, false, callback)
        }
    }

    private fun fetchNonce(
        callback: CallbackToFutureAdapter.Completer<Result>
    ) {
        BaseApplication.doServerCall({ userApiInterface.generateChallenge() },
            object : ServerInterface<BaseModel<ChallengeResponse>> {
                override fun onCustomError(e: ApiError) {
                    callback.set(Result.failure())
                }

                override fun onError(e: Throwable) {
                    callback.set(Result.failure())
                }

                override fun onSuccess(data: BaseModel<ChallengeResponse>, dataFromCache: Boolean) {
                    if (!data.data?.challenge.isNullOrEmpty()) {
                        fetchIntegrityToken(data.data?.challenge.toString(), callback)
                    } else {
                        callback.set(Result.failure())
                    }
                }
            })
    }

    private fun fetchIntegrityToken(
        nonce: String,
        callback: CallbackToFutureAdapter.Completer<Result>
    ) {
        val integrityManager = IntegrityManagerFactory.create(applicationContext)
        val integrityTokenResponse =
            integrityManager.requestIntegrityToken(
                IntegrityTokenRequest.builder()
                    .setNonce(nonce)
                    .build())
        integrityTokenResponse.addOnCompleteListener {
            if (it.isSuccessful) {
                verifyToken(it.result.token(), true, callback)
            } else {
                callback.set(Result.failure())
            }
        }
    }

    private fun verifyToken(
        token: String?,
        playServicesAvailable: Boolean,
        callback: CallbackToFutureAdapter.Completer<Result>
    ) {
        val request = JsonObject()
        request.addProperty("isSupported", playServicesAvailable)
        if (!token.isNullOrEmpty()) {
            request.addProperty("integrityToken", token)
        }

        BaseApplication.doServerCall({ userApiInterface.verifyIntegrity(request = request) },
            object : ServerInterface<BaseModel<IntegrityResponse>> {
                override fun onCustomError(e: ApiError) {
                    callback.set(Result.failure())
                }

                override fun onError(e: Throwable) {
                    callback.set(Result.failure())
                }

                override fun onSuccess(data: BaseModel<IntegrityResponse>, dataFromCache: Boolean) {
                    if (!data.data?.integrityFlag.isNullOrEmpty()) {
                        PrefUtils.userIntegrityState = data.data?.integrityFlag

                        BaseApplication.firebaseAnalytics.setUserProperty("integrityState",  data.data?.integrityFlag)
                        callback.set(Result.success())
                    } else {
                        callback.set(Result.failure())
                    }
                }
            })
    }

    companion object {
        /**
         * unique name of the worker
         */
        private val WORKER_NAME = PlayIntegrityWorker::class.java.simpleName

        /**
         * can be used to start start this worker operation from out side of the class
         */
        fun beginWork() = WorkManager.getInstance(BaseApplication.getBaseAppContext())
            .beginUniqueWork(
                WORKER_NAME,
                ExistingWorkPolicy.KEEP,
                OneTimeWorkRequest.from(PlayIntegrityWorker::class.java)
            ).enqueue()
    }
}