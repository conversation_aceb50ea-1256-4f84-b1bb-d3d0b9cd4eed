package noice.app.workmanager

import android.content.Context
import androidx.work.Worker
import androidx.work.WorkerParameters
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import noice.app.BaseApplication
import noice.app.modules.podcast.repository.ChannelPodcastApiRepository

class EventWorker (appContext: Context, workerParams: WorkerParameters)
    : Worker(appContext, workerParams) {

    override fun doWork(): Result {
        // Do the work here--in this case, upload the images.

        updateEvents()

        // Indicate whether the task finished successfully with the Result
        return Result.success()
    }

    private fun updateEvents() {
        CoroutineScope(Dispatchers.IO).launch {
            val eventList = BaseApplication.application.getAppDb().eventDao().getEventList()
            if (eventList.isNotEmpty()) {
                withContext(Dispatchers.Main) {
                    ChannelPodcastApiRepository().updateEvents(eventList)
                }
            }
        }
    }
}