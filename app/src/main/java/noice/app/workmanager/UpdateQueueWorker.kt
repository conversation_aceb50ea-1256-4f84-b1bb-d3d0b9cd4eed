package noice.app.workmanager

import android.content.Context
import androidx.concurrent.futures.CallbackToFutureAdapter
import androidx.lifecycle.MutableLiveData
import androidx.work.*
import com.google.common.util.concurrent.ListenableFuture
import noice.app.BaseApplication
import noice.app.enums.Priority
import noice.app.model.generics.BaseModel
import noice.app.modules.media.model.Queue
import noice.app.modules.media.model.QueueItem
import noice.app.modules.media.model.QueueObject
import noice.app.player.managers.QueueManager
import noice.app.rest.ApiError
import noice.app.rest.NetworkRequests
import noice.app.rest.Resource
import noice.app.rest.ServerInterface
import noice.app.utils.Constants.Companion.ENTITY_TYPE_PODCAST
import noice.app.utils.PrefUtils

/**
 * This worker is responsible to prepare and the save updated queue,
 * on server and on local storage (preferences, in our case),
 *
 * it job is scheduling from [BaseApplication.updateQueue],
 * and it is recommended to use only that function to invoke it.
 */
class UpdateQueueWorker(
    /**
     * @param context of the application or activity from which the signal needs to send
     */
    context: Context,
    /**
     * @param workerParams configuration, we can use to manage the job's behaviour
     */
    workerParams: WorkerParameters) : ListenableWorker(context, workerParams) {

    /**
     * Override this method to start your actual background processing. This method is called on
     * the main thread.
     *
     * The future will also be cancelled if this worker is stopped for any reason
     * See [ListenableWorker.onStopped]
     *
     * @return A [ListenableFuture] with the [Result] of the computation. If we
     * cancel this Future, WorkManager will treat this unit of work as failed.
     */
    override fun startWork(): ListenableFuture<Result> =
        CallbackToFutureAdapter.getFuture { doWork(it) }

    /**
     * this method will prepare the queue and push it to server
     *
     * @param callback The provided callback is invoked immediately inline. Any exceptions thrown by it will
     * fail the returned {@code Future}.
     */
    private fun doWork(callback: CallbackToFutureAdapter.Completer<Result>) {
        val list = QueueManager.queue
        if (list.isEmpty())
            return

        val current = list.find { it.priority == Priority.PLAYING.type }
        val currentPlaying = if (current?.isRadio == true) {
            QueueItem(current.catalogId)
        } else {
            QueueItem(current?.id)
        }

        val manual = list.filter { it.priority == Priority.MANUAL.type }

        val manualList = ArrayList<QueueItem>()
        for (manualObject in manual) {
            manualList.add(QueueItem((manualObject.id)))
        }

        val auto = list.filter { it.priority == Priority.AUTOMATIC.type }
        val autoList = ArrayList<QueueItem>()
        for (autoObject in auto) {
            autoList.add(QueueItem((autoObject.id)))
        }
        var titile = ""
        if (list.size > 0)
            titile = PrefUtils.queueTitle.toString()

        var entitySubtype = ENTITY_TYPE_PODCAST

        if (current != null) {
            entitySubtype = current.entitySubType ?: ""
        }

        val queueRequest = if (PrefUtils.isLoggedIn) {
            Queue(
                PrefUtils.userDetails?.id ?: "",
                QueueObject(
                    entitySubtype,
                    titile,
                    list[0].pageSource ?: "",
                    currentPlaying,
                    manualList,
                    autoList
                )
            )
        } else {
            Queue(
                PrefUtils.guestId ?: "",
                QueueObject(
                    entitySubtype,
                    titile,
                    list[0].pageSource ?: "",
                    currentPlaying,
                    manualList,
                    autoList
                )
            )
        }

        val mutableData = MutableLiveData<Resource<Any>>()

        BaseApplication.doServerCall({ NetworkRequests.updateQueue(queueRequest) },
            object : ServerInterface<BaseModel<Any>> {
                override fun onCustomError(e: ApiError) {
                    mutableData.value = Resource.error(e.message)
                    callback.set(Result.failure())
                }

                override fun onError(e: Throwable) {
                    mutableData.value = Resource.error(Resource.DEFAULT_MESSAGE, Exception(e))
                    callback.set(Result.failure())
                }

                override fun onSuccess(data: BaseModel<Any>, dataFromCache: Boolean) {
                    mutableData.value = Resource.success(data.data, dataFromCache)
                    callback.set(Result.success())
                }
            })
    }

    companion object {
        /**
         * unique name of the worker
         */
        private val WORKER_NAME = UpdateQueueWorker::class.java.simpleName

        /**
         * can be used to start start this worker operation from out side of the class
         */
        fun beginWork(): Operation {
            return WorkManager.getInstance(BaseApplication.application)
                .beginUniqueWork(
                    WORKER_NAME,
                    ExistingWorkPolicy.KEEP,
                    OneTimeWorkRequest.from(UpdateQueueWorker::class.java)
                ).enqueue()
        }
    }
}