package noice.app.utils

import noice.app.di.AppModule
import noice.app.domain.ToggleReminderUseCase
import noice.app.domain.profile.BlockUserUseCase
import noice.app.domain.profile.GetArtistCatalogsUseCase
import noice.app.domain.profile.GetGenreUseCase
import noice.app.domain.profile.GetProfileDataUseCase
import noice.app.domain.profile.GetUserActivitiesUseCase
import noice.app.domain.profile.GetUserDetailsByUsernameUseCase
import noice.app.domain.profile.GetUserPreferencesUseCase
import noice.app.domain.profile.PerformActionUseCase
import noice.app.domain.profile.ProfileUseCase
import noice.app.domain.profile.UpdateUserPreferencesUseCase
import noice.app.modules.dashboard.repository.DashboardApiRepository
import noice.app.modules.live.analytics.LiveAnalytics
import noice.app.modules.live.repository.LiveApiRepository
import noice.app.modules.onboarding.repository.OnBoardingApiRepository
import noice.app.modules.profile.repository.ProfileRepository

class Providers {

    val onBoardingRepository = OnBoardingApiRepository(
        AppModule.provideConfigService(),
        AppModule.provideUserService(),
        AppModule.provideCatalogService()
    )

    val profileRepository = ProfileRepository(
        AppModule.provideUserService(),
        AppModule.provideCatalogService(),
        AppModule.provideAggregatedService()
    )

    val dashboardRepository = DashboardApiRepository(
        AppModule.providePlaylistService(),
        AppModule.provideCatalogService()
    )
    val liveRepository = LiveApiRepository()

    val profileUseCase = ProfileUseCase(
        getUserDetailsByUserName = GetUserDetailsByUsernameUseCase(profileRepository),
        getUserActivities = GetUserActivitiesUseCase(profileRepository),
        getProfileData = GetProfileDataUseCase(profileRepository),
        getArtistCatalogs = GetArtistCatalogsUseCase(profileRepository),
        performAction = PerformActionUseCase(dashboardRepository),
        blockUser = BlockUserUseCase(profileRepository),
        updateUserPreferences = UpdateUserPreferencesUseCase(profileRepository),
        getUserPreferences = GetUserPreferencesUseCase(profileRepository),
        getGenre = GetGenreUseCase(onBoardingRepository),
        toggleReminderUseCase = ToggleReminderUseCase(liveRepository, LiveAnalytics())
    )

}