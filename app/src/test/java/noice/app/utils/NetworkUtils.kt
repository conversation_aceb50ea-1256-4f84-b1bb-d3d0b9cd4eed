package noice.app.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.LinkProperties
import android.net.Network
import android.net.NetworkCapabilities
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import noice.app.BaseApplication
import noice.app.R
import noice.app.workmanager.EventWorker
import okhttp3.Response
import org.json.JSONObject

object NetworkUtils {
    internal var isNetworkConnected: Boolean = false
    internal var networkType: String = "wifi"

    fun getDefaultIpAddresses(): String = ""

    fun isNetworkConnected(context: Context): Boolean = isNetworkConnected

    @JvmStatic
    fun getNetworkType(context: Context): String = networkType

    @RequiresApi(Build.VERSION_CODES.N)
    fun registerNetworkCallback(ctx: Context) {
        throw Exception("Not allowed from tests")
    }
}

fun enableNetwork(shouldEnable: Boolean) {
    NetworkUtils.isNetworkConnected = shouldEnable
}

fun switchNetworkAdapter(networkType: String) {
    NetworkUtils.networkType = networkType
}