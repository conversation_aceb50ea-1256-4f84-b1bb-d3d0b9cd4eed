package noice.app.rest

import okhttp3.ConnectionSpec
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object RetrofitBuilder {
    private lateinit var retrofit: Retrofit

    internal fun setup() {
        retrofit = Retrofit.Builder().baseUrl("https://xyz.com")
            .addConverterFactory(GsonConverterFactory.create())
            .callbackExecutor(Executors.newFixedThreadPool(10))
            .client(getBuilder())
            .build()
    }

    internal fun destroy() {}

    internal fun getRetrofit(baseUrl: String): Retrofit = retrofit

    private fun getBuilder(): OkHttpClient {
        val okHttpBuilder = OkHttpClient.Builder()
            .addInterceptor(CustomResponseInterceptor())
            .addInterceptor(MockInterceptor())
            .connectionSpecs(listOf(ConnectionSpec.CLEARTEXT, ConnectionSpec.COMPATIBLE_TLS))
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .callTimeout(30, TimeUnit.SECONDS)

        return okHttpBuilder.build()
    }
}