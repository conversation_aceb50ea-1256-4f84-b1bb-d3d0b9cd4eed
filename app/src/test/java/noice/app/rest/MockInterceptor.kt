package noice.app.rest

import noice.app.BuildConfig
import noice.app.utils.PrefUtils
import noice.app.utils.PrefUtils._executionCaseId
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import retrofit2.Invocation
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import java.io.File
import java.util.*

/**
 * This will help us to test our networking code
 */
class MockInterceptor : Interceptor {

    private fun getTemplateUrl(method: String, tag: Invocation?): String {
        val methodType = Class.forName("retrofit2.http.$method")
        var templateUrl = ""

        val annotation = tag?.method()
            ?.getAnnotation(methodType as Class<Annotation>)

        when (annotation) {
            is POST -> templateUrl = annotation.value
            is GET -> templateUrl = annotation.value
            is PUT -> templateUrl = annotation.value
            is DELETE -> templateUrl = annotation.value
        }
        return templateUrl
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        if (!BuildConfig.DEBUG) {
            throw IllegalAccessError(
                "MockInterceptor is only meant for Testing Purposes and " +
                        "bound to be used only with DEBUG mode"
            )
        }

        val request = chain.request()
        val method = request.method.uppercase()

        var templateUrl = getTemplateUrl(method, request.tag(Invocation::class.java))


        print("_executionCaseId$_executionCaseId")
        val responseFileName = "${method}_${
            templateUrl.replace("/", "_")
                .replace("{", "").replace("}", "")
        }${
            if (_executionCaseId != null && _executionCaseId != "") {
                "_${_executionCaseId}"
            } else {
                ""
            }
        }.json"

        val file = File("./src/mocks/responses/${responseFileName}")
        val sc = Scanner(file)

        // Declaring a string variable
        var responseString = ""

        while (sc.hasNextLine()) responseString += sc.nextLine() + "\n"

        return Response.Builder()
            .request(request)
            .code(200)
            .protocol(Protocol.HTTP_1_1)
            .message(responseString)
            .body(
                ResponseBody.create(
                    "application/json".toMediaTypeOrNull(),
                    responseString.toByteArray()
                )
            )
            .addHeader("content-type", "application/json")
            .build()
    }
}