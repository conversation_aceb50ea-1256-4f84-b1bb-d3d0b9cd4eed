package noice.app.profile.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import kotlinx.coroutines.test.runTest
import noice.app.model.user.User
import noice.app.modules.profile.fragment.userprofile.UserProfileViewModel
import noice.app.rest.ResponseStatus
import noice.app.utils.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.rules.TestRule

class ProfileViewModelTest {

    @get:Rule
    var rule: TestRule = InstantTaskExecutorRule()

    private val _provider: Providers by lazy { Providers() }
    private lateinit var profileViewModel: UserProfileViewModel

    @Before
    fun setup() {
        Environment.setup()
        profileViewModel = UserProfileViewModel(
            _provider.profileUseCase
        )
    }

    @Test
    fun `should receive error state on any io exception on user profile api`() = runTest {
        //enabling network
        enableNetwork(true)

        //executing api call with no cache
        var resource =
            profileViewModel.profileUseCase.getUserDetailsByUserName(userName = "", cachingConstant = "")
                .getOrAwaitValue(waitForThreads = 2)

        //check for any error
        assert(resource?.status == ResponseStatus.ERROR)
    }

    @Test
    fun `should receive network error on user profile api`() = runTest {
        //disabling network
        enableNetwork(false)

        //executing api call with no cache
        var resource =
            profileViewModel.profileUseCase.getUserDetailsByUserName(userName = "", cachingConstant = "")
                .getOrAwaitValue()

        //check for internet error
        assert(resource?.message?.toInt() == noice.app.R.string.this_action_requires_internet)
    }

    @Test
    fun `should receive loading state on user profile api`() = runTest {
        //enabling network
        enableNetwork(true)
        //executing api call with no cache
        var resource =
            profileViewModel.profileUseCase.getUserDetailsByUserName(userName = "", cachingConstant = "")
                .getOrAwaitValue()
        //check for loading state
        assert(resource?.status === ResponseStatus.LOADING)
    }

    @Test
    fun `should receive success response on user profile api`() = runTest {
        //enabling network
        enableNetwork(true)
        PrefUtils.token = "testing_token"

        //executing api call with no cache
        var resource =
            profileViewModel.profileUseCase.getUserDetailsByUserName(userName = "", cachingConstant = "")
                .getOrAwaitValue(waitForThreads = 2)

        //check for loading state
        assert(
            resource?.status == ResponseStatus.SUCCESS &&
                    (resource.data?.data as User).displayName == "Test User"
        )
    }

    @Test
    fun `should receive failure response on user profile api`() = runTest {
        //enabling network
        enableNetwork(true)

        PrefUtils._executionCaseId = "1"
        PrefUtils.token = "testing_token"

        //executing api call with no cache
        var resource =
            profileViewModel.profileUseCase.getUserDetailsByUserName(userName = "", cachingConstant = "")
                .getOrAwaitValue(waitForThreads = 2)

        //check for loading state
        assert(
            resource?.status == ResponseStatus.ERROR
        )
    }

    @After
    fun destroy() = Environment.destroy()
}