<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootTitleLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/headingLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="visible">

        <TextView
            android:id="@+id/headerText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_weight="1"
            android:ellipsize="end"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp32"
            android:includeFontPadding="false"
            android:fontFamily="@font/readex_pro_semi_bold"
            android:maxLines="1"
            android:textColor="@color/white88"
            android:textSize="@dimen/sp32"
            tools:text="Noice Comedy Series" />

        <TextView
            android:id="@+id/seeAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp8"
            android:background="@drawable/custom_ripple_bg_4dp"
            android:fontFamily="@font/readex_pro"
            android:lineSpacingExtra="-1sp"
            android:includeFontPadding="false"
            android:padding="@dimen/dp8"
            android:text="@string/see_more"
            android:textColor="@color/dull_yellow"
            android:textSize="@dimen/sp14"
            android:textStyle="normal"
            android:visibility="gone"
            tools:visibility="gone" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/image_lihat_semua"
            android:layout_width="@dimen/dp44"
            android:layout_height="@dimen/dp44"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp8"
            android:background="@drawable/custom_ripple_bg_4dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/ic_arrow_right_white" />

        <Spinner
            android:id="@+id/spinnerFilter"
            android:layout_width="@dimen/dp90"
            android:layout_height="@dimen/dp48"
            android:layout_marginEnd="@dimen/dp16"
            android:background="@drawable/border_white_radius_4dp"
            android:dropDownSelector="@drawable/ic_arrow_down"
            android:visibility="gone" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/headingLayoutImage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dp16"
        android:paddingEnd="@dimen/size_0dp"
        android:layout_marginTop="@dimen/dp10"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintHeader"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_marginEnd="@dimen/dp8"
            android:layout_toStartOf="@+id/image_lihat_semua_byl">

            <androidx.cardview.widget.CardView
                android:id="@+id/imgCardView"
                android:layout_width="@dimen/dp47"
                android:layout_height="@dimen/dp47"
                app:cardCornerRadius="@dimen/dp8"
                app:cardElevation="@dimen/size_0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/imgHeader"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:adjustViewBounds="true"
                    android:scaleType="centerCrop"
                    tools:srcCompat="@mipmap/ic_launcher" />
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/txtHeaderTitle"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp16"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/neutral_80"
                android:textSize="@dimen/sp12"
                android:includeFontPadding="false"
                android:fontFamily="@font/readex_pro"
                app:layout_constraintBottom_toTopOf="@+id/txtHeaderDesc"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imgCardView"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Musuh Masyarakat" />

            <TextView
                android:id="@+id/txtHeaderDesc"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="@font/readex_pro"
                android:maxLines="1"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/size_0dp"
                android:textColor="@color/white"
                android:textSize="@dimen/sp20"
                android:includeFontPadding="false"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/imgCardView"
                app:layout_constraintTop_toBottomOf="@+id/txtHeaderTitle"
                tools:text="Noice Comedy Series" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/image_lihat_semua_byl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:background="@drawable/custom_ripple"
            android:contentDescription="@string/app_name"
            android:paddingStart="@dimen/dp16"
            android:paddingTop="@dimen/dp8"
            android:paddingEnd="@dimen/dp16"
            android:paddingBottom="@dimen/dp8"
            android:src="@drawable/ic_arrow_right_white" />
    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:nestedScrollingEnabled="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />
</LinearLayout>