<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout
    android:id="@+id/pullToRefresh"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".modules.dashboard.home.fragment.RadioHomeFragment">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.android.car.ui.recyclerview.CarUiRecyclerViewImpl
            android:id="@+id/radioRecycler"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <noice.app.views.ErrorView
            android:id="@+id/errorView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:error_padding_bottom="@dimen/dp60"/>
    </FrameLayout>
</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>