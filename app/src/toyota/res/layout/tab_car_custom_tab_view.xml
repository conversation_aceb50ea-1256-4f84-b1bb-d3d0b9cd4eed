<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dp199"
    android:layout_height="@dimen/dp140">

<androidx.appcompat.widget.LinearLayoutCompat
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center">

    <ImageView
        android:id="@android:id/icon"
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_gravity="center"
        tools:src="@drawable/ic_home" />

    <TextView
        android:id="@android:id/text1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textSize="@dimen/sp28"
        android:textAppearance="@style/CarTabLayoutStyle"
        android:fontFamily="@font/readex_pro"
        android:includeFontPadding="false"
        android:textColor="@color/white50"
        android:gravity="center"
        tools:text="Hello World!"
        tools:textColor="@color/white80"/>
</androidx.appcompat.widget.LinearLayoutCompat>
</FrameLayout>