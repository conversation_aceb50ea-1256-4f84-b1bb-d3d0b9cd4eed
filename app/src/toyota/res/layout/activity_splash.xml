<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".modules.onboarding.activity.CustomSplashActivity"
    tools:ignore="MissingDefaultResource">


<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    >

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieView"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24"
        android:layout_height="match_parent"
        android:scaleType="center"
        android:adjustViewBounds="true"
        app:lottie_autoPlay="true"
        app:lottie_loop="false"
        app:lottie_repeatCount="0"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgSplash"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24"
        android:layout_height="match_parent"
        android:scaleType="centerInside"
        android:adjustViewBounds="true"
        android:visibility="visible"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>