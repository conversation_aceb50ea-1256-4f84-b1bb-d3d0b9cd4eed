<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/bottomSheetPlayer"
    android:layout_width="match_parent"
    android:clickable="true"
    android:layout_height="match_parent"
    app:behavior_peekHeight="@dimen/size_0dp"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imgBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:alpha="0.9"
        android:clickable="false" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/playerLayoutMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <FrameLayout
                android:id="@+id/toolbarPlayerHolder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

            <noice.app.views.CustomCarToolbarView
                android:id="@+id/toolbarPlayer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:customStreamType="audio"
                app:dropDownButtonVisibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:threeDotIconVisibility="visible"
                app:titleTextSize="@dimen/sp32" />

            </FrameLayout>

            <com.android.car.ui.recyclerview.CarUiRecyclerViewImpl
                android:id="@+id/rvQueuePlayer"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:elevation="@dimen/dp10"
                android:background="@color/black"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/toolbarPlayerHolder" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/playerViewPager"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="@dimen/size_0dp"
                app:layout_constraintBottom_toTopOf="@id/controllerLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/toolbarPlayerHolder" />

            <FrameLayout
                android:id="@+id/controllerLayout"
                android:layout_width="@dimen/size_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp142"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <noice.app.exoplayer.CarPlayerControllerView
                    android:id="@+id/playerControlView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:controller_layout_id="@layout/car_custom_player_view"
                    app:show_timeout="0"
                    tools:visibility="visible" />

                <noice.app.exoplayer.CarPlayerControllerView
                    android:id="@+id/playerControlViewRadio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:controller_layout_id="@layout/radio_controller_view"
                    app:show_timeout="0" />
            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>


    <noice.app.views.ErrorView
        android:id="@+id/errorViewPlayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/swipeScrim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black70"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/dp16"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:contentDescription="@string/app_name"
                android:src="@drawable/ic_swipe_left_white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp52"
                android:layout_marginTop="@dimen/dp18"
                android:layout_marginEnd="@dimen/dp52"
                android:gravity="center"
                android:lineHeight="@dimen/dp25"
                android:text="@string/swipe_left_to_change_other_content"
                android:textColor="@color/white_300"
                android:textSize="@dimen/sp18" />
        </LinearLayout>
    </FrameLayout>

</FrameLayout>