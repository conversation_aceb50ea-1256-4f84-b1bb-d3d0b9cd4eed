<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/black"
    android:paddingBottom="@dimen/dp22">

    <FrameLayout
        android:id="@+id/btnLayout"
        android:layout_marginTop="@dimen/dp8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="@dimen/dp84"
        android:layout_height="@dimen/dp84">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@id/exo_play"
            android:layout_width="@dimen/dp84"
            android:layout_height="@dimen/dp84"
            android:layout_gravity="center"
            android:adjustViewBounds="true"
            android:scaleType="fitXY"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_play_grey_white"
            android:background="@drawable/circuler_white_ripple"/>

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@id/exo_pause"
            android:layout_width="@dimen/dp84"
            android:layout_height="@dimen/dp84"
            android:contentDescription="@string/app_name"
            app:srcCompat="@drawable/ic_stop"
            android:scaleType="fitXY"
            android:background="@drawable/circuler_white_ripple"
            android:layout_gravity="center"
            android:visibility="gone"/>

        <View
            android:id="@+id/preClickPlayPause"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:focusable="true"/>

        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:padding="@dimen/dp3"
            android:indeterminateTint="@color/white"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>