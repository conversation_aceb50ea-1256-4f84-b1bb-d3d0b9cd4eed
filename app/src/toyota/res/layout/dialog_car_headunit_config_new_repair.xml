<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/black500"
    app:cardCornerRadius="@dimen/dp16">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp30">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgHeader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_car_new_repair"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txtDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readix_pro_bold"
            android:text="@string/is_new_headunit"
            android:textColor="@color/neutral_90"
            android:textSize="@dimen/sp22"
            android:layout_marginTop="@dimen/dp24"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imgHeader" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnNew"
            android:layout_width="@dimen/dp158"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:backgroundTint="@color/white"
            android:fontFamily="@font/readix_pro_bold"
            android:insetLeft="@dimen/size_0dp"
            android:insetTop="@dimen/size_0dp"
            android:insetRight="@dimen/size_0dp"
            android:insetBottom="@dimen/size_0dp"
            android:text="@string/iya"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="@dimen/sp16"
            android:textStyle="bold"
            app:cornerRadius="@dimen/dp14"
            android:letterSpacing="0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtDesc" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnReplacement"
            android:layout_width="@dimen/dp158"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/white"
            android:layout_marginStart="@dimen/dp16"
            android:fontFamily="@font/readix_pro_bold"
            android:insetLeft="@dimen/size_0dp"
            android:insetTop="@dimen/size_0dp"
            android:insetRight="@dimen/size_0dp"
            android:insetBottom="@dimen/size_0dp"
            android:textStyle="normal"
            android:letterSpacing="0"
            android:text="@string/tidak"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="@dimen/sp16"
            app:cornerRadius="@dimen/dp14"
            app:layout_constraintBottom_toBottomOf="@+id/btnNew"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnNew"
            app:layout_constraintTop_toTopOf="@+id/btnNew" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/layoutProgress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/overlay"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            app:trackThickness="@dimen/dp2"
            android:indeterminateTint="@color/dull_yellow"
            app:indicatorSize="@dimen/dp10"
            android:layout_gravity="center"
            app:layout_constraintBottom_toBottomOf="@+id/img_cover"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/img_cover" />

    </FrameLayout>


</com.google.android.material.card.MaterialCardView>