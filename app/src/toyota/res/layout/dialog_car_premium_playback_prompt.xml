<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/black500"
    app:cardCornerRadius="@dimen/dp16">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp30">

        <TextView
            android:id="@+id/txtTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readix_pro_bold"
            android:text="@string/content_is_locked"
            android:textColor="@color/white"
            android:textSize="@dimen/sp36"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/txtDesc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/readex_pro"
            android:textSize="@dimen/sp28"
            android:gravity="center"
            android:text="@string/content_lock_desc"
            android:textColor="@color/neutral_90"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/txtTitle" />


        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnOkay"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:backgroundTint="@color/white"
            android:fontFamily="@font/readix_pro_bold"
            android:insetLeft="@dimen/size_0dp"
            android:insetTop="@dimen/size_0dp"
            android:insetRight="@dimen/size_0dp"
            android:insetBottom="@dimen/size_0dp"
            android:text="@string/oke"
            android:textColor="@color/black"
            android:textSize="@dimen/sp22"
            android:textAllCaps="false"
            android:textStyle="bold"
            app:cornerRadius="@dimen/dp14"
            android:letterSpacing="0"
            app:layout_constraintEnd_toEndOf="@+id/txtDesc"
            app:layout_constraintStart_toStartOf="@+id/txtDesc"
            app:layout_constraintTop_toBottomOf="@+id/txtDesc" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</com.google.android.material.card.MaterialCardView>