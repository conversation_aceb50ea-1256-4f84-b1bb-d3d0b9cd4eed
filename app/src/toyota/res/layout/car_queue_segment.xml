<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rvQueue"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp32"
    android:paddingVertical="@dimen/dp20"
    style="@style/SelectableItemForeground"
    tools:background="@color/black">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <noice.app.views.SquareCardView
            android:id="@+id/squareCardView3"
            android:layout_width="@dimen/dp76"
            android:layout_height="@dimen/dp76"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="@dimen/dp4"
            app:cardElevation="@dimen/size_0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:id="@+id/audioBookLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/imgPremiumCarLock"
                    android:layout_width="@dimen/dp24"
                    android:layout_height="@dimen/dp24"
                    android:contentDescription="@string/app_name"
                    android:elevation="@dimen/dp2"
                    android:visibility="gone"
                    android:layout_gravity="end"
                    app:layout_constraintEnd_toEndOf="@+id/coverImage"
                    app:layout_constraintTop_toTopOf="@+id/coverImage"
                    app:srcCompat="@drawable/ic_premium_car_lock"
                    tools:visibility="visible" />

                <ImageView
                    android:id="@+id/coverBackgroundImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/app_name"
                    android:scaleType="centerCrop" />


                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_margin="@dimen/dp6"
                    app:cardBackgroundColor="@color/transparent"
                    app:cardCornerRadius="@dimen/dp8"
                    app:cardElevation="@dimen/size_0dp">

                    <ImageView
                        android:id="@+id/coverImageAudioBook"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:adjustViewBounds="true"
                        android:contentDescription="@string/app_name" />
                </androidx.cardview.widget.CardView>
            </FrameLayout>

            <ImageView
                android:id="@+id/coverImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/app_name" />

            <ImageView
                android:id="@+id/origExc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:contentDescription="@string/original"
                android:layout_marginStart="2dp"
                android:layout_marginBottom="2dp"
                android:src="@drawable/ic_noice_original"
                android:visibility="gone" />
        </noice.app.views.SquareCardView>

        <TextView
            android:id="@+id/title"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp52"
            android:ellipsize="end"
            android:fontFamily="@font/readex_pro_regular"
            android:includeFontPadding="false"
            android:lineSpacingExtra="@dimen/sp3"
            android:maxLines="1"
            tools:text="Eps  9: Berdoa Tidak Ada Guna..."
            android:textColor="@color/white88"
            android:textSize="@dimen/sp32"
            app:layout_constraintBottom_toTopOf="@+id/subTitle"
            app:layout_constraintEnd_toStartOf="@+id/relative_equalizer"
            app:layout_constraintStart_toEndOf="@+id/squareCardView3"
            app:layout_constraintTop_toTopOf="@+id/squareCardView3"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/subTitle"
            android:layout_width="@dimen/size_0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="@font/readex_pro_regular"
            android:includeFontPadding="false"
            android:lineSpacingExtra="-3.6sp"
            android:maxLines="1"
            android:textColor="@color/neutral_80"
            android:textSize="@dimen/sp24"
            app:layout_constraintBottom_toBottomOf="@+id/squareCardView3"
            app:layout_constraintEnd_toEndOf="@+id/title"
            app:layout_constraintStart_toStartOf="@+id/title"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:text="Musuh Masyarakat" />


        <FrameLayout
            android:id="@+id/relative_equalizer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp32"
            android:background="@color/black30"
            app:layout_constraintBottom_toBottomOf="@+id/squareCardView3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/squareCardView3">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/equalizer"
                android:layout_width="@dimen/dp60"
                android:layout_height="@dimen/dp60"
                android:layout_gravity="center"
                android:contentDescription="@string/app_name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/equalizer" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>