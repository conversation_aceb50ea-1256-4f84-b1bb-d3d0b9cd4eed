<?xml version="1.0" encoding="utf-8"?>
<defaultsMap>
    <entry>
        <key>is_force_update</key>
        <value>false</value>
    </entry>
    <entry>
        <key>android_should_enter_as_guest</key>
        <value>true</value>
    </entry>
    <entry>
        <key>noiceLiveConfig</key>
    </entry>
    <entry>
        <key>android_app_update_config</key>
        <value>
            {
            "forcedLogout": false,
            "minForceUpdateVersionCode": 100,
            "maxForceUpdateVersionCode": 100
            }
        </value>
    </entry>
    <entry>
        <key>jwt_expiry_time</key>
        <value>90</value>
    </entry>
    <entry>
        <key>max_live_chat_delay_factor</key>
        <value>2</value>
    </entry>
    <entry>
        <key>app_config</key>
        <value>
            {
            "caching_config": {
            "min_free_memory_mb": 1024,
            "total_content_caching_mb": 500,
            "total_image_caching_mb": 250
            },
            "app_update_config": {
            "android": {
            "forcedLogout": false,
            "minForceUpdateVersionCode": 40,
            "maxForceUpdateVersionCode": 159,
            "current_app_version_code": 240,
            "app_icon_id": 1,
            "liveStreamUpdateVersionCode": 225,
            "liveRadioUpdateVersionCode": 225
            },
            "ios": {
            "shouldDoForceLogout": false,
            "forceUpdateAppVersion": "3.12",
            "minForceUpdateAppVersion": "2.0.0",
            "maxForceUpdateAppVersion": "3.12",
            "liveStreamUpdateAppVersion": "3.29",
            "liveRadioUpdateAppVersion": "3.29",
            "currentVersionOnAppStore": "3.31"
            }
            },
            "app_links": {
            "premium_faq":
            "https://noiceid.zendesk.com/hc/en-us/categories/10775255663641-Coin-dan-Diamond?"
            },
            "noice_live_room_config": {
            "room_participant_limit": 10000,
            "live_room_max_life_span": 24,
            "is_live_chat_enabled": true,
            "max_live_chat_delay_factor": 10,
            "live_room_max_chat_store_limit": 500,
            "rejoin_flow_enabled": true
            },
            "auth_config": {
            "jwt_expiry_time": 90,
            "enter_as_guest": true
            },
            "common_config": {
            "show_creator_studio_button": true,
            "playback_timeout": 180
            },
            "app_launch_flow_config": {
            "enterAsGuestByDefault": true,
            "min_app_version": "3.0.0"
            },
            "share_config": {
            "enable_appsflyer_deferred_deep_link_android": false,
            "enable_appsflyer_deferred_deep_link_ios": false
            },
            "live_config": {
            "cipherKey": "QfTjWnZr4t7w!z%C",
            "liveChatMaxCharLimit": 360,
            "reactionOnScreenMaxCount": 100,
            "reactionIntervalMillis": 1000
            },
            "top_bar_navigation": {
            "enableLiveTab": false
            }
            }
        </value>
    </entry>
    <entry>
        <key>similar_catalog_variant_aos</key>
        <value>
            {
            "experimentName": "similar-catalog-experiment",
            "variant": "b"
            }
        </value>
    </entry>
    <entry>
        <key>quick_picks_variant_aos</key>
        <value>
            {
            "experimentName": "quick-picks-experiment",
            "variant": "b"
            }
        </value>
    </entry>
    <entry>
        <key>genre_based_top_podcast_variant_aos</key>
        <value>
            {
            "experimentName": "genre-based-top-podcast-experiment",
            "variant": "a"
            }
        </value>
    </entry>
    <entry>
        <key>noice_lite_variant_aos</key>
        <value>
            {
            "experimentName": "noice-lite-experiment",
            "variant": "a"
            }
        </value>
    </entry>
</defaultsMap>