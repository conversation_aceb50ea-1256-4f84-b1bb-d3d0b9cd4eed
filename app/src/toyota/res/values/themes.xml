<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="CarTabLayoutStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">@dimen/sp28</item>
        <item name="textAllCaps">false</item>
        <item name="fontFamily">@font/readex_pro_regular</item>
    </style>

    <style name="ShapeAppearanceOverlay.App.Round16" parent="">
        <item name="cornerSize">@dimen/dp16</item>
    </style>

    <style name="SelectableItemTheme">
        <item name="colorControlHighlight">@color/white50</item>
    </style>


    <style name="SelectableItemBackground">
        <item name="android:theme">@style/SelectableItemTheme</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
    </style>

    <style name="SelectableItemThemeForeground">
        <item name="colorControlHighlight">@color/white30</item>
    </style>


    <style name="SelectableItemForegroundBounded">
        <item name="android:theme">@style/SelectableItemThemeForeground</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
    </style>

    <style name="SelectableItemForeground">
        <item name="android:theme">@style/SelectableItemTheme</item>
        <item name="android:foreground">?attr/selectableItemBackgroundBorderless</item>
    </style>

</resources>