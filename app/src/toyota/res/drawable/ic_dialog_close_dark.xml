<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M24,24m-24,0a24,24 0,1 1,48 0a24,24 0,1 1,-48 0"
      android:fillColor="#333333"/>
  <group>
    <clip-path
        android:pathData="M8,8h32v32h-32z"/>
    <path
        android:pathData="M13.334,10.667C11.861,10.667 10.667,11.861 10.667,13.334V34.667C10.667,34.906 10.698,35.138 10.757,35.358L19.021,27.094H16.387L15.561,29.334H13.334L17.445,18.667H19.448L21.662,24.454L25.279,20.836V18.667H27.448L35.358,10.757C35.138,10.698 34.906,10.667 34.667,10.667H13.334ZM37.243,12.643L31.037,18.85C31.252,18.907 31.461,18.978 31.662,19.063C32.29,19.317 32.828,19.683 33.277,20.16C33.725,20.628 34.069,21.191 34.308,21.852C34.547,22.502 34.667,23.218 34.667,24C34.667,24.793 34.547,25.514 34.308,26.164C34.069,26.814 33.725,27.378 33.277,27.856C32.828,28.323 32.29,28.689 31.662,28.953C31.044,29.207 30.352,29.334 29.584,29.334H25.279V24.607L22.705,27.181L23.529,29.334H21.242L21.056,28.831L12.643,37.243C12.863,37.302 13.095,37.334 13.334,37.334H34.667C36.14,37.334 37.334,36.14 37.334,34.667V13.334C37.334,13.095 37.302,12.863 37.243,12.643ZM27.521,27.277V22.365L29.162,20.724H29.51C29.958,20.724 30.357,20.8 30.706,20.953C31.064,21.105 31.368,21.329 31.618,21.623C31.877,21.908 32.071,22.253 32.201,22.659C32.33,23.056 32.395,23.503 32.395,24C32.395,24.498 32.33,24.95 32.201,25.357C32.071,25.753 31.877,26.098 31.618,26.393C31.368,26.677 31.064,26.896 30.706,27.048C30.357,27.2 29.958,27.277 29.51,27.277H27.521ZM19.727,25.235L19.119,23.589C19.059,23.416 18.984,23.203 18.895,22.949C18.815,22.695 18.73,22.426 18.64,22.141C18.557,21.877 18.478,21.626 18.403,21.388L18.401,21.395C18.331,21.638 18.252,21.892 18.162,22.156C18.082,22.421 17.998,22.675 17.908,22.918C17.828,23.162 17.749,23.396 17.669,23.619L17.073,25.235H19.727Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24"
            android:startY="10.667"
            android:endX="24"
            android:endY="37.334"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFDB99"/>
          <item android:offset="1" android:color="#FFD8AB55"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M36.806,30.388C37.323,29.871 38.16,29.871 38.677,30.388L39.612,31.323C40.129,31.84 40.129,32.677 39.612,33.194L31.194,41.612C30.677,42.129 29.84,42.129 29.323,41.612L28.388,40.677C27.871,40.16 27.871,39.323 28.388,38.806L36.806,30.388Z"
      android:fillColor="#FF3939"/>
  <path
      android:pathData="M39.612,38.806C40.129,39.323 40.129,40.16 39.612,40.677L38.677,41.612C38.16,42.129 37.323,42.129 36.806,41.612L28.388,33.194C27.871,32.677 27.871,31.84 28.388,31.323L29.323,30.388C29.84,29.871 30.677,29.871 31.194,30.388L39.612,38.806Z"
      android:fillColor="#FF3939"/>
</vector>
