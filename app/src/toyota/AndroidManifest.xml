<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk tools:overrideLibrary="com.android.car.ui" />

    <uses-feature
        android:name="android.hardware.type.automotive"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.portrait"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.screen.landscape"
        android:required="false" />

    <application android:icon="${appIcon}"
        android:appCategory="audio">
        <activity
            android:name=".modules.dashboard.CarDashBoardActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="${screenOrientation}"
            android:configChanges="uiMode"
            android:windowSoftInputMode="adjustNothing">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.media.intent.action.MEDIA_BUTTON" />
            </intent-filter>
            <meta-data android:name=
                "distractionOptimized" android:value="true"/>
        </activity>

        <meta-data android:name="com.android.automotive"
            android:resource="@xml/automotive_app_desc"/>

    </application>
</manifest>