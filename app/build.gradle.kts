plugins {
    alias(libs.plugins.noice.android.application)
    alias(libs.plugins.noice.android.application.signing)
    alias(libs.plugins.noice.android.application.flavors)
    alias(libs.plugins.noice.android.application.build.types)
    alias(libs.plugins.noice.android.application.packaging)
    alias(libs.plugins.noice.android.application.toyota.configuration)
    alias(libs.plugins.noice.android.application.dependencies.firebase)
    alias(libs.plugins.noice.android.application.dependencies.kapts)
    alias(libs.plugins.noice.android.application.dependencies.compose)
    alias(libs.plugins.noice.android.application.dependencies.hilt)
    alias(libs.plugins.noice.android.application.dependencies.room)
    id("com.techknights.keyninja")
    alias(libs.plugins.android.application)
    alias(libs.plugins.baselineprofile)
}

android {
    defaultConfig {
        applicationId = libs.versions.appId.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled = true

        vectorDrawables {
            useSupportLibrary = true
        }

        renderscriptTargetApi = libs.versions.appRenderscript.get().toInt()
        renderscriptSupportModeEnabled = true

    }

    namespace = libs.versions.appId.get()
}

keyNinja {
    keyJsonVersion = 6
    keyJsonPath = rootDir.path + "/keys.json"
}

dependencies {

    implementation(libs.androidx.profileinstaller)
    "baselineProfile"(project(":baselineprofile"))
    runtimeOnly(libs.jsonwebtoken.impl)
    runtimeOnly(libs.jsonwebtoken.orgjson) {
        exclude(group = "org.json", module = "json")
    }

    implementation(libs.bundles.androidx.major)
    implementation(libs.bundles.androidx.ui)
    implementation(libs.bundles.androidx.lifecycle)
    implementation(libs.bundles.androidx.camera)

    implementation(libs.bundles.http)
    implementation(libs.glide.okhttp3) {
        exclude(group = "glide-parent")
    }

    implementation(libs.bundles.others)
    implementation(libs.bundles.google)
    implementation(libs.bundles.others)
    implementation(libs.bundles.exoplayer)

    toyotaImplementation(libs.bundles.toyota)

    testImplementation(libs.bundles.android.unit.testing)
    androidTestImplementation(libs.bundles.android.ui.testing)
    implementation(libs.androidx.media3.session)
    implementation(libs.androidx.media3.exoplayer)
    implementation(libs.androidx.media3.datasource.cronet)
    implementation(libs.androidx.media3.exoplayer.ima)
    implementation(libs.androidx.media3.cast)
    implementation(libs.androidx.media3.ui)
    implementation(libs.androidx.media3.exoplayer.hls)
    implementation(libs.androidx.media3.common)
    implementation(libs.androidx.media3.transformer)
    implementation(libs.androidx.media3.exoplayer.rtsp)
    implementation(libs.androidx.media3.exoplayer.dash)
}
