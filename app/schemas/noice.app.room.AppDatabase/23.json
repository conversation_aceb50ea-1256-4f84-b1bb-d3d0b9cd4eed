{"formatVersion": 1, "database": {"version": 23, "identityHash": "34e1017be53538e79c52c705722a7166", "entities": [{"tableName": "Downloads", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `contentId` TEXT NOT NULL, `catalogId` TEXT, `catalogType` TEXT, `createdAt` TEXT, `description` TEXT, `duration` INTEGER, `id` TEXT, `isActive` INTEGER, `publishedAt` TEXT, `title` TEXT, `type` TEXT, `source` TEXT, `url` TEXT, `catalogTitle` TEXT, `contentNumber` INTEGER, `tags` TEXT, `htmlDescription` TEXT, `entityType` TEXT, `price` INTEGER, `isPremium` INTEGER, `hasPurchased` INTEGER, `isDownloadable` INTEGER, `earlyAccessBenefits` TEXT, `earlyAccessDescription` TEXT, `earlyAccessFinishDate` TEXT, `videoUrl` TEXT, `downloadTime` INTEGER, `entitySubType` TEXT, `displayAds` INTEGER, `isVideoPremium` INTEGER, `showVideo` INTEGER, `hasVideoAccess` INTEGER, `prerollEnabled` INTEGER, `midrollEnabled` INTEGER, `displayAdsEnabled` INTEGER, `isSubscriptionAvailable` INTEGER, `size300` TEXT, `size500` TEXT, `size40` TEXT, `original` TEXT, `entityId` TEXT, `timeElapsed` INTEGER, `markedAsPlayed` INTEGER, `totalDuration` INTEGER, `contentCount` INTEGER, `state` INTEGER, `cuePointsCount` INTEGER, `showCue` INTEGER, `listeningTime` INTEGER, `comments` INTEGER, `dislikes` INTEGER, `followers` INTEGER, `likes` INTEGER, `listens` INTEGER, PRIMARY KEY(`userId`, `contentId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentId", "columnName": "contentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content.catalogId", "columnName": "catalogId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.catalogType", "columnName": "catalogType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.duration", "columnName": "duration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.id", "columnName": "id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.publishedAt", "columnName": "publishedAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.source", "columnName": "source", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.url", "columnName": "url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.catalogTitle", "columnName": "catalogTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.contentNumber", "columnName": "contentNumber", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.tags", "columnName": "tags", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.htmlDescription", "columnName": "htmlDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.entityType", "columnName": "entityType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.price", "columnName": "price", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.isPremium", "columnName": "isPremium", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.hasPurchased", "columnName": "hasPurchased", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.isDownloadable", "columnName": "isDownloadable", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.earlyAccessBenefits", "columnName": "earlyAccessBenefits", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.earlyAccessDescription", "columnName": "earlyAccessDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.earlyAccessFinishDate", "columnName": "earlyAccessFinishDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.videoUrl", "columnName": "videoUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.downloadTime", "columnName": "downloadTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.entitySubType", "columnName": "entitySubType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.displayAds", "columnName": "displayAds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.isVideoPremium", "columnName": "isVideoPremium", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.showVideo", "columnName": "showVideo", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.hasVideoAccess", "columnName": "hasVideoAccess", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.prerollEnabled", "columnName": "prerollEnabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.midrollEnabled", "columnName": "midrollEnabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.displayAdsEnabled", "columnName": "displayAdsEnabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.isSubscriptionAvailable", "columnName": "isSubscriptionAvailable", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.imageMeta.size300", "columnName": "size300", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.imageMeta.size500", "columnName": "size500", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.imageMeta.size40", "columnName": "size40", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.imageMeta.original", "columnName": "original", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.meta.entityId", "columnName": "entityId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content.meta.timeElapsed", "columnName": "timeElapsed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.markedAsPlayed", "columnName": "markedAsPlayed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.totalDuration", "columnName": "totalDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.contentCount", "columnName": "contentCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.state", "columnName": "state", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.cuePointsCount", "columnName": "cuePointsCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.showCue", "columnName": "showCue", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.listeningTime", "columnName": "listeningTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.comments", "columnName": "comments", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.dislikes", "columnName": "dislikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.followers", "columnName": "followers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.likes", "columnName": "likes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "content.meta.aggregations.listens", "columnName": "listens", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["userId", "contentId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "eventpost", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`event` TEXT NOT NULL, `contentId` TEXT NOT NULL, `userId` TEXT NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `durationPlayed` INTEGER, `entityId` TEXT, `entityType` TEXT, `entitySubType` TEXT, `pageSource` TEXT, `sessionId` TEXT, `totalDuration` INTEGER, `listeningTime` INTEGER, `createdAt` TEXT, `catalogId` TEXT)", "fields": [{"fieldPath": "event", "columnName": "event", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentId", "columnName": "contentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "payload.durationPlayed", "columnName": "durationPlayed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.entityId", "columnName": "entityId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.entityType", "columnName": "entityType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.entitySubType", "columnName": "entitySubType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.pageSource", "columnName": "pageSource", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.totalDuration", "columnName": "totalDuration", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.listeningTime", "columnName": "listeningTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.createdAt", "columnName": "createdAt", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.catalogId", "columnName": "catalogId", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "LocalCacheData", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`cachingConstant` TEXT NOT NULL, `data` TEXT NOT NULL, PRIMARY KEY(`cachingConstant`))", "fields": [{"fieldPath": "cachingConstant", "columnName": "cachingConstant", "affinity": "TEXT", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["cachingConstant"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "event_activation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` TEXT NOT NULL, `d1_30_reached` INTEGER NOT NULL, `aha_reached` INTEGER NOT NULL, `habit_reached` INTEGER NOT NULL, `listenCount` INTEGER NOT NULL, `tslMin` INTEGER NOT NULL, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "d1_30_reached", "columnName": "d1_30_reached", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "aha_reached", "columnName": "aha_reached", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "habit_reached", "columnName": "habit_reached", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "listenCount", "columnName": "listenCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tslMin", "columnName": "tslMin", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["userId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '34e1017be53538e79c52c705722a7166')"]}}