pipeline {
    agent { label 'mac_mini' }

    environment {
        FIREBASE_APP_ID = '1:363855535132:android:afa78d23de47d9453ed3d9' // Project Noice Firebase -> Noice Prod
        FIREBASE_GROUP = 'qa-team'
        ANDROID_HOME = '/opt/homebrew/share/android-commandlinetools' // Path to Android SDK
        ARTIFACT_PATH = '**app/build/outputs/apk/production/debug/*.apk'
        KEYCHAIN_PATH = '/Users/<USER>/Library/Keychains/login.keychain-db'
        // WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAoytLEO4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cYNlO_Zb_wCCu5hWZimznj34O2hxdBJ8gQBb1t3blPE' // webhook alert-test
        WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAueY_CYI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cGL46j_40Ty9SP172bfnW5brelW757U1bAieH5_ITCQ' // webhook android-builds
    }

    stages {
        stage('Get Commit Info') {
            steps {
                git branch: '${BRANCH_NAME}', credentialsId: 'jenkins-github', url: '**************:noiceid/noice-android.git'
                script {
                    env.GIT_COMMIT_MESSAGE = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
                    env.GIT_AUTHOR = sh(returnStdout: true, script: 'git log -1 --pretty=%an').trim()
                }
            }
        }

        // stage('Install Dependency') {
        //     steps {
        //         sh 'bundle install'
        //         sh 'bundle add abbrev'
        //         sh 'bundle update httpclient'
        //         sh 'bundle update fastlane-plugin-firebase_app_distribution'
        //     }
        // }

        stage('Fastlane Build') {
            steps {
                withCredentials([
                    string(credentialsId: '75ed1097-f89e-4e0b-84b0-0cb08ba4a406', variable: 'NOICE_KEYS'),
                    string(credentialsId: 'ce21d274-a719-489d-aba6-3caa54a9284a', variable: 'PASSWORD_KEYCHAIN'),
                ]) {
                    script {
                        sh 'security unlock-keychain -p "${PASSWORD_KEYCHAIN}" "${KEYCHAIN_PATH}"'
                        sh 'echo $NOICE_KEYS | base64 --decode > ./keys.json'
                        echo 'Running Fastlane build...'
                        sh 'fastlane build'
                    }
                }
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Build APK Failed', '#FF5252')
                }
            }
        }
        
        stage('Fastlane Deploy') {
            steps {
                withCredentials([
                    file(credentialsId: 'f22743b2-0ca8-44de-a11a-4d8d98e24ef1', variable: 'GOOGLE_APPLICATION_CREDENTIALS')
                ]) {
                    script {
                        echo 'Running Fastlane deploy...'
                        def firebaseOutput = sh(
                            script: 'fastlane deploy_to_firebase 2>&1',
                            returnStdout: true
                        ).trim()

                        echo "Firebase Output: ${firebaseOutput}"

                        def consoleUrlMatch = firebaseOutput =~ /View this release in the Firebase console: (https?:\/\/[^\s]+)/
                        def shareUrlMatch = firebaseOutput =~ /Share this release with testers who have access: (https?:\/\/[^\s]+)/

                        def consoleUrl = consoleUrlMatch ? consoleUrlMatch[0][1] : null
                        def shareUrl = shareUrlMatch ? shareUrlMatch[0][1] : null

                        if (consoleUrl && shareUrl) {
                            echo "Console URL: ${consoleUrl}"
                            echo "Share URL: ${shareUrl}"

                            env.CONSOLE_URL = consoleUrl
                            env.SHARE_URL = shareUrl
                        } else {
                            error "Failed to extract release URLs from Fastlane output"
                        }
                    }
                }
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Deployment to Firebase Failed', '#FF5252')
                }
            }
        }
    }

    post {
        success {
            sendGoogleChatNotification('SUCCESS', 'Deploy to Firebase Success', '#60a327')
            echo 'APK deployed successfully!'
        }
        failure {
            echo 'APK deployment failed!'
        }
        always {
            sh 'rm -f ./keys.json'
        }
    }
}

// Function to send Google Chat notifications
def sendGoogleChatNotification(String title, String message, String color) {
    def payload = """{
    "cardsV2": [
      {
        "cardId": "unique-card-id",
        "card": { 
          "header": {
            "title": "Build Status Android Apps",
            "subtitle": "Noice Mobile",
            "imageUrl": "https://s3-us-west-2.amazonaws.com/slack-files2/bot_icons/2022-06-17/3684546022083_48.png",
            "imageType": "SQUARE"
          },
          "sections": [
            {
              "collapsible": false,
              "uncollapsibleWidgetsCount": 1,
              "widgets": [
                {
                  "textParagraph": {
                    "text": "<font color=\\\"${color}\\\"><b>${title}</b></font>"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "PERSON"
                    },
                    "topLabel": "Author",
                    "text": "${env.GIT_AUTHOR}"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "DESCRIPTION"
                    },
                    "topLabel": "Release Notes",
                    "text": "${env.GIT_COMMIT_MESSAGE}"
                  }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "BOOKMARK"
                      },
                      "topLabel": "Branch",
                      "text": "${env.BRANCH_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "MAP_PIN"
                      },
                      "topLabel": "Job Name",
                      "text": "${env.JOB_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "CONFIRMATION_NUMBER_ICON"
                      },
                      "topLabel": "Message",
                      "text": "${message}"
                    }
                }, 
                {
                  "buttonList": {
                    "buttons": [
                      {
                        "text": "View Release in Firebase",
                        "onClick": {
                          "openLink": {
                            "url": "${env.CONSOLE_URL}"
                          }
                        }
                      },
                      {
                        "text": "Download for Tester Device",
                        "onClick": {
                          "openLink": {
                            "url": "${env.SHARE_URL}"
                          }
                        }
                      },
                      {
                        "text": "Jenkins Job Logs",
                        "onClick": {
                          "openLink": {
                            "url": "https://www.google.com/url?q=${env.BUILD_URL}/console"
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
          ]
        }
      }
    ]
  }"""

    sh """
        curl -X POST -H 'Content-Type: application/json' -d '${payload}' '${WEBHOOK_URL}'
    """
}