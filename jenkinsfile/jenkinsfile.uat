pipeline {
    agent { label 'mac_mini' }

    environment {
        FIREBASE_APP_ID = '1:15236264899:android:2d3ed3538da654a6d74376' // Project Noice UAT -> Noice QA
        FIREBASE_GROUP = 'qa-team'
        ANDROID_HOME = '/opt/homebrew/share/android-commandlinetools' // Path to Android SDK
        ARTIFACT_PATH = '**app/build/outputs/apk/qa/debug/*.apk'
        KEYCHAIN_PATH = '/Users/<USER>/Library/Keychains/login.keychain-db'
        // WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAoytLEO4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cYNlO_Zb_wCCu5hWZimznj34O2hxdBJ8gQBb1t3blPE' // webhook alert-test
        WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAueY_CYI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cGL46j_40Ty9SP172bfnW5brelW757U1bAieH5_ITCQ' // webhook android-builds
    }

    stages {
        stage('Get Commit Info') {
            steps {
                // Create variable from git, parse to google chat notification 
                script {
                    env.GIT_COMMIT_MESSAGE = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
                    env.GIT_AUTHOR = sh(returnStdout: true, script: 'git log -1 --pretty=%an').trim()
                }
            }
        }

        stage('Build APK') {
            steps {
                withCredentials([string(credentialsId: '75ed1097-f89e-4e0b-84b0-0cb08ba4a406', variable: 'NOICE_KEYS')]) {
                    withCredentials([string(credentialsId: 'ce21d274-a719-489d-aba6-3caa54a9284a', variable: 'PASSWORD_KEYCHAIN')]) {
                        script {
                            sh 'security unlock-keychain -p "${PASSWORD_KEYCHAIN}" "${KEYCHAIN_PATH}"'
                            sh 'echo $NOICE_KEYS | base64 --decode > ./keys.json'
                            echo 'Building APK...'
                            sh './gradlew clean'
                            sh './gradlew assembleQADebug'
                        }
                    }
                }
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Build APK Failed', '#FF5252')
                }
            }            
        }

        stage('Deploy to Firebase App Distribution') {
            steps {
                withCredentials([file(credentialsId: '9738c8a6-c2b1-44e1-9594-a4440e4d4fb1', variable: 'GOOGLE_APPLICATION_CREDENTIALS')]) {
                    echo 'Deploying to Firebase...'
                    script {
                        def firebaseOutput = sh(script: '''
                            firebase appdistribution:distribute ${ARTIFACT_PATH} \
                                --app ${FIREBASE_APP_ID} \
                                --groups ${FIREBASE_GROUP} \
                                --release-notes "Release from Jenkins with commit ID $(git rev-parse HEAD), Details: \n$(git log -1 --pretty=%B)" \
                                2>&1
                        ''', returnStdout: true).trim()
                    
                        // Print the output for debugging
                        echo "Firebase Output: ${firebaseOutput}"
                    
                        /// Extract the URLs from the output
                        def consoleUrlMatch = firebaseOutput =~ /View this release in the Firebase console: (https?:\/\/[^\s]+)/ 
                        def shareUrlMatch = firebaseOutput =~ /Share this release with testers who have access: (https?:\/\/[^\s]+)/ 
    
                        def consoleUrl = consoleUrlMatch ? consoleUrlMatch[0][1] : null
                        def shareUrl = shareUrlMatch ? shareUrlMatch[0][1] : null
    
                        // Check if URLs were extracted successfully
                        if (consoleUrl && shareUrl) {
                            echo "Console URL: ${consoleUrl}"
                            echo "Share URL: ${shareUrl}"
    
                            // Set the URLs to environment variables for later use
                            env.CONSOLE_URL = consoleUrl
                            env.SHARE_URL = shareUrl
                        } else {
                            error "Failed to extract release URLs from Firebase output"
                        }
                    }
                }
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Deployment to Firebase Failed', '#FF5252')
                }
            }
        }
    }

    post {
        success {
            sendGoogleChatNotification('SUCCESS', 'Deployment to Firebase Success', '#60a327')
            echo 'APK deployed successfully!'
        }
        failure {
            echo 'APK deployment failed!'
        }
        always {
            sh 'rm -f ./keys.json'
        }
    }
}

// Function to send Google Chat notifications
def sendGoogleChatNotification(String title, String message, String color) {
    def payload = """{
    "cardsV2": [
      {
        "cardId": "unique-card-id",
        "card": { 
          "header": {
            "title": "Build Status Android Apps",
            "subtitle": "Noice Mobile",
            "imageUrl": "https://s3-us-west-2.amazonaws.com/slack-files2/bot_icons/2022-06-17/3684546022083_48.png",
            "imageType": "SQUARE"
          },
          "sections": [
            {
              "collapsible": false,
              "uncollapsibleWidgetsCount": 1,
              "widgets": [
                {
                  "textParagraph": {
                    "text": "<font color=\\\"${color}\\\"><b>${title}</b></font>"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "PERSON"
                    },
                    "topLabel": "Author",
                    "text": "${env.GIT_AUTHOR}"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "DESCRIPTION"
                    },
                    "topLabel": "Release Notes",
                    "text": "${env.GIT_COMMIT_MESSAGE}"
                  }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "BOOKMARK"
                      },
                      "topLabel": "Branch",
                      "text": "${BRANCH_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "MAP_PIN"
                      },
                      "topLabel": "Job Name",
                      "text": "${env.JOB_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "CONFIRMATION_NUMBER_ICON"
                      },
                      "topLabel": "Message",
                      "text": "${message}"
                    }
                }, 
                {
                  "buttonList": {
                    "buttons": [
                      {
                        "text": "View Release in Firebase",
                        "onClick": {
                          "openLink": {
                            "url": "${env.CONSOLE_URL}"
                          }
                        }
                      },
                      {
                        "text": "Download for Tester Device",
                        "onClick": {
                          "openLink": {
                            "url": "${env.SHARE_URL}"
                          }
                        }
                      },
                      {
                        "text": "Jenkins Job Logs",
                        "onClick": {
                          "openLink": {
                            "url": "https://www.google.com/url?q=${env.BUILD_URL}/console"
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
          ]
        }
      }
    ]
  }"""

    sh """
        curl -X POST -H 'Content-Type: application/json' -d '${payload}' '${WEBHOOK_URL}'
    """
}