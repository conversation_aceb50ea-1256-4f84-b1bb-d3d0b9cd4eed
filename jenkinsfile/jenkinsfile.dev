#!/usr/bin/env groovy

import groovy.json.JsonOutput
import java.util.Optional

def slackNotificationChannel = "ci-alerts"
def author = ""
def message = ""

def notifySlack(text, channel, attachments) {
    def slackURL = '*********************************************************************************'

    def payload = JsonOutput.toJson([text: text,
        channel: channel,
        username: "<PERSON>",
        attachments: attachments
    ])

    sh "curl -X POST --data-urlencode \'payload=${payload}\' ${slackURL}"
}

def getGitAuthor = {
    def commit = sh(returnStdout: true, script: 'git rev-parse HEAD')
    author = sh(returnStdout: true, script: "git --no-pager show -s --format='%an' ${commit}").trim()
}

def getLastCommitMessage = {
    message = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
}

def populateGlobalVariables = {
    getLastCommitMessage()
    getGitAuthor()
}

node {
    try {
        stage('Checkout') {
            checkout scm
        }

        stage('Build Android APK') {
            sh './gradlew assembleDebug'
        }

        stage('Push APK to Google Storage'){   
            sh 'gsutil cp -r app/build/outputs/apk gs://noice-misc/Android_Build/'
        } 

        populateGlobalVariables()

        def buildColor = currentBuild.result == null ? "good" : "warning"
        def buildStatus = currentBuild.result == null ? "Success" : currentBuild.result
        
        notifySlack("", slackNotificationChannel, [
            [
                title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                title_link: "${env.BUILD_URL}",
                color: "${buildColor}",
                text: "${buildStatus}\n${author}",
                "mrkdwn_in": ["fields"],
                fields: [
                    [
                        title: "Last Commit",
                        value: "${message}",
                        short: false
                    ],
                    [
                        title: "Android Dev APK LINK",
                        value: "https://storage.googleapis.com/noice-misc/Android_Build/apk/development/debug/app-development-debug.apk"
                    ]
                ]
            ]
        ])          
    }
        
    catch (e) {
        def buildStatus = "Failed"

        notifySlack("", slackNotificationChannel, [
            [
                title: "${env.JOB_NAME}, build #${env.BUILD_NUMBER}",
                title_link: "${env.BUILD_URL}",
                color: "danger",
                author_name: "${author}",
                text: "${buildStatus}",
                fields: [
                    [
                        title: "Last Commit",
                        value: "${message}",
                        short: false
                    ],
                    [
                        title: "Error",
                        value: "${e}",
                        short: false
                    ]
                ]
            ]
        ])

        throw e
    }
}
