pipeline {
    agent { label 'mac_mini' }

    environment {
        FIREBASE_APP_ID = '1:363855535132:android:afa78d23de47d9453ed3d9' // Project Noice Firebase -> Noice Prod
        FIREBASE_GROUP = 'qa-team'
        ANDROID_HOME = '/opt/homebrew/share/android-commandlinetools' // Path to Android SDK
        // ARTIFACT_PATH = '**/build/outputs/**/*.aab' 
        ARTIFACT_PATH = 'app/build/outputs/bundle/**/*.aab'
        KEYCHAIN_PATH = '/Users/<USER>/Library/Keychains/login.keychain-db'
        // WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAoytLEO4/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cYNlO_Zb_wCCu5hWZimznj34O2hxdBJ8gQBb1t3blPE' // webhook alert-test
        WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAAueY_CYI/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=cGL46j_40Ty9SP172bfnW5brelW757U1bAieH5_ITCQ' // webhook android-builds
    }

    stages {
        stage('Get Commit Info and Release Name') {
            steps {
                // Create variable from git, parse to google chat notification 
                script {
                    env.GIT_COMMIT_MESSAGE = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()
                    env.GIT_AUTHOR = sh(returnStdout: true, script: 'git log -1 --pretty=%an').trim()
                    env.RELEASE_NAME = sh(returnStdout: true, script: "sed -n 's/^appVersionName = \"\\(.*\\)\"/\\1/p' gradle/libs.versions.toml").trim()
                }
            }
        }

        stage('Build APK') {
            steps {
                withCredentials([string(credentialsId: '75ed1097-f89e-4e0b-84b0-0cb08ba4a406', variable: 'NOICE_KEYS')]) {
                    withCredentials([string(credentialsId: 'ce21d274-a719-489d-aba6-3caa54a9284a', variable: 'PASSWORD_KEYCHAIN')]) {
                        script {
                            sh 'security unlock-keychain -p "${PASSWORD_KEYCHAIN}" "${KEYCHAIN_PATH}"'
                            sh 'echo $NOICE_KEYS | base64 --decode > ./keys.json'
                            echo 'Building APK...'
                            sh './gradlew clean'
                            sh './gradlew generateKeysProductionRelease'
                            sh './gradlew app:bundleProductionRelease'
                        }
                    }
                }
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Build AAB Failed', '#FF5252')
                }
            }
        }

        stage('Deploy to Google Play Console Internal Testing') {
            steps {
                androidApkUpload \
                    filesPattern: "${ARTIFACT_PATH}",
                    googleCredentialsId: 'noice-b16cb',
                    releaseName: "${env.RELEASE_NAME}",
                    rolloutPercentage: '0',
                    trackName: 'internal'
            }
            post {
                failure {
                    sendGoogleChatNotification('FAILED', 'Deployment to Google Play Console Failed', '#FF5252')
                }
            }
        }
    }

    post {
        success {
            sendGoogleChatNotification('SUCCESS', 'Deployment to Firebase Success', '#60a327')
            echo 'APK deployed successfully!'
        }
        failure {
            echo 'APK deployment failed!'
        }
        always {
            sh 'rm -f ./keys.json'
        }
    }
}

// Function to send Google Chat notifications
def sendGoogleChatNotification(String title, String message, String color) {
    def payload = """{
    "cardsV2": [
      {
        "cardId": "unique-card-id",
        "card": { 
          "header": {
            "title": "Build Status Android Apps",
            "subtitle": "Noice Mobile",
            "imageUrl": "https://s3-us-west-2.amazonaws.com/slack-files2/bot_icons/2022-06-17/3684546022083_48.png",
            "imageType": "SQUARE"
          },
          "sections": [
            {
              "collapsible": false,
              "uncollapsibleWidgetsCount": 1,
              "widgets": [
                {
                  "textParagraph": {
                    "text": "<font color=\\\"${color}\\\"><b>${title}</b></font>"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "PERSON"
                    },
                    "topLabel": "Author",
                    "text": "${env.GIT_AUTHOR}"
                  }
                },
                {
                  "decoratedText": {
                    "startIcon": {
                      "knownIcon": "DESCRIPTION"
                    },
                    "topLabel": "Release Notes",
                    "text": "${env.GIT_COMMIT_MESSAGE}"
                  }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "BOOKMARK"
                      },
                      "topLabel": "Branch",
                      "text": "${BRANCH_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "MAP_PIN"
                      },
                      "topLabel": "Job Name",
                      "text": "${env.JOB_NAME}"
                    }
                },
                {
                    "decoratedText": {
                      "startIcon": {
                        "knownIcon": "CONFIRMATION_NUMBER_ICON"
                      },
                      "topLabel": "Message",
                      "text": "${message}"
                    }
                }, 
                {
                  "buttonList": {
                    "buttons": [
                      {
                        "text": "Jenkins Job Logs",
                        "onClick": {
                          "openLink": {
                            "url": "https://www.google.com/url?q=${env.BUILD_URL}/console"
                          }
                        }
                      },
                      {
                        "text": "View Release in Play Console",
                        "onClick": {
                          "openLink": {
                            "url": "https://play.google.com/console/u/1/developers/7871237984706179858/app/4974876197998568123/tracks/internal-testing"
                          }
                        }
                      }
                    ]
                  }
                }
              ]
            }
          ]
        }
      }
    ]
  }"""

    sh """
        curl -X POST -H 'Content-Type: application/json' -d '${payload}' '${WEBHOOK_URL}'
    """
}